<template>
  <div class="market-analysis-panel" :class="{ 'dark-theme': isDarkTheme }">
    <a-card title="市场分析" size="small" :bordered="false">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新数据">
            <a-button size="small" type="text" @click="refreshData">
              <ReloadOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="分析设置">
            <a-button size="small" type="text" @click="showSettings = true">
              <SettingOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="导出报告">
            <a-button size="small" type="primary" @click="exportReport">
              <ExportOutlined /> 导出
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <div class="analysis-container">
        <!-- 市场概览 -->
        <div class="market-overview">
          <div class="overview-cards">
            <div class="market-card">
              <div class="card-header">
                <h4>市场总值</h4>
                <a-tooltip title="全球加密货币市值">
                  <InfoCircleOutlined class="info-icon" />
                </a-tooltip>
              </div>
              <div class="card-value">
                <span class="amount">{{ formatCurrency(marketCap) }}</span>
                <span class="change" :class="marketCapChange >= 0 ? 'positive' : 'negative'">
                  {{ formatPercentage(marketCapChange) }}
                </span>
              </div>
            </div>
            
            <div class="market-card">
              <div class="card-header">
                <h4>24h交易量</h4>
                <a-tooltip title="全球24小时交易量">
                  <InfoCircleOutlined class="info-icon" />
                </a-tooltip>
              </div>
              <div class="card-value">
                <span class="amount">{{ formatCurrency(volume24h) }}</span>
                <span class="change" :class="volume24hChange >= 0 ? 'positive' : 'negative'">
                  {{ formatPercentage(volume24hChange) }}
                </span>
              </div>
            </div>
            
            <div class="market-card">
              <div class="card-header">
                <h4>BTC占比</h4>
                <a-tooltip title="比特币市值占比">
                  <InfoCircleOutlined class="info-icon" />
                </a-tooltip>
              </div>
              <div class="card-value">
                <span class="amount">{{ formatPercentage(btcDominance) }}</span>
                <span class="trend" :class="btcDominanceTrend >= 0 ? 'positive' : 'negative'">
                  {{ btcDominanceTrend >= 0 ? '↗' : '↘' }}
                </span>
              </div>
            </div>
            
            <div class="market-card">
              <div class="card-header">
                <h4>恐慌指数</h4>
                <a-tooltip title="市场恐慌与贪婪指数">
                  <InfoCircleOutlined class="info-icon" />
                </a-tooltip>
              </div>
              <div class="card-value">
                <span class="amount" :class="getFearGreedClass(fearGreedIndex)">
                  {{ fearGreedIndex }}
                </span>
                <span class="sentiment">{{ getFearGreedText(fearGreedIndex) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 热门币种 -->
        <div class="hot-coins">
          <div class="section-header">
            <h4>热门币种</h4>
            <a-radio-group v-model:value="hotCoinsFilter" size="small">
              <a-radio-button value="gainers">涨幅榜</a-radio-button>
              <a-radio-button value="losers">跌幅榜</a-radio-button>
              <a-radio-button value="volume">成交量</a-radio-button>
            </a-radio-group>
          </div>
          
          <div class="coins-grid">
            <div 
              v-for="coin in filteredHotCoins" 
              :key="coin.symbol" 
              class="coin-card"
              @click="selectCoin(coin)"
            >
              <div class="coin-header">
                <div class="coin-info">
                  <img :src="coin.icon" :alt="coin.symbol" class="coin-icon" />
                  <div class="coin-name">
                    <div class="symbol">{{ coin.symbol }}</div>
                    <div class="name">{{ coin.name }}</div>
                  </div>
                </div>
                <div class="coin-price">
                  <div class="price">{{ formatCurrency(coin.price) }}</div>
                  <div class="change" :class="coin.change24h >= 0 ? 'positive' : 'negative'">
                    {{ formatPercentage(coin.change24h) }}
                  </div>
                </div>
              </div>
              
              <div class="coin-chart">
                <div :ref="el => setCoinChartRef(el, coin.symbol)" class="mini-chart"></div>
              </div>
              
              <div class="coin-stats">
                <div class="stat">
                  <span class="label">成交量</span>
                  <span class="value">{{ formatVolume(coin.volume) }}</span>
                </div>
                <div class="stat">
                  <span class="label">市值</span>
                  <span class="value">{{ formatVolume(coin.marketCap) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 技术分析 -->
        <div class="technical-analysis">
          <div class="section-header">
            <h4>技术分析</h4>
            <a-select
              v-model:value="selectedSymbol"
              size="small"
              style="width: 150px"
              @change="updateTechnicalAnalysis"
            >
              <a-select-option v-for="coin in hotCoins" :key="coin.symbol" :value="coin.symbol">
                {{ coin.symbol }}
              </a-select-option>
            </a-select>
          </div>
          
          <div class="analysis-content">
            <div class="indicators-summary">
              <div class="summary-card">
                <div class="summary-header">
                  <h5>综合评级</h5>
                  <div class="rating" :class="overallRating.class">
                    {{ overallRating.text }}
                  </div>
                </div>
                <div class="rating-breakdown">
                  <div class="rating-item">
                    <span class="label">技术指标</span>
                    <div class="rating-bar">
                      <div class="bar-bg">
                        <div 
                          class="bar-fill" 
                          :style="{ width: `${technicalScore}%`, backgroundColor: getTechnicalColor(technicalScore) }"
                        ></div>
                      </div>
                      <span class="score">{{ technicalScore }}/100</span>
                    </div>
                  </div>
                  
                  <div class="rating-item">
                    <span class="label">趋势分析</span>
                    <div class="rating-bar">
                      <div class="bar-bg">
                        <div 
                          class="bar-fill" 
                          :style="{ width: `${trendScore}%`, backgroundColor: getTrendColor(trendScore) }"
                        ></div>
                      </div>
                      <span class="score">{{ trendScore }}/100</span>
                    </div>
                  </div>
                  
                  <div class="rating-item">
                    <span class="label">动量指标</span>
                    <div class="rating-bar">
                      <div class="bar-bg">
                        <div 
                          class="bar-fill" 
                          :style="{ width: `${momentumScore}%`, backgroundColor: getMomentumColor(momentumScore) }"
                        ></div>
                      </div>
                      <span class="score">{{ momentumScore }}/100</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="indicators-grid">
                <div class="indicator-group">
                  <h6>移动平均线</h6>
                  <div class="indicators">
                    <div 
                      v-for="ma in movingAverages" 
                      :key="ma.period" 
                      class="indicator-item"
                    >
                      <span class="period">MA{{ ma.period }}</span>
                      <span class="value">{{ formatNumber(ma.value) }}</span>
                      <span class="signal" :class="ma.signal">
                        {{ getSignalText(ma.signal) }}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div class="indicator-group">
                  <h6>技术指标</h6>
                  <div class="indicators">
                    <div class="indicator-item">
                      <span class="period">RSI(14)</span>
                      <span class="value">{{ formatNumber(rsi) }}</span>
                      <span class="signal" :class="getRsiSignal(rsi)">
                        {{ getRsiText(rsi) }}
                      </span>
                    </div>
                    
                    <div class="indicator-item">
                      <span class="period">MACD</span>
                      <span class="value">{{ formatNumber(macd.value) }}</span>
                      <span class="signal" :class="macd.signal">
                        {{ getSignalText(macd.signal) }}
                      </span>
                    </div>
                    
                    <div class="indicator-item">
                      <span class="period">KDJ</span>
                      <span class="value">{{ formatNumber(kdj.k) }}</span>
                      <span class="signal" :class="kdj.signal">
                        {{ getSignalText(kdj.signal) }}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div class="indicator-group">
                  <h6>支撑阻力</h6>
                  <div class="indicators">
                    <div class="indicator-item">
                      <span class="period">支撑位</span>
                      <span class="value support">{{ formatCurrency(supportLevel) }}</span>
                      <span class="distance">-{{ formatPercentage(getSupportDistance()) }}</span>
                    </div>
                    
                    <div class="indicator-item">
                      <span class="period">阻力位</span>
                      <span class="value resistance">{{ formatCurrency(resistanceLevel) }}</span>
                      <span class="distance">+{{ formatPercentage(getResistanceDistance()) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 市场情绪 -->
        <div class="market-sentiment">
          <div class="section-header">
            <h4>市场情绪</h4>
            <a-radio-group v-model:value="sentimentTimeframe" size="small">
              <a-radio-button value="1h">1小时</a-radio-button>
              <a-radio-button value="4h">4小时</a-radio-button>
              <a-radio-button value="1d">1天</a-radio-button>
              <a-radio-button value="1w">1周</a-radio-button>
            </a-radio-group>
          </div>
          
          <div class="sentiment-content">
            <div class="sentiment-chart">
              <div ref="sentimentChart" class="chart-container"></div>
            </div>
            
            <div class="sentiment-indicators">
              <div class="sentiment-card">
                <div class="card-title">多空比例</div>
                <div class="ratio-display">
                  <div class="ratio-bar">
                    <div class="long-bar" :style="{ width: `${longShortRatio.long}%` }"></div>
                    <div class="short-bar" :style="{ width: `${longShortRatio.short}%` }"></div>
                  </div>
                  <div class="ratio-labels">
                    <span class="long-label">多头 {{ longShortRatio.long }}%</span>
                    <span class="short-label">空头 {{ longShortRatio.short }}%</span>
                  </div>
                </div>
              </div>
              
              <div class="sentiment-card">
                <div class="card-title">资金流向</div>
                <div class="flow-display">
                  <div class="flow-item">
                    <span class="flow-label">净流入</span>
                    <span class="flow-value positive">{{ formatCurrency(netInflow) }}</span>
                  </div>
                  <div class="flow-item">
                    <span class="flow-label">大单流入</span>
                    <span class="flow-value" :class="largeOrderFlow >= 0 ? 'positive' : 'negative'">
                      {{ formatCurrency(Math.abs(largeOrderFlow)) }}
                    </span>
                  </div>
                  <div class="flow-item">
                    <span class="flow-label">散户流入</span>
                    <span class="flow-value" :class="retailFlow >= 0 ? 'positive' : 'negative'">
                      {{ formatCurrency(Math.abs(retailFlow)) }}
                    </span>
                  </div>
                </div>
              </div>
              
              <div class="sentiment-card">
                <div class="card-title">社交情绪</div>
                <div class="social-sentiment">
                  <div class="sentiment-meter">
                    <div class="meter-bg">
                      <div 
                        class="meter-fill" 
                        :style="{ 
                          width: `${socialSentiment}%`, 
                          backgroundColor: getSentimentColor(socialSentiment) 
                        }"
                      ></div>
                    </div>
                    <div class="meter-labels">
                      <span>悲观</span>
                      <span>中性</span>
                      <span>乐观</span>
                    </div>
                  </div>
                  <div class="sentiment-score">{{ socialSentiment }}/100</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 相关性分析 -->
        <div class="correlation-analysis">
          <div class="section-header">
            <h4>相关性分析</h4>
            <a-select
              v-model:value="correlationPeriod"
              size="small"
              style="width: 120px"
              @change="updateCorrelation"
            >
              <a-select-option value="7d">7天</a-select-option>
              <a-select-option value="30d">30天</a-select-option>
              <a-select-option value="90d">90天</a-select-option>
            </a-select>
          </div>
          
          <div class="correlation-matrix">
            <div class="matrix-header">
              <div class="header-cell"></div>
              <div v-for="symbol in correlationSymbols" :key="symbol" class="header-cell">
                {{ symbol }}
              </div>
            </div>
            
            <div v-for="(row, i) in correlationMatrix" :key="i" class="matrix-row">
              <div class="row-header">{{ correlationSymbols[i] }}</div>
              <div 
                v-for="(value, j) in row" 
                :key="j" 
                class="matrix-cell"
                :style="{ backgroundColor: getCorrelationColor(value) }"
              >
                {{ formatCorrelation(value) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="分析设置"
      @ok="saveSettings"
      @cancel="resetSettings"
      width="600px"
    >
      <a-form :model="settings" layout="vertical">
        <a-form-item label="显示设置">
          <a-space direction="vertical" style="width: 100%">
            <div>
              <a-switch v-model:checked="settings.showMiniCharts" />
              <span class="setting-label">显示迷你图表</span>
            </div>
            
            <div>
              <a-switch v-model:checked="settings.autoRefresh" />
              <span class="setting-label">自动刷新数据</span>
            </div>
            <div v-if="settings.autoRefresh" style="margin-left: 24px;">
              <a-form-item label="刷新间隔 (秒)" style="margin-bottom: 8px">
                <a-input-number
                  v-model:value="settings.refreshInterval"
                  :min="10"
                  :max="300"
                  style="width: 200px"
                />
              </a-form-item>
            </div>
            
            <div>
              <a-switch v-model:checked="settings.enableNotifications" />
              <span class="setting-label">启用通知</span>
            </div>
          </a-space>
        </a-form-item>
        
        <a-form-item label="技术指标设置">
          <a-checkbox-group v-model:value="settings.enabledIndicators">
            <a-checkbox value="ma">移动平均线</a-checkbox>
            <a-checkbox value="rsi">RSI</a-checkbox>
            <a-checkbox value="macd">MACD</a-checkbox>
            <a-checkbox value="kdj">KDJ</a-checkbox>
            <a-checkbox value="bollinger">布林带</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="警报设置">
          <a-space direction="vertical" style="width: 100%">
            <div>
              <a-switch v-model:checked="settings.priceAlerts" />
              <span class="setting-label">价格警报</span>
            </div>
            
            <div>
              <a-switch v-model:checked="settings.volumeAlerts" />
              <span class="setting-label">成交量警报</span>
            </div>
            
            <div>
              <a-switch v-model:checked="settings.sentimentAlerts" />
              <span class="setting-label">情绪警报</span>
            </div>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { message } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { useChartStore } from '@/stores/chartStore'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  SettingOutlined,
  ExportOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'

// 定义事件
const emit = defineEmits(['coin-selected', 'analysis-updated'])

// 使用状态管理
const chartStore = useChartStore()
const { isDarkTheme } = storeToRefs(chartStore)

// 本地状态
const showSettings = ref(false)
const sentimentChart = ref(null)
const coinChartRefs = ref(new Map())
const updateInterval = ref(null)

// 过滤和选择
const hotCoinsFilter = ref('gainers')
const selectedSymbol = ref('BTC-USDT')
const sentimentTimeframe = ref('1d')
const correlationPeriod = ref('30d')

// 市场数据
const marketCap = ref(2150000000000)
const marketCapChange = ref(2.35)
const volume24h = ref(85600000000)
const volume24hChange = ref(-1.25)
const btcDominance = ref(52.8)
const btcDominanceTrend = ref(0.5)
const fearGreedIndex = ref(65)

// 热门币种数据
const hotCoins = ref([
  {
    symbol: 'BTC-USDT',
    name: 'Bitcoin',
    icon: '/icons/btc.png',
    price: 42580.50,
    change24h: 3.25,
    volume: 1250000000,
    marketCap: 835000000000,
    chartData: [42000, 42100, 42300, 42200, 42580]
  },
  {
    symbol: 'ETH-USDT',
    name: 'Ethereum',
    icon: '/icons/eth.png',
    price: 2485.80,
    change24h: -1.85,
    volume: 850000000,
    marketCap: 298000000000,
    chartData: [2500, 2520, 2480, 2490, 2485]
  },
  {
    symbol: 'BNB-USDT',
    name: 'BNB',
    icon: '/icons/bnb.png',
    price: 315.60,
    change24h: 0.95,
    volume: 420000000,
    marketCap: 47000000000,
    chartData: [312, 314, 318, 316, 315]
  },
  {
    symbol: 'ADA-USDT',
    name: 'Cardano',
    icon: '/icons/ada.png',
    price: 0.485,
    change24h: 5.20,
    volume: 180000000,
    marketCap: 17000000000,
    chartData: [0.46, 0.47, 0.48, 0.49, 0.485]
  }
])

// 技术分析数据
const technicalScore = ref(72)
const trendScore = ref(68)
const momentumScore = ref(75)

const movingAverages = ref([
  { period: 5, value: 42450.20, signal: 'bullish' },
  { period: 10, value: 42380.50, signal: 'bullish' },
  { period: 20, value: 42200.80, signal: 'bullish' },
  { period: 50, value: 41850.30, signal: 'bullish' }
])

const rsi = ref(68.5)
const macd = ref({ value: 125.80, signal: 'bullish' })
const kdj = ref({ k: 72.5, signal: 'bullish' })
const supportLevel = ref(41800)
const resistanceLevel = ref(43200)

// 市场情绪数据
const longShortRatio = ref({ long: 65, short: 35 })
const netInflow = ref(125800000)
const largeOrderFlow = ref(85600000)
const retailFlow = ref(40200000)
const socialSentiment = ref(72)

// 相关性数据
const correlationSymbols = ref(['BTC', 'ETH', 'BNB', 'ADA'])
const correlationMatrix = ref([
  [1.00, 0.85, 0.72, 0.68],
  [0.85, 1.00, 0.78, 0.75],
  [0.72, 0.78, 1.00, 0.65],
  [0.68, 0.75, 0.65, 1.00]
])

// 设置
const settings = ref({
  showMiniCharts: true,
  autoRefresh: true,
  refreshInterval: 30,
  enableNotifications: true,
  enabledIndicators: ['ma', 'rsi', 'macd', 'kdj'],
  priceAlerts: true,
  volumeAlerts: false,
  sentimentAlerts: true
})

// 计算属性
const filteredHotCoins = computed(() => {
  let filtered = [...hotCoins.value]
  
  switch (hotCoinsFilter.value) {
    case 'gainers':
      return filtered.sort((a, b) => b.change24h - a.change24h).slice(0, 8)
    case 'losers':
      return filtered.sort((a, b) => a.change24h - b.change24h).slice(0, 8)
    case 'volume':
      return filtered.sort((a, b) => b.volume - a.volume).slice(0, 8)
    default:
      return filtered.slice(0, 8)
  }
})

const overallRating = computed(() => {
  const avgScore = (technicalScore.value + trendScore.value + momentumScore.value) / 3
  
  if (avgScore >= 80) {
    return { text: '强烈买入', class: 'strong-buy' }
  } else if (avgScore >= 60) {
    return { text: '买入', class: 'buy' }
  } else if (avgScore >= 40) {
    return { text: '中性', class: 'neutral' }
  } else if (avgScore >= 20) {
    return { text: '卖出', class: 'sell' }
  } else {
    return { text: '强烈卖出', class: 'strong-sell' }
  }
})

/**
 * 设置币种图表引用
 */
const setCoinChartRef = (el, symbol) => {
  if (el) {
    coinChartRefs.value.set(symbol, el)
  }
}

/**
 * 获取恐慌指数样式类
 */
const getFearGreedClass = (index) => {
  if (index <= 25) return 'extreme-fear'
  if (index <= 45) return 'fear'
  if (index <= 55) return 'neutral'
  if (index <= 75) return 'greed'
  return 'extreme-greed'
}

/**
 * 获取恐慌指数文本
 */
const getFearGreedText = (index) => {
  if (index <= 25) return '极度恐慌'
  if (index <= 45) return '恐慌'
  if (index <= 55) return '中性'
  if (index <= 75) return '贪婪'
  return '极度贪婪'
}

/**
 * 获取技术指标颜色
 */
const getTechnicalColor = (score) => {
  if (score >= 70) return '#52c41a'
  if (score >= 50) return '#faad14'
  return '#f5222d'
}

const getTrendColor = getTechnicalColor
const getMomentumColor = getTechnicalColor

/**
 * 获取信号文本
 */
const getSignalText = (signal) => {
  switch (signal) {
    case 'bullish': return '看涨'
    case 'bearish': return '看跌'
    default: return '中性'
  }
}

/**
 * 获取RSI信号
 */
const getRsiSignal = (value) => {
  if (value >= 70) return 'bearish'
  if (value <= 30) return 'bullish'
  return 'neutral'
}

/**
 * 获取RSI文本
 */
const getRsiText = (value) => {
  if (value >= 70) return '超买'
  if (value <= 30) return '超卖'
  return '正常'
}

/**
 * 获取支撑位距离
 */
const getSupportDistance = () => {
  const currentPrice = hotCoins.value.find(c => c.symbol === selectedSymbol.value)?.price || 42580
  return ((currentPrice - supportLevel.value) / currentPrice * 100)
}

/**
 * 获取阻力位距离
 */
const getResistanceDistance = () => {
  const currentPrice = hotCoins.value.find(c => c.symbol === selectedSymbol.value)?.price || 42580
  return ((resistanceLevel.value - currentPrice) / currentPrice * 100)
}

/**
 * 获取情绪颜色
 */
const getSentimentColor = (sentiment) => {
  if (sentiment <= 30) return '#f5222d'
  if (sentiment <= 70) return '#faad14'
  return '#52c41a'
}

/**
 * 获取相关性颜色
 */
const getCorrelationColor = (value) => {
  const intensity = Math.abs(value)
  if (value > 0) {
    return `rgba(82, 196, 26, ${intensity})`
  } else {
    return `rgba(245, 34, 45, ${intensity})`
  }
}

/**
 * 格式化货币
 */
const formatCurrency = (value) => {
  if (value >= 1000000000) {
    return `$${(value / 1000000000).toFixed(2)}B`
  } else if (value >= 1000000) {
    return `$${(value / 1000000).toFixed(2)}M`
  } else if (value >= 1000) {
    return `$${(value / 1000).toFixed(2)}K`
  }
  return `$${value.toFixed(2)}`
}

/**
 * 格式化成交量
 */
const formatVolume = (value) => {
  if (value >= 1000000000) {
    return `${(value / 1000000000).toFixed(2)}B`
  } else if (value >= 1000000) {
    return `${(value / 1000000).toFixed(2)}M`
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(2)}K`
  }
  return value.toFixed(2)
}

/**
 * 格式化数字
 */
const formatNumber = (value, precision = 2) => {
  return value.toFixed(precision)
}

/**
 * 格式化百分比
 */
const formatPercentage = (value) => {
  return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`
}

/**
 * 格式化相关性
 */
const formatCorrelation = (value) => {
  return value.toFixed(2)
}

/**
 * 选择币种
 */
const selectCoin = (coin) => {
  selectedSymbol.value = coin.symbol
  updateTechnicalAnalysis()
  emit('coin-selected', coin)
}

/**
 * 更新技术分析
 */
const updateTechnicalAnalysis = () => {
  // 模拟更新技术分析数据
  technicalScore.value = Math.floor(Math.random() * 40) + 50
  trendScore.value = Math.floor(Math.random() * 40) + 50
  momentumScore.value = Math.floor(Math.random() * 40) + 50
  
  // 更新指标数据
  movingAverages.value.forEach(ma => {
    ma.value += (Math.random() - 0.5) * 100
    ma.signal = Math.random() > 0.5 ? 'bullish' : 'bearish'
  })
  
  rsi.value = Math.random() * 100
  macd.value.value = (Math.random() - 0.5) * 200
  macd.value.signal = Math.random() > 0.5 ? 'bullish' : 'bearish'
  
  emit('analysis-updated', {
    symbol: selectedSymbol.value,
    technical: technicalScore.value,
    trend: trendScore.value,
    momentum: momentumScore.value
  })
}

/**
 * 更新相关性
 */
const updateCorrelation = () => {
  // 模拟更新相关性矩阵
  correlationMatrix.value = correlationMatrix.value.map(row => 
    row.map(value => {
      if (value === 1.00) return 1.00
      return Math.max(-1, Math.min(1, value + (Math.random() - 0.5) * 0.2))
    })
  )
}

/**
 * 初始化迷你图表
 */
const initMiniCharts = () => {
  if (!settings.value.showMiniCharts) return
  
  coinChartRefs.value.forEach((chartEl, symbol) => {
    if (!chartEl) return
    
    const coin = hotCoins.value.find(c => c.symbol === symbol)
    if (!coin) return
    
    const chart = echarts.init(chartEl)
    
    const option = {
      grid: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0
      },
      xAxis: {
        type: 'category',
        show: false,
        data: coin.chartData.map((_, i) => i)
      },
      yAxis: {
        type: 'value',
        show: false
      },
      series: [{
        type: 'line',
        data: coin.chartData,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: coin.change24h >= 0 ? '#52c41a' : '#f5222d',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: coin.change24h >= 0 ? 'rgba(82, 196, 26, 0.3)' : 'rgba(245, 34, 45, 0.3)'
            }, {
              offset: 1,
              color: coin.change24h >= 0 ? 'rgba(82, 196, 26, 0.05)' : 'rgba(245, 34, 45, 0.05)'
            }]
          }
        }
      }]
    }
    
    chart.setOption(option)
  })
}

/**
 * 初始化情绪图表
 */
const initSentimentChart = () => {
  if (!sentimentChart.value) return
  
  const chart = echarts.init(sentimentChart.value)
  
  // 生成模拟数据
  const hours = []
  const sentimentData = []
  const volumeData = []
  
  for (let i = 23; i >= 0; i--) {
    hours.push(`${i}:00`)
    sentimentData.push(Math.floor(Math.random() * 40) + 40)
    volumeData.push(Math.floor(Math.random() * 1000) + 500)
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['情绪指数', '交易量'],
      textStyle: {
        color: isDarkTheme.value ? '#fff' : '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: hours,
      axisLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#434343' : '#d9d9d9'
        }
      },
      axisLabel: {
        color: isDarkTheme.value ? '#999' : '#666'
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '情绪指数',
        min: 0,
        max: 100,
        axisLine: {
          lineStyle: {
            color: isDarkTheme.value ? '#434343' : '#d9d9d9'
          }
        },
        axisLabel: {
          color: isDarkTheme.value ? '#999' : '#666'
        },
        splitLine: {
          lineStyle: {
            color: isDarkTheme.value ? '#434343' : '#f0f0f0'
          }
        }
      },
      {
        type: 'value',
        name: '交易量',
        axisLine: {
          lineStyle: {
            color: isDarkTheme.value ? '#434343' : '#d9d9d9'
          }
        },
        axisLabel: {
          color: isDarkTheme.value ? '#999' : '#666'
        }
      }
    ],
    series: [
      {
        name: '情绪指数',
        type: 'line',
        data: sentimentData,
        smooth: true,
        lineStyle: {
          color: '#1890ff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: 'rgba(24, 144, 255, 0.3)'
            }, {
              offset: 1,
              color: 'rgba(24, 144, 255, 0.05)'
            }]
          }
        }
      },
      {
        name: '交易量',
        type: 'bar',
        yAxisIndex: 1,
        data: volumeData,
        itemStyle: {
          color: 'rgba(82, 196, 26, 0.6)'
        }
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式调整
  const resizeChart = () => {
    chart.resize()
  }
  
  window.addEventListener('resize', resizeChart)
  
  return () => {
    window.removeEventListener('resize', resizeChart)
    chart.dispose()
  }
}

/**
 * 导出报告
 */
const exportReport = () => {
  message.info('正在生成市场分析报告...')
  // 这里可以实现导出报告的逻辑
}

/**
 * 刷新数据
 */
const refreshData = () => {
  // 模拟数据刷新
  marketCap.value += (Math.random() - 0.5) * 100000000000
  marketCapChange.value = (Math.random() - 0.5) * 5
  volume24h.value += (Math.random() - 0.5) * 10000000000
  volume24hChange.value = (Math.random() - 0.5) * 3
  btcDominance.value += (Math.random() - 0.5) * 2
  fearGreedIndex.value = Math.max(0, Math.min(100, fearGreedIndex.value + (Math.random() - 0.5) * 10))
  
  // 更新热门币种
  hotCoins.value.forEach(coin => {
    coin.price += (Math.random() - 0.5) * coin.price * 0.05
    coin.change24h += (Math.random() - 0.5) * 2
    coin.volume += (Math.random() - 0.5) * coin.volume * 0.1
    
    // 更新图表数据
    coin.chartData.shift()
    coin.chartData.push(coin.price)
  })
  
  // 更新情绪数据
  longShortRatio.value.long = Math.max(20, Math.min(80, longShortRatio.value.long + (Math.random() - 0.5) * 10))
  longShortRatio.value.short = 100 - longShortRatio.value.long
  
  netInflow.value += (Math.random() - 0.5) * 50000000
  largeOrderFlow.value += (Math.random() - 0.5) * 30000000
  retailFlow.value += (Math.random() - 0.5) * 20000000
  socialSentiment.value = Math.max(0, Math.min(100, socialSentiment.value + (Math.random() - 0.5) * 5))
  
  updateTechnicalAnalysis()
  updateCorrelation()
  
  message.success('数据已刷新')
}

/**
 * 保存设置
 */
const saveSettings = () => {
  localStorage.setItem('market-analysis-settings', JSON.stringify(settings.value))
  showSettings.value = false
  message.success('设置已保存')
  
  // 重新初始化图表
  if (settings.value.showMiniCharts) {
    nextTick(() => {
      initMiniCharts()
    })
  }
}

/**
 * 重置设置
 */
const resetSettings = () => {
  settings.value = {
    showMiniCharts: true,
    autoRefresh: true,
    refreshInterval: 30,
    enableNotifications: true,
    enabledIndicators: ['ma', 'rsi', 'macd', 'kdj'],
    priceAlerts: true,
    volumeAlerts: false,
    sentimentAlerts: true
  }
}

/**
 * 加载设置
 */
const loadSettings = () => {
  const saved = localStorage.getItem('market-analysis-settings')
  if (saved) {
    try {
      settings.value = { ...settings.value, ...JSON.parse(saved) }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }
}

/**
 * 启动自动刷新
 */
const startAutoRefresh = () => {
  if (!settings.value.autoRefresh || updateInterval.value) return
  
  updateInterval.value = setInterval(() => {
    refreshData()
  }, settings.value.refreshInterval * 1000)
}

/**
 * 停止自动刷新
 */
const stopAutoRefresh = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
    updateInterval.value = null
  }
}

// 生命周期
onMounted(() => {
  loadSettings()
  nextTick(() => {
    initSentimentChart()
    if (settings.value.showMiniCharts) {
      initMiniCharts()
    }
  })
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})

// 监听设置变化
watch(() => settings.value.autoRefresh, (newValue) => {
  if (newValue) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})

watch(() => settings.value.refreshInterval, () => {
  if (settings.value.autoRefresh) {
    stopAutoRefresh()
    startAutoRefresh()
  }
})

watch(() => settings.value.showMiniCharts, (newValue) => {
  if (newValue) {
    nextTick(() => {
      initMiniCharts()
    })
  }
})

watch(isDarkTheme, () => {
  nextTick(() => {
    initSentimentChart()
  })
})
</script>

<style scoped>
.market-analysis-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.analysis-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.market-overview {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.market-card {
  background: white;
  border-radius: 6px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.card-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.info-icon {
  color: #999;
  font-size: 12px;
}

.card-value {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}

.amount {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.change {
  font-size: 12px;
  font-weight: 500;
}

.trend {
  font-size: 16px;
  font-weight: 600;
}

.sentiment {
  font-size: 12px;
  color: #999;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.hot-coins {
  flex: 1;
}

.coins-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
}

.coin-card {
  background: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
}

.coin-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.coin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.coin-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.coin-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.coin-name .symbol {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.coin-name .name {
  font-size: 12px;
  color: #999;
}

.coin-price {
  text-align: right;
}

.coin-price .price {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.coin-price .change {
  font-size: 12px;
  font-weight: 500;
}

.coin-chart {
  height: 60px;
  margin: 8px 0;
}

.mini-chart {
  width: 100%;
  height: 100%;
}

.coin-stats {
  display: flex;
  justify-content: space-between;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat .label {
  font-size: 11px;
  color: #999;
  margin-bottom: 2px;
}

.stat .value {
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.technical-analysis {
  margin-top: 16px;
}

.analysis-content {
  display: flex;
  gap: 16px;
}

.indicators-summary {
  flex: 1;
}

.summary-card {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.summary-header h5 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.rating {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.rating.strong-buy {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.rating.buy {
  background: #f6ffed;
  color: #73d13d;
  border: 1px solid #b7eb8f;
}

.rating.neutral {
  background: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.rating.sell {
  background: #fff2e8;
  color: #ff7a45;
  border: 1px solid #ffbb96;
}

.rating.strong-sell {
  background: #fff1f0;
  color: #f5222d;
  border: 1px solid #ffa39e;
}

.rating-breakdown {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rating-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rating-item .label {
  width: 80px;
  font-size: 12px;
  color: #666;
}

.rating-bar {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.bar-bg {
  flex: 1;
  height: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s;
}

.score {
  font-size: 12px;
  color: #666;
  min-width: 50px;
}

.indicators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.indicator-group {
  background: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #f0f0f0;
}

.indicator-group h6 {
  margin: 0 0 12px 0;
  font-size: 13px;
  font-weight: 600;
  color: #333;
}

.indicators {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.indicator-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.indicator-item .period {
  color: #666;
  min-width: 60px;
}

.indicator-item .value {
  color: #333;
  font-weight: 500;
  min-width: 80px;
  text-align: right;
}

.indicator-item .signal {
  font-weight: 500;
  min-width: 40px;
  text-align: right;
}

.indicator-item .distance {
  font-size: 11px;
  color: #999;
}

.signal.bullish {
  color: #52c41a;
}

.signal.bearish {
  color: #f5222d;
}

.signal.neutral {
  color: #faad14;
}

.value.support {
  color: #52c41a;
}

.value.resistance {
  color: #f5222d;
}

.market-sentiment {
  margin-top: 16px;
}

.sentiment-content {
  display: flex;
  gap: 16px;
}

.sentiment-chart {
  flex: 2;
  background: white;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #f0f0f0;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.sentiment-indicators {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sentiment-card {
  background: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #f0f0f0;
}

.card-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.ratio-display {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ratio-bar {
  display: flex;
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
}

.long-bar {
  background: #52c41a;
  transition: width 0.3s;
}

.short-bar {
  background: #f5222d;
  transition: width 0.3s;
}

.ratio-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.long-label {
  color: #52c41a;
}

.short-label {
  color: #f5222d;
}

.flow-display {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.flow-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.flow-label {
  color: #666;
}

.flow-value {
  font-weight: 500;
}

.social-sentiment {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sentiment-meter {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meter-bg {
  height: 20px;
  background: #f5f5f5;
  border-radius: 10px;
  overflow: hidden;
}

.meter-fill {
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s;
}

.meter-labels {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #999;
}

.sentiment-score {
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.correlation-analysis {
  margin-top: 16px;
}

.correlation-matrix {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  overflow-x: auto;
}

.matrix-header {
  display: flex;
  margin-bottom: 8px;
}

.header-cell {
  min-width: 60px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.matrix-row {
  display: flex;
  margin-bottom: 4px;
}

.row-header {
  min-width: 60px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: #333;
  background: #fafafa;
  border-radius: 4px;
}

.matrix-cell {
  min-width: 60px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 500;
  color: #333;
  border-radius: 4px;
  margin-right: 4px;
}

.setting-label {
  margin-left: 8px;
  font-size: 14px;
}

/* 颜色定义 */
.positive {
  color: #52c41a;
}

.negative {
  color: #f5222d;
}

.extreme-fear {
  color: #f5222d;
}

.fear {
  color: #ff7a45;
}

.neutral {
  color: #faad14;
}

.greed {
  color: #73d13d;
}

.extreme-greed {
  color: #52c41a;
}

/* 深色主题 */
.dark-theme .market-overview {
  background: #1f1f1f;
}

.dark-theme .market-card,
.dark-theme .coin-card,
.dark-theme .summary-card,
.dark-theme .indicator-group,
.dark-theme .sentiment-card,
.dark-theme .sentiment-chart,
.dark-theme .correlation-matrix {
  background: #262626;
  border-color: #434343;
}

.dark-theme .card-header h4,
.dark-theme .section-header h4,
.dark-theme .summary-header h5,
.dark-theme .indicator-group h6,
.dark-theme .card-title,
.dark-theme .amount,
.dark-theme .coin-name .symbol,
.dark-theme .coin-price .price,
.dark-theme .indicator-item .value,
.dark-theme .sentiment-score {
  color: #fff;
}

.dark-theme .info-icon,
.dark-theme .sentiment,
.dark-theme .coin-name .name,
.dark-theme .stat .label,
.dark-theme .rating-item .label,
.dark-theme .indicator-item .period,
.dark-theme .indicator-item .distance,
.dark-theme .flow-label,
.dark-theme .meter-labels {
  color: #999;
}

.dark-theme .stat .value,
.dark-theme .score {
  color: #d9d9d9;
}

.dark-theme .bar-bg,
.dark-theme .meter-bg {
  background: #434343;
}

.dark-theme .row-header {
  background: #1f1f1f;
  color: #fff;
}

.dark-theme .header-cell,
.dark-theme .matrix-cell {
  color: #fff;
}
</style>