<template>
  <div>
    <a-table :dataSource="orders" :columns="columns" rowKey="id">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-button type="link" @click="showDetail(record.id)">详情</a-button>
          <a-popconfirm v-if="record.status === 'submitted'" title="确定撤销该订单吗？" @confirm="cancelOrder(record.id)">
            <a-button type="link" danger>撤单</a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <a-modal :open="detailVisible" @update:open="detailVisible = $event" title="订单详情" :footer="null">
      <a-descriptions :column="1" v-if="detail">
        <a-descriptions-item label="ID">{{ detail.id }}</a-descriptions-item>
        <a-descriptions-item label="交易对">{{ detail.inst_id }}</a-descriptions-item>
        <a-descriptions-item label="方向">{{ detail.side }}</a-descriptions-item>
        <a-descriptions-item label="价格">{{ detail.price }}</a-descriptions-item>
        <a-descriptions-item label="数量">{{ detail.size }}</a-descriptions-item>
        <a-descriptions-item label="类型">{{ detail.order_type }}</a-descriptions-item>
        <a-descriptions-item label="状态">{{ detail.status }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ detail.created_at }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
const orders = ref([])
const columns = [
  { title: 'ID', dataIndex: 'id' },
  { title: '交易对', dataIndex: 'inst_id' },
  { title: '方向', dataIndex: 'side' },
  { title: '价格', dataIndex: 'price' },
  { title: '数量', dataIndex: 'size' },
  { title: '类型', dataIndex: 'order_type' },
  { title: '状态', dataIndex: 'status' },
  { title: '创建时间', dataIndex: 'created_at' },
  { title: '操作', key: 'action' }
]
const detailVisible = ref(false)
const detail = ref(null)
async function fetchOrders() {
  const res = await axios.get('/api/order/list')
  if (res.data.code === 0) {
    orders.value = res.data.data
  }
}
async function showDetail(id) {
  const res = await axios.get(`/api/order/detail/${id}`)
  if (res.data.code === 0) {
    detail.value = res.data.data
    detailVisible.value = true
  }
}
async function cancelOrder(id) {
  try {
    await axios.post(`/api/order/cancel/${id}`)
    fetchOrders()
  } catch (e) {
    alert('撤单失败')
  }
}
onMounted(fetchOrders)
</script> 