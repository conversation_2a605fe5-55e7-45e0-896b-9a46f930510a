#!/usr/bin/env python3
"""
配置OKX API密钥的脚本
"""

import sqlite3

def setup_okx_api():
    print("=== OKX API密钥配置 ===")
    print("请输入您的OKX API信息：")
    
    # 从config.env文件读取或手动输入
    api_key = input("API Key: ").strip()
    if not api_key:
        print("请输入有效的API Key")
        return
    
    api_secret = input("Secret Key (从config.env中的OKX_SECRET_KEY): ").strip()
    if not api_secret:
        api_secret = "B036379F9F56E5A83FB30EF27D7C57CF"  # 使用您提供的密钥
    
    passphrase = input("Passphrase: ").strip()
    if not passphrase:
        print("请输入有效的Passphrase")
        return
    
    environment = input("环境 (demo/live) [demo]: ").strip() or "demo"
    
    # 连接数据库
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    
    # 更新admin用户的API配置
    cursor.execute('''
        UPDATE user 
        SET api_key = ?, api_secret = ?, passphrase = ?
        WHERE username = 'admin'
    ''', (api_key, api_secret, passphrase))
    
    if cursor.rowcount > 0:
        conn.commit()
        print(f"✅ API密钥配置成功！")
        print(f"   API Key: {api_key}")
        print(f"   Secret Key: {api_secret[:8]}****")
        print(f"   Passphrase: {passphrase[:4]}****")
        print(f"   环境: {environment}")
    else:
        print("❌ 配置失败：找不到admin用户")
    
    conn.close()

if __name__ == "__main__":
    setup_okx_api()
