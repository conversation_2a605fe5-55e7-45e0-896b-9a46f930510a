import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import axios from 'axios'
import { message } from 'ant-design-vue'
import { createPinia } from 'pinia'
// import './assets/main.css'

// axios 请求拦截器，自动携带 token
axios.interceptors.request.use(config => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = 'Bearer ' + token
  }
  return config
})

// axios 响应拦截器，自动处理 token 过期
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response && error.response.status === 401) {
      message.error('登录已过期，请重新登录')
      localStorage.removeItem('token')
      setTimeout(() => {
        window.location.href = '/login'
      }, 800)
    }
    return Promise.reject(error)
  }
)

const app = createApp(App)
app.use(router)
app.use(Antd)
app.use(createPinia())
app.mount('#app')
