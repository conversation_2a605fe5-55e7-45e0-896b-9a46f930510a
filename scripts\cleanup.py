#!/usr/bin/env python3
"""
项目清理脚本
清理临时文件、缓存、日志等
"""

import os
import shutil
import glob
from pathlib import Path
import argparse

def remove_pycache():
    """删除Python缓存文件"""
    print("🧹 清理Python缓存...")
    count = 0
    
    for root, dirs, files in os.walk('.'):
        # 删除 __pycache__ 目录
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            shutil.rmtree(pycache_path)
            print(f"  删除: {pycache_path}")
            count += 1
        
        # 删除 .pyc 文件
        for file in files:
            if file.endswith('.pyc') or file.endswith('.pyo'):
                file_path = os.path.join(root, file)
                os.remove(file_path)
                print(f"  删除: {file_path}")
                count += 1
    
    print(f"✅ 清理了 {count} 个Python缓存文件/目录")

def remove_node_cache():
    """删除Node.js缓存"""
    print("🧹 清理Node.js缓存...")
    count = 0
    
    # 清理前端缓存
    frontend_path = Path("web-frontend")
    if frontend_path.exists():
        cache_dirs = [
            frontend_path / "node_modules" / ".cache",
            frontend_path / ".vite",
            frontend_path / "dist",
            frontend_path / ".nuxt",
            frontend_path / ".next"
        ]
        
        for cache_dir in cache_dirs:
            if cache_dir.exists():
                shutil.rmtree(cache_dir)
                print(f"  删除: {cache_dir}")
                count += 1
    
    print(f"✅ 清理了 {count} 个Node.js缓存目录")

def remove_logs():
    """删除日志文件"""
    print("🧹 清理日志文件...")
    count = 0
    
    # 删除根目录下的日志文件
    log_patterns = ['*.log', 'logs/*.log', 'web-frontend/*.log']
    
    for pattern in log_patterns:
        for log_file in glob.glob(pattern):
            os.remove(log_file)
            print(f"  删除: {log_file}")
            count += 1
    
    # 清理logs目录
    logs_dir = Path("logs")
    if logs_dir.exists():
        for log_file in logs_dir.glob("*.log"):
            log_file.unlink()
            print(f"  删除: {log_file}")
            count += 1
    
    print(f"✅ 清理了 {count} 个日志文件")

def remove_temp_files():
    """删除临时文件"""
    print("🧹 清理临时文件...")
    count = 0
    
    temp_patterns = [
        '*.tmp',
        '*.temp',
        '*.bak',
        '*.backup',
        '*.old',
        '*~',
        '.DS_Store',
        'Thumbs.db',
        'ehthumbs.db'
    ]
    
    for pattern in temp_patterns:
        for temp_file in glob.glob(pattern, recursive=True):
            os.remove(temp_file)
            print(f"  删除: {temp_file}")
            count += 1
    
    print(f"✅ 清理了 {count} 个临时文件")

def remove_test_files():
    """删除测试文件（谨慎使用）"""
    print("🧹 清理测试文件...")
    count = 0
    
    # 只删除根目录下的测试文件，不删除tests目录下的
    test_patterns = [
        'test_*.py',
        'test_*.js',
        'debug_*.py',
        'debug_*.js'
    ]
    
    for pattern in test_patterns:
        for test_file in glob.glob(pattern):
            # 确保不在tests目录下
            if not test_file.startswith('tests/'):
                os.remove(test_file)
                print(f"  删除: {test_file}")
                count += 1
    
    print(f"✅ 清理了 {count} 个测试文件")

def remove_build_artifacts():
    """删除构建产物"""
    print("🧹 清理构建产物...")
    count = 0
    
    build_dirs = [
        "build",
        "dist",
        "*.egg-info",
        "web-frontend/dist",
        "web-frontend/build"
    ]
    
    for pattern in build_dirs:
        for path in glob.glob(pattern):
            if os.path.isdir(path):
                shutil.rmtree(path)
                print(f"  删除目录: {path}")
                count += 1
            elif os.path.isfile(path):
                os.remove(path)
                print(f"  删除文件: {path}")
                count += 1
    
    print(f"✅ 清理了 {count} 个构建产物")

def remove_coverage_files():
    """删除测试覆盖率文件"""
    print("🧹 清理测试覆盖率文件...")
    count = 0
    
    coverage_patterns = [
        '.coverage',
        'coverage.xml',
        'htmlcov/',
        '.pytest_cache/',
        '.tox/',
        '.nox/'
    ]
    
    for pattern in coverage_patterns:
        for path in glob.glob(pattern):
            if os.path.isdir(path):
                shutil.rmtree(path)
                print(f"  删除目录: {path}")
                count += 1
            elif os.path.isfile(path):
                os.remove(path)
                print(f"  删除文件: {path}")
                count += 1
    
    print(f"✅ 清理了 {count} 个覆盖率文件")

def get_directory_size(path):
    """获取目录大小"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            if os.path.exists(filepath):
                total_size += os.path.getsize(filepath)
    return total_size

def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"

def show_disk_usage():
    """显示磁盘使用情况"""
    print("\n💾 磁盘使用情况:")
    
    directories = [
        ".",
        "web-frontend",
        "tests",
        "docs",
        "logs"
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            size = get_directory_size(directory)
            print(f"  {directory}: {format_size(size)}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OKX量化交易系统清理脚本")
    parser.add_argument('--all', action='store_true', help='执行所有清理操作')
    parser.add_argument('--cache', action='store_true', help='清理缓存文件')
    parser.add_argument('--logs', action='store_true', help='清理日志文件')
    parser.add_argument('--temp', action='store_true', help='清理临时文件')
    parser.add_argument('--test', action='store_true', help='清理测试文件（谨慎使用）')
    parser.add_argument('--build', action='store_true', help='清理构建产物')
    parser.add_argument('--coverage', action='store_true', help='清理测试覆盖率文件')
    parser.add_argument('--dry-run', action='store_true', help='预览清理操作，不实际执行')
    
    args = parser.parse_args()
    
    print("🧹 OKX量化交易系统 - 项目清理")
    print("=" * 50)
    
    if args.dry_run:
        print("🔍 预览模式 - 不会实际删除文件")
        print()
    
    # 显示清理前的磁盘使用情况
    show_disk_usage()
    print()
    
    if args.dry_run:
        print("⚠️ 这是预览模式，实际运行时将执行以下清理操作:")
        return
    
    # 执行清理操作
    if args.all or args.cache:
        remove_pycache()
        remove_node_cache()
    
    if args.all or args.logs:
        remove_logs()
    
    if args.all or args.temp:
        remove_temp_files()
    
    if args.test:
        print("⚠️ 即将删除测试文件，请确认这是你想要的操作")
        confirm = input("继续? (y/N): ")
        if confirm.lower() == 'y':
            remove_test_files()
        else:
            print("跳过测试文件清理")
    
    if args.all or args.build:
        remove_build_artifacts()
    
    if args.all or args.coverage:
        remove_coverage_files()
    
    # 如果没有指定任何选项，显示帮助
    if not any([args.all, args.cache, args.logs, args.temp, args.test, args.build, args.coverage]):
        print("请指定要执行的清理操作:")
        print("  --all      执行所有清理操作")
        print("  --cache    清理缓存文件")
        print("  --logs     清理日志文件")
        print("  --temp     清理临时文件")
        print("  --test     清理测试文件")
        print("  --build    清理构建产物")
        print("  --coverage 清理测试覆盖率文件")
        print("  --dry-run  预览清理操作")
        print("\n示例: python scripts/cleanup.py --cache --logs")
        return
    
    print("\n🎉 清理完成！")
    
    # 显示清理后的磁盘使用情况
    show_disk_usage()

if __name__ == "__main__":
    main()