<script setup>
import NavBar from './components/NavBar.vue'
import { onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import axios from 'axios'
import { useUserStore } from './stores/user'

const userStore = useUserStore()
const route = useRoute()

// 检查是否是登录页面
const isAuthPage = computed(() => {
  return route.path === '/login' || route.path === '/register'
})
onMounted(async () => {
  const token = localStorage.getItem('token')
  const tokenExp = localStorage.getItem('token_exp')

  // 检查token是否存在且未过期
  if (token && tokenExp) {
    const now = Math.floor(Date.now() / 1000)
    const exp = Number(tokenExp)

    if (exp > now) {
      try {
        const res = await axios.get('/api/user/me')
        if (res.data.code === 0) {
          userStore.setUserInfo(res.data.data)
        }
      } catch (e) {
        // token 失效，清除本地存储
        localStorage.removeItem('token')
        localStorage.removeItem('token_exp')
      }
    } else {
      // token已过期，清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('token_exp')
    }
  }
})
</script>

<template>
  <div v-if="isAuthPage">
    <!-- 登录/注册页面，不显示导航栏 -->
    <router-view />
  </div>
  <a-layout v-else class="layout">
    <!-- 其他页面，显示完整布局 -->
    <NavBar />
    <a-layout-content class="content">
      <div class="content-wrapper">
        <router-view />
      </div>
    </a-layout-content>
  </a-layout>
</template>

<style scoped>
.layout {
  min-height: 100vh;
}

.content {
  padding: 0;
  background: #f0f2f5;
}

.content-wrapper {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: #fff;
  margin: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}


</style>
