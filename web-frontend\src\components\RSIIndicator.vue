<template>
  <div class="rsi-indicator-container">
    <a-card size="small" :title="`RSI 指标`">
            <div v-if="chartDataStore.isLoading" class="indicator-skeleton">
        <a-skeleton active :paragraph="{ rows: 2 }" />
      </div>
      <div v-else class="indicators-grid">
        <div v-for="rsi in rsiIndicators" :key="rsi.period" class="indicator-item">
          <div class="indicator-label">RSI ({{ rsi.period }})</div>
          <div class="indicator-value">{{ rsi.value.toFixed(2) }}</div>
          <a-tag :color="rsi.status.color">{{ rsi.status.text }}</a-tag>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useInfoPanelStore } from '@/stores/infoPanelStore';
import { useChartDataStore } from '@/stores/chartDataStore';

const infoPanelStore = useInfoPanelStore();
const chartDataStore = useChartDataStore();

const rsiIndicators = computed(() => {
  if (!infoPanelStore.indicatorData || !infoPanelStore.indicatorData.rsi) {
    return [];
  }
  return infoPanelStore.indicatorData.rsi.map(rsi => ({
    ...rsi,
    status: getRSIStatus(rsi.value)
  }));
});

const getRSIStatus = (rsiValue) => {
  if (rsiValue >= 70) {
    return { text: '超买', color: 'red' };
  } else if (rsiValue <= 30) {
    return { text: '超卖', color: 'green' };
  } else {
    return { text: '中性', color: 'blue' };
  }
};
</script>

<style scoped>
.rsi-indicator-container {
  margin-bottom: 16px;
}

.indicators-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.indicator-item {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  align-items: center;
  text-align: center;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
}

.indicator-label {
  font-size: 12px;
  color: #8c8c8c;
  text-align: left;
}

.indicator-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.dark-theme .indicator-item {
  background: #2a2e39;
}

.dark-theme .indicator-value {
  color: #ffffff;
}

.dark-theme .indicator-label {
  color: #a0a0a0;
}
</style>