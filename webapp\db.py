from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from pathlib import Path

# 获取项目根目录
# __file__ 是当前文件 (db.py) 的路径
# Path(__file__).parent 是 webapp 目录
# Path(__file__).parent.parent 是项目根目录
BASE_DIR = Path(__file__).parent.parent

# 数据库文件的绝对路径
DB_PATH = BASE_DIR / "db.sqlite3"

SQLALCHEMY_DATABASE_URL = f"sqlite:///{DB_PATH}"

engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={
                       "check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
