<template>
  <div class="kline-indicator-panel">
    <a-card title="技术指标" size="small">
      <div class="indicator-controls">
        <a-space wrap>
          <a-button 
            type="primary" 
            size="small" 
            @click="addIndicator('RSI')"
            :disabled="hasIndicator('RSI')"
          >
            添加RSI
          </a-button>
          <a-button 
            type="primary" 
            size="small" 
            @click="addIndicator('MACD')"
            :disabled="hasIndicator('MACD')"
          >
            添加MACD
          </a-button>
          <a-button 
            type="primary" 
            size="small" 
            @click="addIndicator('KDJ')"
            :disabled="hasIndicator('KDJ')"
          >
            添加KDJ
          </a-button>
          <a-button 
            type="primary" 
            size="small" 
            @click="addIndicator('BOLL')"
            :disabled="hasIndicator('BOLL')"
          >
            添加布林带
          </a-button>
          <a-button 
            type="default" 
            size="small" 
            @click="clearAllIndicators"
            :disabled="activeIndicators.length === 0"
          >
            清除所有
          </a-button>
        </a-space>
      </div>
      
      <div class="indicator-list" v-if="activeIndicators.length > 0">
        <a-list size="small" :data-source="activeIndicators">
          <template #renderItem="{ item }">
            <a-list-item>
              <template #actions>
                <a-button 
                  type="text" 
                  size="small" 
                  @click="removeIndicator(item.name)"
                  danger
                >
                  移除
                </a-button>
                <a-button 
                  type="text" 
                  size="small" 
                  @click="configureIndicator(item.name)"
                >
                  配置
                </a-button>
              </template>
              <a-list-item-meta>
                <template #title>
                  {{ item.displayName }}
                </template>
                <template #description>
                  参数: {{ formatParams(item.params) }}
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>
    </a-card>
    
    <!-- 指标配置弹窗 -->>
    <a-modal 
      v-model:open="configModalVisible"
      :title="`配置 ${currentIndicator?.displayName}`"
      @ok="saveIndicatorConfig"
      @cancel="cancelIndicatorConfig"
    >
      <div v-if="currentIndicator">
        <!-- RSI配置 -->
        <div v-if="currentIndicator.name === 'RSI'">
          <a-form-item label="周期">
            <a-slider 
              v-model:value="indicatorConfig.period" 
              :min="6" 
              :max="30" 
              :marks="{ 14: '14' }"
            />
          </a-form-item>
        </div>
        
        <!-- MACD配置 -->
        <div v-if="currentIndicator.name === 'MACD'">
          <a-form-item label="快线周期">
            <a-slider 
              v-model:value="indicatorConfig.fastPeriod" 
              :min="5" 
              :max="20" 
              :marks="{ 12: '12' }"
            />
          </a-form-item>
          <a-form-item label="慢线周期">
            <a-slider 
              v-model:value="indicatorConfig.slowPeriod" 
              :min="20" 
              :max="40" 
              :marks="{ 26: '26' }"
            />
          </a-form-item>
          <a-form-item label="信号线周期">
            <a-slider 
              v-model:value="indicatorConfig.signalPeriod" 
              :min="5" 
              :max="15" 
              :marks="{ 9: '9' }"
            />
          </a-form-item>
        </div>
        
        <!-- KDJ配置 -->
        <div v-if="currentIndicator.name === 'KDJ'">
          <a-form-item label="K周期">
            <a-slider 
              v-model:value="indicatorConfig.kPeriod" 
              :min="5" 
              :max="20" 
              :marks="{ 9: '9' }"
            />
          </a-form-item>
          <a-form-item label="D周期">
            <a-slider 
              v-model:value="indicatorConfig.dPeriod" 
              :min="2" 
              :max="10" 
              :marks="{ 3: '3' }"
            />
          </a-form-item>
          <a-form-item label="J周期">
            <a-slider 
              v-model:value="indicatorConfig.jPeriod" 
              :min="2" 
              :max="10" 
              :marks="{ 3: '3' }"
            />
          </a-form-item>
        </div>
        
        <!-- 布林带配置 -->
        <div v-if="currentIndicator.name === 'BOLL'">
          <a-form-item label="周期">
            <a-slider 
              v-model:value="indicatorConfig.period" 
              :min="10" 
              :max="50" 
              :marks="{ 20: '20' }"
            />
          </a-form-item>
          <a-form-item label="标准差倍数">
            <a-slider 
              v-model:value="indicatorConfig.stdDev" 
              :min="1" 
              :max="3" 
              :step="0.1" 
              :marks="{ 2: '2.0' }"
            />
          </a-form-item>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 技术指标面板组件
 * 使用klinecharts内置指标替代自定义组件
 */
const props = defineProps({
  chartInstance: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['indicator-added', 'indicator-removed', 'indicator-updated'])

// 响应式数据
const activeIndicators = ref([])
const configModalVisible = ref(false)
const currentIndicator = ref(null)
const indicatorConfig = ref({})

// 指标定义
const indicatorDefinitions = {
  RSI: {
    name: 'RSI',
    displayName: 'RSI相对强弱指标',
    defaultParams: [14],
    configFields: ['period']
  },
  MACD: {
    name: 'MACD',
    displayName: 'MACD指标',
    defaultParams: [12, 26, 9],
    configFields: ['fastPeriod', 'slowPeriod', 'signalPeriod']
  },
  KDJ: {
    name: 'KDJ',
    displayName: 'KDJ随机指标',
    defaultParams: [9, 3, 3],
    configFields: ['kPeriod', 'dPeriod', 'jPeriod']
  },
  BOLL: {
    name: 'BOLL',
    displayName: '布林带',
    defaultParams: [20, 2],
    configFields: ['period', 'stdDev']
  }
}

/**
 * 检查指标是否已添加
 * @param {string} indicatorName - 指标名称
 * @returns {boolean}
 */
const hasIndicator = (indicatorName) => {
  return activeIndicators.value.some(indicator => indicator.name === indicatorName)
}

/**
 * 添加技术指标
 * @param {string} indicatorName - 指标名称
 */
const addIndicator = (indicatorName) => {
  if (!props.chartInstance) {
    message.error('图表实例未初始化')
    return
  }
  
  if (hasIndicator(indicatorName)) {
    message.warning(`${indicatorName}指标已存在`)
    return
  }
  
  try {
    const definition = indicatorDefinitions[indicatorName]
    if (!definition) {
      message.error(`未知的指标类型: ${indicatorName}`)
      return
    }
    
    // 使用klinecharts的createIndicator方法
    const paneId = props.chartInstance.createIndicator(indicatorName, true, {
      calcParams: definition.defaultParams
    })
    
    if (paneId) {
      const indicator = {
        name: indicatorName,
        displayName: definition.displayName,
        params: definition.defaultParams,
        paneId: paneId
      }
      
      activeIndicators.value.push(indicator)
      emit('indicator-added', indicator)
      message.success(`${definition.displayName}添加成功`)
    } else {
      message.error(`${definition.displayName}添加失败`)
    }
  } catch (error) {
    console.error('添加指标失败:', error)
    message.error(`添加${indicatorName}指标失败: ${error.message}`)
  }
}

/**
 * 移除技术指标
 * @param {string} indicatorName - 指标名称
 */
const removeIndicator = (indicatorName) => {
  const indicatorIndex = activeIndicators.value.findIndex(indicator => indicator.name === indicatorName)
  if (indicatorIndex === -1) {
    message.warning(`${indicatorName}指标不存在`)
    return
  }
  
  try {
    const indicator = activeIndicators.value[indicatorIndex]
    
    // 使用klinecharts的removeIndicator方法
    if (props.chartInstance && indicator.paneId) {
      props.chartInstance.removeIndicator(indicator.paneId)
    }
    
    activeIndicators.value.splice(indicatorIndex, 1)
    emit('indicator-removed', indicator)
    message.success(`${indicator.displayName}移除成功`)
  } catch (error) {
    console.error('移除指标失败:', error)
    message.error(`移除${indicatorName}指标失败: ${error.message}`)
  }
}

/**
 * 清除所有指标
 */
const clearAllIndicators = () => {
  try {
    activeIndicators.value.forEach(indicator => {
      if (props.chartInstance && indicator.paneId) {
        props.chartInstance.removeIndicator(indicator.paneId)
      }
    })
    
    activeIndicators.value = []
    message.success('所有指标已清除')
  } catch (error) {
    console.error('清除指标失败:', error)
    message.error('清除指标失败')
  }
}

/**
 * 配置指标
 * @param {string} indicatorName - 指标名称
 */
const configureIndicator = (indicatorName) => {
  const indicator = activeIndicators.value.find(ind => ind.name === indicatorName)
  if (!indicator) {
    message.error('指标不存在')
    return
  }
  
  currentIndicator.value = indicator
  
  // 初始化配置数据
  const definition = indicatorDefinitions[indicatorName]
  indicatorConfig.value = {}
  
  if (indicatorName === 'RSI') {
    indicatorConfig.value.period = indicator.params[0] || 14
  } else if (indicatorName === 'MACD') {
    indicatorConfig.value.fastPeriod = indicator.params[0] || 12
    indicatorConfig.value.slowPeriod = indicator.params[1] || 26
    indicatorConfig.value.signalPeriod = indicator.params[2] || 9
  } else if (indicatorName === 'KDJ') {
    indicatorConfig.value.kPeriod = indicator.params[0] || 9
    indicatorConfig.value.dPeriod = indicator.params[1] || 3
    indicatorConfig.value.jPeriod = indicator.params[2] || 3
  } else if (indicatorName === 'BOLL') {
    indicatorConfig.value.period = indicator.params[0] || 20
    indicatorConfig.value.stdDev = indicator.params[1] || 2
  }
  
  configModalVisible.value = true
}

/**
 * 保存指标配置
 */
const saveIndicatorConfig = () => {
  if (!currentIndicator.value || !props.chartInstance) {
    message.error('配置保存失败')
    return
  }
  
  try {
    const indicatorName = currentIndicator.value.name
    let newParams = []
    
    // 根据指标类型构建参数
    if (indicatorName === 'RSI') {
      newParams = [indicatorConfig.value.period]
    } else if (indicatorName === 'MACD') {
      newParams = [
        indicatorConfig.value.fastPeriod,
        indicatorConfig.value.slowPeriod,
        indicatorConfig.value.signalPeriod
      ]
    } else if (indicatorName === 'KDJ') {
      newParams = [
        indicatorConfig.value.kPeriod,
        indicatorConfig.value.dPeriod,
        indicatorConfig.value.jPeriod
      ]
    } else if (indicatorName === 'BOLL') {
      newParams = [
        indicatorConfig.value.period,
        indicatorConfig.value.stdDev
      ]
    }
    
    // 先移除旧指标
    props.chartInstance.removeIndicator(currentIndicator.value.paneId)
    
    // 添加新配置的指标
    const newPaneId = props.chartInstance.createIndicator(indicatorName, true, {
      calcParams: newParams
    })
    
    if (newPaneId) {
      // 更新指标信息
      const indicatorIndex = activeIndicators.value.findIndex(ind => ind.name === indicatorName)
      if (indicatorIndex !== -1) {
        activeIndicators.value[indicatorIndex].params = newParams
        activeIndicators.value[indicatorIndex].paneId = newPaneId
      }
      
      emit('indicator-updated', {
        name: indicatorName,
        params: newParams,
        paneId: newPaneId
      })
      
      message.success('指标配置已更新')
    } else {
      message.error('指标配置更新失败')
    }
    
    configModalVisible.value = false
    currentIndicator.value = null
  } catch (error) {
    console.error('保存指标配置失败:', error)
    message.error('保存配置失败')
  }
}

/**
 * 取消指标配置
 */
const cancelIndicatorConfig = () => {
  configModalVisible.value = false
  currentIndicator.value = null
  indicatorConfig.value = {}
}

/**
 * 格式化参数显示
 * @param {Array} params - 参数数组
 * @returns {string}
 */
const formatParams = (params) => {
  return params.join(', ')
}

// 监听图表实例变化
watch(() => props.chartInstance, (newInstance, oldInstance) => {
  if (oldInstance && newInstance !== oldInstance) {
    // 图表实例变化时清除所有指标
    activeIndicators.value = []
  }
})
</script>

<style scoped>
.kline-indicator-panel {
  margin-bottom: 16px;
}

.indicator-controls {
  margin-bottom: 16px;
}

.indicator-list {
  margin-top: 16px;
}
</style>