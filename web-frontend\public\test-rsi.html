<!DOCTYPE html>
<html>
<head>
    <title>RSI修复测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chart { width: 100%; height: 300px; margin: 20px 0; border: 1px solid #ccc; }
        .debug { background: #f5f5f5; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>RSI指标修复测试</h1>
    
    <button onclick="testRSI()">测试RSI计算</button>
    <button onclick="fetchRealData()">获取真实数据测试</button>
    
    <div class="debug" id="debug"></div>
    
    <div class="chart" id="price-chart"></div>
    <div class="chart" id="rsi-chart"></div>

    <script>
        // 标准RSI计算方法（与Vue组件中相同）
        function calculateRSIArray(prices, period = 14) {
            const rsiArray = []
            
            if (prices.length < period + 1) {
                return prices.map(() => null)
            }

            // 计算价格变化
            const changes = []
            for (let i = 1; i < prices.length; i++) {
                changes.push(prices[i] - prices[i - 1])
            }

            // 初始化前14个为null
            for (let i = 0; i < period; i++) {
                rsiArray.push(null)
            }

            // 计算初始的平均收益和平均损失
            let gains = 0
            let losses = 0
            
            for (let i = 0; i < period; i++) {
                if (changes[i] > 0) {
                    gains += changes[i]
                } else {
                    losses += Math.abs(changes[i])
                }
            }

            let avgGain = gains / period
            let avgLoss = losses / period

            // 计算第一个RSI值
            if (avgLoss === 0) {
                rsiArray.push(avgGain === 0 ? 50 : 100)
            } else {
                const rs = avgGain / avgLoss
                const rsi = 100 - (100 / (1 + rs))
                rsiArray.push(rsi)
            }

            // 使用指数移动平均计算后续的RSI值
            for (let i = period; i < changes.length; i++) {
                const change = changes[i]
                const gain = change > 0 ? change : 0
                const loss = change < 0 ? Math.abs(change) : 0

                // 指数移动平均
                avgGain = (avgGain * (period - 1) + gain) / period
                avgLoss = (avgLoss * (period - 1) + loss) / period

                if (avgLoss === 0) {
                    rsiArray.push(avgGain === 0 ? 50 : 100)
                } else {
                    const rs = avgGain / avgLoss
                    const rsi = 100 - (100 / (1 + rs))
                    rsiArray.push(rsi)
                }
            }
            
            return rsiArray
        }

        // 生成模拟数据
        function generateMockData() {
            const data = []
            let price = 50000
            
            for (let i = 0; i < 50; i++) {
                // 模拟趋势和波动
                const trend = Math.sin(i * 0.1) * 500
                const noise = (Math.random() - 0.5) * 1000
                price += trend + noise
                data.push(price)
            }
            
            return data
        }

        // 测试RSI计算
        function testRSI() {
            const prices = generateMockData()
            const rsiData = calculateRSIArray(prices, 14)
            
            const validRSI = rsiData.filter(v => v !== null)
            
            const debugInfo = {
                价格数据长度: prices.length,
                RSI数据长度: rsiData.length,
                有效RSI数据: validRSI.length,
                RSI范围: {
                    最小: Math.min(...validRSI).toFixed(2),
                    最大: Math.max(...validRSI).toFixed(2),
                    平均: (validRSI.reduce((a, b) => a + b, 0) / validRSI.length).toFixed(2)
                },
                最后5个价格: prices.slice(-5).map(p => p.toFixed(2)),
                最后5个RSI: rsiData.slice(-5).map(r => r ? r.toFixed(2) : 'null')
            }
            
            console.log('RSI测试结果:', debugInfo)
            
            document.getElementById('debug').innerHTML = `
                <h3>RSI计算测试结果</h3>
                <p><strong>价格数据长度:</strong> ${debugInfo.价格数据长度}</p>
                <p><strong>RSI数据长度:</strong> ${debugInfo.RSI数据长度}</p>
                <p><strong>有效RSI数据:</strong> ${debugInfo.有效RSI数据}</p>
                <p><strong>RSI范围:</strong> ${debugInfo.RSI范围.最小} - ${debugInfo.RSI范围.最大}</p>
                <p><strong>平均RSI:</strong> ${debugInfo.RSI范围.平均}</p>
                <p><strong>最后5个价格:</strong> ${debugInfo.最后5个价格.join(', ')}</p>
                <p><strong>最后5个RSI:</strong> ${debugInfo.最后5个RSI.join(', ')}</p>
            `
            
            // 绘制价格图表
            const priceChart = echarts.init(document.getElementById('price-chart'))
            priceChart.setOption({
                title: { text: '价格走势' },
                xAxis: { type: 'category', data: prices.map((_, i) => i) },
                yAxis: { type: 'value' },
                series: [{
                    name: '价格',
                    type: 'line',
                    data: prices,
                    lineStyle: { color: '#1890ff' }
                }]
            })
            
            // 绘制RSI图表
            const rsiChart = echarts.init(document.getElementById('rsi-chart'))
            rsiChart.setOption({
                title: { text: 'RSI指标' },
                xAxis: { type: 'category', data: rsiData.map((_, i) => i) },
                yAxis: { type: 'value', min: 0, max: 100 },
                series: [{
                    name: 'RSI',
                    type: 'line',
                    data: rsiData,
                    lineStyle: { color: '#ff4d4f' },
                    markLine: {
                        data: [
                            { yAxis: 70, lineStyle: { color: '#ff7875' } },
                            { yAxis: 30, lineStyle: { color: '#52c41a' } }
                        ]
                    }
                }]
            })
        }

        // 获取真实数据测试
        async function fetchRealData() {
            try {
                const response = await fetch('/api/okx/market/candles?instId=BTC-USDT-SWAP&bar=1H&limit=50')
                const data = await response.json()
                
                if (data.code === '0' && data.data) {
                    const prices = data.data.reverse().map(item => parseFloat(item[4]))
                    const rsiData = calculateRSIArray(prices, 14)
                    
                    const validRSI = rsiData.filter(v => v !== null)
                    
                    document.getElementById('debug').innerHTML = `
                        <h3>真实数据RSI测试结果</h3>
                        <p><strong>数据来源:</strong> OKX BTC-USDT-SWAP 1H</p>
                        <p><strong>价格数据长度:</strong> ${prices.length}</p>
                        <p><strong>有效RSI数据:</strong> ${validRSI.length}</p>
                        <p><strong>RSI范围:</strong> ${Math.min(...validRSI).toFixed(2)} - ${Math.max(...validRSI).toFixed(2)}</p>
                        <p><strong>当前RSI:</strong> ${rsiData[rsiData.length - 1]?.toFixed(2) || 'N/A'}</p>
                        <p><strong>最后5个价格:</strong> ${prices.slice(-5).map(p => p.toFixed(2)).join(', ')}</p>
                        <p><strong>最后5个RSI:</strong> ${rsiData.slice(-5).map(r => r ? r.toFixed(2) : 'null').join(', ')}</p>
                    `
                    
                    // 绘制真实数据图表
                    const priceChart = echarts.init(document.getElementById('price-chart'))
                    priceChart.setOption({
                        title: { text: 'BTC-USDT-SWAP 价格走势' },
                        xAxis: { type: 'category', data: prices.map((_, i) => i) },
                        yAxis: { type: 'value' },
                        series: [{
                            name: '价格',
                            type: 'line',
                            data: prices,
                            lineStyle: { color: '#1890ff' }
                        }]
                    })
                    
                    const rsiChart = echarts.init(document.getElementById('rsi-chart'))
                    rsiChart.setOption({
                        title: { text: 'BTC-USDT-SWAP RSI指标' },
                        xAxis: { type: 'category', data: rsiData.map((_, i) => i) },
                        yAxis: { type: 'value', min: 0, max: 100 },
                        series: [{
                            name: 'RSI',
                            type: 'line',
                            data: rsiData,
                            lineStyle: { color: '#ff4d4f' },
                            markLine: {
                                data: [
                                    { yAxis: 70, lineStyle: { color: '#ff7875' } },
                                    { yAxis: 30, lineStyle: { color: '#52c41a' } }
                                ]
                            }
                        }]
                    })
                } else {
                    document.getElementById('debug').innerHTML = `
                        <h3>获取真实数据失败</h3>
                        <p>错误: ${data.msg || '未知错误'}</p>
                    `
                }
            } catch (error) {
                document.getElementById('debug').innerHTML = `
                    <h3>获取真实数据失败</h3>
                    <p>错误: ${error.message}</p>
                `
            }
        }

        // 页面加载时自动测试
        window.onload = function() {
            testRSI()
        }
    </script>
</body>
</html>
