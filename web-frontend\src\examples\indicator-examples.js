/**
 * 增强技术指标面板使用示例
 * 展示各种指标的配置和使用方法
 */

import { indicatorOptimizer } from '@/utils/indicatorOptimizer'

/**
 * 示例1: 基础指标配置
 */
export const basicIndicatorConfigs = {
  // RSI配置示例
  rsi: {
    name: 'RSI',
    displayName: 'RSI相对强弱指标',
    params: [14],
    visible: true,
    lineWidth: 2,
    opacity: 0.8,
    showValues: true,
    overbought: 70,
    oversold: 30,
    lineColor: '#722ed1'
  },

  // MACD配置示例
  macd: {
    name: 'MACD',
    displayName: 'MACD指标',
    params: [12, 26, 9],
    visible: true,
    lineWidth: 2,
    opacity: 0.8,
    showValues: true,
    showHistogram: true,
    difColor: '#1890ff',
    deaColor: '#f5222d'
  },

  // KDJ配置示例
  kdj: {
    name: 'KDJ',
    displayName: 'KDJ随机指标',
    params: [9, 3, 3],
    visible: true,
    lineWidth: 2,
    opacity: 0.8,
    showValues: true,
    overbought: 80,
    oversold: 20,
    kColor: '#1890ff',
    dColor: '#f5222d',
    jColor: '#faad14',
    showCrossPoints: true
  },

  // 布林带配置示例
  boll: {
    name: 'BOLL',
    displayName: '布林带',
    params: [20, 2],
    visible: true,
    lineWidth: 1,
    opacity: 0.6,
    showValues: false,
    upperColor: '#f5222d',
    midColor: '#1890ff',
    lowerColor: '#52c41a',
    showFill: true,
    fillColor: 'rgba(24, 144, 255, 0.1)'
  }
}

/**
 * 示例2: 预设配置方案
 */
export const presetConfigurations = {
  // 短线交易配置
  shortTerm: {
    name: '短线交易',
    description: '适合日内短线交易的敏感参数配置',
    indicators: {
      RSI: {
        params: [9],
        overbought: 65,
        oversold: 35
      },
      MACD: {
        params: [8, 21, 5]
      },
      KDJ: {
        params: [6, 3, 3],
        overbought: 75,
        oversold: 25
      }
    }
  },

  // 中线交易配置
  mediumTerm: {
    name: '中线交易',
    description: '适合中期持仓的平衡参数配置',
    indicators: {
      RSI: {
        params: [14],
        overbought: 70,
        oversold: 30
      },
      MACD: {
        params: [12, 26, 9]
      },
      KDJ: {
        params: [9, 3, 3],
        overbought: 80,
        oversold: 20
      },
      BOLL: {
        params: [20, 2]
      }
    }
  },

  // 长线投资配置
  longTerm: {
    name: '长线投资',
    description: '适合长期投资的稳健参数配置',
    indicators: {
      RSI: {
        params: [21],
        overbought: 75,
        oversold: 25
      },
      MACD: {
        params: [12, 26, 9]
      },
      BOLL: {
        params: [30, 2.5]
      }
    }
  }
}

/**
 * 示例3: 高级样式配置
 */
export const advancedStyleConfigs = {
  // 深色主题样式
  darkTheme: {
    RSI: {
      lineColor: '#9254de',
      overboughtColor: '#ff7875',
      oversoldColor: '#95de64',
      backgroundColor: 'rgba(146, 84, 222, 0.05)'
    },
    MACD: {
      difColor: '#40a9ff',
      deaColor: '#ff7875',
      upColor: '#95de64',
      downColor: '#ff7875',
      backgroundColor: 'rgba(0, 0, 0, 0.8)'
    },
    KDJ: {
      kColor: '#40a9ff',
      dColor: '#ff7875',
      jColor: '#ffc53d',
      gridColor: 'rgba(255, 255, 255, 0.1)'
    }
  },

  // 浅色主题样式
  lightTheme: {
    RSI: {
      lineColor: '#722ed1',
      overboughtColor: '#f5222d',
      oversoldColor: '#52c41a',
      backgroundColor: 'rgba(114, 46, 209, 0.05)'
    },
    MACD: {
      difColor: '#1890ff',
      deaColor: '#f5222d',
      upColor: '#52c41a',
      downColor: '#f5222d',
      backgroundColor: 'rgba(255, 255, 255, 0.9)'
    },
    KDJ: {
      kColor: '#1890ff',
      dColor: '#f5222d',
      jColor: '#faad14',
      gridColor: 'rgba(0, 0, 0, 0.1)'
    }
  },

  // 高对比度样式
  highContrast: {
    RSI: {
      lineColor: '#000000',
      lineWidth: 3,
      overboughtColor: '#ff0000',
      oversoldColor: '#00ff00'
    },
    MACD: {
      difColor: '#0000ff',
      deaColor: '#ff0000',
      lineWidth: 3,
      upColor: '#00ff00',
      downColor: '#ff0000'
    },
    KDJ: {
      kColor: '#0000ff',
      dColor: '#ff0000',
      jColor: '#ffff00',
      lineWidth: 3
    }
  }
}

/**
 * 示例4: 性能优化配置
 */
export const performanceConfigs = {
  // 高性能配置
  highPerformance: {
    updateDelay: 500,
    enableCache: true,
    cacheMaxAge: 60000,
    enableDataSmoothing: false,
    maxIndicators: 3,
    enablePerformanceMonitoring: true
  },

  // 平衡配置
  balanced: {
    updateDelay: 300,
    enableCache: true,
    cacheMaxAge: 30000,
    enableDataSmoothing: true,
    maxIndicators: 5,
    enablePerformanceMonitoring: true
  },

  // 高质量配置
  highQuality: {
    updateDelay: 100,
    enableCache: false,
    enableDataSmoothing: true,
    smoothingType: 'ema',
    removeOutliers: true,
    maxIndicators: 8,
    enablePerformanceMonitoring: true
  }
}

/**
 * 示例5: 实际使用案例
 */
export class IndicatorExamples {
  constructor(chartInstance) {
    this.chartInstance = chartInstance
    this.optimizer = indicatorOptimizer
  }

  /**
   * 案例1: 添加基础RSI指标
   */
  async addBasicRSI() {
    const config = basicIndicatorConfigs.rsi
    
    try {
      const paneId = this.chartInstance.createIndicator('RSI', true, {
        calcParams: config.params,
        styles: this.optimizer.getOptimizedStyles('RSI', config)
      })
      
      console.log('RSI指标添加成功，面板ID:', paneId)
      return paneId
    } catch (error) {
      console.error('添加RSI指标失败:', error)
      throw error
    }
  }

  /**
   * 案例2: 添加高级MACD指标
   */
  async addAdvancedMACD() {
    const config = basicIndicatorConfigs.macd
    const styles = this.optimizer.getOptimizedStyles('MACD', {
      ...config,
      ...advancedStyleConfigs.darkTheme.MACD
    })
    
    try {
      const paneId = this.chartInstance.createIndicator('MACD', true, {
        calcParams: config.params,
        styles: styles
      })
      
      console.log('高级MACD指标添加成功，面板ID:', paneId)
      return paneId
    } catch (error) {
      console.error('添加高级MACD指标失败:', error)
      throw error
    }
  }

  /**
   * 案例3: 批量添加预设指标
   */
  async addPresetIndicators(presetName = 'mediumTerm') {
    const preset = presetConfigurations[presetName]
    if (!preset) {
      throw new Error(`未找到预设配置: ${presetName}`)
    }

    const results = []
    
    for (const [indicatorName, config] of Object.entries(preset.indicators)) {
      try {
        const styles = this.optimizer.getOptimizedStyles(indicatorName, config)
        const paneId = this.chartInstance.createIndicator(indicatorName, true, {
          calcParams: config.params,
          styles: styles
        })
        
        results.push({
          name: indicatorName,
          paneId: paneId,
          success: true
        })
        
        console.log(`${indicatorName}指标添加成功，面板ID:`, paneId)
      } catch (error) {
        results.push({
          name: indicatorName,
          error: error.message,
          success: false
        })
        
        console.error(`添加${indicatorName}指标失败:`, error)
      }
    }
    
    return results
  }

  /**
   * 案例4: 实时更新指标参数
   */
  async updateIndicatorRealTime(indicatorName, paneId, newParams) {
    const updateFn = () => {
      try {
        // 移除旧指标
        this.chartInstance.removeIndicator(paneId)
        
        // 添加新配置的指标
        const newPaneId = this.chartInstance.createIndicator(indicatorName, true, {
          calcParams: newParams,
          styles: this.optimizer.getOptimizedStyles(indicatorName)
        })
        
        console.log(`${indicatorName}指标更新成功，新面板ID:`, newPaneId)
        return newPaneId
      } catch (error) {
        console.error(`更新${indicatorName}指标失败:`, error)
        throw error
      }
    }
    
    // 使用优化器进行防抖更新
    this.optimizer.optimizeUpdate(`${indicatorName}_${paneId}`, updateFn, {
      delay: 300,
      enableCache: true,
      enablePerformanceMonitoring: true
    })
  }

  /**
   * 案例5: 性能监控示例
   */
  monitorPerformance(indicatorId) {
    const stats = this.optimizer.getPerformanceStats(indicatorId)
    
    if (stats) {
      console.log(`${indicatorId} 性能统计:`, {
        平均耗时: `${stats.average.toFixed(2)}ms`,
        最小耗时: `${stats.min.toFixed(2)}ms`,
        最大耗时: `${stats.max.toFixed(2)}ms`,
        更新次数: stats.count,
        最近10次: stats.recent.map(t => `${t.toFixed(2)}ms`).join(', ')
      })
      
      // 性能警告
      if (stats.average > 100) {
        console.warn(`${indicatorId} 平均更新耗时过长，建议优化参数或减少指标数量`)
      }
    } else {
      console.log(`${indicatorId} 暂无性能数据`)
    }
  }

  /**
   * 案例6: 数据平滑处理示例
   */
  processDataWithSmoothing(rawData, options = {}) {
    const defaultOptions = {
      smooth: true,
      smoothType: 'ema',
      smoothPeriod: 3,
      removeOutliers: true,
      outlierThreshold: 2
    }
    
    const finalOptions = { ...defaultOptions, ...options }
    const processedData = this.optimizer.processData(rawData, finalOptions)
    
    console.log('数据处理结果:', {
      原始数据长度: rawData.length,
      处理后数据长度: processedData.length,
      处理选项: finalOptions
    })
    
    return processedData
  }

  /**
   * 案例7: 清理和资源管理
   */
  cleanup() {
    // 清理优化器资源
    this.optimizer.cleanup()
    
    console.log('指标优化器资源已清理')
  }
}

/**
 * 示例6: 工具函数
 */
export const indicatorUtils = {
  /**
   * 验证指标参数
   */
  validateParams(indicatorName, params) {
    const validations = {
      RSI: (p) => p.length === 1 && p[0] >= 6 && p[0] <= 30,
      MACD: (p) => p.length === 3 && p[0] < p[1] && p[2] > 0,
      KDJ: (p) => p.length === 3 && p.every(v => v > 0 && v <= 20),
      BOLL: (p) => p.length === 2 && p[0] > 0 && p[1] > 0 && p[1] <= 3
    }
    
    const validator = validations[indicatorName]
    return validator ? validator(params) : true
  },

  /**
   * 获取推荐参数
   */
  getRecommendedParams(indicatorName, tradingStyle = 'medium') {
    const recommendations = {
      short: {
        RSI: [9],
        MACD: [8, 21, 5],
        KDJ: [6, 3, 3],
        BOLL: [15, 1.8]
      },
      medium: {
        RSI: [14],
        MACD: [12, 26, 9],
        KDJ: [9, 3, 3],
        BOLL: [20, 2]
      },
      long: {
        RSI: [21],
        MACD: [12, 26, 9],
        KDJ: [14, 3, 3],
        BOLL: [30, 2.5]
      }
    }
    
    return recommendations[tradingStyle]?.[indicatorName] || null
  },

  /**
   * 计算指标信号强度
   */
  calculateSignalStrength(indicatorName, value, params) {
    switch (indicatorName) {
      case 'RSI':
        if (value > 70) return { strength: 'strong', direction: 'sell' }
        if (value < 30) return { strength: 'strong', direction: 'buy' }
        if (value > 60) return { strength: 'medium', direction: 'sell' }
        if (value < 40) return { strength: 'medium', direction: 'buy' }
        return { strength: 'weak', direction: 'neutral' }
        
      case 'KDJ':
        const [k, d, j] = value
        if (k > 80 && d > 80) return { strength: 'strong', direction: 'sell' }
        if (k < 20 && d < 20) return { strength: 'strong', direction: 'buy' }
        return { strength: 'medium', direction: 'neutral' }
        
      default:
        return { strength: 'unknown', direction: 'neutral' }
    }
  }
}

// 导出默认配置
export default {
  basicIndicatorConfigs,
  presetConfigurations,
  advancedStyleConfigs,
  performanceConfigs,
  IndicatorExamples,
  indicatorUtils
}