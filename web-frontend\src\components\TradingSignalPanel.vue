<template>
  <div class="trading-signal-panel" :class="{ 'dark-theme': isDarkTheme }">
    <a-card title="交易信号" size="small" :bordered="false">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新信号">
            <a-button size="small" type="text" @click="refreshSignals">
              <ReloadOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="信号设置">
            <a-button size="small" type="text" @click="showSettings = true">
              <SettingOutlined />
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <div class="signal-container">
        <!-- 信号过滤器 -->
        <div class="signal-filters">
          <a-space wrap>
            <a-select
              v-model:value="selectedTimeframe"
              size="small"
              style="width: 80px"
              @change="filterSignals"
            >
              <a-select-option value="all">全部</a-select-option>
              <a-select-option value="1m">1分钟</a-select-option>
              <a-select-option value="5m">5分钟</a-select-option>
              <a-select-option value="15m">15分钟</a-select-option>
              <a-select-option value="1h">1小时</a-select-option>
              <a-select-option value="4h">4小时</a-select-option>
              <a-select-option value="1d">日线</a-select-option>
            </a-select>
            
            <a-select
              v-model:value="selectedType"
              size="small"
              style="width: 80px"
              @change="filterSignals"
            >
              <a-select-option value="all">全部</a-select-option>
              <a-select-option value="buy">买入</a-select-option>
              <a-select-option value="sell">卖出</a-select-option>
            </a-select>
            
            <a-select
              v-model:value="selectedStrength"
              size="small"
              style="width: 80px"
              @change="filterSignals"
            >
              <a-select-option value="all">全部</a-select-option>
              <a-select-option value="strong">强</a-select-option>
              <a-select-option value="medium">中</a-select-option>
              <a-select-option value="weak">弱</a-select-option>
            </a-select>
          </a-space>
        </div>

        <!-- 信号统计 -->
        <div class="signal-stats">
          <div class="stat-item">
            <span class="stat-label">今日信号</span>
            <span class="stat-value">{{ todaySignalsCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">成功率</span>
            <span class="stat-value" :class="getSuccessRateClass(successRate)">{{ formatPercentage(successRate) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">盈亏比</span>
            <span class="stat-value">{{ profitLossRatio.toFixed(2) }}</span>
          </div>
        </div>

        <!-- 信号列表 -->
        <div class="signals-list">
          <div v-if="filteredSignals.length === 0" class="empty-signals">
            <a-empty description="暂无交易信号" size="small" />
          </div>
          
          <div v-else class="signals-content">
            <div
              v-for="signal in filteredSignals"
              :key="signal.id"
              class="signal-item"
              :class="[
                `signal-${signal.type}`,
                `strength-${signal.strength}`,
                { 'signal-active': signal.isActive }
              ]"
              @click="handleSignalClick(signal)"
            >
              <div class="signal-header">
                <div class="signal-info">
                  <span class="signal-symbol">{{ signal.symbol }}</span>
                  <a-tag
                    :color="signal.type === 'buy' ? 'green' : 'red'"
                    size="small"
                  >
                    {{ signal.type === 'buy' ? '买入' : '卖出' }}
                  </a-tag>
                  <a-tag
                    :color="getStrengthColor(signal.strength)"
                    size="small"
                  >
                    {{ getStrengthText(signal.strength) }}
                  </a-tag>
                </div>
                <div class="signal-time">
                  {{ formatTime(signal.timestamp) }}
                </div>
              </div>
              
              <div class="signal-details">
                <div class="detail-row">
                  <span class="detail-label">价格:</span>
                  <span class="detail-value">{{ formatPrice(signal.price) }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">时间框架:</span>
                  <span class="detail-value">{{ signal.timeframe }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">指标:</span>
                  <span class="detail-value">{{ signal.indicator }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">置信度:</span>
                  <span class="detail-value">{{ formatPercentage(signal.confidence) }}</span>
                </div>
              </div>
              
              <div class="signal-description">
                <p class="signal-reason">{{ signal.reason }}</p>
                <div v-if="signal.targets" class="signal-targets">
                  <div class="target-item">
                    <span class="target-label">目标价:</span>
                    <span class="target-value">{{ formatPrice(signal.targets.target) }}</span>
                  </div>
                  <div class="target-item">
                    <span class="target-label">止损价:</span>
                    <span class="target-value stop-loss">{{ formatPrice(signal.targets.stopLoss) }}</span>
                  </div>
                </div>
              </div>
              
              <div class="signal-actions">
                <a-space size="small">
                  <a-button
                    size="small"
                    type="primary"
                    @click.stop="executeSignal(signal)"
                    :disabled="!signal.isActive"
                  >
                    执行
                  </a-button>
                  <a-button
                    size="small"
                    @click.stop="ignoreSignal(signal)"
                  >
                    忽略
                  </a-button>
                  <a-button
                    size="small"
                    type="text"
                    @click.stop="viewSignalDetails(signal)"
                  >
                    详情
                  </a-button>
                </a-space>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 信号设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="信号设置"
      @ok="saveSettings"
      @cancel="resetSettings"
    >
      <a-form :model="settings" layout="vertical">
        <a-form-item label="启用信号">
          <a-switch v-model:checked="settings.enabled" />
          <span class="setting-desc">开启/关闭交易信号功能</span>
        </a-form-item>
        
        <a-form-item label="信号强度过滤">
          <a-checkbox-group v-model:value="settings.strengthFilter">
            <a-checkbox value="strong">强信号</a-checkbox>
            <a-checkbox value="medium">中等信号</a-checkbox>
            <a-checkbox value="weak">弱信号</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="最小置信度">
          <a-slider
            v-model:value="settings.minConfidence"
            :min="50"
            :max="100"
            :marks="{ 50: '50%', 70: '70%', 90: '90%', 100: '100%' }"
          />
        </a-form-item>
        
        <a-form-item label="声音提醒">
          <a-switch v-model:checked="settings.soundAlert" />
          <span class="setting-desc">新信号时播放提示音</span>
        </a-form-item>
        
        <a-form-item label="桌面通知">
          <a-switch v-model:checked="settings.desktopNotification" />
          <span class="setting-desc">新信号时显示桌面通知</span>
        </a-form-item>
        
        <a-form-item label="自动执行">
          <a-switch v-model:checked="settings.autoExecute" />
          <span class="setting-desc">自动执行高置信度信号</span>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 信号详情模态框 -->
    <a-modal
      v-model:open="showSignalDetails"
      title="信号详情"
      :footer="null"
      width="600px"
    >
      <div v-if="selectedSignal" class="signal-details-modal">
        <div class="detail-header">
          <h3>{{ selectedSignal.symbol }} - {{ selectedSignal.type === 'buy' ? '买入' : '卖出' }}信号</h3>
          <a-tag :color="getStrengthColor(selectedSignal.strength)">
            {{ getStrengthText(selectedSignal.strength) }}
          </a-tag>
        </div>
        
        <div class="detail-content">
          <div class="detail-section">
            <h4>基本信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <span class="label">触发价格:</span>
                <span class="value">{{ formatPrice(selectedSignal.price) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">时间框架:</span>
                <span class="value">{{ selectedSignal.timeframe }}</span>
              </div>
              <div class="detail-item">
                <span class="label">指标来源:</span>
                <span class="value">{{ selectedSignal.indicator }}</span>
              </div>
              <div class="detail-item">
                <span class="label">置信度:</span>
                <span class="value">{{ formatPercentage(selectedSignal.confidence) }}</span>
              </div>
            </div>
          </div>
          
          <div class="detail-section" v-if="selectedSignal.targets">
            <h4>交易目标</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <span class="label">目标价格:</span>
                <span class="value">{{ formatPrice(selectedSignal.targets.target) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">止损价格:</span>
                <span class="value stop-loss">{{ formatPrice(selectedSignal.targets.stopLoss) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">风险回报比:</span>
                <span class="value">{{ selectedSignal.targets.riskReward }}</span>
              </div>
            </div>
          </div>
          
          <div class="detail-section">
            <h4>信号分析</h4>
            <p class="signal-analysis">{{ selectedSignal.analysis }}</p>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { message, notification } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { useChartStore } from '@/stores/chartStore'
import { useMarketDataStore } from '@/stores/marketDataStore'
import {
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

// 定义事件
const emit = defineEmits(['signal-execute', 'signal-ignore'])

// 使用状态管理
const chartStore = useChartStore()
const marketDataStore = useMarketDataStore()
const {
  selectedSymbol,
  isDarkTheme
} = storeToRefs(chartStore)

// 本地状态
const showSettings = ref(false)
const showSignalDetails = ref(false)
const selectedSignal = ref(null)
const updateInterval = ref(null)

// 过滤器状态
const selectedTimeframe = ref('all')
const selectedType = ref('all')
const selectedStrength = ref('all')

// 设置
const settings = ref({
  enabled: true,
  strengthFilter: ['strong', 'medium'],
  minConfidence: 70,
  soundAlert: true,
  desktopNotification: true,
  autoExecute: false
})

// 模拟交易信号数据
const signals = ref([
  {
    id: 1,
    symbol: 'BTC-USDT',
    type: 'buy',
    strength: 'strong',
    price: 43250.80,
    timeframe: '1h',
    indicator: 'MACD + RSI',
    confidence: 85,
    timestamp: Date.now() - 300000,
    reason: 'MACD金叉，RSI从超卖区域反弹，成交量放大',
    analysis: 'BTC在43000附近获得强支撑，MACD指标出现金叉信号，同时RSI从30以下的超卖区域快速反弹至50以上，成交量明显放大，技术面呈现多头排列。建议在当前价位附近建立多头仓位。',
    targets: {
      target: 44500.00,
      stopLoss: 42800.00,
      riskReward: '1:2.8'
    },
    isActive: true
  },
  {
    id: 2,
    symbol: 'ETH-USDT',
    type: 'sell',
    strength: 'medium',
    price: 2620.45,
    timeframe: '4h',
    indicator: '布林带 + KDJ',
    confidence: 72,
    timestamp: Date.now() - 600000,
    reason: '价格触及布林带上轨，KDJ指标高位死叉',
    analysis: 'ETH价格已触及布林带上轨阻力位，KDJ指标在80以上高位区域出现死叉信号，短期存在回调压力。建议谨慎做多或考虑减仓。',
    targets: {
      target: 2550.00,
      stopLoss: 2680.00,
      riskReward: '1:1.2'
    },
    isActive: true
  },
  {
    id: 3,
    symbol: 'BNB-USDT',
    type: 'buy',
    strength: 'weak',
    price: 312.45,
    timeframe: '15m',
    indicator: 'MA均线',
    confidence: 65,
    timestamp: Date.now() - 900000,
    reason: '价格站上20日均线，短期均线多头排列',
    analysis: 'BNB价格重新站上20日均线，5日、10日、20日均线呈现多头排列，短期技术面有所改善，但需要成交量配合确认。',
    targets: {
      target: 325.00,
      stopLoss: 308.00,
      riskReward: '1:2.8'
    },
    isActive: false
  }
])

// 计算属性
const filteredSignals = computed(() => {
  return signals.value.filter(signal => {
    if (selectedTimeframe.value !== 'all' && signal.timeframe !== selectedTimeframe.value) {
      return false
    }
    if (selectedType.value !== 'all' && signal.type !== selectedType.value) {
      return false
    }
    if (selectedStrength.value !== 'all' && signal.strength !== selectedStrength.value) {
      return false
    }
    if (signal.confidence < settings.value.minConfidence) {
      return false
    }
    if (!settings.value.strengthFilter.includes(signal.strength)) {
      return false
    }
    return true
  })
})

const todaySignalsCount = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return signals.value.filter(signal => signal.timestamp >= today.getTime()).length
})

const successRate = computed(() => {
  // 模拟成功率计算
  return 73.5
})

const profitLossRatio = computed(() => {
  // 模拟盈亏比计算
  return 2.3
})

/**
 * 格式化价格
 */
const formatPrice = (price) => {
  if (!price) return '0.00'
  return parseFloat(price).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

/**
 * 格式化百分比
 */
const formatPercentage = (percent) => {
  if (!percent) return '0%'
  return `${parseFloat(percent).toFixed(1)}%`
}

/**
 * 格式化时间
 */
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

/**
 * 获取强度颜色
 */
const getStrengthColor = (strength) => {
  switch (strength) {
    case 'strong': return 'red'
    case 'medium': return 'orange'
    case 'weak': return 'blue'
    default: return 'default'
  }
}

/**
 * 获取强度文本
 */
const getStrengthText = (strength) => {
  switch (strength) {
    case 'strong': return '强'
    case 'medium': return '中'
    case 'weak': return '弱'
    default: return '未知'
  }
}

/**
 * 获取成功率样式类
 */
const getSuccessRateClass = (rate) => {
  if (rate >= 80) return 'high-success'
  if (rate >= 60) return 'medium-success'
  return 'low-success'
}

/**
 * 过滤信号
 */
const filterSignals = () => {
  // 过滤逻辑已在计算属性中实现
}

/**
 * 刷新信号
 */
const refreshSignals = () => {
  // 模拟新信号生成
  const newSignal = {
    id: Date.now(),
    symbol: selectedSymbol.value || 'BTC-USDT',
    type: Math.random() > 0.5 ? 'buy' : 'sell',
    strength: ['strong', 'medium', 'weak'][Math.floor(Math.random() * 3)],
    price: 43000 + Math.random() * 1000,
    timeframe: ['1m', '5m', '15m', '1h', '4h'][Math.floor(Math.random() * 5)],
    indicator: ['MACD', 'RSI', '布林带', 'KDJ', 'MA均线'][Math.floor(Math.random() * 5)],
    confidence: 60 + Math.random() * 40,
    timestamp: Date.now(),
    reason: '技术指标发出信号',
    analysis: '基于当前市场技术分析生成的交易信号',
    targets: {
      target: 44000,
      stopLoss: 42000,
      riskReward: '1:2.0'
    },
    isActive: true
  }
  
  signals.value.unshift(newSignal)
  
  // 限制信号数量
  if (signals.value.length > 20) {
    signals.value = signals.value.slice(0, 20)
  }
  
  message.success('信号已刷新')
  
  // 新信号通知
  if (settings.value.soundAlert) {
    playNotificationSound()
  }
  
  if (settings.value.desktopNotification) {
    showDesktopNotification(newSignal)
  }
}

/**
 * 处理信号点击
 */
const handleSignalClick = (signal) => {
  selectedSignal.value = signal
  showSignalDetails.value = true
}

/**
 * 执行信号
 */
const executeSignal = (signal) => {
  emit('signal-execute', signal)
  message.success(`执行${signal.type === 'buy' ? '买入' : '卖出'}信号: ${signal.symbol}`)
}

/**
 * 忽略信号
 */
const ignoreSignal = (signal) => {
  signal.isActive = false
  emit('signal-ignore', signal)
  message.info(`已忽略信号: ${signal.symbol}`)
}

/**
 * 查看信号详情
 */
const viewSignalDetails = (signal) => {
  selectedSignal.value = signal
  showSignalDetails.value = true
}

/**
 * 播放通知音
 */
const playNotificationSound = () => {
  try {
    const audio = new Audio('/notification.mp3')
    audio.play().catch(() => {
      // 忽略播放失败
    })
  } catch (error) {
    // 忽略音频错误
  }
}

/**
 * 显示桌面通知
 */
const showDesktopNotification = (signal) => {
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification('新交易信号', {
      body: `${signal.symbol} - ${signal.type === 'buy' ? '买入' : '卖出'}信号`,
      icon: '/favicon.ico'
    })
  }
}

/**
 * 保存设置
 */
const saveSettings = () => {
  localStorage.setItem('tradingSignalSettings', JSON.stringify(settings.value))
  showSettings.value = false
  message.success('设置已保存')
  
  // 请求通知权限
  if (settings.value.desktopNotification && 'Notification' in window) {
    Notification.requestPermission()
  }
}

/**
 * 重置设置
 */
const resetSettings = () => {
  settings.value = {
    enabled: true,
    strengthFilter: ['strong', 'medium'],
    minConfidence: 70,
    soundAlert: true,
    desktopNotification: true,
    autoExecute: false
  }
}

/**
 * 加载设置
 */
const loadSettings = () => {
  const saved = localStorage.getItem('tradingSignalSettings')
  if (saved) {
    try {
      settings.value = { ...settings.value, ...JSON.parse(saved) }
    } catch (error) {
      console.error('加载信号设置失败:', error)
    }
  }
}

/**
 * 开始自动刷新
 */
const startAutoRefresh = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
  }
  
  updateInterval.value = setInterval(() => {
    if (settings.value.enabled && Math.random() > 0.7) {
      refreshSignals()
    }
  }, 30000) // 30秒检查一次
}

/**
 * 停止自动刷新
 */
const stopAutoRefresh = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
    updateInterval.value = null
  }
}

// 监听设置变化
watch(() => settings.value.enabled, (enabled) => {
  if (enabled) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})

// 生命周期
onMounted(() => {
  loadSettings()
  if (settings.value.enabled) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.trading-signal-panel {
  height: 100%;
}

.trading-signal-panel .ant-card {
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.signal-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.signal-filters {
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.signal-stats {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 11px;
  color: #666;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.stat-value.high-success {
  color: #52c41a;
}

.stat-value.medium-success {
  color: #fa8c16;
}

.stat-value.low-success {
  color: #f5222d;
}

.signals-list {
  flex: 1;
  overflow: hidden;
}

.empty-signals {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.signals-content {
  height: 100%;
  overflow-y: auto;
}

.signal-item {
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.signal-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.signal-item.signal-buy {
  border-left: 4px solid #52c41a;
}

.signal-item.signal-sell {
  border-left: 4px solid #f5222d;
}

.signal-item.strength-strong {
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.05), rgba(255, 77, 79, 0.02));
}

.signal-item.strength-medium {
  background: linear-gradient(135deg, rgba(250, 140, 22, 0.05), rgba(250, 140, 22, 0.02));
}

.signal-item.strength-weak {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05), rgba(24, 144, 255, 0.02));
}

.signal-item.signal-active::before {
  content: '';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background: #52c41a;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

.signal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.signal-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.signal-symbol {
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.signal-time {
  font-size: 11px;
  color: #999;
}

.signal-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.detail-label {
  color: #666;
}

.detail-value {
  font-weight: 500;
  color: #333;
}

.signal-description {
  margin-bottom: 12px;
}

.signal-reason {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.signal-targets {
  display: flex;
  gap: 16px;
}

.target-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.target-label {
  font-size: 10px;
  color: #999;
}

.target-value {
  font-size: 11px;
  font-weight: 500;
  color: #333;
}

.target-value.stop-loss {
  color: #f5222d;
}

.signal-actions {
  display: flex;
  justify-content: flex-end;
}

.setting-desc {
  margin-left: 8px;
  font-size: 12px;
  color: #999;
}

.signal-details-modal {
  padding: 16px 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-header h3 {
  margin: 0;
  color: #333;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.detail-item .label {
  color: #666;
  font-size: 12px;
}

.detail-item .value {
  font-weight: 500;
  color: #333;
  font-size: 12px;
}

.detail-item .value.stop-loss {
  color: #f5222d;
}

.signal-analysis {
  font-size: 13px;
  line-height: 1.6;
  color: #666;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin: 0;
}

/* 深色主题样式 */
.trading-signal-panel.dark-theme .ant-card {
  background: #1f1f1f;
  border-color: #434343;
}

.trading-signal-panel.dark-theme .signal-filters,
.trading-signal-panel.dark-theme .signal-stats {
  background: #2a2a2a;
}

.trading-signal-panel.dark-theme .signal-item {
  background: #2a2a2a;
  border-color: #434343;
}

.trading-signal-panel.dark-theme .signal-item:hover {
  background: #333;
}

.trading-signal-panel.dark-theme .stat-label,
.trading-signal-panel.dark-theme .detail-label,
.trading-signal-panel.dark-theme .signal-time,
.trading-signal-panel.dark-theme .signal-reason,
.trading-signal-panel.dark-theme .target-label {
  color: #999;
}

.trading-signal-panel.dark-theme .stat-value,
.trading-signal-panel.dark-theme .detail-value,
.trading-signal-panel.dark-theme .signal-symbol,
.trading-signal-panel.dark-theme .target-value {
  color: #fff;
}

.trading-signal-panel.dark-theme .detail-item {
  background: #333;
}

.trading-signal-panel.dark-theme .signal-analysis {
  background: #333;
  color: #d1d5db;
}
</style>