# OKX量化交易系统文档

## 项目概述

本项目是一个基于OKX交易所API的专业量化交易系统，集成了K线图表分析、AI智能分析、策略管理、实时交易等核心功能。系统采用前后端分离架构，前端使用Vue 3 + TypeScript，后端使用FastAPI + Python，为用户提供专业、稳定、高效的量化交易解决方案。

## 项目状态

**当前版本**: v1.0.0-beta  
**开发状态**: 核心功能已完成，正在进行功能完善和测试  
**最后更新**: 2024年12月

### 完成度概览
- ✅ **基础架构**: 100% (前后端框架搭建完成)
- ✅ **用户系统**: 90% (注册、登录、权限管理)
- ✅ **图表系统**: 95% (K线图表、技术指标、绘图工具)
- ✅ **交易功能**: 85% (下单、撤单、订单管理)
- ✅ **OKX集成**: 90% (API对接、实时数据)
- 🔄 **AI分析**: 70% (趋势分析、信号生成)
- 🔄 **策略系统**: 75% (策略创建、回测、执行)
- 🔄 **风险管理**: 60% (风险控制、资金管理)
- ⏳ **数据分析**: 40% (统计报表、性能分析)
- ⏳ **移动端**: 30% (响应式适配)

## 文档目录

### 1. [产品说明](./产品说明.md)
- 产品概述与核心价值
- 专业图表分析功能 (KLineCharts 9.8.6)
- AI智能分析能力 (趋势识别、信号生成)
- 交易管理系统 (多种订单类型、风险控制)
- 数据分析平台 (实时监控、统计报表)
- 技术架构详解 (Vue 3 + FastAPI)

### 2. [系统设计](./系统设计.md)
- 系统架构总览 (微服务架构)
- 核心业务模块 (用户、交易、策略、AI)
- 数据分析模块 (实时计算、历史分析)
- 技术支撑模块 (缓存、消息队列、监控)
- 前后端交互流程
- WebSocket推送架构

### 3. [接口文档](./接口文档.md)
- 用户认证接口 (注册、登录、JWT)
- OKX交易接口 (账户、订单、持仓)
- 行情数据接口 (K线、深度、实时行情)
- AI分析接口 (趋势分析、交易信号)
- 策略管理接口 (创建、回测、执行)
- WebSocket接口 (实时推送、订阅管理)

### 4. [数据结构](./数据结构.md)
- 数据库表设计 (用户、订单、策略、AI分析)
- API响应结构 (OKX订单、K线、账户余额)
- 业务数据模型 (策略参数、风险管理)
- 实时数据格式 (WebSocket推送)

### 5. [页面设计](./页面设计.md)
- 技术栈详解 (Vue 3.4.21 + Ant Design Vue 4.1.2)
- 核心页面设计 (交易、策略、AI分析、账户)
- 组件库架构 (图表、交易、面板组件)
- 状态管理方案 (Pinia)
- 响应式设计规范
- 主题系统与性能优化

### 6. [实施计划](./实施计划.md)
- 项目整体进展 (已完成、进行中、待完成)
- 技术架构实现状态
- 核心功能实现清单
- 下一步开发计划 (短期、中期、长期)
- 技术债务与优化点
- 风险评估与应对措施

## 快速开始

### 环境要求
- **Python**: 3.8+ (推荐 3.11)
- **Node.js**: 16+ (推荐 18.x)
- **数据库**: SQLite (开发) / MySQL 8.0+ (生产)
- **Redis**: 6.0+ (可选，用于缓存)
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 20.04+

### 安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd python-okx
```

#### 2. 后端环境配置
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate
# 激活虚拟环境 (macOS/Linux)
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

#### 3. 前端环境配置
```bash
cd web-frontend
npm install
# 或使用 yarn
yarn install
```

#### 4. 配置文件
```bash
# 复制配置文件模板
cp webapp/config.py.example webapp/config.py

# 编辑配置文件，设置数据库连接、OKX API等
```

#### 5. 启动服务

**启动后端服务**:
```bash
# 使用提供的启动脚本
python run_backend.py

# 或直接使用uvicorn
uvicorn webapp.main:app --host 127.0.0.1 --port 8080 --reload
```

**启动前端服务**:
```bash
# 进入前端目录
cd web-frontend

# 使用提供的启动脚本 (Windows)
start-vite.bat

# 或直接使用npm
npm run dev
```

### 访问系统
- **前端地址**: http://localhost:5173
- **后端API**: http://localhost:8080
- **API文档**: http://localhost:8080/docs

## 核心功能特性

### 🎯 专业图表分析
- **K线图表**: 基于KLineCharts 9.8.6，支持多时间周期
- **技术指标**: 集成20+专业指标 (MA, MACD, RSI, BOLL等)
- **绘图工具**: 趋势线、支撑阻力位、斐波那契回调
- **实时更新**: WebSocket推送，毫秒级数据同步

### 🤖 AI智能分析
- **趋势识别**: 自动识别市场趋势和转折点
- **信号生成**: 基于多因子模型的交易信号
- **风险评估**: 实时风险监控和预警
- **情绪分析**: 市场情绪指标和投资者行为分析

### 📊 策略管理
- **策略创建**: 可视化策略编辑器
- **回测系统**: 历史数据回测和性能评估
- **实盘交易**: 策略自动执行和监控
- **风险控制**: 多层级风险管理机制

### 💼 交易管理
- **多种订单**: 市价、限价、止损、止盈订单
- **仓位管理**: 智能仓位分配和风险控制
- **实时监控**: 订单状态和持仓实时更新
- **交易统计**: 详细的交易记录和分析

## 技术架构

### 前端技术栈
- **框架**: Vue 3.4.21 + TypeScript 5.3.3
- **构建**: Vite 5.1.4 + ESBuild
- **UI库**: Ant Design Vue 4.1.2
- **状态**: Pinia 2.1.7
- **路由**: Vue Router 4.3.0
- **图表**: KLineCharts 9.8.6 + ECharts 5.5.0
- **HTTP**: Axios 1.6.7
- **样式**: Sass 1.71.1

### 后端技术栈
- **框架**: FastAPI 0.104.1
- **服务器**: Uvicorn 0.24.0
- **数据库**: SQLAlchemy 2.0.23 + SQLite/MySQL
- **认证**: JWT + bcrypt
- **API集成**: OKX API v5
- **实时推送**: WebSocket
- **异步**: asyncio + aiohttp

### 部署架构
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **进程管理**: PM2 / Supervisor
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

## 开发指南

### 代码规范
- **Python**: PEP 8 + Black + isort
- **JavaScript/TypeScript**: ESLint + Prettier
- **Vue**: Vue 3 Composition API + `<script setup>`
- **CSS**: BEM命名规范 + Sass

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 分支管理
- `main`: 主分支，稳定版本
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 部署说明

### 开发环境
- 使用SQLite数据库
- 前后端分离开发
- 热重载支持

### 生产环境
- MySQL数据库
- Redis缓存
- Nginx反向代理
- HTTPS证书
- 容器化部署

## 常见问题

### Q: 如何配置OKX API？
A: 在`webapp/config.py`中设置API密钥，确保账户有相应的交易权限。

### Q: 前端启动失败？
A: 检查Node.js版本，清除node_modules重新安装依赖。

### Q: 后端API无法访问？
A: 检查端口占用，确保防火墙设置正确。

### Q: WebSocket连接失败？
A: 检查网络连接，确保WebSocket端口未被阻塞。

## 技术支持

### 文档资源
- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [Vue 3官方文档](https://vuejs.org/)
- [OKX API文档](https://www.okx.com/docs-v5/)
- [KLineCharts文档](https://klinecharts.com/)

### 社区支持
- GitHub Issues: 报告bug和功能请求
- 技术交流群: 开发者讨论和经验分享
- 在线文档: 详细的使用说明和API参考

### 联系方式
如有技术问题或商务合作，请通过以下方式联系：
- 邮箱: <EMAIL>
- 微信群: 扫描二维码加入
- QQ群: 123456789

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](../LICENSE) 文件。

## 更新日志

### v1.0.0-beta (2024-12)
- ✅ 完成基础架构搭建
- ✅ 实现用户认证系统
- ✅ 集成OKX交易API
- ✅ 完成K线图表功能
- ✅ 实现基础交易功能
- 🔄 AI分析功能开发中
- 🔄 策略系统完善中

### 下一版本计划 (v1.1.0)
- 完善AI分析功能
- 增强策略回测系统
- 优化移动端体验
- 增加更多技术指标
- 完善风险管理系统