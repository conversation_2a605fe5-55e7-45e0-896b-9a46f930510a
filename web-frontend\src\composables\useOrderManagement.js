import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 订单管理组合式函数
 * 提供交易订单的创建、修改、取消和执行功能
 */
export function useOrderManagement() {
  // 订单列表
  const orders = ref([])
  const isLoading = ref(false)
  const isSubmitting = ref(false)

  // 订单统计
  const totalOrders = computed(() => orders.value.length)
  const pendingOrders = computed(() => orders.value.filter(o => o.status === 'pending'))
  const filledOrders = computed(() => orders.value.filter(o => o.status === 'filled'))
  const cancelledOrders = computed(() => orders.value.filter(o => o.status === 'cancelled'))
  const partiallyFilledOrders = computed(() => orders.value.filter(o => o.status === 'partially_filled'))
  
  const buyOrders = computed(() => orders.value.filter(o => o.side === 'buy'))
  const sellOrders = computed(() => orders.value.filter(o => o.side === 'sell'))
  
  const marketOrders = computed(() => orders.value.filter(o => o.type === 'market'))
  const limitOrders = computed(() => orders.value.filter(o => o.type === 'limit'))
  const stopOrders = computed(() => orders.value.filter(o => o.type === 'stop'))
  const stopLimitOrders = computed(() => orders.value.filter(o => o.type === 'stop_limit'))

  // 订单类型定义
  const orderTypes = {
    market: '市价单',
    limit: '限价单',
    stop: '止损单',
    stop_limit: '止损限价单',
    trailing_stop: '跟踪止损单',
    iceberg: '冰山单',
    twap: 'TWAP单',
    bracket: '括号单'
  }

  // 订单状态定义
  const orderStatuses = {
    pending: '待成交',
    partially_filled: '部分成交',
    filled: '已成交',
    cancelled: '已取消',
    rejected: '已拒绝',
    expired: '已过期'
  }

  /**
   * 创建新订单
   * @param {Object} orderData - 订单数据
   * @returns {Promise<Object>} 操作结果
   */
  async function createOrder(orderData) {
    try {
      isSubmitting.value = true

      // 验证订单数据
      const validatedData = validateOrderData(orderData)
      
      // 检查风险限制
      const riskCheck = await checkOrderRisk(validatedData)
      if (!riskCheck.allowed) {
        return {
          success: false,
          reason: riskCheck.reason,
          riskLevel: riskCheck.riskLevel
        }
      }

      // 创建订单对象
      const order = {
        id: generateOrderId(),
        ...validatedData,
        status: 'pending',
        createTime: new Date(),
        updateTime: new Date(),
        filledQuantity: 0,
        remainingQuantity: validatedData.quantity,
        averagePrice: 0,
        fees: 0,
        commission: 0,
        executedValue: 0,
        priority: calculateOrderPriority(validatedData),
        estimatedFees: calculateEstimatedFees(validatedData)
      }

      // 特殊订单类型处理
      if (order.type === 'bracket') {
        order.childOrders = createBracketChildOrders(order)
      }

      // 添加到订单列表
      orders.value.push(order)
      
      // 模拟订单提交到交易所
      await submitOrderToExchange(order)
      
      message.success(`订单已提交: ${order.symbol} ${orderTypes[order.type]}`)
      
      return {
        success: true,
        order,
        orderId: order.id
      }
    } catch (error) {
      console.error('创建订单失败:', error)
      message.error('创建订单失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    } finally {
      isSubmitting.value = false
    }
  }

  /**
   * 修改订单
   * @param {string} orderId - 订单ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 操作结果
   */
  async function modifyOrder(orderId, updateData) {
    try {
      const order = orders.value.find(o => o.id === orderId)
      if (!order) {
        throw new Error('订单不存在')
      }

      if (!['pending', 'partially_filled'].includes(order.status)) {
        throw new Error('只能修改待成交或部分成交的订单')
      }

      // 验证修改数据
      const validatedUpdate = validateOrderUpdate(order, updateData)
      
      // 更新订单
      const updatedOrder = {
        ...order,
        ...validatedUpdate,
        updateTime: new Date(),
        modifyCount: (order.modifyCount || 0) + 1
      }

      // 重新计算相关字段
      if (updateData.quantity) {
        updatedOrder.remainingQuantity = updateData.quantity - order.filledQuantity
        updatedOrder.estimatedFees = calculateEstimatedFees(updatedOrder)
      }

      // 更新订单
      const index = orders.value.findIndex(o => o.id === orderId)
      orders.value[index] = updatedOrder

      // 提交修改到交易所
      await submitOrderModificationToExchange(updatedOrder)

      message.success('订单修改成功')
      
      return {
        success: true,
        order: updatedOrder
      }
    } catch (error) {
      console.error('修改订单失败:', error)
      message.error('修改订单失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 取消订单
   * @param {string} orderId - 订单ID
   * @param {string} reason - 取消原因
   * @returns {Promise<Object>} 操作结果
   */
  async function cancelOrder(orderId, reason = 'user_cancelled') {
    try {
      const order = orders.value.find(o => o.id === orderId)
      if (!order) {
        throw new Error('订单不存在')
      }

      if (!['pending', 'partially_filled'].includes(order.status)) {
        throw new Error('只能取消待成交或部分成交的订单')
      }

      // 更新订单状态
      const cancelledOrder = {
        ...order,
        status: 'cancelled',
        cancelTime: new Date(),
        cancelReason: reason,
        updateTime: new Date()
      }

      // 更新订单
      const index = orders.value.findIndex(o => o.id === orderId)
      orders.value[index] = cancelledOrder

      // 提交取消到交易所
      await submitOrderCancellationToExchange(orderId, reason)

      message.success('订单已取消')
      
      return {
        success: true,
        order: cancelledOrder
      }
    } catch (error) {
      console.error('取消订单失败:', error)
      message.error('取消订单失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 批量取消订单
   * @param {Array} orderIds - 订单ID列表
   * @param {string} reason - 取消原因
   * @returns {Promise<Object>} 操作结果
   */
  async function batchCancelOrders(orderIds, reason = 'batch_cancelled') {
    try {
      const results = []
      
      for (const orderId of orderIds) {
        const result = await cancelOrder(orderId, reason)
        results.push({
          orderId,
          ...result
        })
      }

      const successCount = results.filter(r => r.success).length
      message.success(`成功取消 ${successCount} 个订单`)

      return {
        success: true,
        results,
        successCount
      }
    } catch (error) {
      console.error('批量取消订单失败:', error)
      message.error('批量取消订单失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 取消所有订单
   * @param {string} symbol - 交易对（可选）
   * @param {string} reason - 取消原因
   * @returns {Promise<Object>} 操作结果
   */
  async function cancelAllOrders(symbol = null, reason = 'cancel_all') {
    try {
      let targetOrders = pendingOrders.value.concat(partiallyFilledOrders.value)
      
      if (symbol) {
        targetOrders = targetOrders.filter(o => o.symbol === symbol)
      }

      if (targetOrders.length === 0) {
        message.info('没有可取消的订单')
        return { success: true, cancelledCount: 0 }
      }

      const orderIds = targetOrders.map(o => o.id)
      const result = await batchCancelOrders(orderIds, reason)

      return {
        success: true,
        cancelledCount: result.successCount
      }
    } catch (error) {
      console.error('取消所有订单失败:', error)
      message.error('取消所有订单失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 创建市价单
   * @param {Object} params - 订单参数
   * @returns {Promise<Object>} 操作结果
   */
  async function createMarketOrder(params) {
    const orderData = {
      ...params,
      type: 'market',
      timeInForce: 'IOC' // 立即成交或取消
    }
    
    return await createOrder(orderData)
  }

  /**
   * 创建限价单
   * @param {Object} params - 订单参数
   * @returns {Promise<Object>} 操作结果
   */
  async function createLimitOrder(params) {
    if (!params.price) {
      throw new Error('限价单必须指定价格')
    }
    
    const orderData = {
      ...params,
      type: 'limit',
      timeInForce: params.timeInForce || 'GTC' // 撤销前有效
    }
    
    return await createOrder(orderData)
  }

  /**
   * 创建止损单
   * @param {Object} params - 订单参数
   * @returns {Promise<Object>} 操作结果
   */
  async function createStopOrder(params) {
    if (!params.stopPrice) {
      throw new Error('止损单必须指定止损价格')
    }
    
    const orderData = {
      ...params,
      type: 'stop',
      timeInForce: params.timeInForce || 'GTC'
    }
    
    return await createOrder(orderData)
  }

  /**
   * 创建止损限价单
   * @param {Object} params - 订单参数
   * @returns {Promise<Object>} 操作结果
   */
  async function createStopLimitOrder(params) {
    if (!params.stopPrice || !params.price) {
      throw new Error('止损限价单必须指定止损价格和限价')
    }
    
    const orderData = {
      ...params,
      type: 'stop_limit',
      timeInForce: params.timeInForce || 'GTC'
    }
    
    return await createOrder(orderData)
  }

  /**
   * 创建括号单（包含止损和止盈）
   * @param {Object} params - 订单参数
   * @returns {Promise<Object>} 操作结果
   */
  async function createBracketOrder(params) {
    if (!params.stopLossPrice || !params.takeProfitPrice) {
      throw new Error('括号单必须指定止损和止盈价格')
    }
    
    const orderData = {
      ...params,
      type: 'bracket',
      timeInForce: params.timeInForce || 'GTC'
    }
    
    return await createOrder(orderData)
  }

  /**
   * 创建TWAP订单（时间加权平均价格）
   * @param {Object} params - 订单参数
   * @returns {Promise<Object>} 操作结果
   */
  async function createTWAPOrder(params) {
    if (!params.duration || !params.sliceSize) {
      throw new Error('TWAP订单必须指定执行时长和切片大小')
    }
    
    const orderData = {
      ...params,
      type: 'twap',
      timeInForce: 'GTC',
      slices: Math.ceil(params.quantity / params.sliceSize),
      sliceInterval: params.duration / Math.ceil(params.quantity / params.sliceSize)
    }
    
    return await createOrder(orderData)
  }

  /**
   * 处理订单执行更新
   * @param {Object} executionData - 执行数据
   */
  function handleOrderExecution(executionData) {
    const { orderId, executedQuantity, executedPrice, fees, timestamp } = executionData
    
    const order = orders.value.find(o => o.id === orderId)
    if (!order) return

    // 更新订单执行信息
    const newFilledQuantity = order.filledQuantity + executedQuantity
    const newRemainingQuantity = order.quantity - newFilledQuantity
    
    // 计算平均价格
    const totalExecutedValue = order.executedValue + (executedQuantity * executedPrice)
    const averagePrice = totalExecutedValue / newFilledQuantity
    
    // 确定新状态
    let newStatus
    if (newRemainingQuantity <= 0) {
      newStatus = 'filled'
    } else if (newFilledQuantity > 0) {
      newStatus = 'partially_filled'
    } else {
      newStatus = order.status
    }

    // 更新订单
    const updatedOrder = {
      ...order,
      status: newStatus,
      filledQuantity: newFilledQuantity,
      remainingQuantity: newRemainingQuantity,
      averagePrice,
      executedValue: totalExecutedValue,
      fees: order.fees + fees,
      lastExecutionTime: new Date(timestamp),
      updateTime: new Date(),
      executions: [...(order.executions || []), {
        quantity: executedQuantity,
        price: executedPrice,
        fees,
        timestamp: new Date(timestamp)
      }]
    }

    // 更新订单列表
    const index = orders.value.findIndex(o => o.id === orderId)
    orders.value[index] = updatedOrder

    // 处理括号单的子订单
    if (order.type === 'bracket' && newStatus === 'filled') {
      activateBracketChildOrders(order)
    }

    // 发送执行通知
    const statusText = orderStatuses[newStatus]
    message.success(`订单${statusText}: ${order.symbol} ${executedQuantity}@${executedPrice}`)
  }

  /**
   * 获取指定交易对的订单
   * @param {string} symbol - 交易对
   * @param {string} status - 订单状态（可选）
   * @returns {Array} 订单列表
   */
  function getOrdersBySymbol(symbol, status = null) {
    let filteredOrders = orders.value.filter(o => o.symbol === symbol)
    
    if (status) {
      filteredOrders = filteredOrders.filter(o => o.status === status)
    }
    
    return filteredOrders
  }

  /**
   * 获取订单历史
   * @param {Object} filters - 过滤条件
   * @returns {Array} 订单历史
   */
  function getOrderHistory(filters = {}) {
    let filteredOrders = [...orders.value]
    
    if (filters.symbol) {
      filteredOrders = filteredOrders.filter(o => o.symbol === filters.symbol)
    }
    
    if (filters.status) {
      filteredOrders = filteredOrders.filter(o => o.status === filters.status)
    }
    
    if (filters.type) {
      filteredOrders = filteredOrders.filter(o => o.type === filters.type)
    }
    
    if (filters.side) {
      filteredOrders = filteredOrders.filter(o => o.side === filters.side)
    }
    
    if (filters.startDate) {
      filteredOrders = filteredOrders.filter(o => 
        new Date(o.createTime) >= new Date(filters.startDate)
      )
    }
    
    if (filters.endDate) {
      filteredOrders = filteredOrders.filter(o => 
        new Date(o.createTime) <= new Date(filters.endDate)
      )
    }
    
    // 按创建时间倒序排列
    return filteredOrders.sort((a, b) => 
      new Date(b.createTime) - new Date(a.createTime)
    )
  }

  /**
   * 计算订单统计
   * @param {Object} filters - 过滤条件
   * @returns {Object} 统计结果
   */
  function calculateOrderStatistics(filters = {}) {
    const filteredOrders = getOrderHistory(filters)
    
    const stats = {
      totalOrders: filteredOrders.length,
      filledOrders: filteredOrders.filter(o => o.status === 'filled').length,
      cancelledOrders: filteredOrders.filter(o => o.status === 'cancelled').length,
      totalVolume: 0,
      totalFees: 0,
      averageOrderSize: 0,
      fillRate: 0,
      cancelRate: 0
    }
    
    if (stats.totalOrders > 0) {
      stats.totalVolume = filteredOrders.reduce((sum, o) => 
        sum + (o.executedValue || 0), 0
      )
      
      stats.totalFees = filteredOrders.reduce((sum, o) => 
        sum + (o.fees || 0), 0
      )
      
      stats.averageOrderSize = stats.totalVolume / stats.totalOrders
      stats.fillRate = (stats.filledOrders / stats.totalOrders) * 100
      stats.cancelRate = (stats.cancelledOrders / stats.totalOrders) * 100
    }
    
    return stats
  }

  /**
   * 验证订单数据
   * @param {Object} orderData - 订单数据
   * @returns {Object} 验证后的数据
   */
  function validateOrderData(orderData) {
    const required = ['symbol', 'side', 'quantity', 'type']
    
    for (const field of required) {
      if (!orderData[field]) {
        throw new Error(`缺少必要字段: ${field}`)
      }
    }

    if (!['buy', 'sell'].includes(orderData.side)) {
      throw new Error('订单方向必须是 buy 或 sell')
    }

    if (orderData.quantity <= 0) {
      throw new Error('订单数量必须大于0')
    }

    if (!Object.keys(orderTypes).includes(orderData.type)) {
      throw new Error('无效的订单类型')
    }

    if (['limit', 'stop_limit'].includes(orderData.type) && !orderData.price) {
      throw new Error('限价单必须指定价格')
    }

    if (['stop', 'stop_limit'].includes(orderData.type) && !orderData.stopPrice) {
      throw new Error('止损单必须指定止损价格')
    }

    return {
      ...orderData,
      timeInForce: orderData.timeInForce || 'GTC',
      clientOrderId: orderData.clientOrderId || generateClientOrderId()
    }
  }

  /**
   * 验证订单更新数据
   * @param {Object} order - 原订单
   * @param {Object} updateData - 更新数据
   * @returns {Object} 验证后的更新数据
   */
  function validateOrderUpdate(order, updateData) {
    const allowedFields = ['quantity', 'price', 'stopPrice', 'timeInForce']
    const validatedUpdate = {}
    
    for (const [key, value] of Object.entries(updateData)) {
      if (!allowedFields.includes(key)) {
        throw new Error(`不允许修改字段: ${key}`)
      }
      
      if (key === 'quantity' && value <= order.filledQuantity) {
        throw new Error('新数量不能小于等于已成交数量')
      }
      
      if ((key === 'price' || key === 'stopPrice') && value <= 0) {
        throw new Error('价格必须大于0')
      }
      
      validatedUpdate[key] = value
    }
    
    return validatedUpdate
  }

  /**
   * 检查订单风险
   * @param {Object} orderData - 订单数据
   * @returns {Promise<Object>} 风险检查结果
   */
  async function checkOrderRisk(orderData) {
    // 检查最大订单数量
    if (pendingOrders.value.length >= 50) {
      return {
        allowed: false,
        reason: '待成交订单数量过多',
        riskLevel: 'high'
      }
    }

    // 检查订单金额
    const orderValue = orderData.quantity * (orderData.price || 0)
    if (orderValue > 1000000) { // 假设最大单笔100万
      return {
        allowed: false,
        reason: '单笔订单金额过大',
        riskLevel: 'high'
      }
    }

    // 检查价格偏离度
    if (orderData.type === 'limit' && orderData.price) {
      const marketPrice = await getMarketPrice(orderData.symbol)
      const deviation = Math.abs(orderData.price - marketPrice) / marketPrice
      
      if (deviation > 0.1) { // 偏离市价超过10%
        return {
          allowed: false,
          reason: '订单价格偏离市价过大',
          riskLevel: 'medium'
        }
      }
    }

    return {
      allowed: true,
      riskLevel: 'low'
    }
  }

  /**
   * 计算订单优先级
   * @param {Object} orderData - 订单数据
   * @returns {number} 优先级分数
   */
  function calculateOrderPriority(orderData) {
    let priority = 0
    
    // 市价单优先级最高
    if (orderData.type === 'market') {
      priority += 100
    }
    
    // 大额订单优先级较高
    const orderValue = orderData.quantity * (orderData.price || 0)
    priority += Math.min(orderValue / 10000, 50)
    
    // IOC订单优先级较高
    if (orderData.timeInForce === 'IOC') {
      priority += 30
    }
    
    return priority
  }

  /**
   * 计算预估手续费
   * @param {Object} orderData - 订单数据
   * @returns {number} 预估手续费
   */
  function calculateEstimatedFees(orderData) {
    const feeRate = orderData.type === 'market' ? 0.001 : 0.0008 // 市价单0.1%，限价单0.08%
    const orderValue = orderData.quantity * (orderData.price || orderData.estimatedPrice || 0)
    return orderValue * feeRate
  }

  /**
   * 创建括号单的子订单
   * @param {Object} parentOrder - 父订单
   * @returns {Array} 子订单列表
   */
  function createBracketChildOrders(parentOrder) {
    const childOrders = []
    
    // 止损订单
    if (parentOrder.stopLossPrice) {
      childOrders.push({
        id: generateOrderId(),
        parentOrderId: parentOrder.id,
        symbol: parentOrder.symbol,
        side: parentOrder.side === 'buy' ? 'sell' : 'buy',
        quantity: parentOrder.quantity,
        type: 'stop',
        stopPrice: parentOrder.stopLossPrice,
        timeInForce: 'GTC',
        status: 'inactive'
      })
    }
    
    // 止盈订单
    if (parentOrder.takeProfitPrice) {
      childOrders.push({
        id: generateOrderId(),
        parentOrderId: parentOrder.id,
        symbol: parentOrder.symbol,
        side: parentOrder.side === 'buy' ? 'sell' : 'buy',
        quantity: parentOrder.quantity,
        type: 'limit',
        price: parentOrder.takeProfitPrice,
        timeInForce: 'GTC',
        status: 'inactive'
      })
    }
    
    return childOrders
  }

  /**
   * 激活括号单的子订单
   * @param {Object} parentOrder - 父订单
   */
  function activateBracketChildOrders(parentOrder) {
    if (parentOrder.childOrders) {
      parentOrder.childOrders.forEach(childOrder => {
        childOrder.status = 'pending'
        childOrder.createTime = new Date()
        orders.value.push(childOrder)
      })
    }
  }

  /**
   * 获取市场价格（模拟）
   * @param {string} symbol - 交易对
   * @returns {Promise<number>} 市场价格
   */
  async function getMarketPrice(symbol) {
    // 模拟获取市场价格
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(Math.random() * 100 + 50) // 随机价格50-150
      }, 100)
    })
  }

  /**
   * 提交订单到交易所（模拟）
   * @param {Object} order - 订单对象
   * @returns {Promise<Object>} 提交结果
   */
  async function submitOrderToExchange(order) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 模拟交易所响应
    return {
      success: true,
      exchangeOrderId: `ex_${order.id}`,
      timestamp: new Date()
    }
  }

  /**
   * 提交订单修改到交易所（模拟）
   * @param {Object} order - 订单对象
   * @returns {Promise<Object>} 提交结果
   */
  async function submitOrderModificationToExchange(order) {
    await new Promise(resolve => setTimeout(resolve, 150))
    return { success: true, timestamp: new Date() }
  }

  /**
   * 提交订单取消到交易所（模拟）
   * @param {string} orderId - 订单ID
   * @param {string} reason - 取消原因
   * @returns {Promise<Object>} 提交结果
   */
  async function submitOrderCancellationToExchange(orderId, reason) {
    await new Promise(resolve => setTimeout(resolve, 100))
    return { success: true, timestamp: new Date() }
  }

  /**
   * 生成订单ID
   * @returns {string} 订单ID
   */
  function generateOrderId() {
    return `ord_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成客户端订单ID
   * @returns {string} 客户端订单ID
   */
  function generateClientOrderId() {
    return `cli_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`
  }

  /**
   * 导出订单数据
   */
  function exportOrders() {
    const data = {
      timestamp: new Date().toISOString(),
      orders: orders.value,
      statistics: calculateOrderStatistics()
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `orders-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    
    URL.revokeObjectURL(url)
    message.success('订单数据已导出')
  }

  // 监听订单变化
  watch(
    () => orders.value,
    () => {
      // 可以在这里添加自动保存逻辑
    },
    { deep: true }
  )

  return {
    // 状态
    orders,
    isLoading,
    isSubmitting,
    
    // 计算属性
    totalOrders,
    pendingOrders,
    filledOrders,
    cancelledOrders,
    partiallyFilledOrders,
    buyOrders,
    sellOrders,
    marketOrders,
    limitOrders,
    stopOrders,
    stopLimitOrders,
    
    // 常量
    orderTypes,
    orderStatuses,
    
    // 基础方法
    createOrder,
    modifyOrder,
    cancelOrder,
    batchCancelOrders,
    cancelAllOrders,
    
    // 特定类型订单创建方法
    createMarketOrder,
    createLimitOrder,
    createStopOrder,
    createStopLimitOrder,
    createBracketOrder,
    createTWAPOrder,
    
    // 订单处理方法
    handleOrderExecution,
    getOrdersBySymbol,
    getOrderHistory,
    calculateOrderStatistics,
    exportOrders,
    
    // 工具方法
    validateOrderData,
    calculateEstimatedFees,
    getMarketPrice
  }
}