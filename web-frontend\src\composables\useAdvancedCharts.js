import { ref, nextTick } from 'vue'
import * as echarts from 'echarts'

/**
 * 高级图表管理器
 * 提供增强的图表创建、更新和管理功能
 * @param {Object} refs - 图表引用对象
 * @param {Object} displaySettings - 显示设置
 * @returns {Object} 图表管理器实例
 */
export function useAdvancedCharts(refs, displaySettings) {
  // 图表实例存储
  const chartInstances = ref({})
  const isInitialized = ref(false)
  const currentTheme = ref('light')

  /**
   * 获取基础图表配置
   * @param {boolean} isDark - 是否为暗色主题
   * @returns {Object} 基础配置
   */
  const getBaseChartOptions = (isDark = false) => {
    const theme = isDark ? 'dark' : 'light'
    
    return {
      animation: true,
      animationDuration: 300,
      backgroundColor: isDark ? '#1e1e1e' : '#ffffff',
      textStyle: {
        color: isDark ? '#ffffff' : '#333333',
        fontSize: 12
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true,
        borderColor: isDark ? '#333333' : '#e8e8e8'
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLine: {
          lineStyle: {
            color: isDark ? '#666666' : '#cccccc'
          }
        },
        axisLabel: {
          color: isDark ? '#ffffff' : '#333333'
        }
      },
      yAxis: {
        type: 'value',
        scale: true,
        position: 'right',
        axisLine: {
          lineStyle: {
            color: isDark ? '#666666' : '#cccccc'
          }
        },
        axisLabel: {
          color: isDark ? '#ffffff' : '#333333',
          formatter: function(value) {
            return parseFloat(value).toFixed(2);
          }
        },
        splitLine: {
          lineStyle: {
            color: isDark ? '#333333' : '#e8e8e8'
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: isDark ? 'rgba(30, 30, 30, 0.9)' : 'rgba(255, 255, 255, 0.9)',
        borderColor: isDark ? '#555555' : '#cccccc',
        textStyle: {
          color: isDark ? '#ffffff' : '#333333'
        },
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: isDark ? '#666666' : '#999999'
          },
          label: {
            formatter: function(params) {
              if (params.axisDimension === 'x') {
                // 格式化时间显示为年月日时分
                const date = new Date(params.value);
                return date.toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit'
                });
              }
              return params.value;
            }
          }
        }
      }
    }
  }

  /**
   * 测试函数 - 简单的K线图配置生成
   * @param {Array} data - K线数据
   * @param {boolean} isDark - 是否为暗色主题
   * @returns {Object} 图表配置
   */
  const generateKlineConfig = (data, isDark = false) => {
    try {
      const baseOptions = getBaseChartOptions(isDark)
      
      // 提取时间轴数据
      const xAxisData = data && data.length > 0 
        ? data.map(item => {
            // 如果数据是数组格式 [timestamp, open, high, low, close, volume]
            if (Array.isArray(item) && item.length >= 6) {
              return parseInt(item[0]); // 返回时间戳，让ECharts自动格式化
            }
            // 如果数据是对象格式
            if (item.timestamp || item.time) {
              return parseInt(item.timestamp || item.time);
            }
            return 0
          })
        : []
      
      // 提取K线数据 [open, close, low, high]
      const candlestickData = data && data.length > 0
        ? data.map(item => {
            if (Array.isArray(item) && item.length >= 5) {
              return [parseFloat(item[1]), parseFloat(item[4]), parseFloat(item[3]), parseFloat(item[2])]
            }
            if (item.open !== undefined) {
              return [parseFloat(item.open), parseFloat(item.close), parseFloat(item.low), parseFloat(item.high)]
            }
            return [0, 0, 0, 0]
          })
        : []
      
      // 提取成交量数据
      const volumeData = data && data.length > 0
        ? data.map(item => {
            if (Array.isArray(item) && item.length >= 6) {
              return parseFloat(item[5])
            }
            if (item.volume !== undefined) {
              return parseFloat(item.volume)
            }
            return 0
          })
        : []

      return {
        ...baseOptions,
        xAxis: [
          {
            ...baseOptions.xAxis,
            type: 'time',
            data: xAxisData,
            gridIndex: 0,
            axisLabel: {
              formatter: function(value) {
                const date = new Date(value);
                return date.toLocaleString('zh-CN', {
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit'
                });
              }
            }
          },
          {
            ...baseOptions.xAxis,
            type: 'time',
            data: xAxisData,
            gridIndex: 1,
            axisLabel: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        grid: [
          {
            left: '10%',
            right: '8%',
            height: '60%'
          },
          {
            left: '10%',
            right: '8%',
            top: '75%',
            height: '16%'
          }
        ],
        yAxis: [
          {
            ...baseOptions.yAxis,
            gridIndex: 0
          },
          {
            type: 'value',
            gridIndex: 1,
            scale: true,
            axisLabel: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'candlestick',
            data: candlestickData.map((item, index) => [xAxisData[index], ...item]),
            yAxisIndex: 0,
            xAxisIndex: 0
          },
          {
            type: 'bar',
            name: '成交量',
            data: volumeData.map((item, index) => [xAxisData[index], item]),
            yAxisIndex: 1,
            xAxisIndex: 1,
            itemStyle: {
              color: function(params) {
                const dataIndex = params.dataIndex;
                if (dataIndex > 0 && candlestickData[dataIndex] && candlestickData[dataIndex - 1]) {
                  return candlestickData[dataIndex][1] >= candlestickData[dataIndex - 1][1] ? '#00b894' : '#d63031';
                }
                return '#666';
              }
            }
          }
        ]
      }
    } catch (error) {
      console.error('K线图配置生成失败:', error)
      const baseOptions = getBaseChartOptions(isDark)
      return { 
        ...baseOptions,
        xAxis: {
          ...baseOptions.xAxis,
          data: []
        },
        series: [] 
      }
    }
  }

  /**
   * 初始化所有图表
   * @returns {Promise<void>}
   */
  const initializeAllCharts = async () => {
    try {
      console.log('开始初始化所有图表...', refs)
      
      // 清理现有图表实例
      Object.values(chartInstances.value).forEach(chart => {
        if (chart && typeof chart.dispose === 'function') {
          chart.dispose()
        }
      })
      chartInstances.value = {}
      
      // 等待DOM更新
      await nextTick()
      
      // 初始化K线图表
      if (refs?.klineChartRef?.value?.getChartElement?.()) {
        const klineElement = refs.klineChartRef.value.getChartElement()
        const klineChart = echarts.init(klineElement, currentTheme.value)
        chartInstances.value.kline = klineChart
        
        // 开发环境下注册到全局
        if (process.env.NODE_ENV === 'development' && window.registerChartInstance) {
          window.registerChartInstance('advanced_kline', klineChart)
        }
        
        console.log('K线图表初始化完成')
      }
      
      // 初始化成交量图表
      if (refs?.volumeChartRef?.value?.getChartElement?.()) {
        const volumeElement = refs.volumeChartRef.value.getChartElement()
        const volumeChart = echarts.init(volumeElement, currentTheme.value)
        chartInstances.value.volume = volumeChart
        
        // 开发环境下注册到全局
        if (process.env.NODE_ENV === 'development' && window.registerChartInstance) {
          window.registerChartInstance('advanced_volume', volumeChart)
        }
        
        console.log('成交量图表初始化完成')
      }
      
      // 初始化RSI图表
      if (refs?.rsiChartRef?.value?.getChartElement?.()) {
        const rsiElement = refs.rsiChartRef.value.getChartElement()
        const rsiChart = echarts.init(rsiElement, currentTheme.value)
        chartInstances.value.rsi = rsiChart
        
        // 开发环境下注册到全局
        if (process.env.NODE_ENV === 'development' && window.registerChartInstance) {
          window.registerChartInstance('advanced_rsi', rsiChart)
        }
        
        console.log('RSI图表初始化完成')
      }
      
      // 初始化MACD图表
      if (refs?.macdChartRef?.value?.getChartElement?.()) {
        const macdElement = refs.macdChartRef.value.getChartElement()
        const macdChart = echarts.init(macdElement, currentTheme.value)
        chartInstances.value.macd = macdChart
        
        // 开发环境下注册到全局
        if (process.env.NODE_ENV === 'development' && window.registerChartInstance) {
          window.registerChartInstance('advanced_macd', macdChart)
        }
        
        console.log('MACD图表初始化完成')
      }
      
      // 初始化KDJ图表
      if (refs?.kdjChartRef?.value?.getChartElement?.()) {
        const kdjElement = refs.kdjChartRef.value.getChartElement()
        const kdjChart = echarts.init(kdjElement, currentTheme.value)
        chartInstances.value.kdj = kdjChart
        
        // 开发环境下注册到全局
        if (process.env.NODE_ENV === 'development' && window.registerChartInstance) {
          window.registerChartInstance('advanced_kdj', kdjChart)
        }
        
        console.log('KDJ图表初始化完成')
      }
      
      // 初始化布林带图表
      if (refs?.bollingerBandsChartRef?.value?.getChartElement?.()) {
        const bollingerElement = refs.bollingerBandsChartRef.value.getChartElement()
        const bollingerChart = echarts.init(bollingerElement, currentTheme.value)
        chartInstances.value.bollinger = bollingerChart
        
        // 开发环境下注册到全局
        if (process.env.NODE_ENV === 'development' && window.registerChartInstance) {
          window.registerChartInstance('advanced_bollinger', bollingerChart)
        }
        
        console.log('布林带图表初始化完成')
      }
      
      isInitialized.value = true
      console.log('所有图表初始化完成，图表实例:', Object.keys(chartInstances.value))
      
    } catch (error) {
      console.error('图表初始化失败:', error)
      isInitialized.value = false
      throw error
    }
  }
  
  /**
   * 更新图表配置
   * @param {string} chartType - 图表类型 ('main', 'sub', 'indicator')
   * @param {Object} options - 图表配置
   */
  const updateChart = (chartType, options) => {
    try {
      const chart = chartInstances.value[chartType]
      if (chart && options) {
        chart.setOption(options, true)
      }
    } catch (error) {
      console.error(`更新${chartType}图表失败:`, error)
    }
  }
  
  /**
   * 更新所有图表数据
   * @param {Array} data - 图表数据
   * @param {Object} indicators - 技术指标数据
   * @param {Object} settings - 显示设置
   * @returns {Promise<void>}
   */
  const updateAllCharts = async (data, indicators = {}, settings = {}) => {
    try {
      console.log('开始更新所有图表数据...', {
        dataLength: data?.length,
        indicators: Object.keys(indicators || {}),
        chartInstances: Object.keys(chartInstances.value)
      })
      
      if (!isInitialized.value) {
        console.warn('图表未初始化，跳过更新')
        return
      }
      
      // 更新K线图表
      if (chartInstances.value.kline && data && data.length > 0) {
        const klineOptions = generateKlineConfig(data, currentTheme.value === 'dark')
        chartInstances.value.kline.setOption(klineOptions, true)
        console.log('K线图表数据更新完成')
      }
      
      // 更新成交量图表
      if (chartInstances.value.volume && data && data.length > 0) {
        // 提取时间轴数据
        const xAxisData = data.map(item => parseInt(item[0]))
        
        // 提取成交量数据
        const volumeData = data.map(item => parseFloat(item[5]))
        
        const baseOptions = getBaseChartOptions(currentTheme.value === 'dark')
        const volumeOptions = {
          ...baseOptions,
          xAxis: {
            ...baseOptions.xAxis,
            type: 'time',
            data: xAxisData,
            axisLabel: {
              formatter: function(value) {
                const date = new Date(value);
                return date.toLocaleString('zh-CN', {
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit'
                });
              }
            }
          },
          series: [{
            type: 'bar',
            data: volumeData.map((item, index) => [xAxisData[index], item]),
            name: '成交量',
            itemStyle: {
              color: function(params) {
                // 根据涨跌设置颜色
                const dataIndex = params.dataIndex
                if (dataIndex > 0 && data[dataIndex] && data[dataIndex - 1]) {
                  return data[dataIndex][4] >= data[dataIndex - 1][4] ? '#00b894' : '#d63031'
                }
                return '#666'
              }
            }
          }]
        }
        chartInstances.value.volume.setOption(volumeOptions, true)
        console.log('成交量图表数据更新完成')
      }
      
      // 更新RSI图表
      if (chartInstances.value.rsi && indicators.rsi && indicators.rsi.length > 0) {
        // 提取时间轴数据
        const xAxisData = data && data.length > 0 ? data.map(item => parseInt(item[0])) : []
        
        const baseOptions = getBaseChartOptions(currentTheme.value === 'dark')
        const rsiOptions = {
          ...baseOptions,
          xAxis: {
            ...baseOptions.xAxis,
            type: 'time',
            data: xAxisData,
            axisLabel: {
              formatter: function(value) {
                const date = new Date(value);
                return date.toLocaleString('zh-CN', {
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit'
                });
              }
            }
          },
          yAxis: {
            ...baseOptions.yAxis,
            min: 0,
            max: 100,
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#70', '#30'], // 超买超卖线
                type: 'dashed'
              }
            }
          },
          series: [{
            type: 'line',
            name: 'RSI',
            data: indicators.rsi.map((item, index) => [xAxisData[index], item]),
            smooth: true,
            lineStyle: { color: '#ff7875' }
          }]
        }
        chartInstances.value.rsi.setOption(rsiOptions, true)
        console.log('RSI图表数据更新完成')
      }
      
      // 更新MACD图表
      if (chartInstances.value.macd && indicators.macd) {
        const macdSeries = []
        
        if (indicators.macd.dif) {
          macdSeries.push({
            type: 'line',
            name: 'DIF',
            data: indicators.macd.dif,
            smooth: true,
            lineStyle: { color: '#1890ff' }
          })
        }
        
        if (indicators.macd.dea) {
          macdSeries.push({
            type: 'line',
              name: 'DEA',
            data: indicators.macd.dea,
            smooth: true,
            lineStyle: { color: '#ff7875' }
          })
        }
        
        if (indicators.macd.histogram) {
          macdSeries.push({
            type: 'bar',
            name: 'MACD',
            data: indicators.macd.histogram,
            itemStyle: {
              color: function(params) {
                return params.value >= 0 ? '#00b894' : '#d63031'
              }
            }
          })
        }
        
        if (macdSeries.length > 0) {
          // 提取时间轴数据
          const xAxisData = data && data.length > 0 ? data.map(item => {
            const timestamp = new Date(parseInt(item[0]))
            return timestamp.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
          }) : []
          
          const baseOptions = getBaseChartOptions(currentTheme.value === 'dark')
          const macdOptions = {
            ...baseOptions,
            xAxis: {
              ...baseOptions.xAxis,
              data: xAxisData
            },
            series: macdSeries
          }
          chartInstances.value.macd.setOption(macdOptions, true)
          console.log('MACD图表数据更新完成')
        }
      }
      
      // 更新KDJ图表
      if (chartInstances.value.kdj && indicators.kdj) {
        const kdjSeries = []
        
        if (indicators.kdj.k) {
          kdjSeries.push({
            type: 'line',
            name: 'K',
            data: indicators.kdj.k,
            smooth: true,
            lineStyle: { color: '#1890ff' }
          })
        }
        
        if (indicators.kdj.d) {
          kdjSeries.push({
            type: 'line',
            name: 'D',
            data: indicators.kdj.d,
            smooth: true,
            lineStyle: { color: '#ff7875' }
          })
        }
        
        if (indicators.kdj.j) {
          kdjSeries.push({
            type: 'line',
            name: 'J',
            data: indicators.kdj.j,
            smooth: true,
            lineStyle: { color: '#52c41a' }
          })
        }
        
        if (kdjSeries.length > 0) {
          // 提取时间轴数据
          const xAxisData = data && data.length > 0 ? data.map(item => {
            const timestamp = new Date(parseInt(item[0]))
            return timestamp.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
          }) : []
          
          const baseOptions = getBaseChartOptions(currentTheme.value === 'dark')
          const kdjOptions = {
            ...baseOptions,
            xAxis: {
              ...baseOptions.xAxis,
              data: xAxisData
            },
            yAxis: {
              ...baseOptions.yAxis,
              min: 0,
              max: 100
            },
            series: kdjSeries
          }
          chartInstances.value.kdj.setOption(kdjOptions, true)
          console.log('KDJ图表数据更新完成')
        }
      }
      
      // 更新布林带图表
      if (chartInstances.value.bollingerBands && indicators.bollingerBands) {
        const bbSeries = []
        
        if (indicators.bollingerBands.upper) {
          bbSeries.push({
            type: 'line',
            name: '上轨',
            data: indicators.bollingerBands.upper,
            smooth: true,
            lineStyle: { color: '#ff7875' }
          })
        }
        
        if (indicators.bollingerBands.middle) {
          bbSeries.push({
            type: 'line',
            name: '中轨',
            data: indicators.bollingerBands.middle,
            smooth: true,
            lineStyle: { color: '#1890ff' }
          })
        }
        
        if (indicators.bollingerBands.lower) {
          bbSeries.push({
            type: 'line',
            name: '下轨',
            data: indicators.bollingerBands.lower,
            smooth: true,
            lineStyle: { color: '#52c41a' }
          })
        }
        
        if (bbSeries.length > 0) {
          // 提取时间轴数据
          const xAxisData = data && data.length > 0 ? data.map(item => {
            const timestamp = new Date(parseInt(item[0]))
            return timestamp.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
          }) : []
          
          const baseOptions = getBaseChartOptions(currentTheme.value === 'dark')
          const bbOptions = {
            ...baseOptions,
            xAxis: {
              ...baseOptions.xAxis,
              data: xAxisData
            },
            series: bbSeries
          }
          chartInstances.value.bollingerBands.setOption(bbOptions, true)
          console.log('布林带图表数据更新完成')
        }
      }
      
      console.log('所有图表数据更新完成')
      
    } catch (error) {
      console.error('更新图表数据失败:', error)
      throw error
    }
  }
  
  /**
   * 销毁所有图表
   */
  const destroyAllCharts = () => {
    try {
      Object.values(chartInstances.value).forEach(chart => {
        if (chart && typeof chart.dispose === 'function') {
          chart.dispose()
        }
      })
      
      // 开发环境下注销全局注册的图表实例
      if (process.env.NODE_ENV === 'development' && window.unregisterChartInstance) {
        const chartNames = ['advanced_kline', 'advanced_volume', 'advanced_rsi', 'advanced_macd', 'advanced_kdj', 'advanced_bollinger']
        chartNames.forEach(name => {
          window.unregisterChartInstance(name)
        })
      }
      
      chartInstances.value = {}
      isInitialized.value = false
      console.log('所有图表已销毁')
    } catch (error) {
      console.error('销毁图表失败:', error)
    }
  }

  return {
    chartInstances,
    isInitialized,
    currentTheme,
    getBaseChartOptions,
    generateKlineConfig,
    initializeAllCharts,
    updateChart,
    updateAllCharts,
    destroyAllCharts
  }
}