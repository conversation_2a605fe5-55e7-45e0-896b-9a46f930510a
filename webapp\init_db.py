from pathlib import Path
from sqlalchemy import create_engine
from database import Base, engine as db_engine

# 获取项目根目录
BASE_DIR = Path(__file__).parent.parent

# 数据库文件的绝对路径
DB_PATH = BASE_DIR / "db.sqlite3"

# 使用database.py中的引擎
engine = db_engine


def init_database():
    """初始化数据库，创建所有定义的表"""
    print("正在初始化数据库...")
    print(f"数据库路径: {DB_PATH}")
    Base.metadata.create_all(bind=engine)
    print("数据库初始化完成！")


if __name__ == "__main__":
    init_database()
