/**
 * 内存管理工具
 * 用于管理图表实例、事件监听器和缓存数据，防止内存泄漏
 */

import chartConfig from '@/config/chartConfig'
import performanceMonitor from './performanceMonitor'

/**
 * 内存管理器
 */
class MemoryManager {
  constructor() {
    this.chartInstances = new Map() // 图表实例注册表
    this.eventListeners = new Map() // 事件监听器注册表
    this.cacheData = new Map() // 缓存数据
    this.timers = new Map() // 定时器注册表
    this.observers = new Map() // 观察者注册表
    this.weakRefs = new Set() // 弱引用集合
    
    this.cleanupTimer = null
    this.isRunning = false
    
    // 绑定方法
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this)
    this.handleBeforeUnload = this.handleBeforeUnload.bind(this)
  }

  /**
   * 启动内存管理
   */
  start() {
    if (this.isRunning) return
    
    this.isRunning = true
    
    // 启动定期清理
    this.startPeriodicCleanup()
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange)
    
    // 监听页面卸载
    window.addEventListener('beforeunload', this.handleBeforeUnload)
    
    console.log('内存管理器已启动')
  }

  /**
   * 停止内存管理
   */
  stop() {
    if (!this.isRunning) return
    
    this.isRunning = false
    
    // 停止定期清理
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    
    // 移除事件监听
    document.removeEventListener('visibilitychange', this.handleVisibilityChange)
    window.removeEventListener('beforeunload', this.handleBeforeUnload)
    
    // 执行最终清理
    this.cleanup()
    
    console.log('内存管理器已停止')
  }

  /**
   * 注册图表实例
   * @param {string} id - 图表ID
   * @param {Object} chartInstance - 图表实例
   * @param {Object} options - 选项
   */
  registerChart(id, chartInstance, options = {}) {
    if (!id || !chartInstance) {
      console.warn('注册图表实例时缺少必要参数')
      return
    }
    
    const registration = {
      instance: chartInstance,
      createdAt: Date.now(),
      lastAccessed: Date.now(),
      accessCount: 0,
      options: {
        autoCleanup: true,
        maxIdleTime: 300000, // 5分钟
        ...options
      }
    }
    
    this.chartInstances.set(id, registration)
    
    // 如果启用弱引用
    if (chartConfig.get('memory.enableWeakReferences')) {
      this.weakRefs.add(new WeakRef(chartInstance))
    }
    
    console.log(`图表实例已注册: ${id}`)
  }

  /**
   * 注销图表实例
   * @param {string} id - 图表ID
   */
  unregisterChart(id) {
    const registration = this.chartInstances.get(id)
    if (!registration) return
    
    try {
      // 清理图表实例
      if (registration.instance && typeof registration.instance.dispose === 'function') {
        registration.instance.dispose()
      }
      
      this.chartInstances.delete(id)
      console.log(`图表实例已注销: ${id}`)
      
    } catch (error) {
      console.error(`注销图表实例失败 [${id}]:`, error)
      performanceMonitor.recordError(error, 'MemoryManager.unregisterChart')
    }
  }

  /**
   * 访问图表实例
   * @param {string} id - 图表ID
   */
  accessChart(id) {
    const registration = this.chartInstances.get(id)
    if (registration) {
      registration.lastAccessed = Date.now()
      registration.accessCount++
      return registration.instance
    }
    return null
  }

  /**
   * 注册事件监听器
   * @param {string} id - 监听器ID
   * @param {EventTarget} target - 事件目标
   * @param {string} event - 事件类型
   * @param {Function} handler - 事件处理器
   * @param {Object} options - 选项
   */
  registerEventListener(id, target, event, handler, options = {}) {
    if (!id || !target || !event || !handler) {
      console.warn('注册事件监听器时缺少必要参数')
      return
    }
    
    const registration = {
      target,
      event,
      handler,
      options,
      createdAt: Date.now()
    }
    
    // 添加事件监听器
    target.addEventListener(event, handler, options)
    
    this.eventListeners.set(id, registration)
    console.log(`事件监听器已注册: ${id} (${event})`)
  }

  /**
   * 注销事件监听器
   * @param {string} id - 监听器ID
   */
  unregisterEventListener(id) {
    const registration = this.eventListeners.get(id)
    if (!registration) return
    
    try {
      registration.target.removeEventListener(
        registration.event,
        registration.handler,
        registration.options
      )
      
      this.eventListeners.delete(id)
      console.log(`事件监听器已注销: ${id}`)
      
    } catch (error) {
      console.error(`注销事件监听器失败 [${id}]:`, error)
      performanceMonitor.recordError(error, 'MemoryManager.unregisterEventListener')
    }
  }

  /**
   * 注册定时器
   * @param {string} id - 定时器ID
   * @param {number} timerId - 定时器ID
   * @param {string} type - 定时器类型 ('timeout' | 'interval')
   */
  registerTimer(id, timerId, type = 'timeout') {
    this.timers.set(id, {
      timerId,
      type,
      createdAt: Date.now()
    })
    
    console.log(`定时器已注册: ${id} (${type})`)
  }

  /**
   * 注销定时器
   * @param {string} id - 定时器ID
   */
  unregisterTimer(id) {
    const timer = this.timers.get(id)
    if (!timer) return
    
    try {
      if (timer.type === 'timeout') {
        clearTimeout(timer.timerId)
      } else if (timer.type === 'interval') {
        clearInterval(timer.timerId)
      }
      
      this.timers.delete(id)
      console.log(`定时器已注销: ${id}`)
      
    } catch (error) {
      console.error(`注销定时器失败 [${id}]:`, error)
    }
  }

  /**
   * 注册观察者
   * @param {string} id - 观察者ID
   * @param {Object} observer - 观察者实例
   */
  registerObserver(id, observer) {
    if (!observer || typeof observer.disconnect !== 'function') {
      console.warn('无效的观察者实例')
      return
    }
    
    this.observers.set(id, {
      observer,
      createdAt: Date.now()
    })
    
    console.log(`观察者已注册: ${id}`)
  }

  /**
   * 注销观察者
   * @param {string} id - 观察者ID
   */
  unregisterObserver(id) {
    const registration = this.observers.get(id)
    if (!registration) return
    
    try {
      registration.observer.disconnect()
      this.observers.delete(id)
      console.log(`观察者已注销: ${id}`)
      
    } catch (error) {
      console.error(`注销观察者失败 [${id}]:`, error)
    }
  }

  /**
   * 设置缓存数据
   * @param {string} key - 缓存键
   * @param {*} data - 缓存数据
   * @param {number} ttl - 生存时间（毫秒）
   */
  setCache(key, data, ttl = null) {
    const maxEntries = chartConfig.get('memory.maxCacheEntries', 1000)
    
    // 检查缓存大小限制
    if (this.cacheData.size >= maxEntries) {
      this.cleanupExpiredCache()
      
      // 如果仍然超过限制，删除最旧的条目
      if (this.cacheData.size >= maxEntries) {
        const oldestKey = this.cacheData.keys().next().value
        this.cacheData.delete(oldestKey)
      }
    }
    
    const cacheEntry = {
      data,
      createdAt: Date.now(),
      lastAccessed: Date.now(),
      accessCount: 0,
      ttl: ttl ? Date.now() + ttl : null
    }
    
    this.cacheData.set(key, cacheEntry)
  }

  /**
   * 获取缓存数据
   * @param {string} key - 缓存键
   */
  getCache(key) {
    const entry = this.cacheData.get(key)
    if (!entry) return null
    
    // 检查是否过期
    if (entry.ttl && Date.now() > entry.ttl) {
      this.cacheData.delete(key)
      return null
    }
    
    // 更新访问信息
    entry.lastAccessed = Date.now()
    entry.accessCount++
    
    return entry.data
  }

  /**
   * 删除缓存数据
   * @param {string} key - 缓存键
   */
  deleteCache(key) {
    return this.cacheData.delete(key)
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    const now = Date.now()
    const retentionTime = chartConfig.get('memory.dataRetentionTime', 3600000)
    
    for (const [key, entry] of this.cacheData.entries()) {
      // 检查TTL过期
      if (entry.ttl && now > entry.ttl) {
        this.cacheData.delete(key)
        continue
      }
      
      // 检查数据保留时间
      if (now - entry.createdAt > retentionTime) {
        this.cacheData.delete(key)
        continue
      }
    }
  }

  /**
   * 清理空闲图表实例
   */
  cleanupIdleCharts() {
    const now = Date.now()
    
    for (const [id, registration] of this.chartInstances.entries()) {
      if (!registration.options.autoCleanup) continue
      
      const idleTime = now - registration.lastAccessed
      if (idleTime > registration.options.maxIdleTime) {
        console.log(`清理空闲图表实例: ${id} (空闲时间: ${Math.round(idleTime / 1000)}秒)`)
        this.unregisterChart(id)
      }
    }
  }

  /**
   * 清理弱引用
   */
  cleanupWeakReferences() {
    const validRefs = new Set()
    
    for (const ref of this.weakRefs) {
      if (ref.deref()) {
        validRefs.add(ref)
      }
    }
    
    this.weakRefs = validRefs
  }

  /**
   * 执行完整清理
   */
  cleanup() {
    console.log('开始内存清理...')
    
    const startTime = performance.now()
    
    // 清理过期缓存
    this.cleanupExpiredCache()
    
    // 清理空闲图表
    if (chartConfig.get('memory.enableAutoCleanup')) {
      this.cleanupIdleCharts()
    }
    
    // 清理弱引用
    if (chartConfig.get('memory.enableWeakReferences')) {
      this.cleanupWeakReferences()
    }
    
    const endTime = performance.now()
    console.log(`内存清理完成，耗时: ${Math.round(endTime - startTime)}ms`)
    
    // 记录清理性能
    performanceMonitor.recordChartRenderTime('MemoryCleanup', endTime - startTime)
  }

  /**
   * 强制清理所有资源
   */
  forceCleanup() {
    console.log('开始强制内存清理...')
    
    // 清理所有图表实例
    for (const id of this.chartInstances.keys()) {
      this.unregisterChart(id)
    }
    
    // 清理所有事件监听器
    for (const id of this.eventListeners.keys()) {
      this.unregisterEventListener(id)
    }
    
    // 清理所有定时器
    for (const id of this.timers.keys()) {
      this.unregisterTimer(id)
    }
    
    // 清理所有观察者
    for (const id of this.observers.keys()) {
      this.unregisterObserver(id)
    }
    
    // 清空缓存
    this.cacheData.clear()
    
    // 清空弱引用
    this.weakRefs.clear()
    
    console.log('强制内存清理完成')
  }

  /**
   * 启动定期清理
   */
  startPeriodicCleanup() {
    const interval = chartConfig.get('memory.cleanupInterval', 300000) // 5分钟
    
    this.cleanupTimer = setInterval(() => {
      if (this.isRunning) {
        this.cleanup()
      }
    }, interval)
  }

  /**
   * 处理页面可见性变化
   */
  handleVisibilityChange() {
    if (document.hidden) {
      // 页面隐藏时执行清理
      console.log('页面隐藏，执行内存清理')
      this.cleanup()
    }
  }

  /**
   * 处理页面卸载
   */
  handleBeforeUnload() {
    console.log('页面即将卸载，执行强制清理')
    this.forceCleanup()
  }

  /**
   * 获取内存使用统计
   */
  getMemoryStats() {
    const stats = {
      charts: this.chartInstances.size,
      eventListeners: this.eventListeners.size,
      timers: this.timers.size,
      observers: this.observers.size,
      cacheEntries: this.cacheData.size,
      weakReferences: this.weakRefs.size,
      isRunning: this.isRunning
    }
    
    // 添加浏览器内存信息
    if (performance.memory) {
      stats.browserMemory = {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
      }
    }
    
    return stats
  }

  /**
   * 获取详细的资源信息
   */
  getResourceDetails() {
    const now = Date.now()
    
    return {
      charts: Array.from(this.chartInstances.entries()).map(([id, reg]) => ({
        id,
        createdAt: reg.createdAt,
        lastAccessed: reg.lastAccessed,
        accessCount: reg.accessCount,
        idleTime: now - reg.lastAccessed,
        options: reg.options
      })),
      
      eventListeners: Array.from(this.eventListeners.entries()).map(([id, reg]) => ({
        id,
        event: reg.event,
        createdAt: reg.createdAt,
        age: now - reg.createdAt
      })),
      
      cache: Array.from(this.cacheData.entries()).map(([key, entry]) => ({
        key,
        size: JSON.stringify(entry.data).length,
        createdAt: entry.createdAt,
        lastAccessed: entry.lastAccessed,
        accessCount: entry.accessCount,
        ttl: entry.ttl,
        age: now - entry.createdAt
      }))
    }
  }
}

// 创建全局内存管理器实例
const memoryManager = new MemoryManager()

// 自动启动内存管理
if (chartConfig.get('memory.enableAutoCleanup')) {
  memoryManager.start()
}

// 导出内存管理器
export default memoryManager

/**
 * 内存管理装饰器
 * 自动管理组件的内存资源
 */
export function withMemoryManagement(componentId) {
  return {
    mounted() {
      this._memoryId = componentId || `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      console.log(`组件内存管理已启动: ${this._memoryId}`)
    },
    
    beforeUnmount() {
      if (this._memoryId) {
        // 清理组件相关的所有资源
        const prefix = this._memoryId
        
        // 清理图表实例
        for (const id of memoryManager.chartInstances.keys()) {
          if (id.startsWith(prefix)) {
            memoryManager.unregisterChart(id)
          }
        }
        
        // 清理事件监听器
        for (const id of memoryManager.eventListeners.keys()) {
          if (id.startsWith(prefix)) {
            memoryManager.unregisterEventListener(id)
          }
        }
        
        // 清理定时器
        for (const id of memoryManager.timers.keys()) {
          if (id.startsWith(prefix)) {
            memoryManager.unregisterTimer(id)
          }
        }
        
        console.log(`组件内存管理已清理: ${this._memoryId}`)
      }
    }
  }
}

/**
 * 自动清理的定时器创建函数
 * @param {Function} callback - 回调函数
 * @param {number} delay - 延迟时间
 * @param {string} id - 定时器ID
 */
export function createManagedTimeout(callback, delay, id = null) {
  const timerId = setTimeout(callback, delay)
  const managedId = id || `timeout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  memoryManager.registerTimer(managedId, timerId, 'timeout')
  
  return {
    id: managedId,
    timerId,
    clear: () => memoryManager.unregisterTimer(managedId)
  }
}

/**
 * 自动清理的定时器创建函数
 * @param {Function} callback - 回调函数
 * @param {number} interval - 间隔时间
 * @param {string} id - 定时器ID
 */
export function createManagedInterval(callback, interval, id = null) {
  const timerId = setInterval(callback, interval)
  const managedId = id || `interval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  memoryManager.registerTimer(managedId, timerId, 'interval')
  
  return {
    id: managedId,
    timerId,
    clear: () => memoryManager.unregisterTimer(managedId)
  }
}