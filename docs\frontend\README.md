# 前端文档中心

欢迎来到 OKX 量化交易系统前端文档中心。这里包含了前端开发、部署和使用的完整文档。

## 📁 文档结构

### 🔧 技术文档 (`technical/`)
- [项目状态](./technical/PROJECT-STATUS.md) - 项目完成度和技术栈状态
- [变更日志](./technical/CHANGELOG.md) - 版本更新记录
- [图表修复说明](./technical/CHART_FIX_README.md) - K线图错误修复方案
- [性能优化工具](./technical/OPTIMIZATION_TOOLS_USAGE.md) - 性能监控和优化工具使用
- [性能故障排除](./technical/PERFORMANCE_TROUBLESHOOTING.md) - 性能问题诊断和解决
- [更新频率优化](./technical/UPDATE_FREQUENCY_OPTIMIZATION.md) - 数据更新频率优化方案

### 🌐 API 文档 (`api/`)
- [API 接口文档](./api/API-DOCUMENTATION.md) - 前端与后端 API 接口规范
- [K线图 API 参考](./api/KLineChart-API-Reference.md) - K线图后端 API 接口详细说明

### 🚀 部署指南 (`deployment/`)
- [部署指南](./deployment/DEPLOYMENT-GUIDE.md) - 完整的部署配置和环境设置

### 📖 使用指南 (`guides/`)
- [高级功能](./guides/Advanced-Features.md) - 交易仪表板高级功能说明
- [增强指标](./guides/Enhanced-Indicators.md) - 技术指标面板使用指南
- [模块化图表](./guides/Modular-Charts.md) - 图表系统模块化架构
- [K线图集成指南](./guides/KLineChart-Integration-Guide.md) - KLineChart 项目集成
- [K线图使用指南](./guides/KLineChart-Usage-Guide.md) - K线图组件使用方法
- [增强指标面板](./guides/enhanced-indicator-panel.md) - 指标面板功能详解

## 🚀 快速开始

### 开发环境启动
```bash
# 进入前端目录
cd web-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
# 或使用批处理文件
start-vite.bat
```

### 生产环境部署
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 📋 文档导航

| 类别 | 文档 | 描述 |
|------|------|------|
| 🏗️ **架构** | [模块化图表](./guides/Modular-Charts.md) | 图表系统架构设计 |
| 📊 **图表** | [K线图集成指南](./guides/KLineChart-Integration-Guide.md) | K线图组件集成 |
| 📈 **指标** | [增强指标](./guides/Enhanced-Indicators.md) | 技术指标使用 |
| ⚡ **性能** | [性能优化工具](./technical/OPTIMIZATION_TOOLS_USAGE.md) | 性能监控优化 |
| 🔧 **故障** | [性能故障排除](./technical/PERFORMANCE_TROUBLESHOOTING.md) | 问题诊断解决 |
| 🌐 **API** | [API 接口文档](./api/API-DOCUMENTATION.md) | 接口规范说明 |
| 🚀 **部署** | [部署指南](./deployment/DEPLOYMENT-GUIDE.md) | 环境配置部署 |

## 🔗 相关链接

- [项目主文档](../../README.md)
- [后端文档](../README.md)
- [API 文档](../api/)
- [部署指南](../deployment/)

---

> 💡 **提示**: 如果您是新手开发者，建议从 [K线图集成指南](./guides/KLineChart-Integration-Guide.md) 开始阅读。
> 
> 🔧 **维护**: 文档持续更新中，如有问题请查看 [项目状态](./technical/PROJECT-STATUS.md)。