/**
 * 图表错误处理 Composable
 * 提供统一的图表错误处理、恢复和降级机制
 */
import { ref } from 'vue'
import { message, notification } from 'ant-design-vue'

export const useChartErrorHandler = () => {
  const errorCount = ref(0)
  const lastErrorTime = ref(0)
  const maxRetries = 3
  const retryInterval = 5000 // 5秒

  /**
   * 处理图表错误
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   * @param {Function} retryCallback - 重试回调函数
   * @returns {string} 错误类型
   */
  const handleChartError = (error, context = 'unknown', retryCallback = null) => {
    const now = Date.now()
    const errorMessage = error?.message || error?.toString() || '未知错误'
    
    console.error(`图表错误 [${context}]:`, error)
    
    // 判断错误类型
    let errorType = 'UNKNOWN_ERROR'
    if (errorMessage.includes('dataSample') || errorMessage.includes('Cannot read properties of undefined')) {
      errorType = 'DATA_ERROR'
    } else if (errorMessage.includes('init') || errorMessage.includes('创建失败')) {
      errorType = 'INIT_ERROR'
    } else if (errorMessage.includes('applyNewData') || errorMessage.includes('应用数据')) {
      errorType = 'DATA_APPLY_ERROR'
    } else if (errorMessage.includes('dispose') || errorMessage.includes('销毁')) {
      errorType = 'CLEANUP_ERROR'
    }
    
    // 错误频率控制
    if (now - lastErrorTime.value < retryInterval) {
      errorCount.value++
    } else {
      errorCount.value = 1
    }
    lastErrorTime.value = now
    
    // 显示用户友好的错误信息
    const userMessage = getUserFriendlyMessage(errorType, errorCount.value)
    if (errorCount.value <= 2) {
      message.error(userMessage)
    } else {
      notification.error({
        message: '图表错误',
        description: `${userMessage}\n错误已发生 ${errorCount.value} 次，请检查网络连接或刷新页面。`,
        duration: 10
      })
    }
    
    // 自动重试逻辑
    if (retryCallback && errorCount.value <= maxRetries) {
      console.log(`将在 ${retryInterval / 1000} 秒后自动重试 (${errorCount.value}/${maxRetries})`)
      setTimeout(() => {
        try {
          retryCallback()
        } catch (retryError) {
          console.error('重试失败:', retryError)
        }
      }, retryInterval)
    }
    
    return errorType
  }
  
  /**
   * 获取用户友好的错误信息
   * @param {string} errorType - 错误类型
   * @param {number} count - 错误次数
   * @returns {string} 用户友好的错误信息
   */
  const getUserFriendlyMessage = (errorType, count) => {
    const messages = {
      DATA_ERROR: '图表数据处理异常',
      INIT_ERROR: '图表初始化失败',
      DATA_APPLY_ERROR: '图表数据更新失败',
      CLEANUP_ERROR: '图表清理时出现问题',
      UNKNOWN_ERROR: '图表出现未知错误'
    }
    
    const baseMessage = messages[errorType] || messages.UNKNOWN_ERROR
    
    if (count === 1) {
      return `${baseMessage}，正在自动重试...`
    } else if (count <= maxRetries) {
      return `${baseMessage}，第 ${count} 次重试中...`
    } else {
      return `${baseMessage}，已达到最大重试次数`
    }
  }
  
  /**
   * 重置错误计数
   */
  const resetErrorCount = () => {
    errorCount.value = 0
    lastErrorTime.value = 0
  }
  
  /**
   * 检查是否应该停止重试
   * @returns {boolean} 是否应该停止重试
   */
  const shouldStopRetry = () => {
    return errorCount.value > maxRetries
  }
  
  /**
   * 创建错误边界处理器
   * @param {Function} fallbackHandler - 降级处理函数
   * @returns {Function} 错误边界处理器
   */
  const createErrorBoundary = (fallbackHandler) => {
    return async (operation, context = 'operation') => {
      try {
        return await operation()
      } catch (error) {
        const errorType = handleChartError(error, context)
        
        // 如果达到最大重试次数，执行降级处理
        if (shouldStopRetry() && fallbackHandler) {
          console.log('执行降级处理')
          try {
            return await fallbackHandler()
          } catch (fallbackError) {
            console.error('降级处理也失败了:', fallbackError)
            throw fallbackError
          }
        }
        
        throw error
      }
    }
  }
  
  return {
    errorCount,
    handleChartError,
    resetErrorCount,
    shouldStopRetry,
    createErrorBoundary
  }
}