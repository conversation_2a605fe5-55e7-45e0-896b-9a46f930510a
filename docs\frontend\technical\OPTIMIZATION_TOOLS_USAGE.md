# 优化工具使用指南

本文档介绍如何使用我们创建的性能监控、配置管理、错误边界和内存管理工具来提升应用的性能和稳定性。

## 1. 性能监控 (Performance Monitor)

### 基本使用

```javascript
import performanceMonitor from '@/utils/performanceMonitor'

// 启动监控
performanceMonitor.startMonitoring()

// 记录自定义指标
performanceMonitor.recordMetric('chart_render_time', 150)

// 记录错误
performanceMonitor.recordError('chart_error', new Error('图表渲染失败'))

// 获取性能报告
const report = performanceMonitor.getReport()
console.log('性能报告:', report)

// 停止监控
performanceMonitor.stopMonitoring()
```

### 装饰器使用

```javascript
import { wrapWithPerformanceMonitoring } from '@/utils/performanceMonitor'

// 包装函数进行性能监控
const optimizedFunction = wrapWithPerformanceMonitoring('my_function', () => {
  // 你的代码逻辑
  return someExpensiveOperation()
})
```

// 包装API调用
const monitoredApiCall = withApiMonitoring(async () => {
  const response = await fetch('/api/data')
  return response.json()
})
```

## 2. 配置管理 (Chart Config)

### 基本配置获取

```javascript
import chartConfig, { getUpdateConfig, getPerformanceConfig } from '@/config/chartConfig'

// 获取更新配置
const updateConfig = getUpdateConfig()
console.log('K线更新间隔:', updateConfig.klineInterval)

// 获取性能配置
const perfConfig = getPerformanceConfig()
console.log('是否启用监控:', perfConfig.enableMonitoring)

// 获取特定配置值
const theme = chartConfig.get('display.theme')
const autoUpdate = chartConfig.get('update.autoUpdate')
```

### 动态配置更新

```javascript
// 设置单个配置
chartConfig.set('update.klineInterval', 60000) // 设置为60秒

// 批量设置配置
chartConfig.setBatch({
  'display.theme': 'dark',
  'update.autoUpdate': true,
  'performance.enableMonitoring': true
})

// 监听配置变化
const unsubscribe = chartConfig.addListener((path, value) => {
  console.log(`配置 ${path} 更新为:`, value)
})

// 取消监听
unsubscribe()
```

### 环境特定配置

```javascript
// 开发环境配置
if (process.env.NODE_ENV === 'development') {
  chartConfig.setBatch({
    'performance.enableMonitoring': true,
    'performance.enableReporting': true,
    'update.klineInterval': 10000 // 开发环境更频繁更新
  })
}

// 生产环境配置
if (process.env.NODE_ENV === 'production') {
  chartConfig.setBatch({
    'performance.enableMonitoring': false,
    'update.klineInterval': 30000 // 生产环境较低频率
  })
}
```

## 3. 错误边界 (Error Boundary)

### 在组件中使用

```vue
<template>
  <ErrorBoundary>
    <YourComponent />
  </ErrorBoundary>
</template>

<script setup>
import ErrorBoundary from '@/components/ErrorBoundary.vue'
import YourComponent from './YourComponent.vue'
</script>
```

### 自定义错误处理

```vue
<template>
  <ErrorBoundary 
    :enable-reporting="true"
    :max-retries="3"
    @error="handleError"
    @retry="handleRetry"
  >
    <ChartComponent />
  </ErrorBoundary>
</template>

<script setup>
const handleError = (error, errorInfo) => {
  console.error('组件错误:', error)
  // 发送错误报告到监控服务
}

const handleRetry = () => {
  console.log('用户点击重试')
  // 执行重试逻辑
}
</script>
```

## 4. 内存管理 (Memory Manager)

### 基本使用

```javascript
import memoryManager, { withMemoryManagement, createManagedInterval } from '@/utils/memoryManager'

// 注册图表实例
const chartId = 'my_chart_' + Date.now()
memoryManager.registerChart(chartId, {
  type: 'KLineChart',
  dispose: () => {
    chart.dispose()
  }
})

// 注册事件监听器
const listener = (event) => console.log(event)
element.addEventListener('click', listener)
memoryManager.registerListener(element, 'click', listener)

// 使用托管定时器
const timerId = createManagedInterval(() => {
  console.log('定时任务执行')
}, 5000)

// 清理所有资源
memoryManager.cleanup()
```

### 装饰器使用

```javascript
// 自动内存管理的组件
const ManagedComponent = withMemoryManagement({
  setup() {
    // 组件逻辑
    return {
      // 组件状态
    }
  },
  cleanup() {
    // 自定义清理逻辑
    console.log('组件清理完成')
  }
})
```

### 在Vue组件中使用

```vue
<script setup>
import { onMounted, onUnmounted } from 'vue'
import memoryManager, { createManagedInterval } from '@/utils/memoryManager'

let updateTimer = null

onMounted(() => {
  // 注册组件
  const componentId = `component_${Date.now()}`
  memoryManager.registerChart(componentId, {
    type: 'MyComponent',
    dispose: () => {
      // 组件特定的清理逻辑
    }
  })
  
  // 使用托管定时器
  updateTimer = createManagedInterval(() => {
    // 定时更新逻辑
  }, 10000)
})

onUnmounted(() => {
  // 清理资源
  if (updateTimer) {
    memoryManager.unregisterTimer(updateTimer)
  }
  memoryManager.cleanup()
})
</script>
```

## 5. 集成示例

### 完整的图表组件优化

```vue
<template>
  <ErrorBoundary>
    <div ref="chartContainer" class="chart-container">
      <!-- 图表内容 -->
    </div>
  </ErrorBoundary>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import ErrorBoundary from '@/components/ErrorBoundary.vue'
import performanceMonitor, { withPerformanceMonitoring } from '@/utils/performanceMonitor'
import memoryManager, { createManagedInterval } from '@/utils/memoryManager'
import chartConfig, { getUpdateConfig, getPerformanceConfig } from '@/config/chartConfig'

const chartContainer = ref(null)
let chart = null
let updateTimer = null

// 性能监控包装的图表初始化
const initChart = wrapWithPerformanceMonitoring('chart_init', async () => {
  // 图表初始化逻辑
  chart = createChart(chartContainer.value)
  
  // 注册到内存管理器
  memoryManager.registerChart('main_chart', {
    type: 'KLineChart',
    dispose: () => chart.dispose()
  })
})

// 性能监控包装的数据更新
const updateData = wrapWithPerformanceMonitoring('chart_update', async () => {
  // 数据更新逻辑
  const data = await fetchChartData()
  chart.updateData(data)
})

onMounted(async () => {
  // 启动性能监控
  if (getPerformanceConfig().enableMonitoring) {
    performanceMonitor.startMonitoring()
  }
  
  // 初始化图表
  await initChart()
  
  // 设置定时更新
  const updateConfig = getUpdateConfig()
  updateTimer = createManagedInterval(updateData, updateConfig.klineInterval)
  
  // 监听配置变化
  const configUnsubscribe = chartConfig.addListener((path, value) => {
    if (path === 'update.klineInterval') {
      // 重新设置定时器
      if (updateTimer) {
        memoryManager.unregisterTimer(updateTimer)
      }
      updateTimer = createManagedInterval(updateData, value)
    }
  })
})

onUnmounted(() => {
  // 停止性能监控
  if (getPerformanceConfig().enableMonitoring) {
    performanceMonitor.stopMonitoring()
    
    // 输出性能报告
    if (getPerformanceConfig().enableReporting) {
      const report = performanceMonitor.getReport()
      console.log('组件性能报告:', report)
    }
  }
  
  // 清理内存
  memoryManager.cleanup()
})
</script>
```

## 6. 最佳实践

### 性能监控最佳实践

1. **选择性监控**: 只在关键路径上启用监控，避免过度监控影响性能
2. **合理的指标**: 监控有意义的指标，如渲染时间、API响应时间、内存使用等
3. **错误追踪**: 记录和分析错误模式，及时发现问题
4. **定期报告**: 定期生成和分析性能报告，持续优化

### 配置管理最佳实践

1. **环境分离**: 为不同环境设置不同的配置
2. **动态调整**: 支持运行时配置调整，无需重启应用
3. **配置验证**: 验证配置值的有效性
4. **默认值**: 为所有配置提供合理的默认值

### 内存管理最佳实践

1. **及时清理**: 在组件卸载时及时清理资源
2. **统一管理**: 使用内存管理器统一管理所有资源
3. **监控内存**: 定期监控内存使用情况
4. **避免循环引用**: 注意避免创建循环引用导致内存泄漏

### 错误边界最佳实践

1. **粒度控制**: 在合适的组件层级设置错误边界
2. **用户友好**: 提供用户友好的错误信息和恢复选项
3. **错误报告**: 自动收集和报告错误信息
4. **降级策略**: 提供合理的降级和恢复策略

## 7. 故障排除

### 常见问题

1. **性能监控不工作**: 检查是否正确启动监控，配置是否正确
2. **配置不生效**: 检查配置路径是否正确，是否有缓存问题
3. **内存泄漏**: 检查是否正确注册和清理资源
4. **错误边界不捕获错误**: 检查错误类型和边界设置

### 调试技巧

1. **启用详细日志**: 在开发环境启用详细的调试日志
2. **使用浏览器工具**: 利用浏览器的性能和内存分析工具
3. **分步测试**: 逐步启用优化功能，确定问题范围
4. **监控指标**: 关注关键性能指标的变化趋势