<template>
  <a-card title="快速交易" :bordered="false">
    <a-form layout="vertical">
      <a-form-item label="交易类型">
        <a-radio-group v-model:value="tradeType">
          <a-radio-button value="market">市价</a-radio-button>
          <a-radio-button value="limit">限价</a-radio-button>
        </a-radio-group>
      </a-form-item>
      <a-form-item v-if="tradeType === 'limit'" label="价格">
        <a-input-number v-model:value="price" style="width: 100%" />
      </a-form-item>
      <a-form-item label="数量">
        <a-input-number v-model:value="amount" style="width: 100%" />
      </a-form-item>
      <a-form-item>
        <a-space>
          <a-button type="primary" @click="onBuy" :loading="loading">买入</a-button>
          <a-button type="danger" @click="onSell" :loading="loading">卖出</a-button>
          <a-button @click="onCloseAll" :loading="loading">全部平仓</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script setup>
import { ref } from 'vue';

const tradeType = ref('market');
const price = ref(0);
const amount = ref(0.01);
const loading = ref(false);

const emit = defineEmits(['buy', 'sell', 'closeAll']);

const onBuy = () => {
  emit('buy', { 
    type: tradeType.value, 
    price: price.value, 
    amount: amount.value 
  });
};

const onSell = () => {
  emit('sell', { 
    type: tradeType.value, 
    price: price.value, 
    amount: amount.value 
  });
};

const onCloseAll = () => {
  emit('closeAll');
};
</script>