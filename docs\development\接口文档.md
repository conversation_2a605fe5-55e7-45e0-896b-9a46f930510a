# OKX量化交易系统-接口文档

## API概览

### 基础信息
- **Base URL**: `http://localhost:8080`
- **认证方式**: JWT <PERSON>ken (Bearer Token)
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API版本**: v1
- **WebSocket地址**: `ws://localhost:8080/ws`

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误码说明
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权/Token无效
- `403`: 禁止访问/权限不足
- `404`: 资源不存在
- `429`: 请求频率限制
- `500`: 服务器内部错误
- `502`: OKX API服务不可用
- `503`: 服务暂时不可用

## 🚀 REST API接口

### 🔐 用户认证接口

#### 1. 用户注册
**POST** `/api/user/register`

**请求参数**:
```json
{
  "username": "string",
  "password": "string",
  "email": "string",
  "phone": "string (可选)"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "user_id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 2. 用户登录
**POST** `/api/user/login`

**请求参数**:
```json
{
  "username": "string",
  "password": "string"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "user_info": {
      "user_id": 1,
      "username": "testuser",
      "email": "<EMAIL>"
    }
  }
}
```

#### 3. 获取用户信息
**GET** `/api/user/profile`

**请求头**:
```
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "user_id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "phone": "+86138****8888",
    "created_at": "2024-01-01T00:00:00Z",
    "last_login": "2024-01-01T12:00:00Z"
  }
}
```

#### 4. 更新用户信息
**PUT** `/api/user/profile`

**请求头**:
```
Authorization: Bearer {access_token}
```

**请求参数**:
```json
{
  "email": "string (可选)",
  "phone": "string (可选)",
  "password": "string (可选)"
}
```

### 📊 市场数据接口

#### 获取实时行情
```http
GET /api/okx/market/ticker?instId=BTC-USDT
```
**参数**:
- `instId`: 产品ID (必填)

**响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "instId": "BTC-USDT",
    "last": "43250.5",
    "lastSz": "0.001",
    "askPx": "43251.0",
    "bidPx": "43250.0",
    "open24h": "42800.0",
    "high24h": "43500.0",
    "low24h": "42500.0",
    "vol24h": "1234.56",
    "ts": "*************"
  }
}
```

#### 获取K线数据
```http
GET /api/okx/market/candles?instId=BTC-USDT&bar=1m&limit=100
```
**参数**:
- `instId`: 产品ID (必填)
- `bar`: 时间周期 (1m, 5m, 15m, 1H, 4H, 1D)
- `limit`: 数据条数 (默认100)

### 💰 账户管理接口

#### 获取账户余额
```http
GET /api/okx/account/balance
Authorization: Bearer <token>
```
**响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      "ccy": "USDT",
      "bal": "10000.5",
      "frozenBal": "100.0",
      "availBal": "9900.5"
    }
  ]
}
```

### 📈 交易接口

#### 创建订单
```http
POST /api/okx/order/create
Authorization: Bearer <token>
Content-Type: application/json
```
**请求体**:
```json
{
  "instId": "BTC-USDT",
  "tdMode": "cash",
  "side": "buy",
  "ordType": "limit",
  "px": "43000",
  "sz": "0.001",
  "clOrdId": "custom_order_id"
}
```

**响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "ordId": "*********",
    "clOrdId": "custom_order_id",
    "sCode": "0",
    "sMsg": "Order placed successfully"
  }
}
```

#### 撤销订单
```http
POST /api/okx/order/cancel
Authorization: Bearer <token>
Content-Type: application/json
```
**请求体**:
```json
{
  "instId": "BTC-USDT",
  "ordId": "*********"
}
```

#### 查询订单列表
```http
GET /api/okx/order/list?instId=BTC-USDT&state=live
Authorization: Bearer <token>
```
**参数**:
- `instId`: 产品ID (可选)
- `state`: 订单状态 (live, filled, canceled)
- `limit`: 返回数量 (默认100)

### ⚙️ 杠杆管理接口

#### 查询杠杆倍数
```http
GET /api/okx/leverage-info?instId=BTC-USDT&mgnMode=isolated
Authorization: Bearer <token>
```
**参数**:
- `instId`: 产品ID (必填)
- `mgnMode`: 保证金模式 (isolated, cross)

#### 设置杠杆倍数
```http
POST /api/okx/set-leverage
Authorization: Bearer <token>
Content-Type: application/json
```
**请求体**:
```json
{
  "lever": "10",
  "mgnMode": "isolated",
  "instId": "BTC-USDT"
}
```

### 🤖 AI分析接口

#### 获取技术指标
```http
GET /api/ai/indicators?instId=BTC-USDT&indicators=RSI,MACD,BOLL
Authorization: Bearer <token>
```
**参数**:
- `instId`: 产品ID (必填)
- `indicators`: 指标列表 (RSI,MACD,BOLL,KDJ)
- `period`: 计算周期 (默认14)

#### 获取交易信号
```http
GET /api/ai/signals?instId=BTC-USDT
Authorization: Bearer <token>
```
**响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "signal": "BUY",
    "strength": 0.75,
    "confidence": 0.85,
    "reasons": ["RSI超卖", "MACD金叉", "突破阻力位"],
    "targetPrice": "44000",
    "stopLoss": "42000"
  }
}
```

## 🔌 WebSocket接口

### 连接信息
- **URL**: `ws://localhost:8000/ws`
- **协议**: WebSocket
- **认证**: 连接时发送token

### 📊 市场数据推送

#### 订阅实时行情
```json
{
  "op": "subscribe",
  "args": [
    {
      "channel": "tickers",
      "instId": "BTC-USDT"
    }
  ]
}
```

#### 行情数据推送
```json
{
  "arg": {
    "channel": "tickers",
    "instId": "BTC-USDT"
  },
  "data": [
    {
      "instId": "BTC-USDT",
      "last": "43250.5",
      "lastSz": "0.001",
      "askPx": "43251.0",
      "bidPx": "43250.0",
      "ts": "*************"
    }
  ]
}
```

### 📈 K线数据推送

#### 订阅K线数据
```json
{
  "op": "subscribe",
  "args": [
    {
      "channel": "candle1m",
      "instId": "BTC-USDT"
    }
  ]
}
```

### 💼 私有数据推送

#### 订阅账户变化
```json
{
  "op": "subscribe",
  "args": [
    {
      "channel": "account"
    }
  ]
}
```

#### 订阅订单变化
```json
{
  "op": "subscribe",
  "args": [
    {
      "channel": "orders",
      "instType": "SPOT"
    }
  ]
}
```

### 资金划转
- 路径：`POST /api/okx/transfer`
- Header：Authorization: Bearer <token>
- 参数：{ ccy, amt, from, to, type, subAcct }
- 返回：{ code, msg, data }
- 权限：需登录

### 查询合约持仓
- 路径：`GET /api/okx/positions?instType=SWAP`
- Header：Authorization: Bearer <token>
- 参数：instType, instId, posId
- 返回：{ code, msg, data }
- 权限：需登录

---

## OKX WebSocket接口

### 行情推送
- 路径：`ws://<host>/ws/okx/market`
- 说明：推送BTC-USDT等行情数据
- 权限：公开

### 账户/持仓/订单推送
- 路径：`ws://<host>/ws/okx/private?token=<token>`
- 连接后发送：
  ```json
  { "channels": [ { "channel": "account" }, { "channel": "positions" }, { "channel": "orders" } ] }
  ```
- 推送：OKX官方原始数据
- 权限：需登录

---

## 统一返回结构
- 所有接口返回：`{ code: "0/1", msg: "", data: ... }`
- code=0为成功，1为失败，msg为提示信息

## 用户认证接口

### 1. 用户注册
**POST** `/api/user/register`

**请求参数**:
```json
{
  "username": "string",
  "password": "string",
  "email": "string",
  "phone": "string (可选)"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "user_id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. 用户登录
**POST** `/api/user/login`

**请求参数**:
```json
{
  "username": "string",
  "password": "string"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "user_info": {
      "user_id": 1,
      "username": "testuser",
      "email": "<EMAIL>"
    }
  }
}
```

### 3. 获取用户信息
**GET** `/api/user/profile`

**请求头**:
```
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "user_id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "phone": "+86138****8888",
    "created_at": "2024-01-01T00:00:00Z",
    "last_login": "2024-01-01T12:00:00Z"
  }
}
```

### 4. 更新用户信息
**PUT** `/api/user/profile`

**请求头**:
```
Authorization: Bearer {access_token}
```

**请求参数**:
```json
{
  "email": "string (可选)",
  "phone": "string (可选)",
  "password": "string (可选)"
}
```

## OKX交易接口

### 1. 获取账户余额
**GET** `/api/okx/account/balance`

**请求头**:
```
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "balances": [
      {
        "currency": "USDT",
        "available": "1000.00",
        "frozen": "0.00",
        "total": "1000.00"
      },
      {
        "currency": "BTC",
        "available": "0.05",
        "frozen": "0.00",
        "total": "0.05"
      }
    ],
    "total_equity": "3500.00",
    "update_time": "2024-01-01T12:00:00Z"
  }
}
```

### 2. 获取持仓信息
**GET** `/api/okx/account/positions`

**请求头**:
```
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "positions": [
      {
        "instId": "BTC-USDT-SWAP",
        "pos": "0.1",
        "posSide": "long",
        "avgPx": "50000",
        "markPx": "51000",
        "upl": "100.00",
        "uplRatio": "0.02",
        "margin": "500.00",
        "lever": "10"
      }
    ]
  }
}
```

### 3. 下单
**POST** `/api/okx/trade/order`

**请求头**:
```
Authorization: Bearer {access_token}
```

**请求参数**:
```json
{
  "instId": "BTC-USDT",
  "tdMode": "cash",
  "side": "buy",
  "ordType": "limit",
  "sz": "0.01",
  "px": "50000",
  "tgtCcy": "base_ccy"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "下单成功",
  "data": {
    "ordId": "*********0*********0",
    "clOrdId": "client_order_001",
    "sCode": "0",
    "sMsg": "Order placed successfully"
  }
}
```

### 4. 撤销订单
**POST** `/api/okx/trade/cancel-order`

**请求参数**:
```json
{
  "instId": "BTC-USDT",
  "ordId": "*********0*********0"
}
```

### 5. 获取订单信息
**GET** `/api/okx/trade/order`

**查询参数**:
- `instId`: 产品ID (可选)
- `ordId`: 订单ID (可选)
- `clOrdId`: 客户端订单ID (可选)
- `state`: 订单状态 (可选)

### 6. 获取历史订单
**GET** `/api/okx/trade/orders-history`

**查询参数**:
- `instType`: 产品类型
- `instId`: 产品ID (可选)
- `ordType`: 订单类型 (可选)
- `state`: 订单状态 (可选)
- `after`: 分页游标
- `before`: 分页游标
- `limit`: 返回结果数量 (默认100，最大100)

## 行情数据接口

### 1. 获取K线数据
**GET** `/api/okx/market/candles`

**查询参数**:
- `instId`: 产品ID (必填)
- `bar`: K线周期 (1m, 3m, 5m, 15m, 30m, 1H, 2H, 4H, 6H, 12H, 1D, 1W, 1M, 3M, 6M, 1Y)
- `after`: 分页游标 (时间戳)
- `before`: 分页游标 (时间戳)
- `limit`: 返回结果数量 (默认100，最大300)

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "ts": "*************",
      "o": "50000",
      "h": "51000",
      "l": "49500",
      "c": "50500",
      "vol": "100.5",
      "volCcy": "5025000",
      "volCcyQuote": "5025000",
      "confirm": "1"
    }
  ]
}
```

### 2. 获取实时行情
**GET** `/api/okx/market/ticker`

**查询参数**:
- `instId`: 产品ID

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "instType": "SPOT",
    "instId": "BTC-USDT",
    "last": "50500",
    "lastSz": "0.01",
    "askPx": "50510",
    "askSz": "1.5",
    "bidPx": "50490",
    "bidSz": "2.0",
    "open24h": "49800",
    "high24h": "51200",
    "low24h": "49200",
    "vol24h": "15420.5",
    "volCcy24h": "778532500",
    "ts": "*************"
  }
}
```

### 3. 获取深度数据
**GET** `/api/okx/market/books`

**查询参数**:
- `instId`: 产品ID
- `sz`: 深度档位数量 (默认1，最大400)

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "asks": [
      ["50510", "1.5", "0", "2"],
      ["50520", "2.0", "0", "1"]
    ],
    "bids": [
      ["50490", "2.0", "0", "3"],
      ["50480", "1.8", "0", "1"]
    ],
    "ts": "*************"
  }
}
```

### 4. 获取产品信息
**GET** `/api/okx/public/instruments`

**查询参数**:
- `instType`: 产品类型 (SPOT, MARGIN, SWAP, FUTURES, OPTION)
- `uly`: 标的指数 (可选)
- `instFamily`: 交易品种 (可选)
- `instId`: 产品ID (可选)

### 5. 获取交易规则
**GET** `/api/okx/public/instruments/{instId}/rules`

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "instId": "BTC-USDT",
    "minSz": "0.00001",
    "maxSz": "9000000000",
    "tickSz": "0.1",
    "lotSz": "0.00001",
    "ctVal": "1",
    "ctMult": "1",
    "ctValCcy": "BTC"
  }
}
```