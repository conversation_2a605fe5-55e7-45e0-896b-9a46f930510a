# KLineChart 使用说明和API接口文档

## 📖 概述

KLineChart 是一个专业的 K 线图表库，为金融交易应用提供强大的图表功能。本文档基于 KLineChart v9.x 版本，详细介绍了在本项目中的使用方法和 API 接口。

## 🚀 快速开始

### 安装和引入

```javascript
import * as klinecharts from 'klinecharts'
```

### 基本使用

```javascript
// 创建图表实例
const chart = klinecharts.init('chart-container')

// 添加数据
chart.applyNewData([
  { timestamp: 1640995200000, open: 50000, high: 51000, low: 49000, close: 50500, volume: 1000 },
  // ... 更多数据
])
```

## 🎨 样式配置

### 完整样式配置结构

```javascript
const styles = {
  // 网格配置
  grid: {
    show: true,
    horizontal: {
      show: true,
      size: 1,
      color: '#EDEDED',
      style: 'dashed',
      dashedValue: [2, 2]
    },
    vertical: {
      show: true,
      size: 1,
      color: '#EDEDED',
      style: 'dashed',
      dashedValue: [2, 2]
    }
  },
  
  // K线配置
  candle: {
    type: 'candle_solid', // 'candle_solid' | 'candle_stroke' | 'candle_up_stroke' | 'candle_down_stroke' | 'ohlc' | 'area'
    bar: {
      compareRule: 'current_open', // 'current_open' | 'previous_close'
      upColor: '#2DC08E',
      downColor: '#F92855',
      noChangeColor: '#888888',
      upBorderColor: '#2DC08E',
      downBorderColor: '#F92855',
      noChangeBorderColor: '#888888',
      upWickColor: '#2DC08E',
      downWickColor: '#F92855',
      noChangeWickColor: '#888888'
    },
    
    // 面积图配置
    area: {
      lineSize: 2,
      lineColor: '#2196F3',
      smooth: false,
      value: 'close',
      backgroundColor: [{
        offset: 0,
        color: 'rgba(33, 150, 243, 0.01)'
      }, {
        offset: 1,
        color: 'rgba(33, 150, 243, 0.2)'
      }]
    },
    
    // 价格标记配置
    priceMark: {
      show: true,
      high: {
        show: true,
        color: '#D9D9D9',
        textMargin: 5,
        textSize: 10,
        textFamily: 'Helvetica Neue',
        textWeight: 'normal'
      },
      low: {
        show: true,
        color: '#D9D9D9',
        textMargin: 5,
        textSize: 10,
        textFamily: 'Helvetica Neue',
        textWeight: 'normal'
      },
      last: {
        show: true,
        compareRule: 'current_open',
        upColor: '#2DC08E',
        downColor: '#F92855',
        noChangeColor: '#888888',
        line: {
          show: true,
          style: 'dashed',
          dashedValue: [4, 4],
          size: 1
        },
        text: {
          show: true,
          style: 'fill',
          size: 12,
          paddingLeft: 4,
          paddingTop: 4,
          paddingRight: 4,
          paddingBottom: 4,
          borderStyle: 'solid',
          borderSize: 0,
          borderColor: 'transparent',
          color: '#FFFFFF',
          family: 'Helvetica Neue',
          weight: 'normal',
          borderRadius: 2
        }
      }
    },
    
    // 提示框配置
    tooltip: {
      offsetLeft: 4,
      offsetTop: 6,
      offsetRight: 4,
      offsetBottom: 6,
      showRule: 'always', // 'always' | 'follow_cross' | 'none'
      showType: 'standard', // 'standard' | 'rect'
      rect: {
        position: 'fixed', // 'fixed' | 'pointer'
        paddingLeft: 4,
        paddingRight: 4,
        paddingTop: 4,
        paddingBottom: 4,
        borderRadius: 4,
        borderSize: 1,
        borderColor: '#f2f3f5',
        color: '#FEFEFE'
      }
    }
  }
}

// 应用样式
chart.setStyles(styles)
```

### 主题配置

```javascript
// 设置主题
chart.setStyles({
  candle: {
    bar: {
      upColor: theme === 'dark' ? '#00b894' : '#2DC08E',
      downColor: theme === 'dark' ? '#d63031' : '#F92855'
    }
  }
})
```

## 📊 技术指标

### 内置技术指标列表

| 指标名 | 默认参数 | 说明 |
|--------|----------|------|
| MA | [5, 10, 30, 60] | 移动平均线 |
| EMA | [6, 12, 20] | 指数移动平均线 |
| SMA | [12, 2] | 简单移动平均线 |
| BBI | [3, 6, 12, 24] | 多空指数 |
| VOL | [5, 10, 20] | 成交量 |
| MACD | [12, 26, 9] | 指数平滑异同移动平均线 |
| BOLL | [20, 2] | 布林带 |
| KDJ | [9, 3, 3] | 随机指标 |
| RSI | [6, 12, 24] | 相对强弱指标 |
| BIAS | [6, 12, 24] | 乖离率 |
| BRAR | [26] | 人气意愿指标 |
| CCI | [13] | 顺势指标 |
| DMI | [14, 6] | 动向指标 |
| CR | [26, 10, 20, 40, 60] | 能量指标 |
| PSY | [12, 6] | 心理线 |
| DMA | [10, 50, 10] | 平行线差指标 |
| TRIX | [12, 20] | 三重指数平滑移动平均 |
| OBV | [30] | 能量潮 |
| VR | [24, 30] | 成交量变异率 |
| WR | [6, 10, 14] | 威廉指标 |
| MTM | [6, 10] | 动量指标 |
| EMV | [14, 9] | 简易波动指标 |
| SAR | [2, 2, 20] | 抛物线指标 |
| AO | [5, 34] | 振荡器 |
| ROC | [12, 6] | 变动率指标 |
| PVT | 无 | 价量趋势指标 |
| AVP | 无 | 平均价格 |

### 添加技术指标

```javascript
// 在主图上添加移动平均线
chart.createIndicator('MA', true, { id: 'candle_pane' })

// 在副图上添加MACD
chart.createIndicator('MACD', false)

// 添加自定义参数的指标
chart.createIndicator({
  name: 'MA',
  calcParams: [5, 10, 20, 60]
}, true, { id: 'candle_pane' })
```

### 兼容主图的指标

以下指标可以叠加在蜡烛图上：
- BBI（多空指数）
- BOLL（布林带）
- EMA（指数移动平均线）
- MA（移动平均线）
- SAR（抛物线指标）
- SMA（简单移动平均线）

### 自定义技术指标

```javascript
// 定义自定义指标
const customIndicator = {
  name: 'CustomMA',
  shortName: 'CMA',
  calcParams: [20],
  plots: [{
    key: 'ma',
    title: 'MA: ',
    type: 'line'
  }],
  calc: function (dataList, indicator) {
    const params = indicator.calcParams
    const period = params[0]
    return dataList.map((kLineData, i) => {
      const ma = { ma: null }
      if (i >= period - 1) {
        let sum = 0
        for (let j = 0; j < period; j++) {
          sum += dataList[i - j].close
        }
        ma.ma = sum / period
      }
      return ma
    })
  }
}

// 注册自定义指标
klinecharts.registerIndicator(customIndicator)

// 使用自定义指标
chart.createIndicator('CustomMA')
```

## 🎯 覆盖物（绘图工具）

### 内置覆盖物类型

- **线条类**：
  - `horizontalRayLine` - 水平射线
  - `horizontalSegment` - 水平线段
  - `horizontalStraightLine` - 水平直线
  - `verticalRayLine` - 垂直射线
  - `verticalSegment` - 垂直线段
  - `verticalStraightLine` - 垂直直线
  - `rayLine` - 射线
  - `segment` - 线段
  - `straightLine` - 直线

- **价格线**：
  - `priceLine` - 价格线
  - `priceChannelLine` - 价格通道线
  - `parallelStraightLine` - 平行线
  - `fibonacciLine` - 斐波那契线

- **标注类**：
  - `simpleAnnotation` - 简单标注
  - `simpleTag` - 简单标签

### 创建覆盖物

```javascript
// 创建趋势线
chart.createOverlay({
  name: 'segment',
  points: [
    { timestamp: 1640995200000, value: 50000 },
    { timestamp: 1641081600000, value: 51000 }
  ]
})

// 创建价格线
chart.createOverlay({
  name: 'priceLine',
  points: [{ value: 50000 }]
})
```

### 自定义覆盖物

```javascript
// 定义自定义覆盖物
const customOverlay = {
  name: 'customLine',
  totalStep: 2,
  needDefaultPointFigure: true,
  createPointFigures: ({ coordinates }) => {
    return [
      {
        type: 'line',
        attrs: {
          coordinates: coordinates
        },
        styles: {
          style: 'solid',
          size: 2,
          color: '#1890FF'
        }
      }
    ]
  }
}

// 注册自定义覆盖物
klinecharts.registerOverlay(customOverlay)
```

## 🔧 API 接口

### 图表实例方法

#### 数据操作

```javascript
// 应用新数据（替换所有数据）
chart.applyNewData(dataList)

// 更新数据（追加或更新最后一条）
chart.updateData(data)

// 获取数据源
const dataList = chart.getDataList()

// 清空数据
chart.clearData()
```

#### 样式设置

```javascript
// 设置样式
chart.setStyles(styles)

// 获取样式
const styles = chart.getStyles()
```

#### 指标操作

```javascript
// 创建指标
chart.createIndicator(indicator, isStack, paneOptions)

// 覆盖指标
chart.overrideIndicator(override, paneId)

// 移除指标
chart.removeIndicator(paneId, name)

// 获取指标
const indicator = chart.getIndicatorByPaneId(paneId, name)
```

#### 覆盖物操作

```javascript
// 创建覆盖物
chart.createOverlay(overlay, paneId)

// 获取覆盖物
const overlays = chart.getOverlayById(id)

// 移除覆盖物
chart.removeOverlay(remove)
```

#### 图表控制

```javascript
// 缩放
chart.zoomAtCoordinate(scale, coordinate, paneId)

// 滚动
chart.scrollByDistance(distance, animationDuration)

// 滚动到实时位置
chart.scrollToRealTime(animationDuration)

// 滚动到指定位置
chart.scrollToDataIndex(dataIndex, animationDuration)
```

#### 坐标转换

```javascript
// 将时间戳转换为x坐标
const x = chart.timestampToCoordinate(timestamp)

// 将价格转换为y坐标
const y = chart.priceToCoordinate(price, paneId)

// 将坐标转换为数据索引
const dataIndex = chart.coordinateToDataIndex(coordinate)
```

#### 事件监听

```javascript
// 订阅事件
chart.subscribeAction('onCrosshairChange', (data) => {
  console.log('十字光标变化:', data)
})

// 取消订阅
chart.unsubscribeAction('onCrosshairChange')
```

### 全局方法

```javascript
// 注册指标
klinecharts.registerIndicator(indicator)

// 获取指标信息
const indicatorInfo = klinecharts.getSupportedIndicators()

// 注册覆盖物
klinecharts.registerOverlay(overlay)

// 获取覆盖物信息
const overlayInfo = klinecharts.getSupportedOverlays()

// 注册字体
klinecharts.registerFont(fontFamily)

// 设置时区
klinecharts.setTimezone(timezone)

// 获取版本
const version = klinecharts.version()
```

## 🎛️ 项目中的使用示例

### 基本图表初始化

```javascript
import { useKLineCharts } from '@/composables/useKLineCharts'

const {
  chartInstances,
  initializeChart,
  updateChart,
  setSymbol
} = useKLineCharts()

// 初始化图表
await initializeChart('main', chartContainer.value, {
  symbol: 'BTC-USDT',
  timeframe: '1m'
})
```

### 数据更新

```javascript
// 更新K线数据
const updateKlineData = (newData) => {
  if (chartInstances.value.main) {
    chartInstances.value.main.applyNewData(newData)
  }
}

// 实时数据更新
const updateRealTimeData = (tickerData) => {
  if (chartInstances.value.main && tickerData) {
    chartInstances.value.main.updateData({
      timestamp: Date.now(),
      open: parseFloat(tickerData.open),
      high: parseFloat(tickerData.high),
      low: parseFloat(tickerData.low),
      close: parseFloat(tickerData.last),
      volume: parseFloat(tickerData.vol24h)
    })
  }
}
```

### 精度设置

```javascript
/**
 * 设置交易对和精度
 * @param {string} symbol - 交易对符号
 * @param {Object} precision - 精度配置
 */
const setSymbol = (symbol, precision = {}) => {
  currentSymbol.value = symbol
  
  // 设置价格精度
  if (precision.pxPrecision !== undefined) {
    pricePrecision.value = precision.pxPrecision
  }
  
  // 设置数量精度
  if (precision.szPrecision !== undefined) {
    volumePrecision.value = precision.szPrecision
  }
  
  // 更新图表样式
  updateChartPrecision()
}

/**
 * 更新图表精度显示
 */
const updateChartPrecision = () => {
  if (chartInstances.value.main) {
    chartInstances.value.main.setStyles({
      candle: {
        tooltip: {
          custom: (data) => {
            return [
              { title: '时间', value: formatTime(data.timestamp) },
              { title: '开盘', value: formatPrice(data.open, pricePrecision.value) },
              { title: '最高', value: formatPrice(data.high, pricePrecision.value) },
              { title: '最低', value: formatPrice(data.low, pricePrecision.value) },
              { title: '收盘', value: formatPrice(data.close, pricePrecision.value) },
              { title: '成交量', value: formatVolume(data.volume, volumePrecision.value) }
            ]
          }
        }
      }
    })
  }
}
```

### 指标管理

```javascript
// 添加技术指标
const addIndicator = (indicatorName, params = {}) => {
  if (chartInstances.value.main) {
    chartInstances.value.main.createIndicator({
      name: indicatorName,
      calcParams: params.calcParams || [],
      precision: params.precision || 2,
      styles: params.styles || {}
    }, params.isStack || false)
  }
}

// 移除技术指标
const removeIndicator = (indicatorName, paneId = 'candle_pane') => {
  if (chartInstances.value.main) {
    chartInstances.value.main.removeIndicator(paneId, indicatorName)
  }
}
```

## 🔍 性能优化

### 数据处理优化

```javascript
// 数据验证和过滤
const validateKlineData = (data) => {
  if (!Array.isArray(data) || data.length === 0) {
    return []
  }
  
  return data.filter(item => {
    return item &&
           typeof item.timestamp === 'number' &&
           typeof item.open === 'number' &&
           typeof item.high === 'number' &&
           typeof item.low === 'number' &&
           typeof item.close === 'number' &&
           typeof item.volume === 'number' &&
           item.high >= item.low &&
           item.high >= Math.max(item.open, item.close) &&
           item.low <= Math.min(item.open, item.close)
  })
}

// 批量数据更新
const batchUpdateData = (dataList) => {
  const validData = validateKlineData(dataList)
  if (validData.length > 0 && chartInstances.value.main) {
    chartInstances.value.main.applyNewData(validData)
  }
}
```

### 内存管理

```javascript
// 清理图表资源
const cleanup = () => {
  Object.values(chartInstances.value).forEach(chart => {
    if (chart && typeof chart.dispose === 'function') {
      chart.dispose()
    }
  })
  chartInstances.value = {}
}

// 组件卸载时清理
onUnmounted(() => {
  cleanup()
})
```

## 🐛 常见问题

### 1. 图表不显示

**原因**：容器元素未正确设置或数据格式错误

**解决方案**：
```javascript
// 确保容器有明确的宽高
.chart-container {
  width: 100%;
  height: 400px;
}

// 检查数据格式
const validData = data.map(item => ({
  timestamp: Number(item.timestamp),
  open: Number(item.open),
  high: Number(item.high),
  low: Number(item.low),
  close: Number(item.close),
  volume: Number(item.volume)
}))
```

### 2. 指标不显示

**原因**：指标参数错误或数据不足

**解决方案**：
```javascript
// 确保有足够的数据点
if (dataList.length >= 20) {
  chart.createIndicator('MA', true, { id: 'candle_pane' })
}
```

### 3. 性能问题

**原因**：数据量过大或更新频率过高

**解决方案**：
```javascript
// 限制数据量
const MAX_DATA_LENGTH = 1000
if (dataList.length > MAX_DATA_LENGTH) {
  dataList = dataList.slice(-MAX_DATA_LENGTH)
}

// 节流更新
const throttledUpdate = throttle(updateChart, 100)
```

## 📚 参考资源

- [KLineChart 官方文档](https://klinecharts.com/)
- [KLineChart GitHub](https://github.com/klinecharts/KLineChart)
- [技术指标说明](https://klinecharts.com/guide/indicator)
- [样式配置指南](https://klinecharts.com/guide/styles)
- [覆盖物使用指南](https://klinecharts.com/guide/overlay)

---

*本文档基于 KLineChart v9.x 版本编写，如有更新请参考官方最新文档。*