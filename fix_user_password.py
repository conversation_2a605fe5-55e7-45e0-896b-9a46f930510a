#!/usr/bin/env python3
"""
修复数据库中用户密码的脚本
将明文密码转换为bcrypt哈希
"""

import sqlite3
from passlib.hash import bcrypt

def fix_user_passwords():
    # 连接数据库
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    
    # 获取所有用户
    cursor.execute('SELECT id, username, password FROM user')
    users = cursor.fetchall()
    
    print(f"找到 {len(users)} 个用户")
    
    for user_id, username, password in users:
        print(f"处理用户: {username}")
        
        # 检查密码是否已经是bcrypt哈希（bcrypt哈希以$2b$开头）
        if not password.startswith('$2b$'):
            print(f"  密码是明文，正在转换为bcrypt哈希...")
            # 将明文密码转换为bcrypt哈希
            hashed_password = bcrypt.hash(password)
            
            # 更新数据库
            cursor.execute('UPDATE user SET password = ? WHERE id = ?', (hashed_password, user_id))
            print(f"  密码已更新")
        else:
            print(f"  密码已经是bcrypt哈希，跳过")
    
    # 提交更改
    conn.commit()
    conn.close()
    print("密码修复完成！")

if __name__ == "__main__":
    fix_user_passwords()
