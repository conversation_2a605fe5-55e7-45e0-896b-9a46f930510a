<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OKX Pro 交易终端</title>
    <link rel="stylesheet" href="/static/style.css?v=2.0">
</head>
<body>
    <div id="app-container">
        <aside class="sidebar">
            <h1 class="logo">OKX TERMINAL</h1>
            <div id="balance-container" class="sidebar-module">
                <h2>账户余额</h2>
                <div id="balance-content" class="content-box"></div>
            </div>
            <div id="status-container" class="sidebar-module">
                <h2>系统状态</h2>
                <div id="status-content" class="content-box"></div>
                <button id="refresh-status-btn" class="sidebar-button">刷新状态</button>
            </div>
        </aside>

        <main class="main-content">
            <div id="market-data-container" class="main-module">
                <div class="module-header">
                    <div class="tabs-container" id="market-tabs">
                        <button class="tab-link" data-tab="favorites">自选</button>
                        <button class="tab-link active" data-tab="swap">永续</button>
                        <button class="tab-link" data-tab="spot">现货</button>
                        <button class="tab-link" data-tab="option">期权</button>
                    </div>
                     <div class="market-controls">
                        <input type="search" id="market-search-input" placeholder="🔍 搜索币对...">
                        <div class="filter-tags" id="quote-filter-tags">
                            <!-- 筛选标签会由JS动态生成 -->
                        </div>
                    </div>
                </div>
                <div class="module-content">
                    <div class="market-list-header">
                        <span class="col-name">名称 / 24h量</span>
                        <span class="col-price">最新价</span>
                        <span class="col-change">24h涨跌幅</span>
                    </div>
                    <div id="market-list-content" class="market-list-container">
                        <!-- 行情列表将由JS动态渲染 -->
                    </div>
                </div>
            </div>
            
            <div class="bottom-zone">
                <div id="trade-container" class="main-module">
                     <div class="module-header">
                        <div class="tabs-container" id="trade-tabs">
                            <button class="tab-link active" data-tab="positions">持仓</button>
                            <button class="tab-link" data-tab="pending-orders">挂单</button>
                            <button class="tab-link" data-tab="history-orders">历史订单</button>
                        </div>
                         <div class="trade-controls" id="trade-filters">
                             <!-- 交易筛选器将由JS动态控制 -->
                         </div>
                    </div>
                    <div id="trade-content" class="module-content">
                        <!-- 持仓/挂单/历史订单表格将由JS渲染 -->
                    </div>
                </div>

                <div id="order-form-container" class="main-module">
                    <div class="module-header">
                        <h2>交易</h2>
                    </div>
                    <form id="orderForm" class="module-content">
                        <div class="trade-tabs-row">
                            <button type="button" class="trade-tab active" data-mode="open">开仓</button>
                            <button type="button" class="trade-tab" data-mode="close">平仓</button>
                        </div>
                        <div class="form-row">
                            <select id="tdMode-select" name="tdMode" class="form-input">
                                <option value="cross">全仓</option>
                                <option value="isolated">逐仓</option>
                            </select>
                            <select id="lever-select" name="lever" class="form-input"></select>
                        </div>
                        <div class="order-type-tabs">
                            <button type="button" class="order-type-tab active" data-type="market">市价委托</button>
                            <button type="button" class="order-type-tab" data-type="limit">限价委托</button>
                            <button type="button" class="order-type-tab" data-type="tp-sl">止盈止损</button>
                        </div>
                        <div class="form-row">
                            <input id="price-input" name="px" class="form-input" placeholder="价格(USDT)">
                            <input id="amount-input" name="sz" class="form-input" placeholder="数量">
                        </div>
                        <div id="tp-sl-box" style="display:none;">
                            <div class="form-row">
                                <input name="tpTriggerPx" class="form-input" placeholder="止盈触发价">
                                <input name="tpOrdPx" class="form-input" placeholder="止盈委托价">
                            </div>
                            <div class="form-row">
                                <input name="slTriggerPx" class="form-input" placeholder="止损触发价">
                                <input name="slOrdPx" class="form-input" placeholder="止损委托价">
                            </div>
                        </div>
                        <div class="trade-info-row">
                            <span id="availBal">可用余额: --</span>
                            <span id="canBuy">可开多: --</span>
                            <span id="canSell">可开空: --</span>
                            <span id="liqPx">强平价: --</span>
                        </div>
                        <div class="form-row">
                            <button type="button" id="buy-btn" class="submit-btn buy">开多</button>
                            <button type="button" id="sell-btn" class="submit-btn sell">开空</button>
                        </div>
                        <div id="order-result-box" class="form-result"></div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="/static/app.js?v=2.3"></script>
</body>
</html> 