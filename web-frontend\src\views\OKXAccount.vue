<template>
  <div>
    <a-card title="OKX 账户余额" style="margin: 20px">
      <a-button type="primary" @click="fetchBalance">刷新余额</a-button>
      <a-table :dataSource="balances" :columns="columns" rowKey="ccy" style="margin-top: 20px" />
    </a-card>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import axios from 'axios'
const balances = ref([])
const columns = [
  { title: '币种', dataIndex: 'ccy' },
  { title: '余额', dataIndex: 'availBal' },
  { title: '权益', dataIndex: 'eq' }
]
const fetchBalance = async () => {
  const res = await axios.get('/api/okx/account/balance')
  // 兼容后端包装的响应格式：{"code": 0, "msg": "", "data": result}
  // 和OKX原始格式：{"code": "0", "data": [...]}
  const isValidResponse = (res.data.code === '0' || res.data.code === 0) && res.data.data && res.data.data.length > 0
  if (isValidResponse) {
    balances.value = res.data.data[0].details || []
  } else {
    balances.value = []
  }
}
</script>