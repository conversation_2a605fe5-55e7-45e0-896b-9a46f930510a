<!DOCTYPE html>
<html>
<head>
    <title>快速测试 - 交易信号</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #40a9ff;
        }
        .result {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin: 2px;
        }
        .status-green { background: #52c41a; color: white; }
        .status-red { background: #ff4d4f; color: white; }
        .status-blue { background: #1890ff; color: white; }
        .status-orange { background: #faad14; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>交易信号快速测试</h1>
        
        <div>
            <button class="button" onclick="testVueApp()">测试Vue应用</button>
            <button class="button" onclick="testAPI()">测试API</button>
            <button class="button" onclick="generateSignals()">生成信号</button>
        </div>
        
        <div id="result" class="result">点击按钮开始测试...</div>
        
        <div id="signals"></div>
    </div>

    <script>
        // 测试Vue应用是否正常运行
        async function testVueApp() {
            try {
                const response = await fetch('/')
                if (response.ok) {
                    document.getElementById('result').textContent = '✓ Vue应用正常运行\n访问地址: http://localhost:3000\n高级图表: http://localhost:3000/charts/advanced'
                } else {
                    document.getElementById('result').textContent = '✗ Vue应用无法访问'
                }
            } catch (error) {
                document.getElementById('result').textContent = `✗ 连接失败: ${error.message}`
            }
        }

        // 测试API接口
        async function testAPI() {
            try {
                const response = await fetch('/api/okx/market/ticker?instId=BTC-USDT-SWAP')
                const data = await response.json()
                
                if (data.code === '0') {
                    const ticker = data.data[0]
                    document.getElementById('result').textContent = `✓ API接口正常
当前价格: ${ticker.last}
24h涨跌: ${ticker.sodUtc8}%
24h最高: ${ticker.high24h}
24h最低: ${ticker.low24h}`
                } else {
                    document.getElementById('result').textContent = `✗ API返回错误: ${data.msg}`
                }
            } catch (error) {
                document.getElementById('result').textContent = `✗ API请求失败: ${error.message}`
            }
        }

        // 生成交易信号
        async function generateSignals() {
            try {
                // 获取K线数据
                const response = await fetch('/api/okx/market/candles?instId=BTC-USDT-SWAP&bar=1H&limit=50')
                const data = await response.json()
                
                if (data.code !== '0') {
                    throw new Error(data.msg || '获取数据失败')
                }
                
                const prices = data.data.reverse().map(item => parseFloat(item[4]))
                const currentPrice = prices[prices.length - 1]
                
                // 计算技术指标
                const indicators = calculateIndicators(prices)
                
                // 生成交易信号
                const signals = generateTradingSignals(indicators, currentPrice)
                
                // 显示结果
                displayResults(indicators, signals, currentPrice)
                
            } catch (error) {
                document.getElementById('result').textContent = `✗ 生成信号失败: ${error.message}`
            }
        }

        // 计算技术指标
        function calculateIndicators(prices) {
            return {
                ma5: calculateMA(prices, 5),
                ma10: calculateMA(prices, 10),
                ma20: calculateMA(prices, 20),
                ema5: calculateEMA(prices, 5),
                ema10: calculateEMA(prices, 10),
                ema20: calculateEMA(prices, 20),
                rsi: calculateRSI(prices, 14),
                macd: calculateMACD(prices)
            }
        }

        // 移动平均线
        function calculateMA(prices, period) {
            if (prices.length < period) return 0
            const sum = prices.slice(-period).reduce((a, b) => a + b, 0)
            return sum / period
        }

        // 指数移动平均线
        function calculateEMA(prices, period) {
            if (prices.length < period) return 0
            const multiplier = 2 / (period + 1)
            let ema = prices[0]
            
            for (let i = 1; i < prices.length; i++) {
                ema = (prices[i] * multiplier) + (ema * (1 - multiplier))
            }
            
            return ema
        }

        // RSI
        function calculateRSI(prices, period = 14) {
            if (prices.length < period + 1) return 50

            const changes = []
            for (let i = 1; i < prices.length; i++) {
                changes.push(prices[i] - prices[i - 1])
            }

            let gains = 0
            let losses = 0
            
            for (let i = 0; i < period; i++) {
                if (changes[i] > 0) {
                    gains += changes[i]
                } else {
                    losses += Math.abs(changes[i])
                }
            }

            const avgGain = gains / period
            const avgLoss = losses / period

            if (avgLoss === 0) {
                return avgGain === 0 ? 50 : 100
            }

            const rs = avgGain / avgLoss
            return 100 - (100 / (1 + rs))
        }

        // MACD
        function calculateMACD(prices) {
            const ema12 = calculateEMA(prices, 12)
            const ema26 = calculateEMA(prices, 26)
            return ema12 - ema26
        }

        // 生成交易信号
        function generateTradingSignals(indicators, currentPrice) {
            return {
                emaSignal: indicators.ema5 > indicators.ema10 ? '金叉' : '死叉',
                emaSlowStatus: currentPrice > indicators.ema20 ? '多头' : '空头',
                emaMidStatus: currentPrice > indicators.ema10 ? '多头' : '空头',
                maFastStatus: currentPrice > indicators.ma5 ? '多头' : '空头',
                maMidStatus: currentPrice > indicators.ma10 ? '多头' : '空头',
                maSlowStatus: currentPrice > indicators.ma20 ? '多头' : '空头',
                rsiStatus: indicators.rsi > 70 ? 'RSI超买' : indicators.rsi < 30 ? 'RSI超卖' : 'RSI中立'
            }
        }

        // 显示结果
        function displayResults(indicators, signals, currentPrice) {
            const resultText = `✓ 交易信号生成成功

当前价格: ${currentPrice.toFixed(2)}

技术指标:
MA5:  ${indicators.ma5.toFixed(2)}
MA10: ${indicators.ma10.toFixed(2)}
MA20: ${indicators.ma20.toFixed(2)}
EMA5:  ${indicators.ema5.toFixed(2)}
EMA10: ${indicators.ema10.toFixed(2)}
EMA20: ${indicators.ema20.toFixed(2)}
RSI: ${indicators.rsi.toFixed(2)}
MACD: ${indicators.macd.toFixed(4)}

交易信号:
EMA交叉: ${signals.emaSignal}
EMA慢线状态: ${signals.emaSlowStatus}
EMA中线状态: ${signals.emaMidStatus}
MA快线状态: ${signals.maFastStatus}
MA中线状态: ${signals.maMidStatus}
MA慢线状态: ${signals.maSlowStatus}
RSI状态: ${signals.rsiStatus}`

            document.getElementById('result').textContent = resultText

            // 显示信号状态
            const getStatusClass = (signal) => {
                if (signal.includes('多头') || signal === '金叉') return 'status-green'
                if (signal.includes('空头') || signal === '死叉') return 'status-red'
                if (signal.includes('超买')) return 'status-orange'
                if (signal.includes('超卖')) return 'status-green'
                return 'status-blue'
            }

            const signalsHtml = `
                <h3>信号状态显示</h3>
                <div>
                    <div>EMA 交叉: <span class="${getStatusClass(signals.emaSignal)} status">${signals.emaSignal}</span></div>
                    <div>EMA 慢线状态: <span class="${getStatusClass(signals.emaSlowStatus)} status">${signals.emaSlowStatus}</span> ${indicators.ema20.toFixed(2)}</div>
                    <div>EMA 中线状态: <span class="${getStatusClass(signals.emaMidStatus)} status">${signals.emaMidStatus}</span> ${indicators.ema10.toFixed(2)}</div>
                    <div>MA 快线状态: <span class="${getStatusClass(signals.maFastStatus)} status">${signals.maFastStatus}</span> ${indicators.ma5.toFixed(2)}</div>
                    <div>MA 中线状态: <span class="${getStatusClass(signals.maMidStatus)} status">${signals.maMidStatus}</span> ${indicators.ma10.toFixed(2)}</div>
                    <div>MA 慢线状态: <span class="${getStatusClass(signals.maSlowStatus)} status">${signals.maSlowStatus}</span> ${indicators.ma20.toFixed(2)}</div>
                    <div>RSI 状态: <span class="${getStatusClass(signals.rsiStatus)} status">${signals.rsiStatus}</span> ${indicators.rsi.toFixed(2)}</div>
                </div>
            `

            document.getElementById('signals').innerHTML = signalsHtml
        }

        // 页面加载时自动测试
        window.onload = function() {
            testVueApp()
        }
    </script>
</body>
</html>
