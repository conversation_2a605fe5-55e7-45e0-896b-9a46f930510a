<!--
  PriceAlerts.vue
  价格预警组件

  该组件允许用户设置和管理价格预警。
  数据来源于 chartStore。
-->
<template>
  <div class="price-alerts">
    <div class="alerts-header">价格预警</div>
    <div class="alert-list">
      <a-tag 
        v-for="alert in priceAlerts" 
        :key="alert.id"
        closable
        @close="removeAlert(alert.id)"
        color="blue"
      >
        {{ alert.price > (latestPrice?.close || 0) ? '▲' : '▼' }} {{ formatPrice(alert.price) }}
      </a-tag>
      <a-button size="small" @click="showAlertModal = true" class="add-alert-btn">+</a-button>
    </div>

    <a-modal
      v-model:open="showAlertModal"
      title="设置价格预警"
      @ok="handleAddAlert"
      :ok-text="'添加'"
      :cancel-text="'取消'"
    >
      <a-input-number
        v-model:value="newAlertPrice"
        :min="0"
        :step="0.01"
        string-mode
        placeholder="输入价格"
        style="width: 100%"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useInfoPanelStore } from '@/stores/infoPanelStore';
import { useChartDataStore } from '@/stores/chartDataStore';
import { formatPrice } from '@/utils/formatters'
import { storeToRefs } from 'pinia'

const infoPanelStore = useInfoPanelStore();
const chartDataStore = useChartDataStore();
const { priceAlerts } = storeToRefs(infoPanelStore);
const { latestPrice } = storeToRefs(chartDataStore);

const showAlertModal = ref(false)
const newAlertPrice = ref(null)



const handleAddAlert = () => {
  if (newAlertPrice.value !== null && newAlertPrice.value > 0) {
    infoPanelStore.addPriceAlert({ price: parseFloat(newAlertPrice.value) });
    newAlertPrice.value = null
    showAlertModal.value = false
  }
}

const removeAlert = (id) => {
  infoPanelStore.removePriceAlert(id)
}
</script>

<style scoped>
.price-alerts {
  padding: 12px;
  background: #fafafa;
  border-radius: 4px;
}

.alerts-header {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.alert-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.add-alert-btn {
  border-style: dashed;
}

.dark-theme .price-alerts {
  background: #2a2e39;
}

.dark-theme .alerts-header {
  color: #ffffff;
}
</style>