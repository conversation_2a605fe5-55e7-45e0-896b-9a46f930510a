import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    username: '',
    token: '',
    role: '',
    avatar: '',
    id: null,
    tokenExp: 0
  }),
  actions: {
    setUser(username, token, exp) {
      this.username = username
      this.token = token
      this.tokenExp = exp || 0
    },
    setUserInfo(info) {
      this.username = info.username
      this.role = info.role || ''
      this.avatar = info.avatar || ''
      this.id = info.id || null
      this.tokenExp = info.exp || this.tokenExp || 0
    },
    clearUser() {
      this.username = ''
      this.token = ''
      this.role = ''
      this.avatar = ''
      this.id = null
      this.tokenExp = 0
    }
  }
}) 