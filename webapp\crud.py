from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import json
from passlib.context import CryptContext
from jose import JWTError, jwt

from database import User, OKXConfig, UserSettings, TradingHistory, Watchlist, ChartConfig, PriceAlert

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT 配置
SECRET_KEY = "your-secret-key-here"  # 在生产环境中应该从环境变量获取
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 密码相关函数


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)

# JWT 相关函数


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[str]:
    """验证令牌并返回用户名"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        return username
    except JWTError:
        return None

# 用户 CRUD 操作


def get_user(db: Session, user_id: int) -> Optional[User]:
    """根据ID获取用户"""
    return db.query(User).filter(User.id == user_id).first()


def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """根据用户名获取用户"""
    return db.query(User).filter(User.username == username).first()


def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """根据邮箱获取用户"""
    return db.query(User).filter(User.email == email).first()


def create_user(db: Session, username: str, email: str, password: str) -> User:
    """创建用户"""
    hashed_password = get_password_hash(password)
    db_user = User(
        username=username,
        email=email,
        hashed_password=hashed_password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """验证用户"""
    user = get_user_by_username(db, username)
    if not user:
        return None

    # 处理新旧密码字段的兼容性
    password_hash = user.hashed_password if user.hashed_password else user.password
    if not password_hash:
        return None

    # 如果是旧的明文密码，直接比较；如果是新的哈希密码，使用verify_password
    if user.hashed_password:
        if not verify_password(password, user.hashed_password):
            return None
    else:
        # 兼容旧的明文密码存储方式
        if password != user.password:
            return None

    return user


def update_user(db: Session, user_id: int, **kwargs) -> Optional[User]:
    """更新用户信息"""
    user = get_user(db, user_id)
    if not user:
        return None

    for key, value in kwargs.items():
        if hasattr(user, key):
            if key == "password":
                setattr(user, "hashed_password", get_password_hash(value))
            else:
                setattr(user, key, value)

    db.commit()
    db.refresh(user)
    return user

# OKX 配置 CRUD 操作


def get_user_okx_config(db: Session, user_id: int) -> Optional[OKXConfig]:
    """获取用户的 OKX 配置"""
    return db.query(OKXConfig).filter(
        and_(OKXConfig.user_id == user_id, OKXConfig.is_active == True)
    ).first()


def create_okx_config(db: Session, user_id: int, api_key: str, api_secret: str, passphrase: str, is_sandbox: bool = True) -> OKXConfig:
    """创建 OKX 配置"""
    # 先禁用用户的其他配置
    db.query(OKXConfig).filter(OKXConfig.user_id ==
                               user_id).update({"is_active": False})

    # 创建新配置
    config = OKXConfig(
        user_id=user_id,
        api_key=api_key,
        api_secret=api_secret,  # 在实际应用中应该加密存储
        passphrase=passphrase,  # 在实际应用中应该加密存储
        is_sandbox=is_sandbox
    )
    db.add(config)
    db.commit()
    db.refresh(config)
    return config


def update_okx_config(db: Session, user_id: int, **kwargs) -> Optional[OKXConfig]:
    """更新 OKX 配置"""
    config = get_user_okx_config(db, user_id)
    if not config:
        return None

    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)

    db.commit()
    db.refresh(config)
    return config

# 用户设置 CRUD 操作


def get_user_setting(db: Session, user_id: int, setting_key: str) -> Optional[UserSettings]:
    """获取用户设置"""
    return db.query(UserSettings).filter(
        and_(UserSettings.user_id == user_id,
             UserSettings.setting_key == setting_key)
    ).first()


def get_user_settings(db: Session, user_id: int) -> List[UserSettings]:
    """获取用户所有设置"""
    return db.query(UserSettings).filter(UserSettings.user_id == user_id).all()


def set_user_setting(db: Session, user_id: int, setting_key: str, setting_value: Any, setting_type: str = "string") -> UserSettings:
    """设置用户配置"""
    # 转换值为字符串存储
    if setting_type == "json":
        value_str = json.dumps(setting_value)
    else:
        value_str = str(setting_value)

    # 查找现有设置
    setting = get_user_setting(db, user_id, setting_key)
    if setting:
        setting.setting_value = value_str
        setting.setting_type = setting_type
        setting.updated_at = datetime.utcnow()
    else:
        setting = UserSettings(
            user_id=user_id,
            setting_key=setting_key,
            setting_value=value_str,
            setting_type=setting_type
        )
        db.add(setting)

    db.commit()
    db.refresh(setting)
    return setting


def get_user_setting_value(db: Session, user_id: int, setting_key: str, default_value: Any = None) -> Any:
    """获取用户设置值"""
    setting = get_user_setting(db, user_id, setting_key)
    if not setting:
        return default_value

    # 根据类型转换值
    if setting.setting_type == "json":
        try:
            return json.loads(setting.setting_value)
        except:
            return default_value
    elif setting.setting_type == "boolean":
        return setting.setting_value.lower() in ("true", "1", "yes")
    elif setting.setting_type == "number":
        try:
            return float(setting.setting_value)
        except:
            return default_value
    else:
        return setting.setting_value


def set_user_settings(db: Session, user_id: int, **settings) -> Dict[str, Any]:
    """批量设置用户配置"""
    result = {}
    for key, value in settings.items():
        if value is not None:  # 只设置非空值
            # 确定设置类型
            if isinstance(value, bool):
                setting_type = "boolean"
            elif isinstance(value, (int, float)):
                setting_type = "number"
            elif isinstance(value, (dict, list)):
                setting_type = "json"
            else:
                setting_type = "string"

            setting = set_user_setting(db, user_id, key, value, setting_type)
            result[key] = get_user_setting_value(db, user_id, key)

    return result


def get_settings_by_user_id(db: Session, user_id: int) -> Dict[str, Any]:
    """获取用户所有设置并转换为字典"""
    settings = get_user_settings(db, user_id)
    result = {}
    for setting in settings:
        result[setting.setting_key] = get_user_setting_value(
            db, user_id, setting.setting_key)
    return result

# 交易历史 CRUD 操作


def create_trading_record(db: Session, user_id: int, **kwargs) -> TradingHistory:
    """创建交易记录"""
    record = TradingHistory(user_id=user_id, **kwargs)
    db.add(record)
    db.commit()
    db.refresh(record)
    return record


def get_trading_history(db: Session, user_id: int, limit: int = 100, offset: int = 0) -> List[TradingHistory]:
    """获取交易历史"""
    return db.query(TradingHistory).filter(
        TradingHistory.user_id == user_id
    ).order_by(TradingHistory.created_at.desc()).offset(offset).limit(limit).all()


def update_trading_record(db: Session, record_id: int, **kwargs) -> Optional[TradingHistory]:
    """更新交易记录"""
    record = db.query(TradingHistory).filter(
        TradingHistory.id == record_id).first()
    if not record:
        return None

    for key, value in kwargs.items():
        if hasattr(record, key):
            setattr(record, key, value)

    record.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(record)
    return record

# 观察列表 CRUD 操作


def get_user_watchlist(db: Session, user_id: int) -> List[Watchlist]:
    """获取用户观察列表"""
    return db.query(Watchlist).filter(
        and_(Watchlist.user_id == user_id, Watchlist.is_active == True)
    ).all()


def add_to_watchlist(db: Session, user_id: int, instrument_id: str, **kwargs) -> Watchlist:
    """添加到观察列表"""
    # 检查是否已存在
    existing = db.query(Watchlist).filter(
        and_(
            Watchlist.user_id == user_id,
            Watchlist.instrument_id == instrument_id,
            Watchlist.is_active == True
        )
    ).first()

    if existing:
        return existing

    watchlist_item = Watchlist(
        user_id=user_id,
        instrument_id=instrument_id,
        **kwargs
    )
    db.add(watchlist_item)
    db.commit()
    db.refresh(watchlist_item)
    return watchlist_item


def remove_from_watchlist(db: Session, user_id: int, instrument_id: str) -> bool:
    """从观察列表移除"""
    result = db.query(Watchlist).filter(
        and_(
            Watchlist.user_id == user_id,
            Watchlist.instrument_id == instrument_id
        )
    ).update({"is_active": False})
    db.commit()
    return result > 0

# 图表配置 CRUD 操作


def save_chart_config(db: Session, user_id: int, config_name: str, instrument_id: str, timeframe: str, indicators: dict, layout: dict) -> ChartConfig:
    """保存图表配置"""
    # 查找现有配置
    existing = db.query(ChartConfig).filter(
        and_(
            ChartConfig.user_id == user_id,
            ChartConfig.config_name == config_name
        )
    ).first()

    if existing:
        existing.instrument_id = instrument_id
        existing.timeframe = timeframe
        existing.indicators = json.dumps(indicators)
        existing.layout = json.dumps(layout)
        existing.updated_at = datetime.utcnow()
        config = existing
    else:
        config = ChartConfig(
            user_id=user_id,
            config_name=config_name,
            instrument_id=instrument_id,
            timeframe=timeframe,
            indicators=json.dumps(indicators),
            layout=json.dumps(layout)
        )
        db.add(config)

    db.commit()
    db.refresh(config)
    return config


def get_chart_configs(db: Session, user_id: int) -> List[ChartConfig]:
    """获取用户图表配置"""
    return db.query(ChartConfig).filter(ChartConfig.user_id == user_id).all()

# 价格提醒 CRUD 操作


def create_price_alert(db: Session, user_id: int, instrument_id: str, alert_type: str, target_value: float, **kwargs) -> PriceAlert:
    """创建价格提醒"""
    alert = PriceAlert(
        user_id=user_id,
        instrument_id=instrument_id,
        alert_type=alert_type,
        target_value=target_value,
        **kwargs
    )
    db.add(alert)
    db.commit()
    db.refresh(alert)
    return alert


def get_active_alerts(db: Session, user_id: int) -> List[PriceAlert]:
    """获取活跃的价格提醒"""
    return db.query(PriceAlert).filter(
        and_(
            PriceAlert.user_id == user_id,
            PriceAlert.is_active == True,
            PriceAlert.is_triggered == False
        )
    ).all()


def trigger_alert(db: Session, alert_id: int) -> Optional[PriceAlert]:
    """触发价格提醒"""
    alert = db.query(PriceAlert).filter(PriceAlert.id == alert_id).first()
    if alert:
        alert.is_triggered = True
        alert.triggered_at = datetime.utcnow()
        db.commit()
        db.refresh(alert)
    return alert
