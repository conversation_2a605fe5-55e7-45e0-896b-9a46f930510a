import { ref } from 'vue';

const WS_URL = 'ws://localhost:8000/ws/okx/market';

let socket = ref(null);
let isConnected = ref(false);
let listeners = new Map(); // 存储频道监听器
let subscriptionSent = ref(false);
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_DELAY = 3000;

/**
 * 连接WebSocket并发送订阅请求
 */
function connect(channels = []) {
  if (socket.value && socket.value.readyState === WebSocket.OPEN) {
    return;
  }

  console.log('Connecting to WebSocket...');
  socket.value = new WebSocket(WS_URL);

  socket.value.onopen = () => {
    console.log('WebSocket connected');
    isConnected.value = true;
    reconnectAttempts = 0;
    
    // 发送订阅请求
    if (channels.length > 0) {
      const subscriptionRequest = {
        channels: channels
      };
      socket.value.send(JSON.stringify(subscriptionRequest));
      subscriptionSent.value = true;
      console.log('Subscription request sent:', subscriptionRequest);
    }
  };

  socket.value.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      
      // 处理OKX数据推送
      if (data.data && Array.isArray(data.data)) {
        const channel = data.arg?.channel;
        const instId = data.arg?.instId;
        
        if (channel && listeners.has(channel)) {
          // 计算延迟（如果有received_at时间戳）
          if (data.received_at) {
            const latency = Date.now() - data.received_at;
            if (latency > 500) {
              console.warn(`High latency detected: ${latency}ms for ${channel}`);
            }
          }
          
          listeners.get(channel).forEach(callback => {
            callback({
              channel,
              instId,
              data: data.data,
              timestamp: data.received_at || Date.now()
            });
          });
        }
      } else {
        // 处理确认消息或错误消息
        console.log('WebSocket message:', data);
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  };

  socket.value.onclose = (event) => {
    console.log('WebSocket disconnected:', event.code, event.reason);
    isConnected.value = false;
    subscriptionSent.value = false;
    
    // 自动重连逻辑
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++;
      console.log(`Attempting to reconnect (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})...`);
      setTimeout(() => {
        connect(channels);
      }, RECONNECT_DELAY);
    } else {
      console.error('Max reconnection attempts reached');
    }
  };

  socket.value.onerror = (error) => {
    console.error('WebSocket error:', error);
  };
}

/**
 * 断开 WebSocket 连接
 */
function disconnect() {
  if (socket.value) {
    socket.value.close();
  }
}

/**
 * 断开WebSocket连接
 */
function disconnect() {
  if (socket.value) {
    socket.value.close();
    socket.value = null;
    isConnected.value = false;
    subscriptionSent.value = false;
    reconnectAttempts = 0;
    console.log('WebSocket manually disconnected');
  }
}

/**
 * 订阅一个频道
 * @param {string} channel - 要订阅的频道名称
 * @param {Function} callback - 收到消息时的回调函数
 */
function subscribe(channel, callback) {
  if (!listeners.has(channel)) {
    listeners.set(channel, new Set());
  }
  listeners.get(channel).add(callback);
  console.log(`Subscribed to channel: ${channel}`);
}

/**
 * 取消订阅一个频道
 * @param {string} channel - 要取消订阅的频道名称
 * @param {Function} callback - 要移除的回调函数
 */
function unsubscribe(channel, callback) {
  if (listeners.has(channel)) {
    listeners.get(channel).delete(callback);
    if (listeners.get(channel).size === 0) {
      listeners.delete(channel);
      console.log(`Unsubscribed from channel: ${channel}`);
    }
  }
}

/**
 * 连接并订阅市场数据（ticker + K线）
 * @param {string} instId - 交易对ID，如 'BTC-USDT'
 * @param {string} bar - K线周期，如 '1m', '5m', '1H' 等
 */
function connectMarketData(instId = 'BTC-USDT', bar = '1m') {
  const channels = [
    { channel: 'tickers', instId },
    { channel: 'candle' + bar, instId }
  ];
  
  console.log(`Connecting to market data for ${instId} with ${bar} candles`);
  connect(channels);
}

/**
 * 获取连接状态
 */
function getConnectionStatus() {
  return {
    isConnected: isConnected.value,
    subscriptionSent: subscriptionSent.value,
    reconnectAttempts
  };
}

export const useWebSocket = () => ({
  isConnected,
  connect,
  disconnect,
  subscribe,
  unsubscribe,
  connectMarketData,
  getConnectionStatus
});