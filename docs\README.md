# OKX量化交易系统 - 文档中心

欢迎来到OKX量化交易系统的文档中心。这里包含了系统的完整文档，帮助您快速了解、部署和使用本系统。

## 📚 文档导航

### 🚀 快速开始
- [项目主文档](../README.md) - 项目概览和快速开始指南
- [部署指南](deployment/DEPLOYMENT-GUIDE.md) - 完整的部署说明
- [环境配置](development/实施计划.md) - 开发环境配置

### 📖 用户指南
- [产品说明](development/产品说明.md) - 产品功能和特性介绍
- [使用手册](user-guide/) - 详细的使用说明（待完善）
- [常见问题](user-guide/) - FAQ和故障排除（待完善）

### 🔧 开发文档
- [系统设计](development/系统设计.md) - 架构设计和技术选型
- [数据结构](development/数据结构.md) - 数据库设计和数据模型
- [接口文档](development/接口文档.md) - 后端API接口说明
- [页面设计](development/页面设计.md) - 前端页面设计规范

### 🎨 前端文档
- [前端文档中心](frontend/README.md) - 前端完整文档导航
- [技术架构](frontend/guides/Modular-Charts.md) - 图表系统模块化架构
- [组件指南](frontend/guides/Enhanced-Indicators.md) - 技术指标组件使用
- [性能优化](frontend/technical/OPTIMIZATION_TOOLS_USAGE.md) - 前端性能优化工具

### 🌐 API文档
- [API文档](api/API-DOCUMENTATION.md) - 完整的REST API和WebSocket API文档
- [接口规范](development/接口文档.md) - API设计规范和标准

### 🚀 部署运维
- [部署指南](deployment/DEPLOYMENT-GUIDE.md) - 开发、测试、生产环境部署
- [环境要求](deployment/DEPLOYMENT-GUIDE.md#系统要求) - 系统环境和依赖要求
- [配置说明](deployment/DEPLOYMENT-GUIDE.md#配置说明) - 配置文件和环境变量

## 🏗️ 系统架构

### 技术栈
- **前端**: Vue 3.4.15 + Vite 5.x + TypeScript + Ant Design Vue 4.2.6
- **图表**: KLineCharts 9.8.0 + ECharts 5.6.0
- **后端**: FastAPI + Python 3.9+ + SQLAlchemy
- **数据库**: SQLite (支持扩展MySQL/PostgreSQL)
- **实时通信**: WebSocket
- **外部API**: OKX API v5

### 核心功能
- 🎯 专业图表分析 - 实时K线图表和20+种技术指标
- 🤖 AI智能分析 - 智能交易信号和模式识别
- 💼 交易管理 - 订单管理和仓位控制
- 📊 数据分析 - 实时数据可视化和统计分析
- 🔒 安全防护 - JWT认证和风险管理

## 📋 项目状态

- **前端**: 98% 完成，生产就绪
- **后端**: 95% 完成，核心功能完整
- **文档**: 90% 完成，持续更新中
- **测试**: 95% 覆盖率

## 🔗 相关链接

- [项目仓库](https://github.com/your-repo/python-okx) - 源代码仓库
- [OKX API文档](https://www.okx.com/docs-v5/zh/) - OKX官方API文档
- [Vue.js文档](https://vuejs.org/) - Vue.js官方文档
- [FastAPI文档](https://fastapi.tiangolo.com/) - FastAPI官方文档

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请通过以下方式联系我们：

- 📧 邮箱: <EMAIL>
- 💬 QQ群: 123456789
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/python-okx/issues)

---

*最后更新: 2024-12-29*