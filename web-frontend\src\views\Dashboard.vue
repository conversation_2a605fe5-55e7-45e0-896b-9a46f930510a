<template>
  <div class="dashboard">
    <h2>综合数据面板</h2>

    <!-- 操作按钮 -->
    <div class="action-buttons" style="margin-bottom: 16px;">
      <a-space>
        <a-button type="primary" @click="refreshData" :loading="loading">
          <template #icon><ReloadOutlined /></template>
          刷新数据
        </a-button>
        <a-button @click="showAIAnalysis" :loading="aiLoading" ghost>
          <template #icon><RobotOutlined /></template>
          AI风控分析
        </a-button>
      </a-space>
    </div>

    <!-- 数据概览卡片 -->
    <a-row :gutter="16" style="margin-bottom: 24px;">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="总权益"
            :value="dashboardData?.summary?.total_equity || 0"
            suffix="USDT"
            :value-style="{ color: '#52c41a' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="可用余额"
            :value="dashboardData?.summary?.available_balance || 0"
            suffix="USDT"
            :value-style="{ color: '#1890ff' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="未实现盈亏"
            :value="dashboardData?.summary?.total_unrealized_pnl || 0"
            suffix="USDT"
            :value-style="{ color: (dashboardData?.summary?.total_unrealized_pnl || 0) >= 0 ? '#52c41a' : '#ff4d4f' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="持仓数量"
            :value="dashboardData?.summary?.total_positions || 0"
            :value-style="{ color: '#722ed1' }"
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- 主要内容区域 -->
    <a-row :gutter="16">
      <!-- 左侧：持仓和订单 -->
      <a-col :span="16">
        <!-- 持仓信息 -->
        <a-card title="持仓信息" style="margin-bottom: 16px;">
          <a-table
            :columns="positionColumns"
            :data-source="dashboardData?.positions || []"
            :pagination="{ pageSize: 5 }"
            size="small"
            :loading="loading"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'upl'">
                <span :style="{ color: parseFloat(record.upl) >= 0 ? '#52c41a' : '#ff4d4f' }">
                  {{ record.upl }} USDT
                </span>
              </template>
              <template v-else>
                {{ record[column.dataIndex] }}
              </template>
            </template>
            <template #empty>
              <div style="text-align:center; padding:16px;">暂无持仓</div>
            </template>
          </a-table>
        </a-card>

        <!-- 未成交订单 -->
        <a-card title="未成交订单" style="margin-bottom: 16px;">
          <a-table
            :columns="orderColumns"
            :data-source="dashboardData?.pending_orders || []"
            :pagination="{ pageSize: 5 }"
            size="small"
            :loading="loading"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-button type="link" @click="showOrderDetail(record)">详情</a-button>
              </template>
              <template v-else>
                {{ record[column.dataIndex] }}
              </template>
            </template>
            <template #empty>
              <div style="text-align:center; padding:16px;">暂无未成交订单</div>
            </template>
          </a-table>
        </a-card>
      </a-col>

      <!-- 右侧：图表和统计 -->
      <a-col :span="8">
        <!-- 持仓分布图表 -->
        <a-card title="持仓分布" style="margin-bottom: 16px;">
          <div ref="positionChartContainer" style="height: 300px;"></div>
        </a-card>

        <!-- 风险评分 -->
        <a-card title="风险评分" style="margin-bottom: 16px;">
          <a-progress
            type="circle"
            :percent="dashboardData?.summary?.risk_score || 0"
            :stroke-color="getRiskColor(dashboardData?.summary?.risk_score || 0)"
            :format="(percent) => `${percent}%`"
          />
          <div style="text-align: center; margin-top: 16px;">
            <a-tag :color="getRiskColor(dashboardData?.summary?.risk_score || 0)">
              {{ getRiskText(dashboardData?.summary?.risk_score || 0) }}
            </a-tag>
          </div>
        </a-card>

        <!-- 账户详情 -->
        <a-card title="账户详情">
          <a-list size="small">
            <a-list-item v-for="account in dashboardData?.account || []" :key="account.ccy">
              <template #actions>
                <a-tag>{{ account.ccy }}</a-tag>
              </template>
              <a-list-item-meta>
                <template #title>{{ account.eq }} {{ account.ccy }}</template>
                <template #description>
                  可用: {{ account.availEq }} | 冻结: {{ account.frozenBal }}
                </template>
              </a-list-item-meta>
            </a-list-item>
          </a-list>
        </a-card>
      </a-col>
    </a-row>

    <!-- AI风控分析弹窗 -->
    <a-modal
      :open="aiModalVisible"
      @update:open="aiModalVisible = $event"
      title="AI风控分析"
      width="1000px"
      :footer="null"
      :destroyOnClose="true"
    >
      <div v-if="aiAnalysis" class="ai-analysis-content">
        <!-- 整体风险评分 -->
        <a-card title="整体风险评分" :bordered="false" style="margin-bottom: 16px;">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-statistic
                title="风险评分"
                :value="aiAnalysis.overall_risk_score"
                suffix="/100"
                :value-style="{ color: getRiskColor(aiAnalysis.overall_risk_level) }"
              />
            </a-col>
            <a-col :span="8">
              <a-statistic
                title="风险等级"
                :value="getRiskText(aiAnalysis.overall_risk_level)"
              />
            </a-col>
            <a-col :span="8">
              <a-statistic
                title="分析时间"
                :value="formatTime(aiAnalysis.analysis_time)"
              />
            </a-col>
          </a-row>

          <!-- 风险进度条 -->
          <div style="margin-top: 16px;">
            <a-progress
              :percent="aiAnalysis.overall_risk_score"
              :show-info="false"
              :stroke-color="getRiskColor(aiAnalysis.overall_risk_level)"
            />
          </div>
        </a-card>

        <!-- AI建议 -->
        <a-card title="AI建议" :bordered="false" style="margin-bottom: 16px;">
          <a-list>
            <a-list-item v-for="(rec, index) in aiAnalysis.recommendations" :key="index">
              <template #actions>
                <a-tag :color="getRecommendationColor(rec)">建议</a-tag>
              </template>
              <a-list-item-meta>
                <template #description>{{ rec }}</template>
              </a-list-item-meta>
            </a-list-item>
          </a-list>
        </a-card>

        <!-- 持仓风险详情 -->
        <a-card title="持仓风险详情" :bordered="false">
          <a-table
            :columns="riskColumns"
            :data-source="aiAnalysis.position_risks"
            :pagination="false"
            size="small"
          >
            <template #risk_score="{ text, record }">
              <a-progress :percent="text" size="small" :stroke-color="getRiskColor(record.risk_level)" />
            </template>
            <template #risk_level="{ text }">
              <a-tag :color="getRiskColor(text)">{{ getRiskText(text) }}</a-tag>
            </template>
          </a-table>
        </a-card>
      </div>

      <div v-else-if="aiLoading" style="text-align: center; padding: 40px;">
        <a-spin size="large" />
        <div style="margin-top: 16px;">AI正在分析您的持仓数据...</div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined, RobotOutlined } from '@ant-design/icons-vue'
import * as echarts from 'echarts'

const loading = ref(false)
const aiLoading = ref(false)
const aiModalVisible = ref(false)
const dashboardData = ref(null)
const aiAnalysis = ref(null)
let positionChart = null

const positionColumns = [
  { title: '产品ID', dataIndex: 'instId', key: 'instId' },
  { title: '方向', dataIndex: 'posSide', key: 'posSide' },
  { title: '持仓量', dataIndex: 'pos', key: 'pos' },
  { title: '未实现盈亏', dataIndex: 'upl', key: 'upl' },
  { title: '杠杆', dataIndex: 'lever', key: 'lever' }
]

const orderColumns = [
  { title: '产品ID', dataIndex: 'instId', key: 'instId' },
  { title: '方向', dataIndex: 'side', key: 'side' },
  { title: '价格', dataIndex: 'px', key: 'px' },
  { title: '数量', dataIndex: 'sz', key: 'sz' },
  { title: '状态', dataIndex: 'state', key: 'state' }
]

const riskColumns = [
  { title: '产品ID', dataIndex: 'instId', key: 'instId' },
  { title: '风险评分', dataIndex: 'risk_score', key: 'risk_score' },
  { title: '风险等级', dataIndex: 'risk_level', key: 'risk_level' },
  { title: '建议', dataIndex: 'recommendation', key: 'recommendation' }
]

function refreshData() {
  loading.value = true
  fetch('/api/okx/dashboard', {
    headers: { 'Authorization': 'Bearer ' + localStorage.getItem('token') }
  })
    .then(res => res.json())
    .then(res => {
      if (res.code === '0') {
        dashboardData.value = res.data
        nextTick(() => {
          renderPositionChart()
        })
        message.success('数据刷新成功')
      } else {
        message.error(res.msg || '刷新失败')
      }
    })
    .catch(() => {
      message.error('网络异常')
    })
    .finally(() => {
      loading.value = false
    })
}

function showAIAnalysis() {
  aiModalVisible.value = true
  aiLoading.value = true
  aiAnalysis.value = null

  fetch('/api/ai/comprehensive-risk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + localStorage.getItem('token')
    },
    body: JSON.stringify({
      include_positions: true,
      include_orders: true,
      include_account: true,
      analysis_type: 'comprehensive'
    })
  })
    .then(res => res.json())
    .then(res => {
      if (res.code === '0') {
        aiAnalysis.value = res.data
        message.success('AI风控分析完成')
      } else {
        message.error(res.msg || 'AI分析失败')
      }
    })
    .catch(() => {
      message.error('网络异常')
    })
    .finally(() => {
      aiLoading.value = false
    })
}

function renderPositionChart() {
  if (!dashboardData.value) return

  const container = document.querySelector('.dashboard .ant-col-8')
  if (!container) return

  if (positionChart) {
    positionChart.dispose()
  }

  positionChart = echarts.init(container)

  const breakdown = dashboardData.value.summary.position_breakdown
  const chartData = Object.keys(breakdown).map(key => ({
    name: key,
    value: breakdown[key].count
  }))

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '持仓分布',
        type: 'pie',
        radius: '50%',
        data: chartData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  positionChart.setOption(option)
}

function getRiskColor(riskScore) {
  if (riskScore > 70) return '#ff4d4f'
  if (riskScore > 30) return '#faad14'
  return '#52c41a'
}

function getRiskText(riskScore) {
  if (riskScore > 70) return '高风险'
  if (riskScore > 30) return '中风险'
  return '低风险'
}

function getRecommendationColor(rec) {
  if (rec.includes('减仓') || rec.includes('止损')) return 'red'
  if (rec.includes('关注')) return 'orange'
  if (rec.includes('加仓')) return 'green'
  return 'blue'
}

function formatTime(timestamp) {
  return new Date(timestamp).toLocaleString('zh-CN')
}

onMounted(() => {
  refreshData()
})

onUnmounted(() => {
  if (positionChart) positionChart.dispose()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-analysis-content {
  max-height: 600px;
  overflow-y: auto;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>