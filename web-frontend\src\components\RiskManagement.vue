<template>
  <div class="risk-management">
    <!-- 风险概览 -->
    <div class="risk-overview">
      <div class="overview-header">
        <h3>风险概览</h3>
        <a-button size="small" @click="refreshRiskData">
          <ReloadOutlined :spin="loading" />
        </a-button>
      </div>
      
      <div class="risk-metrics">
        <!-- 总体风险等级 -->
        <div class="risk-level-card">
          <div class="risk-level-header">
            <span>总体风险等级</span>
            <span class="risk-level" :class="`risk-${overallRisk.level}`">
              {{ getRiskLevelText(overallRisk.level) }}
            </span>
          </div>
          <a-progress 
            :percent="overallRisk.score" 
            :stroke-color="getRiskColor(overallRisk.level)"
            :show-info="false"
          />
          <div class="risk-description">
            {{ overallRisk.description }}
          </div>
        </div>
        
        <!-- 风险指标 -->
        <div class="risk-indicators">
          <div class="indicator-grid">
            <div class="indicator-item" v-for="(indicator, key) in riskIndicators" :key="key">
              <div class="indicator-icon">
                <component :is="getIndicatorIcon(key)" />
              </div>
              <div class="indicator-content">
                <div class="indicator-name">{{ getIndicatorName(key) }}</div>
                <div class="indicator-value" :class="`risk-${indicator.level}`">
                  {{ formatIndicatorValue(key, indicator.value) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 仓位管理 -->
    <div class="position-management">
      <div class="section-header">
        <h3>仓位管理</h3>
        <a-button size="small" @click="showPositionSettings = true">
          <SettingOutlined />
        </a-button>
      </div>
      
      <div class="position-content">
        <!-- 当前仓位 -->
        <div class="current-positions">
          <div class="position-summary">
            <div class="summary-item">
              <span>总仓位价值</span>
              <span class="value">{{ formatCurrency(totalPositionValue) }}</span>
            </div>
            <div class="summary-item">
              <span>可用保证金</span>
              <span class="value">{{ formatCurrency(availableMargin) }}</span>
            </div>
            <div class="summary-item">
              <span>保证金使用率</span>
              <span class="value" :class="getMarginUsageClass(marginUsage)">
                {{ (marginUsage * 100).toFixed(2) }}%
              </span>
            </div>
          </div>
          
          <!-- 仓位列表 -->
          <div class="positions-list">
            <div class="position-item" v-for="position in positions" :key="position.symbol">
              <div class="position-header">
                <span class="symbol">{{ position.symbol }}</span>
                <span class="side" :class="`side-${position.side}`">
                  {{ position.side === 'long' ? '多头' : '空头' }}
                </span>
              </div>
              <div class="position-details">
                <div class="detail-row">
                  <span>数量</span>
                  <span>{{ position.size }}</span>
                </div>
                <div class="detail-row">
                  <span>开仓价</span>
                  <span>{{ position.entryPrice }}</span>
                </div>
                <div class="detail-row">
                  <span>当前价</span>
                  <span>{{ position.currentPrice }}</span>
                </div>
                <div class="detail-row">
                  <span>盈亏</span>
                  <span :class="getPnlClass(position.pnl)">
                    {{ formatCurrency(position.pnl) }}
                  </span>
                </div>
                <div class="detail-row">
                  <span>风险度</span>
                  <span :class="`risk-${position.riskLevel}`">
                    {{ getRiskLevelText(position.riskLevel) }}
                  </span>
                </div>
              </div>
              <div class="position-actions">
                <a-button size="small" @click="adjustPosition(position)">
                  调整
                </a-button>
                <a-button size="small" danger @click="closePosition(position)">
                  平仓
                </a-button>
              </div>
            </div>
            
            <div class="no-positions" v-if="positions.length === 0">
              <EmptyIcon />
              <p>暂无持仓</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 止损止盈设置 -->
    <div class="stop-loss-take-profit">
      <div class="section-header">
        <h3>止损止盈</h3>
        <a-button size="small" @click="showStopLossSettings = true">
          <PlusOutlined />
        </a-button>
      </div>
      
      <div class="stop-orders">
        <div class="order-item" v-for="order in stopOrders" :key="order.id">
          <div class="order-header">
            <span class="order-type" :class="`type-${order.type}`">
              {{ getOrderTypeText(order.type) }}
            </span>
            <span class="order-symbol">{{ order.symbol }}</span>
          </div>
          <div class="order-details">
            <div class="detail-row">
              <span>触发价</span>
              <span>{{ order.triggerPrice }}</span>
            </div>
            <div class="detail-row">
              <span>执行价</span>
              <span>{{ order.orderPrice }}</span>
            </div>
            <div class="detail-row">
              <span>数量</span>
              <span>{{ order.size }}</span>
            </div>
            <div class="detail-row">
              <span>状态</span>
              <span :class="`status-${order.status}`">
                {{ getOrderStatusText(order.status) }}
              </span>
            </div>
          </div>
          <div class="order-actions">
            <a-button size="small" @click="editStopOrder(order)">
              编辑
            </a-button>
            <a-button size="small" danger @click="cancelStopOrder(order.id)">
              取消
            </a-button>
          </div>
        </div>
        
        <div class="no-orders" v-if="stopOrders.length === 0">
          <EmptyIcon />
          <p>暂无止损止盈订单</p>
        </div>
      </div>
    </div>
    
    <!-- 风险控制规则 -->
    <div class="risk-control-rules">
      <div class="section-header">
        <h3>风险控制规则</h3>
        <a-switch 
          v-model:checked="riskControlEnabled" 
          @change="toggleRiskControl"
        />
      </div>
      
      <div class="rules-content" v-if="riskControlEnabled">
        <div class="rule-item" v-for="rule in riskRules" :key="rule.id">
          <div class="rule-header">
            <span class="rule-name">{{ rule.name }}</span>
            <a-switch 
              v-model:checked="rule.enabled" 
              size="small"
              @change="updateRiskRule(rule)"
            />
          </div>
          <div class="rule-description">{{ rule.description }}</div>
          <div class="rule-parameters" v-if="rule.enabled">
            <div class="parameter-item" v-for="param in rule.parameters" :key="param.key">
              <span class="param-name">{{ param.name }}</span>
              <a-input-number 
                v-model:value="param.value"
                :min="param.min"
                :max="param.max"
                :step="param.step"
                :precision="param.precision"
                size="small"
                @change="updateRiskRule(rule)"
              />
              <span class="param-unit">{{ param.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 风险报告 -->
    <div class="risk-report">
      <div class="section-header">
        <h3>风险报告</h3>
        <a-button size="small" @click="generateRiskReport">
          <FileTextOutlined />
        </a-button>
      </div>
      
      <div class="report-content">
        <!-- 风险分析图表 -->
        <div class="risk-chart">
          <ChartComponent
            :data="riskChartData"
            :options="riskChartOptions"
            :height="200"
          />
        </div>
        
        <!-- 风险建议 -->
        <div class="risk-suggestions">
          <div class="suggestion-item" v-for="suggestion in riskSuggestions" :key="suggestion.id">
            <div class="suggestion-header">
              <span class="suggestion-icon" :class="`priority-${suggestion.priority}`">
                {{ getSuggestionIcon(suggestion.priority) }}
              </span>
              <span class="suggestion-title">{{ suggestion.title }}</span>
            </div>
            <div class="suggestion-content">{{ suggestion.content }}</div>
            <div class="suggestion-actions" v-if="suggestion.actions">
              <a-button 
                size="small" 
                v-for="action in suggestion.actions" 
                :key="action.key"
                @click="executeSuggestionAction(action)"
              >
                {{ action.label }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 模态框 -->
    <PositionSettingsModal 
      v-model:visible="showPositionSettings"
      :settings="positionSettings"
      @update="updatePositionSettings"
    />
    
    <StopLossSettingsModal 
      v-model:visible="showStopLossSettings"
      :positions="positions"
      @submit="createStopOrder"
    />
    
    <PositionAdjustModal 
      v-model:visible="showPositionAdjust"
      :position="selectedPosition"
      @submit="handlePositionAdjust"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  ReloadOutlined,
  SettingOutlined,
  PlusOutlined,
  FileTextOutlined,
  DollarOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  AlertOutlined,
  ShieldOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue'

import ChartComponent from './ChartComponent.vue'
import PositionSettingsModal from './PositionSettingsModal.vue'
import StopLossSettingsModal from './StopLossSettingsModal.vue'
import PositionAdjustModal from './PositionAdjustModal.vue'
import EmptyIcon from './EmptyIcon.vue'

import { useRiskManagement } from '@/composables/useRiskManagement'
import { usePositionManagement } from '@/composables/usePositionManagement'
import { useOrderManagement } from '@/composables/useOrderManagement'

// Props
const props = defineProps({
  accountData: {
    type: Object,
    default: () => ({})
  },
  marketData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['position-adjust', 'order-create', 'risk-alert'])

// Composables
const {
  riskIndicators,
  overallRisk,
  riskRules,
  riskControlEnabled,
  calculateRisk,
  updateRiskRule,
  toggleRiskControl,
  generateRiskReport
} = useRiskManagement()

const {
  positions,
  totalPositionValue,
  availableMargin,
  marginUsage,
  positionSettings,
  adjustPosition: adjustPositionSize,
  closePosition: closePositionNow,
  updatePositionSettings
} = usePositionManagement()

const {
  stopOrders,
  createStopOrder: createStopLossOrder,
  cancelStopOrder,
  editStopOrder
} = useOrderManagement()

// 响应式数据
const loading = ref(false)
const showPositionSettings = ref(false)
const showStopLossSettings = ref(false)
const showPositionAdjust = ref(false)
const selectedPosition = ref(null)

// 风险报告数据
const riskChartData = ref([])
const riskSuggestions = ref([])

// 风险图表配置
const riskChartOptions = computed(() => ({
  animation: false,
  backgroundColor: 'transparent',
  grid: {
    left: '10%',
    right: '10%',
    top: '10%',
    bottom: '15%'
  },
  xAxis: {
    type: 'category',
    data: riskChartData.value.map(item => item.date),
    axisLine: { lineStyle: { color: '#8392A5' } }
  },
  yAxis: {
    type: 'value',
    axisLine: { lineStyle: { color: '#8392A5' } }
  },
  series: [
    {
      name: '风险得分',
      type: 'line',
      data: riskChartData.value.map(item => item.riskScore),
      lineStyle: { color: '#ff4d4f' },
      areaStyle: { color: 'rgba(255, 77, 79, 0.1)' }
    },
    {
      name: '收益率',
      type: 'line',
      data: riskChartData.value.map(item => item.returnRate),
      lineStyle: { color: '#52c41a' }
    }
  ],
  tooltip: {
    trigger: 'axis',
    formatter: (params) => {
      let result = params[0].axisValue + '<br/>'
      params.forEach(param => {
        result += `${param.seriesName}: ${param.value}<br/>`
      })
      return result
    }
  }
}))

// 方法
const refreshRiskData = async () => {
  loading.value = true
  try {
    await calculateRisk({
      positions: positions.value,
      marketData: props.marketData,
      accountData: props.accountData
    })
    
    // 生成风险建议
    generateRiskSuggestions()
  } catch (error) {
    console.error('刷新风险数据失败:', error)
    message.error('刷新风险数据失败')
  } finally {
    loading.value = false
  }
}

const generateRiskSuggestions = () => {
  const suggestions = []
  
  // 基于风险等级生成建议
  if (overallRisk.value.level === 'high') {
    suggestions.push({
      id: 'reduce-position',
      priority: 'high',
      title: '建议减少仓位',
      content: '当前风险等级较高，建议适当减少仓位以降低风险。',
      actions: [
        { key: 'reduce-50', label: '减仓50%' },
        { key: 'reduce-30', label: '减仓30%' }
      ]
    })
  }
  
  // 基于保证金使用率生成建议
  if (marginUsage.value > 0.8) {
    suggestions.push({
      id: 'margin-warning',
      priority: 'high',
      title: '保证金使用率过高',
      content: '当前保证金使用率超过80%，存在强制平仓风险。',
      actions: [
        { key: 'add-margin', label: '增加保证金' },
        { key: 'reduce-position', label: '减少仓位' }
      ]
    })
  }
  
  // 基于仓位集中度生成建议
  const symbolConcentration = calculateSymbolConcentration()
  if (symbolConcentration > 0.6) {
    suggestions.push({
      id: 'diversify',
      priority: 'medium',
      title: '建议分散投资',
      content: '当前仓位过于集中在少数品种，建议分散投资以降低风险。',
      actions: [
        { key: 'diversify', label: '查看分散建议' }
      ]
    })
  }
  
  // 基于止损设置生成建议
  const positionsWithoutStopLoss = positions.value.filter(pos => 
    !stopOrders.value.some(order => 
      order.symbol === pos.symbol && order.type === 'stop-loss'
    )
  )
  
  if (positionsWithoutStopLoss.length > 0) {
    suggestions.push({
      id: 'set-stop-loss',
      priority: 'medium',
      title: '设置止损订单',
      content: `有${positionsWithoutStopLoss.length}个仓位未设置止损，建议设置止损以控制风险。`,
      actions: [
        { key: 'set-stop-loss', label: '批量设置止损' }
      ]
    })
  }
  
  riskSuggestions.value = suggestions
}

const calculateSymbolConcentration = () => {
  if (positions.value.length === 0) return 0
  
  const symbolValues = {}
  let totalValue = 0
  
  positions.value.forEach(position => {
    const value = Math.abs(position.size * position.currentPrice)
    symbolValues[position.symbol] = (symbolValues[position.symbol] || 0) + value
    totalValue += value
  })
  
  const maxSymbolValue = Math.max(...Object.values(symbolValues))
  return totalValue > 0 ? maxSymbolValue / totalValue : 0
}

const adjustPosition = (position) => {
  selectedPosition.value = position
  showPositionAdjust.value = true
}

const closePosition = (position) => {
  Modal.confirm({
    title: '确认平仓',
    content: `确定要平仓 ${position.symbol} 吗？`,
    onOk: async () => {
      try {
        await closePositionNow(position)
        message.success('平仓成功')
        emit('position-adjust', { action: 'close', position })
      } catch (error) {
        console.error('平仓失败:', error)
        message.error('平仓失败')
      }
    }
  })
}

const handlePositionAdjust = async (adjustData) => {
  try {
    await adjustPositionSize(selectedPosition.value, adjustData)
    message.success('仓位调整成功')
    showPositionAdjust.value = false
    emit('position-adjust', { action: 'adjust', position: selectedPosition.value, data: adjustData })
  } catch (error) {
    console.error('仓位调整失败:', error)
    message.error('仓位调整失败')
  }
}

const createStopOrder = async (orderData) => {
  try {
    await createStopLossOrder(orderData)
    message.success('止损订单创建成功')
    showStopLossSettings.value = false
    emit('order-create', orderData)
  } catch (error) {
    console.error('创建止损订单失败:', error)
    message.error('创建止损订单失败')
  }
}

const executeSuggestionAction = (action) => {
  switch (action.key) {
    case 'reduce-50':
      executeReducePosition(0.5)
      break
    case 'reduce-30':
      executeReducePosition(0.3)
      break
    case 'add-margin':
      // 跳转到资金管理页面
      break
    case 'diversify':
      // 显示分散投资建议
      break
    case 'set-stop-loss':
      showStopLossSettings.value = true
      break
  }
}

const executeReducePosition = (ratio) => {
  Modal.confirm({
    title: '确认减仓',
    content: `确定要减少${(ratio * 100).toFixed(0)}%的仓位吗？`,
    onOk: async () => {
      try {
        for (const position of positions.value) {
          await adjustPositionSize(position, {
            action: 'reduce',
            ratio: ratio
          })
        }
        message.success('减仓成功')
        await refreshRiskData()
      } catch (error) {
        console.error('减仓失败:', error)
        message.error('减仓失败')
      }
    }
  })
}

// 格式化函数
const formatCurrency = (value) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(value)
}

const formatIndicatorValue = (key, value) => {
  switch (key) {
    case 'volatility':
    case 'correlation':
    case 'sharpe':
      return (value * 100).toFixed(2) + '%'
    case 'var':
    case 'maxDrawdown':
      return formatCurrency(value)
    default:
      return value.toFixed(4)
  }
}

const getRiskLevelText = (level) => {
  const levels = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return levels[level] || level
}

const getRiskColor = (level) => {
  const colors = {
    low: '#52c41a',
    medium: '#faad14',
    high: '#ff4d4f'
  }
  return colors[level] || '#d9d9d9'
}

const getIndicatorName = (key) => {
  const names = {
    volatility: '波动率',
    var: '风险价值',
    maxDrawdown: '最大回撤',
    correlation: '相关性',
    sharpe: '夏普比率',
    leverage: '杠杆率'
  }
  return names[key] || key
}

const getIndicatorIcon = (key) => {
  const icons = {
    volatility: TrendingUpOutlined,
    var: DollarOutlined,
    maxDrawdown: TrendingDownOutlined,
    correlation: BarChartOutlined,
    sharpe: ShieldOutlined,
    leverage: AlertOutlined
  }
  return icons[key] || AlertOutlined
}

const getMarginUsageClass = (usage) => {
  if (usage >= 0.8) return 'risk-high'
  if (usage >= 0.6) return 'risk-medium'
  return 'risk-low'
}

const getPnlClass = (pnl) => {
  return pnl >= 0 ? 'pnl-positive' : 'pnl-negative'
}

const getOrderTypeText = (type) => {
  const types = {
    'stop-loss': '止损',
    'take-profit': '止盈',
    'trailing-stop': '跟踪止损'
  }
  return types[type] || type
}

const getOrderStatusText = (status) => {
  const statuses = {
    active: '活跃',
    triggered: '已触发',
    cancelled: '已取消',
    filled: '已成交'
  }
  return statuses[status] || status
}

const getSuggestionIcon = (priority) => {
  const icons = {
    high: '🚨',
    medium: '⚠️',
    low: 'ℹ️'
  }
  return icons[priority] || 'ℹ️'
}

// 监听数据变化
watch(() => props.accountData, (newData) => {
  if (newData) {
    refreshRiskData()
  }
}, { deep: true })

watch(() => props.marketData, (newData) => {
  if (newData) {
    refreshRiskData()
  }
}, { deep: true })

watch(() => positions.value, () => {
  generateRiskSuggestions()
}, { deep: true })

// 生命周期
onMounted(() => {
  refreshRiskData()
  
  // 定期刷新风险数据
  const interval = setInterval(refreshRiskData, 1000) // 1秒
  
  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>

<style scoped>
.risk-management {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: var(--bg-color);
}

.risk-overview,
.position-management,
.stop-loss-take-profit,
.risk-control-rules,
.risk-report {
  background: var(--bg-color-light);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.overview-header,
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.overview-header h3,
.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.risk-metrics {
  padding: 16px;
}

.risk-level-card {
  padding: 16px;
  background: var(--bg-color);
  border-radius: 8px;
  margin-bottom: 16px;
}

.risk-level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.risk-level {
  font-size: 16px;
  font-weight: bold;
}

.risk-low {
  color: #52c41a;
}

.risk-medium {
  color: #faad14;
}

.risk-high {
  color: #ff4d4f;
}

.risk-description {
  margin-top: 8px;
  font-size: 12px;
  color: var(--text-color-secondary);
}

.indicator-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.indicator-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--bg-color);
  border-radius: 6px;
}

.indicator-icon {
  font-size: 20px;
  color: var(--primary-color);
}

.indicator-content {
  flex: 1;
}

.indicator-name {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin-bottom: 4px;
}

.indicator-value {
  font-size: 14px;
  font-weight: 600;
}

.position-content {
  padding: 16px;
}

.position-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.summary-item span:first-child {
  font-size: 12px;
  color: var(--text-color-secondary);
}

.summary-item .value {
  font-size: 16px;
  font-weight: 600;
}

.positions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.position-item {
  padding: 16px;
  background: var(--bg-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.position-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.symbol {
  font-size: 16px;
  font-weight: 600;
}

.side {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.side-long {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.side-short {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.position-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.detail-row span:first-child {
  color: var(--text-color-secondary);
}

.pnl-positive {
  color: #52c41a;
}

.pnl-negative {
  color: #ff4d4f;
}

.position-actions {
  display: flex;
  gap: 8px;
}

.stop-orders {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-item {
  padding: 16px;
  background: var(--bg-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.order-type {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.type-stop-loss {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.type-take-profit {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.type-trailing-stop {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.order-symbol {
  font-weight: 600;
}

.order-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
  margin-bottom: 12px;
}

.order-actions {
  display: flex;
  gap: 8px;
}

.status-active {
  color: #52c41a;
}

.status-triggered {
  color: #faad14;
}

.status-cancelled {
  color: #d9d9d9;
}

.status-filled {
  color: #1890ff;
}

.rules-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rule-item {
  padding: 16px;
  background: var(--bg-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rule-name {
  font-weight: 600;
}

.rule-description {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin-bottom: 12px;
}

.rule-parameters {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.parameter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-name {
  min-width: 80px;
  font-size: 12px;
  color: var(--text-color-secondary);
}

.param-unit {
  font-size: 12px;
  color: var(--text-color-secondary);
}

.report-content {
  padding: 16px;
}

.risk-chart {
  margin-bottom: 16px;
}

.risk-suggestions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.suggestion-item {
  padding: 16px;
  background: var(--bg-color);
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.suggestion-icon {
  font-size: 16px;
}

.priority-high .suggestion-icon {
  color: #ff4d4f;
}

.priority-medium .suggestion-icon {
  color: #faad14;
}

.priority-low .suggestion-icon {
  color: #1890ff;
}

.suggestion-title {
  font-weight: 600;
}

.suggestion-content {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin-bottom: 12px;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
}

.no-positions,
.no-orders {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-color-secondary);
}

.no-positions p,
.no-orders p {
  margin: 8px 0 0 0;
  font-size: 14px;
}
</style>