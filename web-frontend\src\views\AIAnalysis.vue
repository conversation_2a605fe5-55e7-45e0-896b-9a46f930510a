<template>
  <div>
    <a-form layout="inline" @submit.prevent="onGenerate">
      <a-form-item label="类型">
        <a-select v-model="form.type" style="width:120px">
          <a-select-option value="行情">行情</a-select-option>
          <a-select-option value="新闻">新闻</a-select-option>
          <a-select-option value="情绪">情绪</a-select-option>
          <a-select-option value="链上">链上</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="内容">
        <a-input v-model="form.content" style="width:200px" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" html-type="submit" :loading="loading">生成AI分析</a-button>
      </a-form-item>
    </a-form>
    <a-table :dataSource="analyses" :columns="columns" rowKey="id" style="margin-top:20px" />
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { message } from 'ant-design-vue'

const analyses = ref([])
const columns = [
  { title: 'ID', dataIndex: 'id' },
  { title: '类型', dataIndex: 'type' },
  { title: '内容', dataIndex: 'content' },
  { title: '建议', dataIndex: 'suggestion' },
  { title: '创建时间', dataIndex: 'created_at' }
]

const form = ref({ type: '行情', content: '' })
const loading = ref(false)

const fetchAnalyses = async () => {
  try {
    const res = await axios.get('/api/ai/list')
    if (res.data.code === 0) {
      analyses.value = res.data.data
    }
  } catch (error) {
    console.warn('AI分析列表API不可用:', error.message)
    // 不显示错误消息，只在控制台记录
  }
}

const onGenerate = async () => {
  if (!form.value.content.trim()) {
    message.warning('请输入分析内容')
    return
  }

  loading.value = true
  try {
    const res = await axios.post('/api/ai/generate', form.value)
    if (res.data.code === 0) {
      message.success('生成成功')
      form.value.content = ''
      fetchAnalyses()
    } else {
      message.error(res.data.msg || '生成失败')
    }
  } catch (error) {
    console.warn('AI生成API不可用:', error.message)
    message.warning('AI服务暂时不可用，请稍后再试')
  }
  loading.value = false
}

onMounted(fetchAnalyses)
</script>