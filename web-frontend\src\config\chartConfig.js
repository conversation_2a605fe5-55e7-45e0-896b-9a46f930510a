/**
 * 图表配置管理
 * 统一管理图表相关的配置参数，支持动态调整和环境适配
 */

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
  // 数据更新配置
  update: {
    // K线数据更新间隔（毫秒）
    klineInterval: 30000, // 30秒
    // 价格数据更新间隔（毫秒）
    priceInterval: 5000, // 5秒
    // 倒计时更新间隔（毫秒）
    countdownInterval: 5000, // 5秒
    // 实时数据更新间隔（毫秒）
    realtimeInterval: 1000, // 1秒
    // 批量更新大小
    batchSize: 100,
    // 最大重试次数
    maxRetries: 3
  },
  
  // 性能配置
  performance: {
    // 图表渲染性能阈值（毫秒）
    renderThreshold: 100,
    // 内存使用警告阈值（比例）
    memoryThreshold: 0.8,
    // 最低帧率阈值
    minFrameRate: 30,
    // API响应时间阈值（毫秒）
    apiThreshold: 2000,
    // 启用性能监控
    enableMonitoring: true,
    // 启用自动优化
    enableAutoOptimization: true
  },
  
  // 图表显示配置
  chart: {
    // 默认显示的K线数量
    defaultCandleCount: 200,
    // 最大显示的K线数量
    maxCandleCount: 1000,
    // 图表缓存大小
    cacheSize: 500,
    // 启用图表动画
    enableAnimation: true,
    // 动画持续时间（毫秒）
    animationDuration: 300,
    // 启用数据压缩
    enableDataCompression: true,
    // 启用懒加载
    enableLazyLoading: true
  },
  
  // 错误处理配置
  error: {
    // 启用错误边界
    enableErrorBoundary: true,
    // 错误重试间隔（毫秒）
    retryInterval: 1000,
    // 最大错误计数
    maxErrorCount: 10,
    // 启用错误上报
    enableErrorReporting: false,
    // 错误恢复策略
    recoveryStrategy: 'graceful' // 'graceful' | 'aggressive' | 'manual'
  },
  
  // 内存管理配置
  memory: {
    // 启用自动内存清理
    enableAutoCleanup: true,
    // 内存清理间隔（毫秒）
    cleanupInterval: 300000, // 5分钟
    // 数据保留时间（毫秒）
    dataRetentionTime: 3600000, // 1小时
    // 最大缓存条目数
    maxCacheEntries: 1000,
    // 启用弱引用
    enableWeakReferences: true
  },
  
  // 网络配置
  network: {
    // 请求超时时间（毫秒）
    timeout: 10000,
    // 并发请求限制
    concurrencyLimit: 5,
    // 启用请求缓存
    enableRequestCache: true,
    // 缓存过期时间（毫秒）
    cacheExpiration: 60000, // 1分钟
    // 启用请求去重
    enableRequestDeduplication: true
  },
  
  // 开发配置
  development: {
    // 启用调试模式
    enableDebug: process.env.NODE_ENV === 'development',
    // 启用详细日志
    enableVerboseLogging: false,
    // 启用性能分析
    enableProfiling: false,
    // 模拟网络延迟（毫秒）
    simulateNetworkDelay: 0
  }
}

/**
 * 环境特定配置
 */
const ENVIRONMENT_CONFIGS = {
  development: {
    update: {
      klineInterval: 10000, // 开发环境更频繁更新
      priceInterval: 2000
    },
    performance: {
      enableMonitoring: true
    },
    development: {
      enableDebug: true,
      enableVerboseLogging: true
    }
  },
  
  production: {
    update: {
      klineInterval: 30000,
      priceInterval: 5000
    },
    performance: {
      enableMonitoring: false // 生产环境可选择关闭监控
    },
    development: {
      enableDebug: false,
      enableVerboseLogging: false
    }
  },
  
  testing: {
    update: {
      klineInterval: 1000, // 测试环境快速更新
      priceInterval: 500
    },
    performance: {
      enableMonitoring: true,
      enableAutoOptimization: false // 测试时禁用自动优化
    }
  }
}

/**
 * 配置管理器
 */
class ChartConfigManager {
  constructor() {
    this.config = this.loadConfig()
    this.listeners = new Set()
    this.overrides = new Map()
  }
  
  /**
   * 加载配置
   */
  loadConfig() {
    const baseConfig = { ...DEFAULT_CONFIG }
    const environment = process.env.NODE_ENV || 'development'
    const envConfig = ENVIRONMENT_CONFIGS[environment] || {}
    
    // 深度合并配置
    return this.deepMerge(baseConfig, envConfig)
  }
  
  /**
   * 深度合并对象
   */
  deepMerge(target, source) {
    const result = { ...target }
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key])
      } else {
        result[key] = source[key]
      }
    }
    
    return result
  }
  
  /**
   * 获取配置值
   * @param {string} path - 配置路径，如 'update.klineInterval'
   * @param {*} defaultValue - 默认值
   */
  get(path, defaultValue = undefined) {
    // 检查是否有临时覆盖
    if (this.overrides.has(path)) {
      return this.overrides.get(path)
    }
    
    const keys = path.split('.')
    let value = this.config
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key]
      } else {
        return defaultValue
      }
    }
    
    return value
  }
  
  /**
   * 设置配置值
   * @param {string} path - 配置路径
   * @param {*} value - 配置值
   * @param {boolean} temporary - 是否为临时设置
   */
  set(path, value, temporary = false) {
    if (temporary) {
      this.overrides.set(path, value)
    } else {
      const keys = path.split('.')
      let current = this.config
      
      for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i]
        if (!current[key] || typeof current[key] !== 'object') {
          current[key] = {}
        }
        current = current[key]
      }
      
      current[keys[keys.length - 1]] = value
    }
    
    // 通知监听器
    this.notifyListeners(path, value)
  }
  
  /**
   * 批量设置配置
   * @param {Object} configs - 配置对象
   * @param {boolean} temporary - 是否为临时设置
   */
  setBatch(configs, temporary = false) {
    for (const [path, value] of Object.entries(configs)) {
      this.set(path, value, temporary)
    }
  }
  
  /**
   * 清除临时覆盖
   * @param {string} path - 配置路径，不提供则清除所有
   */
  clearOverrides(path = null) {
    if (path) {
      this.overrides.delete(path)
      this.notifyListeners(path, this.get(path))
    } else {
      const paths = Array.from(this.overrides.keys())
      this.overrides.clear()
      paths.forEach(p => this.notifyListeners(p, this.get(p)))
    }
  }
  
  /**
   * 监听配置变化
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }
  
  /**
   * 通知监听器
   */
  notifyListeners(path, value) {
    this.listeners.forEach(listener => {
      try {
        listener(path, value)
      } catch (error) {
        console.error('配置监听器执行错误:', error)
      }
    })
  }
  
  /**
   * 获取所有配置
   */
  getAll() {
    const result = { ...this.config }
    
    // 应用临时覆盖
    for (const [path, value] of this.overrides) {
      const keys = path.split('.')
      let current = result
      
      for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i]
        if (!current[key]) current[key] = {}
        current = current[key]
      }
      
      current[keys[keys.length - 1]] = value
    }
    
    return result
  }
  
  /**
   * 重置配置
   */
  reset() {
    this.config = this.loadConfig()
    this.overrides.clear()
    this.notifyListeners('*', this.config)
  }
  
  /**
   * 根据性能情况自动调整配置
   * @param {Object} performanceMetrics - 性能指标
   */
  autoOptimize(performanceMetrics) {
    if (!this.get('performance.enableAutoOptimization')) {
      return
    }
    
    const optimizations = []
    
    // 根据帧率调整更新频率
    if (performanceMetrics.frameRate && performanceMetrics.frameRate.current < this.get('performance.minFrameRate')) {
      const currentInterval = this.get('update.klineInterval')
      const newInterval = Math.min(currentInterval * 1.5, 60000) // 最大60秒
      
      this.set('update.klineInterval', newInterval, true)
      optimizations.push(`K线更新间隔调整为 ${newInterval}ms`)
    }
    
    // 根据内存使用情况调整缓存
    if (performanceMetrics.memory && performanceMetrics.memory.current > this.get('performance.memoryThreshold') * 100) {
      const currentCacheSize = this.get('chart.cacheSize')
      const newCacheSize = Math.max(currentCacheSize * 0.8, 100) // 最小100
      
      this.set('chart.cacheSize', newCacheSize, true)
      optimizations.push(`图表缓存大小调整为 ${newCacheSize}`)
    }
    
    // 根据API响应时间调整请求频率
    if (performanceMetrics.api && performanceMetrics.api.average > this.get('performance.apiThreshold')) {
      const currentInterval = this.get('update.priceInterval')
      const newInterval = Math.min(currentInterval * 1.2, 10000) // 最大10秒
      
      this.set('update.priceInterval', newInterval, true)
      optimizations.push(`价格更新间隔调整为 ${newInterval}ms`)
    }
    
    if (optimizations.length > 0) {
      console.log('自动性能优化:', optimizations.join(', '))
    }
  }
  
  /**
   * 导出配置
   */
  export() {
    return JSON.stringify(this.getAll(), null, 2)
  }
  
  /**
   * 导入配置
   * @param {string} configJson - JSON格式的配置
   */
  import(configJson) {
    try {
      const importedConfig = JSON.parse(configJson)
      this.config = this.deepMerge(DEFAULT_CONFIG, importedConfig)
      this.notifyListeners('*', this.config)
      return true
    } catch (error) {
      console.error('配置导入失败:', error)
      return false
    }
  }
}

// 创建全局配置管理器实例
const chartConfig = new ChartConfigManager()

// 导出配置管理器和常用配置获取函数
export default chartConfig

/**
 * 快捷配置获取函数
 */
export const getUpdateConfig = () => chartConfig.get('update')
export const getPerformanceConfig = () => chartConfig.get('performance')
export const getChartConfig = () => chartConfig.get('chart')
export const getErrorConfig = () => chartConfig.get('error')
export const getMemoryConfig = () => chartConfig.get('memory')
export const getNetworkConfig = () => chartConfig.get('network')
export const getDevelopmentConfig = () => chartConfig.get('development')

/**
 * 配置预设
 */
export const CONFIG_PRESETS = {
  // 高性能模式
  highPerformance: {
    'update.klineInterval': 60000,
    'update.priceInterval': 10000,
    'chart.enableAnimation': false,
    'chart.maxCandleCount': 500,
    'performance.enableAutoOptimization': true
  },
  
  // 实时模式
  realtime: {
    'update.klineInterval': 5000,
    'update.priceInterval': 1000,
    'chart.enableAnimation': true,
    'chart.maxCandleCount': 200,
    'performance.enableAutoOptimization': false
  },
  
  // 节能模式
  powerSaving: {
    'update.klineInterval': 120000,
    'update.priceInterval': 30000,
    'chart.enableAnimation': false,
    'chart.maxCandleCount': 100,
    'performance.enableMonitoring': false
  }
}

/**
 * 应用配置预设
 * @param {string} presetName - 预设名称
 */
export function applyConfigPreset(presetName) {
  const preset = CONFIG_PRESETS[presetName]
  if (preset) {
    chartConfig.setBatch(preset, true)
    console.log(`已应用配置预设: ${presetName}`)
  } else {
    console.warn(`未找到配置预设: ${presetName}`)
  }
}