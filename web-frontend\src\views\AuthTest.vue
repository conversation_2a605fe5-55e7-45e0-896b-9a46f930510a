<template>
  <div class="auth-test">
    <a-card title="认证状态测试">
      <a-space direction="vertical" style="width: 100%;">
        <a-button @click="checkToken" type="primary">检查Token</a-button>
        <a-button @click="testAPI" type="default">测试API</a-button>
        <a-button @click="clearToken" danger>清除Token</a-button>

        <a-divider />

        <div v-if="tokenInfo">
          <h3>Token信息:</h3>
          <pre>{{ JSON.stringify(tokenInfo, null, 2) }}</pre>
        </div>

        <div v-if="apiResult">
          <h3>API测试结果:</h3>
          <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
        </div>

        <div v-if="error">
          <h3>错误信息:</h3>
          <pre style="color: red;">{{ error }}</pre>
        </div>
      </a-space>
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import axios from 'axios'

const tokenInfo = ref(null)
const apiResult = ref(null)
const error = ref('')

const checkToken = () => {
  const token = localStorage.getItem('token')
  const userInfo = localStorage.getItem('userInfo')

  tokenInfo.value = {
    hasToken: !!token,
    tokenLength: token ? token.length : 0,
    tokenPreview: token ? token.substring(0, 50) + '...' : null,
    userInfo: userInfo ? JSON.parse(userInfo) : null,
    localStorage: {
      token: !!localStorage.getItem('token'),
      userInfo: !!localStorage.getItem('userInfo')
    }
  }

  message.info('Token信息已更新')
}

const testAPI = async () => {
  try {
    error.value = ''

    // 测试健康检查
    const healthResponse = await axios.get('/health')

    // 测试AI预测API
    const aiResponse = await axios.get('/api/ai/predictions')

    apiResult.value = {
      health: {
        status: healthResponse.status,
        data: healthResponse.data
      },
      ai: {
        status: aiResponse.status,
        data: aiResponse.data
      }
    }

    message.success('API测试成功')
  } catch (err) {
    error.value = `API测试失败: ${err.message}\n状态码: ${err.response?.status}\n响应: ${JSON.stringify(err.response?.data, null, 2)}`
    message.error('API测试失败')
  }
}

const clearToken = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
  tokenInfo.value = null
  apiResult.value = null
  error.value = ''
  message.success('Token已清除')
}

// 页面加载时自动检查
checkToken()
</script>

<style scoped>
.auth-test {
  padding: 24px;
}

pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
}
</style>
