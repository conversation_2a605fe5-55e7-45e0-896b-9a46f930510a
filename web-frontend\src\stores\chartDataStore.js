import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import { getCandlesticks } from '@/services/apiService';
import { useWebSocket } from '@/composables/useWebSocket';
import { useChartSettingsStore } from './chartSettingsStore';
import * as webSocketService from '@/services/webSocketService';

export const useChartDataStore = defineStore('chartData', () => {
  const settingsStore = useChartSettingsStore();

  // State
  const chartData = ref([]);
  const isLoading = ref(false);
  const error = ref(null);
  const currentPrice = ref(null);
  const priceChangeDirection = ref(0); // -1: down, 0: same, 1: up
  const isWebSocketConnected = ref(false);
  const lastUpdateTime = ref(null);
  const updateCount = ref(0);
  
  // WebSocket服务
  const {
    subscribe,
    unsubscribe,
    disconnect,
    connected: isConnected
  } = useWebSocket();



  // Computed
  const hasChartData = computed(() => chartData.value && chartData.value.length > 0);

  const latestPrice = computed(() => {
    if (!hasChartData.value) return null;
    const latest = chartData.value[chartData.value.length - 1];
    return {
      open: parseFloat(latest[1]),
      high: parseFloat(latest[2]),
      low: parseFloat(latest[3]),
      close: parseFloat(latest[4]),
      volume: parseFloat(latest[5])
    };
  });

  // Actions
  const setChartData = (data) => {
    console.log('chartDataStore: setChartData被调用，数据:', {
      length: data?.length,
      sample: data?.slice(0, 2),
      type: typeof data,
      isArray: Array.isArray(data)
    });
    chartData.value = data;
    error.value = null;
    console.log('chartDataStore: chartData已更新，hasChartData:', hasChartData.value);
  };

  const setLoading = (loading) => {
    isLoading.value = loading;
  };

  const setError = (errorMessage) => {
    error.value = errorMessage;
  };

  async function fetchCandlestickData() {
    console.log('🔄 开始获取K线数据...');
    console.log('🔍 当前selectedSymbol:', settingsStore.selectedSymbol);
    console.log('🔍 当前selectedTimeframe:', settingsStore.selectedTimeframe);
    setLoading(true);
    setError(null);
    
    try {
      console.log('📡 准备调用getCandlesticks API...');
      const response = await getCandlesticks(
        settingsStore.selectedSymbol,
        settingsStore.selectedTimeframe
      );
      console.log('📡 getCandlesticks API调用完成');
      
      console.log('📊 获取到K线数据长度:', response.length);
      console.log('📊 K线数据样本:', response.slice(0, 3));
      
      if (response && response.length > 0) {
        setChartData(response);
        console.log('✅ K线数据已加载，长度:', chartData.value.length);
        
        // 从最新K线数据中提取最新价格
        const latestCandle = response[response.length - 1];
        if (latestCandle && latestCandle.close) {
          currentPrice.value = {
            last: latestCandle.close,
            symbol: settingsStore.selectedSymbol,
            timestamp: Date.now()
          };
          console.log('✅ 初始价格设置:', currentPrice.value.last);
        }
      } else {
        setError('无法加载图表数据');
        console.error('Failed to fetch candlestick data: No data returned');
      }
    } catch (err) {
      console.error('❌ 获取K线数据失败:', err);
      setError('获取K线数据失败: ' + err.message);
    } finally {
      setLoading(false);
    }
  }

  // WebSocket ticker数据处理函数
  const handleTickerUpdate = (wsData) => {
    console.log('📡 收到ticker WebSocket数据:', wsData);
    
    if (!wsData.data || !Array.isArray(wsData.data) || wsData.data.length === 0) {
      return;
    }
    
    const tickerData = wsData.data[0]; // OKX ticker数据格式
    if (!tickerData || tickerData.instId !== settingsStore.selectedSymbol) {
      return;
    }
    
    const oldPrice = currentPrice.value ? parseFloat(currentPrice.value.last) : 0;
    const newPrice = parseFloat(tickerData.last);
    
    // 计算涨跌幅
    const open24h = parseFloat(tickerData.open24h);
    const change = newPrice - open24h;
    const changePercent = open24h > 0 ? (change / open24h) * 100 : 0;
    
    // 更新当前价格数据
    currentPrice.value = {
      ...tickerData,
      change: change.toFixed(2),
      changePercent: changePercent.toFixed(2),
      timestamp: wsData.timestamp || Date.now()
    };
    
    // 更新价格变化方向
    if (newPrice > oldPrice) {
      priceChangeDirection.value = 1;
    } else if (newPrice < oldPrice) {
      priceChangeDirection.value = -1;
    } else {
      priceChangeDirection.value = 0;
    }
    
    // 实时更新最新K线数据
    updateLatestCandleWithTicker(tickerData);
    
    // 更新统计信息
    lastUpdateTime.value = Date.now();
    updateCount.value++;
    
    console.log('✅ Ticker数据已更新:', {
      symbol: tickerData.instId,
      price: newPrice,
      change: change.toFixed(2),
      changePercent: changePercent.toFixed(2) + '%',
      direction: priceChangeDirection.value,
      updateCount: updateCount.value
    });
  };

  // WebSocket K线数据处理函数
  const handleCandleUpdate = (wsData) => {
    console.log('📊 收到K线 WebSocket数据:', wsData);
    
    if (!wsData.data || !Array.isArray(wsData.data) || wsData.data.length === 0) {
      return;
    }
    
    const candleData = wsData.data[0]; // OKX K线数据格式
    if (!candleData || candleData[0] === undefined) {
      return;
    }
    
    // OKX K线数据格式: [timestamp, open, high, low, close, volume, volCcy, volCcyQuote, confirm]
    const newCandle = {
      time: parseInt(candleData[0]),
      open: parseFloat(candleData[1]),
      high: parseFloat(candleData[2]),
      low: parseFloat(candleData[3]),
      close: parseFloat(candleData[4]),
      volume: parseFloat(candleData[5])
    };
    
    if (!chartData.value || chartData.value.length === 0) {
      return;
    }
    
    // 检查是否是最新K线的更新
    const latestCandle = chartData.value[chartData.value.length - 1];
    const newChartData = [...chartData.value];
    
    if (latestCandle.time === newCandle.time) {
      // 更新现有K线
      newChartData[newChartData.length - 1] = newCandle;
      console.log('🔄 更新现有K线:', newCandle);
    } else if (newCandle.time > latestCandle.time) {
      // 添加新K线
      newChartData.push(newCandle);
      console.log('➕ 添加新K线:', newCandle);
      
      // 保持数据长度，移除最旧的数据
      if (newChartData.length > 1000) {
        newChartData.shift();
      }
    }
    
    chartData.value = newChartData;
    
    console.log('✅ K线数据已更新:', {
      timestamp: new Date(newCandle.time).toLocaleTimeString(),
      price: newCandle.close,
      volume: newCandle.volume,
      totalCandles: chartData.value.length
    });
  };

  // 新增：使用ticker数据实时更新最新K线
  const updateLatestCandleWithTicker = (tickerData) => {
    if (!chartData.value || chartData.value.length === 0) {
      return;
    }

    const latestCandle = chartData.value[chartData.value.length - 1];
    if (!latestCandle) {
      return;
    }

    // 更新最新K线的收盘价、最高价、最低价
    const newPrice = parseFloat(tickerData.last);
    const currentHigh = latestCandle.high;
    const currentLow = latestCandle.low;
    
    // 创建新的K线数据（对象格式）
    const updatedCandle = {
      time: latestCandle.time, // 时间戳保持不变
      open: latestCandle.open, // 开盘价保持不变
      high: Math.max(currentHigh, newPrice), // 更新最高价
      low: Math.min(currentLow, newPrice),   // 更新最低价
      close: newPrice, // 更新收盘价
      volume: latestCandle.volume // 成交量保持不变
    };

    // 更新chartData中的最新K线
    const newChartData = [...chartData.value];
    newChartData[newChartData.length - 1] = updatedCandle;
    
    console.log('🔄 实时更新最新K线:', {
      oldPrice: latestCandle.close,
      newPrice: newPrice,
      high: updatedCandle.high,
      low: updatedCandle.low
    });
    
    chartData.value = newChartData;
  };





  // 启动WebSocket实时更新
  function startRealtimeUpdates() {
    console.log('🚀 启动WebSocket实时更新...');
    
    // 停止之前的连接
    stopRealtimeUpdates();
    
    // 订阅ticker和K线数据
    subscribe('tickers', handleTickerUpdate);
    subscribe(`candle${settingsStore.selectedTimeframe}`, handleCandleUpdate);
    
    // 连接WebSocket并订阅市场数据
    webSocketService.connectMarketData(settingsStore.selectedSymbol, settingsStore.selectedTimeframe);
    
    // 监控连接状态
    isWebSocketConnected.value = isConnected.value;
    
    console.log('✅ WebSocket实时更新已启动:', {
      symbol: settingsStore.selectedSymbol,
      timeframe: settingsStore.selectedTimeframe,
      connected: isWebSocketConnected.value
    });
  }

  // 停止WebSocket实时更新
  function stopRealtimeUpdates() {
    console.log('🛑 停止WebSocket实时更新...');
    
    // 取消订阅
    unsubscribe('tickers', handleTickerUpdate);
    unsubscribe(`candle${settingsStore.selectedTimeframe}`, handleCandleUpdate);
    
    // 断开WebSocket连接
    disconnect();
    
    isWebSocketConnected.value = false;
    console.log('✅ WebSocket实时更新已停止');
  }

  // 获取实时更新状态
  function getRealtimeStatus() {
    return {
      connected: isConnected.value,
      lastUpdateTime: lastUpdateTime.value,
      updateCount: updateCount.value,
      symbol: settingsStore.selectedSymbol,
      timeframe: settingsStore.selectedTimeframe
    };
  }

  watch([() => settingsStore.selectedSymbol, () => settingsStore.selectedTimeframe], () => {
    fetchCandlestickData();
    startRealtimeUpdates();
  }, { immediate: true });

  return {
    // 状态
    chartData,
    isLoading,
    error,
    currentPrice,
    priceChangeDirection,
    isWebSocketConnected,
    lastUpdateTime,
    updateCount,
    
    // 计算属性
    hasChartData,
    latestPrice,
    selectedSymbol: computed(() => settingsStore.selectedSymbol),
    selectedTimeframe: computed(() => settingsStore.selectedTimeframe),
    
    // 基础操作
    setChartData,
    setLoading,
    setError,
    fetchCandlestickData,
    
    // WebSocket实时更新
    startRealtimeUpdates,
    stopRealtimeUpdates,
    getRealtimeStatus,
    
    // 数据处理函数
    handleTickerUpdate,
    handleCandleUpdate,
    updateLatestCandleWithTicker
  };
});