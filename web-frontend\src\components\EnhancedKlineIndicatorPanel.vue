<template>
  <div class="enhanced-kline-indicator-panel">
    <a-card title="技术指标" size="small">
      <!-- 指标控制区域 -->
      <div class="indicator-controls">
        <a-space wrap>
          <a-button 
            type="primary" 
            size="small" 
            @click="addIndicator('RSI')"
            :disabled="hasIndicator('RSI')"
          >
            <template #icon><LineChartOutlined /></template>
            添加RSI
          </a-button>
          <a-button 
            type="primary" 
            size="small" 
            @click="addIndicator('MACD')"
            :disabled="hasIndicator('MACD')"
          >
            <template #icon><BarChartOutlined /></template>
            添加MACD
          </a-button>
          <a-button 
            type="primary" 
            size="small" 
            @click="addIndicator('KDJ')"
            :disabled="hasIndicator('KDJ')"
          >
            <template #icon><StockOutlined /></template>
            添加KDJ
          </a-button>
          <a-button 
            type="primary" 
            size="small" 
            @click="addIndicator('BOLL')"
            :disabled="hasIndicator('BOLL')"
          >
            <template #icon><FundOutlined /></template>
            添加布林带
          </a-button>
          <a-button 
            type="default" 
            size="small" 
            @click="clearAllIndicators"
            :disabled="activeIndicators.length === 0"
            danger
          >
            <template #icon><ClearOutlined /></template>
            清除所有
          </a-button>
        </a-space>
      </div>
      
      <!-- 活跃指标列表 -->
      <div class="indicator-list" v-if="activeIndicators.length > 0">
        <a-collapse v-model:activeKey="activeCollapseKeys" ghost>
          <a-collapse-panel 
            v-for="indicator in activeIndicators" 
            :key="indicator.name"
            :header="indicator.displayName"
          >
            <template #extra>
              <a-space>
                <a-tag :color="getIndicatorColor(indicator.name)">{{ indicator.name }}</a-tag>
                <a-switch 
                  v-model:checked="indicator.visible"
                  size="small"
                  @change="toggleIndicatorVisibility(indicator)"
                />
                <a-button 
                  type="text" 
                  size="small" 
                  @click.stop="removeIndicator(indicator.name)"
                  danger
                >
                  <DeleteOutlined />
                </a-button>
              </a-space>
            </template>
            
            <!-- 实时参数调整区域 -->
            <div class="real-time-config">
              <!-- RSI实时配置 -->
              <div v-if="indicator.name === 'RSI'">
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="周期" :label-col="{span: 8}">
                      <a-slider 
                        v-model:value="indicator.params[0]" 
                        :min="6" 
                        :max="30" 
                        :marks="{ 14: '14' }"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                      <a-input-number 
                        v-model:value="indicator.params[0]"
                        :min="6" 
                        :max="30"
                        size="small"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="超买线" :label-col="{span: 8}">
                      <a-slider 
                        v-model:value="indicator.overbought" 
                        :min="60" 
                        :max="90" 
                        :marks="{ 70: '70' }"
                        @change="updateIndicatorStyle(indicator)"
                      />
                    </a-form-item>
                    <a-form-item label="超卖线" :label-col="{span: 8}">
                      <a-slider 
                        v-model:value="indicator.oversold" 
                        :min="10" 
                        :max="40" 
                        :marks="{ 30: '30' }"
                        @change="updateIndicatorStyle(indicator)"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
              
              <!-- MACD实时配置 -->
              <div v-if="indicator.name === 'MACD'">
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item label="快线" :label-col="{span: 8}">
                      <a-slider 
                        v-model:value="indicator.params[0]" 
                        :min="5" 
                        :max="20" 
                        :marks="{ 12: '12' }"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                      <a-input-number 
                        v-model:value="indicator.params[0]"
                        :min="5" 
                        :max="20"
                        size="small"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="慢线" :label-col="{span: 8}">
                      <a-slider 
                        v-model:value="indicator.params[1]" 
                        :min="20" 
                        :max="40" 
                        :marks="{ 26: '26' }"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                      <a-input-number 
                        v-model:value="indicator.params[1]"
                        :min="20" 
                        :max="40"
                        size="small"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="信号线" :label-col="{span: 8}">
                      <a-slider 
                        v-model:value="indicator.params[2]" 
                        :min="5" 
                        :max="15" 
                        :marks="{ 9: '9' }"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                      <a-input-number 
                        v-model:value="indicator.params[2]"
                        :min="5" 
                        :max="15"
                        size="small"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
                
                <!-- MACD颜色配置 -->
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item label="DIF颜色" :label-col="{span: 8}">
                      <a-select v-model:value="indicator.difColor" @change="updateIndicatorStyle(indicator)">
                        <a-select-option value="#1890ff">蓝色</a-select-option>
                        <a-select-option value="#52c41a">绿色</a-select-option>
                        <a-select-option value="#faad14">黄色</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="DEA颜色" :label-col="{span: 8}">
                      <a-select v-model:value="indicator.deaColor" @change="updateIndicatorStyle(indicator)">
                        <a-select-option value="#f5222d">红色</a-select-option>
                        <a-select-option value="#722ed1">紫色</a-select-option>
                        <a-select-option value="#fa541c">橙色</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="柱状图" :label-col="{span: 8}">
                      <a-switch 
                        v-model:checked="indicator.showHistogram"
                        @change="updateIndicatorStyle(indicator)"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
              
              <!-- KDJ实时配置 -->
              <div v-if="indicator.name === 'KDJ'">
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item label="K周期" :label-col="{span: 8}">
                      <a-slider 
                        v-model:value="indicator.params[0]" 
                        :min="5" 
                        :max="20" 
                        :marks="{ 9: '9' }"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                      <a-input-number 
                        v-model:value="indicator.params[0]"
                        :min="5" 
                        :max="20"
                        size="small"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="D周期" :label-col="{span: 8}">
                      <a-slider 
                        v-model:value="indicator.params[1]" 
                        :min="2" 
                        :max="10" 
                        :marks="{ 3: '3' }"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                      <a-input-number 
                        v-model:value="indicator.params[1]"
                        :min="2" 
                        :max="10"
                        size="small"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="J周期" :label-col="{span: 8}">
                      <a-slider 
                        v-model:value="indicator.params[2]" 
                        :min="2" 
                        :max="10" 
                        :marks="{ 3: '3' }"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                      <a-input-number 
                        v-model:value="indicator.params[2]"
                        :min="2" 
                        :max="10"
                        size="small"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
                
                <!-- KDJ超买超卖线配置 -->
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="超买线" :label-col="{span: 8}">
                      <a-slider 
                        v-model:value="indicator.overbought" 
                        :min="70" 
                        :max="90" 
                        :marks="{ 80: '80' }"
                        @change="updateIndicatorStyle(indicator)"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="超卖线" :label-col="{span: 8}">
                      <a-slider 
                        v-model:value="indicator.oversold" 
                        :min="10" 
                        :max="30" 
                        :marks="{ 20: '20' }"
                        @change="updateIndicatorStyle(indicator)"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
              
              <!-- 布林带实时配置 -->
              <div v-if="indicator.name === 'BOLL'">
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="周期" :label-col="{span: 8}">
                      <a-slider 
                        v-model:value="indicator.params[0]" 
                        :min="10" 
                        :max="50" 
                        :marks="{ 20: '20' }"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                      <a-input-number 
                        v-model:value="indicator.params[0]"
                        :min="10" 
                        :max="50"
                        size="small"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="标准差" :label-col="{span: 8}">
                      <a-slider 
                        v-model:value="indicator.params[1]" 
                        :min="1" 
                        :max="3" 
                        :step="0.1" 
                        :marks="{ 2: '2.0' }"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                      <a-input-number 
                        v-model:value="indicator.params[1]"
                        :min="1" 
                        :max="3"
                        :step="0.1"
                        size="small"
                        @change="updateIndicatorRealTime(indicator)"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
              
              <!-- 通用样式配置 -->
              <a-divider>显示设置</a-divider>
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="线条粗细" :label-col="{span: 10}">
                    <a-slider 
                      v-model:value="indicator.lineWidth" 
                      :min="1" 
                      :max="5" 
                      :marks="{ 2: '2' }"
                      @change="updateIndicatorStyle(indicator)"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="透明度" :label-col="{span: 10}">
                    <a-slider 
                      v-model:value="indicator.opacity" 
                      :min="0.1" 
                      :max="1" 
                      :step="0.1" 
                      :marks="{ 0.8: '0.8' }"
                      @change="updateIndicatorStyle(indicator)"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="显示数值" :label-col="{span: 10}">
                    <a-switch 
                      v-model:checked="indicator.showValues"
                      @change="updateIndicatorStyle(indicator)"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </div>
      
      <!-- 预设配置 -->
      <div class="preset-configs" v-if="activeIndicators.length > 0">
        <a-divider>预设配置</a-divider>
        <a-space wrap>
          <a-button size="small" @click="applyPreset('conservative')">
            保守型
          </a-button>
          <a-button size="small" @click="applyPreset('aggressive')">
            激进型
          </a-button>
          <a-button size="small" @click="applyPreset('balanced')">
            平衡型
          </a-button>
          <a-button size="small" @click="saveCurrentAsPreset">
            保存当前配置
          </a-button>
        </a-space>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { 
  LineChartOutlined, 
  BarChartOutlined, 
  StockOutlined, 
  FundOutlined,
  ClearOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

/**
 * 增强版技术指标面板组件
 * 支持实时参数调整和优化的显示效果
 */
const props = defineProps({
  chartInstance: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['indicator-added', 'indicator-removed', 'indicator-updated'])

// 响应式数据
const activeIndicators = ref([])
const activeCollapseKeys = ref([])
const updateTimer = ref(null)

// 指标定义
const indicatorDefinitions = {
  RSI: {
    name: 'RSI',
    displayName: 'RSI相对强弱指标',
    defaultParams: [14],
    defaultStyle: {
      visible: true,
      lineWidth: 2,
      opacity: 0.8,
      showValues: true,
      overbought: 70,
      oversold: 30
    }
  },
  MACD: {
    name: 'MACD',
    displayName: 'MACD指标',
    defaultParams: [12, 26, 9],
    defaultStyle: {
      visible: true,
      lineWidth: 2,
      opacity: 0.8,
      showValues: true,
      showHistogram: true,
      difColor: '#1890ff',
      deaColor: '#f5222d'
    }
  },
  KDJ: {
    name: 'KDJ',
    displayName: 'KDJ随机指标',
    defaultParams: [9, 3, 3],
    defaultStyle: {
      visible: true,
      lineWidth: 2,
      opacity: 0.8,
      showValues: true,
      overbought: 80,
      oversold: 20
    }
  },
  BOLL: {
    name: 'BOLL',
    displayName: '布林带',
    defaultParams: [20, 2],
    defaultStyle: {
      visible: true,
      lineWidth: 1,
      opacity: 0.6,
      showValues: false
    }
  }
}

// 预设配置
const presetConfigs = {
  conservative: {
    RSI: { params: [21], overbought: 75, oversold: 25 },
    MACD: { params: [12, 26, 9] },
    KDJ: { params: [14, 3, 3], overbought: 85, oversold: 15 }
  },
  aggressive: {
    RSI: { params: [9], overbought: 65, oversold: 35 },
    MACD: { params: [8, 21, 5] },
    KDJ: { params: [6, 3, 3], overbought: 75, oversold: 25 }
  },
  balanced: {
    RSI: { params: [14], overbought: 70, oversold: 30 },
    MACD: { params: [12, 26, 9] },
    KDJ: { params: [9, 3, 3], overbought: 80, oversold: 20 }
  }
}

/**
 * 检查指标是否已添加
 * @param {string} indicatorName - 指标名称
 * @returns {boolean}
 */
const hasIndicator = (indicatorName) => {
  return activeIndicators.value.some(indicator => indicator.name === indicatorName)
}

/**
 * 获取指标颜色
 * @param {string} indicatorName - 指标名称
 * @returns {string}
 */
const getIndicatorColor = (indicatorName) => {
  const colors = {
    RSI: 'blue',
    MACD: 'green',
    KDJ: 'orange',
    BOLL: 'purple'
  }
  return colors[indicatorName] || 'default'
}

/**
 * 添加技术指标
 * @param {string} indicatorName - 指标名称
 */
const addIndicator = (indicatorName) => {
  if (!props.chartInstance) {
    message.error('图表实例未初始化')
    return
  }
  
  if (hasIndicator(indicatorName)) {
    message.warning(`${indicatorName}指标已存在`)
    return
  }
  
  try {
    const definition = indicatorDefinitions[indicatorName]
    if (!definition) {
      message.error(`未知的指标类型: ${indicatorName}`)
      return
    }
    
    // 使用klinecharts的createIndicator方法
    const paneId = props.chartInstance.createIndicator(indicatorName, true, {
      calcParams: definition.defaultParams,
      styles: getIndicatorStyles(indicatorName, definition.defaultStyle)
    })
    
    if (paneId) {
      const indicator = {
        name: indicatorName,
        displayName: definition.displayName,
        params: [...definition.defaultParams],
        paneId: paneId,
        ...definition.defaultStyle
      }
      
      activeIndicators.value.push(indicator)
      activeCollapseKeys.value.push(indicatorName)
      emit('indicator-added', indicator)
      message.success(`${definition.displayName}添加成功`)
    } else {
      message.error(`${definition.displayName}添加失败`)
    }
  } catch (error) {
    console.error('添加指标失败:', error)
    message.error(`添加${indicatorName}指标失败: ${error.message}`)
  }
}

/**
 * 移除技术指标
 * @param {string} indicatorName - 指标名称
 */
const removeIndicator = (indicatorName) => {
  const indicatorIndex = activeIndicators.value.findIndex(indicator => indicator.name === indicatorName)
  if (indicatorIndex === -1) {
    message.warning(`${indicatorName}指标不存在`)
    return
  }
  
  try {
    const indicator = activeIndicators.value[indicatorIndex]
    
    // 使用klinecharts的removeIndicator方法
    if (props.chartInstance && indicator.paneId) {
      props.chartInstance.removeIndicator(indicator.paneId)
    }
    
    activeIndicators.value.splice(indicatorIndex, 1)
    const collapseIndex = activeCollapseKeys.value.indexOf(indicatorName)
    if (collapseIndex > -1) {
      activeCollapseKeys.value.splice(collapseIndex, 1)
    }
    
    emit('indicator-removed', indicator)
    message.success(`${indicator.displayName}移除成功`)
  } catch (error) {
    console.error('移除指标失败:', error)
    message.error(`移除${indicatorName}指标失败: ${error.message}`)
  }
}

/**
 * 清除所有指标
 */
const clearAllIndicators = () => {
  try {
    activeIndicators.value.forEach(indicator => {
      if (props.chartInstance && indicator.paneId) {
        props.chartInstance.removeIndicator(indicator.paneId)
      }
    })
    
    activeIndicators.value = []
    activeCollapseKeys.value = []
    message.success('所有指标已清除')
  } catch (error) {
    console.error('清除指标失败:', error)
    message.error('清除指标失败')
  }
}

/**
 * 切换指标可见性
 * @param {Object} indicator - 指标对象
 */
const toggleIndicatorVisibility = (indicator) => {
  try {
    if (props.chartInstance && indicator.paneId) {
      // 通过重新创建指标来实现显示/隐藏
      if (indicator.visible) {
        // 显示指标
        props.chartInstance.removeIndicator(indicator.paneId)
        const newPaneId = props.chartInstance.createIndicator(indicator.name, true, {
          calcParams: indicator.params,
          styles: getIndicatorStyles(indicator.name, indicator)
        })
        indicator.paneId = newPaneId
      } else {
        // 隐藏指标
        props.chartInstance.removeIndicator(indicator.paneId)
        indicator.paneId = null
      }
    }
  } catch (error) {
    console.error('切换指标可见性失败:', error)
    message.error('切换指标可见性失败')
  }
}

/**
 * 实时更新指标参数
 * @param {Object} indicator - 指标对象
 */
const updateIndicatorRealTime = (indicator) => {
  // 防抖处理，避免频繁更新
  if (updateTimer.value) {
    clearTimeout(updateTimer.value)
  }
  
  updateTimer.value = setTimeout(() => {
    try {
      if (props.chartInstance && indicator.paneId && indicator.visible) {
        // 移除旧指标
        props.chartInstance.removeIndicator(indicator.paneId)
        
        // 添加新配置的指标
        const newPaneId = props.chartInstance.createIndicator(indicator.name, true, {
          calcParams: indicator.params,
          styles: getIndicatorStyles(indicator.name, indicator)
        })
        
        if (newPaneId) {
          indicator.paneId = newPaneId
          emit('indicator-updated', indicator)
        }
      }
    } catch (error) {
      console.error('实时更新指标失败:', error)
    }
  }, 300) // 300ms防抖
}

/**
 * 更新指标样式
 * @param {Object} indicator - 指标对象
 */
const updateIndicatorStyle = (indicator) => {
  updateIndicatorRealTime(indicator)
}

/**
 * 获取指标样式配置
 * @param {string} indicatorName - 指标名称
 * @param {Object} styleConfig - 样式配置
 * @returns {Object}
 */
const getIndicatorStyles = (indicatorName, styleConfig) => {
  const baseStyle = {
    ohlc: {
      upColor: '#26A69A',
      downColor: '#EF5350',
      noChangeColor: '#888888'
    },
    line: {
      size: styleConfig.lineWidth || 2,
      color: styleConfig.color || '#1890ff'
    },
    area: {
      lineSize: styleConfig.lineWidth || 1,
      lineColor: styleConfig.color || '#1890ff',
      value: styleConfig.opacity || 0.08
    }
  }
  
  // 根据指标类型定制样式
  switch (indicatorName) {
    case 'MACD':
      return {
        ...baseStyle,
        dif: {
          color: styleConfig.difColor || '#1890ff'
        },
        dea: {
          color: styleConfig.deaColor || '#f5222d'
        },
        macd: {
          upColor: '#26A69A',
          downColor: '#EF5350',
          noChangeColor: '#888888'
        }
      }
    case 'RSI':
      return {
        ...baseStyle,
        overbought: {
          color: '#f5222d',
          value: styleConfig.overbought || 70
        },
        oversold: {
          color: '#52c41a',
          value: styleConfig.oversold || 30
        }
      }
    case 'KDJ':
      return {
        ...baseStyle,
        k: {
          color: '#1890ff'
        },
        d: {
          color: '#f5222d'
        },
        j: {
          color: '#faad14'
        },
        overbought: {
          color: '#f5222d',
          value: styleConfig.overbought || 80
        },
        oversold: {
          color: '#52c41a',
          value: styleConfig.oversold || 20
        }
      }
    default:
      return baseStyle
  }
}

/**
 * 应用预设配置
 * @param {string} presetName - 预设名称
 */
const applyPreset = (presetName) => {
  const preset = presetConfigs[presetName]
  if (!preset) {
    message.error('未知的预设配置')
    return
  }
  
  activeIndicators.value.forEach(indicator => {
    const presetConfig = preset[indicator.name]
    if (presetConfig) {
      // 更新参数
      if (presetConfig.params) {
        indicator.params = [...presetConfig.params]
      }
      
      // 更新样式
      Object.keys(presetConfig).forEach(key => {
        if (key !== 'params') {
          indicator[key] = presetConfig[key]
        }
      })
      
      // 实时更新
      updateIndicatorRealTime(indicator)
    }
  })
  
  message.success(`已应用${presetName}预设配置`)
}

/**
 * 保存当前配置为预设
 */
const saveCurrentAsPreset = () => {
  // 这里可以实现保存到本地存储的逻辑
  const currentConfig = {}
  activeIndicators.value.forEach(indicator => {
    currentConfig[indicator.name] = {
      params: [...indicator.params],
      overbought: indicator.overbought,
      oversold: indicator.oversold,
      lineWidth: indicator.lineWidth,
      opacity: indicator.opacity
    }
  })
  
  localStorage.setItem('customIndicatorPreset', JSON.stringify(currentConfig))
  message.success('当前配置已保存')
}

// 监听图表实例变化
watch(() => props.chartInstance, (newInstance, oldInstance) => {
  if (oldInstance && newInstance !== oldInstance) {
    // 图表实例变化时清除所有指标
    activeIndicators.value = []
    activeCollapseKeys.value = []
  }
})

// 组件卸载时清理定时器
watch(() => updateTimer.value, (timer) => {
  if (timer) {
    return () => clearTimeout(timer)
  }
})
</script>

<style scoped>
.enhanced-kline-indicator-panel {
  margin-bottom: 16px;
}

.indicator-controls {
  margin-bottom: 16px;
}

.indicator-list {
  margin-top: 16px;
}

.real-time-config {
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  margin-top: 8px;
}

.preset-configs {
  margin-top: 16px;
  padding: 12px;
  background: #f0f2f5;
  border-radius: 6px;
}

:deep(.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box) {
  padding: 8px 0;
}

:deep(.ant-form-item) {
  margin-bottom: 12px;
}

:deep(.ant-slider) {
  margin-bottom: 8px;
}

:deep(.ant-input-number) {
  width: 100%;
}
</style>