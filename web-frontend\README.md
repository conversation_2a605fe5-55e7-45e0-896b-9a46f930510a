# OKX 交易前端系统

基于 Vue 3 + Vite 构建的专业级加密货币交易前端系统，提供完整的图表分析、交易执行和风险管理功能。

## 🚀 核心特性

- 📊 **专业图表系统**: 基于 KLineCharts 和 ECharts 的多维度图表分析
- 🎯 **智能交易**: 集成交易信号识别、模式分析和智能预警
- 🛡️ **风险管理**: 完整的仓位管理和风险控制系统
- 📱 **响应式设计**: 支持桌面和移动端的自适应布局
- 🔄 **实时数据**: WebSocket 实时行情和交易数据更新

## 🛠️ 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **状态管理**: Pinia
- **UI 组件**: Ant Design Vue
- **图表库**: KLineCharts + ECharts
- **测试框架**: Vitest + Vue Test Utils

## 📁 项目结构

```
src/
├── components/          # 组件库
│   ├── charts/         # 图表组件
│   ├── advanced-charts/ # 高级图表组件
│   └── ...             # 其他业务组件
├── composables/        # 组合式函数
├── stores/            # Pinia 状态管理
├── views/             # 页面组件
├── services/          # API 服务
├── utils/             # 工具函数
└── constants/         # 常量定义
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16
- npm >= 8

### 安装和启动

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
# 或使用批处理文件
start-vite.bat

# 构建生产版本
npm run build

# 运行测试
npm run test
```

开发服务器将在 `http://localhost:5173` 启动

## 📚 完整文档

> 📖 **详细文档已迁移到统一文档中心**: [前端文档中心](../docs/frontend/README.md)

### 快速导航

| 文档类型 | 链接 | 描述 |
|----------|------|------|
| 🏗️ **架构指南** | [模块化图表](../docs/frontend/guides/Modular-Charts.md) | 图表系统架构设计 |
| 📊 **图表集成** | [K线图集成](../docs/frontend/guides/KLineChart-Integration-Guide.md) | K线图组件使用 |
| 📈 **技术指标** | [增强指标](../docs/frontend/guides/Enhanced-Indicators.md) | 指标面板功能 |
| 🌐 **API 接口** | [API 文档](../docs/frontend/api/API-DOCUMENTATION.md) | 接口规范说明 |
| 🚀 **部署指南** | [部署文档](../docs/frontend/deployment/DEPLOYMENT-GUIDE.md) | 环境配置部署 |
| ⚡ **性能优化** | [优化工具](../docs/frontend/technical/OPTIMIZATION_TOOLS_USAGE.md) | 性能监控优化 |
| 🔧 **故障排除** | [故障诊断](../docs/frontend/technical/PERFORMANCE_TROUBLESHOOTING.md) | 问题解决方案 |

## 🔄 最新更新

### 文档重构 (2024年1月)
- ✅ 文档结构统一化，迁移到 `docs/frontend/` 目录
- ✅ 按功能分类：技术文档、API文档、部署指南、使用指南
- ✅ 清理重复和过时的文档文件
- ✅ 优化文档导航和索引

### API响应格式标准化
- ✅ 统一所有API接口响应格式
- ✅ `code` 字段标准化为数字类型 (0=成功, 1=失败)
- ✅ 修复前后端数据类型不一致问题
- ✅ 提升系统稳定性和可维护性

## 🧪 测试

```bash
# 运行所有测试
npm run test

# 运行测试并生成覆盖率报告
npm run test:coverage
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 🔗 相关链接

- [项目主文档](../README.md)
- [后端文档](../docs/README.md)
- [前端完整文档](../docs/frontend/README.md)
- [API 文档](../docs/api/)
- [部署指南](../docs/deployment/)

---

> 💡 **提示**: 如需查看详细的功能说明、架构设计和使用指南，请访问 [前端文档中心](../docs/frontend/README.md)。
> 
> 🆘 **支持**: 如有问题或建议，请提交 Issue 或联系开发团队。
