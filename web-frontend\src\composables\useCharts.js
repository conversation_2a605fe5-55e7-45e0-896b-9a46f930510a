import { ref, watch, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import { formatChartTime, formatUnit } from '@/utils/chartUtils';
import { CHART_DATA_INDEX } from '@/constants';

/**
 * 管理所有ECharts图表的创建、更新和销毁
 * @param {Ref<Object>} chartRefs - 包含所有图表DOM元素引用的对象
 * @param {Ref<Object>} displaySettings - 图表显示设置
 * @param {Ref<Object>} technicalIndicators - 计算出的技术指标
 * @param {Ref<Array>} chartData - K线数据
 * @returns {Object} - 包含图表实例和管理方法的对象
 */
export function useCharts(chartRefs, displaySettings, technicalIndicators, chartData) {
  const chartInstances = ref({});

  const theme = ref('light'); // 默认主题

  // 图表配置获取函数映射（需要在使用前定义）
  const optionGetters = {
    klineChartRef: getCandlestickOptions,
    volumeChartRef: getVolumeOptions,
    rsiChartRef: getRsiOptions,
    macdChartRef: getMacdOptions,
    kdjChartRef: getKdjOptions,
    bollingerBandsChartRef: getBollingerBandsOptions,
  };

  /**
   * 初始化所有图表
   */
  const initAllCharts = () => {
    for (const chartName in chartRefs) {
      const chartRef = chartRefs[chartName];
      if (chartRef && chartRef.value && optionGetters[chartName]) {
        const chartElement = chartRef.value.getChartElement();
        if (chartElement) {
          const instance = echarts.init(chartElement, theme.value);
          chartInstances.value[chartName] = instance;
          
          // 开发环境下注册到全局
          if (process.env.NODE_ENV === 'development' && window.registerChartInstance) {
            window.registerChartInstance(`charts_${chartName}`, instance)
          }
          
          console.log(`Initialized chart: ${chartName}`);
        } else {
          console.warn(`Chart element not found for: ${chartName}`);
        }
      } else {
        console.warn(`Chart ref not available for: ${chartName}`);
      }
    }
    updateAllCharts();
  };

  /**
   * 更新所有图表
   */
  const updateAllCharts = () => {
    if (chartData.value.length === 0) {
      console.warn('No chart data available for updating charts');
      return;
    }

    const dates = chartData.value.map(item => formatChartTime(item[CHART_DATA_INDEX.DATETIME]));
    console.log(`Updating charts with ${chartData.value.length} data points`);

    for (const chartName in chartInstances.value) {
      const getOptionFunc = optionGetters[chartName];
      if (getOptionFunc) {
        const options = getOptionFunc(dates, chartData.value, technicalIndicators.value, displaySettings, theme.value);
        chartInstances.value[chartName].setOption(options, true);
        console.log(`Updated chart: ${chartName}`);
      } else {
        console.warn(`No option getter found for chart: ${chartName}`);
      }
    }
    
    // 关联图表的 'dataZoom' 事件
    if (chartInstances.value.candlestickChart) {
        const chartsToConnect = Object.values(chartInstances.value);
        echarts.connect(chartsToConnect);
    }
  };

  /**
   * 调整所有图表的尺寸
   */
  const resizeAllCharts = () => {
    for (const chartName in chartInstances.value) {
      chartInstances.value[chartName]?.resize();
    }
  };

  /**
   * 销毁所有图表实例
   */
  const disposeAllCharts = () => {
    for (const chartName in chartInstances.value) {
      chartInstances.value[chartName]?.dispose();
      
      // 开发环境下注销全局注册的图表实例
      if (process.env.NODE_ENV === 'development' && window.unregisterChartInstance) {
        window.unregisterChartInstance(`charts_${chartName}`)
      }
    }
    chartInstances.value = {};
  };

  // 监听主题变化
  watch(theme, () => {
    disposeAllCharts();
    initAllCharts();
  });

  onUnmounted(disposeAllCharts);

  return {
    chartInstances,
    initAllCharts,
    updateAllCharts,
    resizeAllCharts,
    disposeAllCharts,
    setTheme: (newTheme) => theme.value = newTheme,
  };
}



// --- 具体图表配置 --- //

function getBaseChartOptions(dates, theme) {
    const isDark = theme === 'dark';
    const axisLabelColor = isDark ? '#a5a5a5' : '#666';
    const splitLineColor = isDark ? '#333' : '#eee';

    return {
        animation: false,
        backgroundColor: 'transparent',
        grid: { left: '50px', right: '50px', top: '10%', bottom: '20%' },
        xAxis: {
            type: 'category',
            data: dates,
            axisLine: { lineStyle: { color: splitLineColor } },
            axisTick: { show: false },
            axisLabel: { color: axisLabelColor },
            splitLine: { show: false },
        },
        yAxis: {
            scale: true,
            splitLine: { lineStyle: { type: 'dashed', color: splitLineColor } },
            axisLine: { show: false },
            axisTick: { show: false },
            axisLabel: { color: axisLabelColor, formatter: (value) => formatUnit(value, 2) }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'cross', crossStyle: { color: '#999' } },
            backgroundColor: isDark ? 'rgba(32, 32, 32, 0.8)' : 'rgba(255, 255, 255, 0.9)',
            borderColor: isDark ? '#555' : '#ddd',
            borderWidth: 1,
            textStyle: { color: isDark ? '#fff' : '#333' },
        },
        dataZoom: [
            { type: 'inside', start: 50, end: 100 },
            {
                show: true,
                type: 'slider',
                top: '90%',
                start: 50,
                end: 100,
                handleIcon: 'path://M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                handleSize: '80%',
                dataBackground: {
                    areaStyle: { color: isDark ? '#444' : '#f0f0f0' },
                    lineStyle: { opacity: 0.8, color: isDark ? '#777' : '#ccc' }
                },
                fillerColor: 'rgba(138, 185, 255, 0.2)',
                borderColor: 'transparent',
                textStyle: { color: axisLabelColor }
            }
        ],
    };
}

/**
 * 生成K线图配置，包含数据验证和异常处理
 * @param {Array} dates - 日期数组
 * @param {Array} data - K线数据
 * @param {Object} indicators - 技术指标
 * @param {Object} settings - 显示设置
 * @param {string} theme - 主题
 * @returns {Object} ECharts配置对象
 */
function getCandlestickOptions(dates, data, indicators, settings, theme) {
  // 数据有效性校验
  if (!Array.isArray(dates) || dates.length === 0) {
    console.warn('K线图配置生成失败：dates为空或格式不正确');
    return { series: [] };
  }
  if (!Array.isArray(data) || data.length === 0) {
    console.warn('K线图配置生成失败：data为空或格式不正确');
    return { series: [] };
  }
  // 检查每个数据项格式
  for (let i = 0; i < data.length; i++) {
    const item = data[i];
    if (!Array.isArray(item) || item.length < 5) {
      console.warn(`K线图配置生成失败：第${i}项数据格式不正确`, item);
      return { series: [] };
    }
  }
  const options = getBaseChartOptions(dates, theme);
  const candleData = data.map(item => [item[CHART_DATA_INDEX.OPEN], item[CHART_DATA_INDEX.CLOSE], item[CHART_DATA_INDEX.LOW], item[CHART_DATA_INDEX.HIGH]]);
  const upColor = '#00b894';
  const downColor = '#d63031';
  options.series = [
    {
      type: 'candlestick',
      data: candleData,
      itemStyle: {
        color: upColor,
        color0: downColor,
        borderColor: upColor,
        borderColor0: downColor,
      },
      markPoint: {},
      markLine: {}
    },
  ];
  // 均线配置
  if (settings && settings.showMA && indicators && indicators.ma) {
    const maColors = ['#f39c12', '#3498db', '#9b59b6', '#e74c3c'];
    if (indicators.ma.ma5) {
      options.series.push({ name: 'MA5', type: 'line', data: indicators.ma.ma5, smooth: true, showSymbol: false, lineStyle: { width: 1, color: maColors[0] } });
    }
    if (indicators.ma.ma10) {
      options.series.push({ name: 'MA10', type: 'line', data: indicators.ma.ma10, smooth: true, showSymbol: false, lineStyle: { width: 1, color: maColors[1] } });
    }
    if (indicators.ma.ma20) {
      options.series.push({ name: 'MA20', type: 'line', data: indicators.ma.ma20, smooth: true, showSymbol: false, lineStyle: { width: 1, color: maColors[2] } });
    }
  }
  return options;
}

function getVolumeOptions(dates, data, indicators, settings, theme) {
  const options = getBaseChartOptions(dates, theme);
  const upColor = 'rgba(0, 184, 148, 0.5)';
  const downColor = 'rgba(214, 48, 49, 0.5)';

  const volumeData = data.map((item, i) => {
      const color = item[CHART_DATA_INDEX.OPEN] <= item[CHART_DATA_INDEX.CLOSE] ? upColor : downColor;
      return { value: item[CHART_DATA_INDEX.VOLUME], itemStyle: { color } };
  });
  options.series = [{ type: 'bar', data: volumeData }];
  return options;
}

function getRsiOptions(dates, data, indicators, settings, theme) {
  const options = getBaseChartOptions(dates, theme);
  options.yAxis.min = 0;
  options.yAxis.max = 100;
  options.series = [];
  if (indicators.rsi) {
    options.series.push(
        { name: 'RSI', type: 'line', data: indicators.rsi, showSymbol: false, lineStyle: { color: '#8e44ad' } },
    );
  }
  return options;
}

function getMacdOptions(dates, data, indicators, settings, theme) {
  const options = getBaseChartOptions(dates, theme);
  options.series = [];
  if (indicators.macd) {
      options.series.push(
          { name: 'DIF', type: 'line', data: indicators.macd.dif, showSymbol: false, lineStyle: { color: '#3498db' } },
          { name: 'DEA', type: 'line', data: indicators.macd.dea, showSymbol: false, lineStyle: { color: '#f1c40f' } },
          { 
            name: 'MACD', 
            type: 'bar', 
            data: indicators.macd.macd,
            itemStyle: {
              color: (params) => (params.value >= 0 ? '#00b894' : '#d63031')
            }
          },
      );
  }
  return options;
}

function getKdjOptions(dates, data, indicators, settings, theme) {
  const options = getBaseChartOptions(dates, theme);
  options.series = [];
  if (indicators.kdj) {
      options.series.push(
          { name: 'K', type: 'line', data: indicators.kdj.k, showSymbol: false, lineStyle: { color: '#2ecc71' } },
          { name: 'D', type: 'line', data: indicators.kdj.d, showSymbol: false, lineStyle: { color: '#e67e22' } },
          { name: 'J', type: 'line', data: indicators.kdj.j, showSymbol: false, lineStyle: { color: '#9b59b6' } },
      );
  }
  return options;
}

function getBollingerBandsOptions(dates, data, indicators, settings, theme) {
  const options = getBaseChartOptions(dates, theme);
  options.series = [];
  if (indicators.bollingerBands) {
      options.series.push(
          { name: 'Upper', type: 'line', data: indicators.bollingerBands.upper, showSymbol: false, lineStyle: { color: '#c23531' } },
          { name: 'Mid', type: 'line', data: indicators.bollingerBands.mid, showSymbol: false, lineStyle: { color: '#2f4554' } },
          { name: 'Lower', type: 'line', data: indicators.bollingerBands.lower, showSymbol: false, lineStyle: { color: '#61a0a8' } },
      );
  }
  return options;
}