# OKX量化交易系统-数据结构

## 数据库设计

### 用户表 (users)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | INT | - | NO | AUTO_INCREMENT | 用户ID |
| username | VARCHAR | 50 | NO | - | 用户名 |
| password_hash | VARCHAR | 255 | NO | - | 密码哈希 |
| email | VARCHAR | 100 | YES | NULL | 邮箱 |
| phone | VARCHAR | 20 | YES | NULL | 手机号 |
| api_key | VARCHAR | 64 | YES | NULL | OKX API密钥 |
| api_secret | VARCHAR | 128 | YES | NULL | OKX API密钥(加密) |
| api_passphrase | VARCHAR | 64 | YES | NULL | OKX API密码(加密) |
| is_active | BOOLEAN | - | NO | TRUE | 账户状态 |
| last_login | DATETIME | - | YES | NULL | 最后登录时间 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 更新时间 |

### 策略表 (strategies)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | INT | - | NO | AUTO_INCREMENT | 策略ID |
| user_id | INT | - | NO | - | 用户ID |
| name | VARCHAR | 100 | NO | - | 策略名称 |
| description | TEXT | - | YES | NULL | 策略描述 |
| strategy_type | VARCHAR | 50 | NO | - | 策略类型 |
| inst_id | VARCHAR | 50 | NO | - | 交易产品 |
| timeframe | VARCHAR | 10 | NO | '1H' | 时间周期 |
| parameters | JSON | - | YES | NULL | 策略参数 |
| risk_management | JSON | - | YES | NULL | 风险管理参数 |
| status | ENUM | - | NO | 'stopped' | 状态(running/stopped/paused) |
| performance | JSON | - | YES | NULL | 策略表现数据 |
| last_signal | DATETIME | - | YES | NULL | 最后信号时间 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 更新时间 |

### 订单表 (orders)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | INT | - | NO | AUTO_INCREMENT | 本地订单ID |
| user_id | INT | - | NO | - | 用户ID |
| ord_id | VARCHAR | 50 | NO | - | OKX订单ID |
| cl_ord_id | VARCHAR | 50 | YES | NULL | 客户端订单ID |
| inst_id | VARCHAR | 50 | NO | - | 产品ID |
| td_mode | VARCHAR | 20 | NO | - | 交易模式 |
| side | VARCHAR | 10 | NO | - | 订单方向(buy/sell) |
| ord_type | VARCHAR | 20 | NO | - | 订单类型 |
| sz | DECIMAL | 20,8 | NO | - | 委托数量 |
| px | DECIMAL | 20,8 | YES | NULL | 委托价格 |
| fill_sz | DECIMAL | 20,8 | NO | 0 | 已成交数量 |
| avg_px | DECIMAL | 20,8 | YES | NULL | 成交均价 |
| state | VARCHAR | 20 | NO | - | 订单状态 |
| lever | VARCHAR | 10 | YES | NULL | 杠杆倍数 |
| tp_trigger_px | DECIMAL | 20,8 | YES | NULL | 止盈触发价 |
| sl_trigger_px | DECIMAL | 20,8 | YES | NULL | 止损触发价 |
| fee | DECIMAL | 20,8 | YES | NULL | 手续费 |
| pnl | DECIMAL | 20,8 | YES | NULL | 收益 |
| strategy_id | INT | - | YES | NULL | 关联策略ID |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 更新时间 |

### AI分析表 (ai_analysis)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | INT | - | NO | AUTO_INCREMENT | 分析ID |
| user_id | INT | - | NO | - | 用户ID |
| inst_id | VARCHAR | 50 | NO | - | 产品ID |
| analysis_type | VARCHAR | 50 | NO | - | 分析类型 |
| timeframe | VARCHAR | 10 | NO | - | 时间周期 |
| result | JSON | - | YES | NULL | 分析结果 |
| confidence | DECIMAL | 5,4 | YES | NULL | 置信度(0-1) |
| trend_direction | VARCHAR | 20 | YES | NULL | 趋势方向 |
| trend_strength | DECIMAL | 5,4 | YES | NULL | 趋势强度 |
| support_levels | JSON | - | YES | NULL | 支撑位数组 |
| resistance_levels | JSON | - | YES | NULL | 阻力位数组 |
| trading_signals | JSON | - | YES | NULL | 交易信号 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 更新时间 |

### 持仓表 (positions)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | INT | - | NO | AUTO_INCREMENT | 持仓ID |
| user_id | INT | - | NO | - | 用户ID |
| inst_id | VARCHAR | 50 | NO | - | 产品ID |
| pos_id | VARCHAR | 50 | YES | NULL | OKX持仓ID |
| pos_side | VARCHAR | 10 | NO | - | 持仓方向(long/short/net) |
| pos | DECIMAL | 20,8 | NO | 0 | 持仓数量 |
| avg_px | DECIMAL | 20,8 | YES | NULL | 开仓均价 |
| mark_px | DECIMAL | 20,8 | YES | NULL | 标记价格 |
| upl | DECIMAL | 20,8 | NO | 0 | 未实现盈亏 |
| upl_ratio | DECIMAL | 10,6 | NO | 0 | 未实现盈亏比例 |
| margin | DECIMAL | 20,8 | YES | NULL | 保证金 |
| lever | VARCHAR | 10 | YES | NULL | 杠杆倍数 |
| td_mode | VARCHAR | 20 | NO | - | 交易模式 |
| strategy_id | INT | - | YES | NULL | 关联策略ID |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 更新时间 |

### 交易记录表 (trades)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | INT | - | NO | AUTO_INCREMENT | 交易ID |
| user_id | INT | - | NO | - | 用户ID |
| trade_id | VARCHAR | 50 | NO | - | OKX交易ID |
| ord_id | VARCHAR | 50 | NO | - | 订单ID |
| inst_id | VARCHAR | 50 | NO | - | 产品ID |
| side | VARCHAR | 10 | NO | - | 交易方向 |
| fill_sz | DECIMAL | 20,8 | NO | - | 成交数量 |
| fill_px | DECIMAL | 20,8 | NO | - | 成交价格 |
| fee | DECIMAL | 20,8 | YES | NULL | 手续费 |
| fee_ccy | VARCHAR | 10 | YES | NULL | 手续费币种 |
| exec_type | VARCHAR | 20 | YES | NULL | 成交类型 |
| ts | BIGINT | - | NO | - | 成交时间戳 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 创建时间 |

### 账户余额表 (account_balances)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | INT | - | NO | AUTO_INCREMENT | 记录ID |
| user_id | INT | - | NO | - | 用户ID |
| currency | VARCHAR | 10 | NO | - | 币种 |
| available | DECIMAL | 20,8 | NO | 0 | 可用余额 |
| frozen | DECIMAL | 20,8 | NO | 0 | 冻结余额 |
| total | DECIMAL | 20,8 | NO | 0 | 总余额 |
| equity | DECIMAL | 20,8 | NO | 0 | 权益 |
| upl | DECIMAL | 20,8 | NO | 0 | 未实现盈亏 |
| account_type | VARCHAR | 20 | NO | 'spot' | 账户类型(spot/swap/futures) |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 更新时间 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 创建时间 |

## API数据结构

### OKX订单响应结构
```json
{
  "ordId": "12345678901234567890",
  "clOrdId": "client_order_001",
  "instId": "BTC-USDT",
  "tdMode": "cash",
  "side": "buy",
  "ordType": "limit",
  "sz": "0.01",
  "px": "50000",
  "state": "filled",
  "fillSz": "0.01",
  "avgPx": "50000",
  "lever": "1",
  "tpTriggerPx": "",
  "slTriggerPx": "",
  "fee": "-0.05",
  "feeCcy": "USDT",
  "pnl": "0",
  "cTime": "*************",
  "uTime": "*************"
}
```

### OKX K线数据结构
```json
{
  "ts": "*************",
  "o": "50000",
  "h": "51000",
  "l": "49500",
  "c": "50500",
  "vol": "100.5",
  "volCcy": "5025000",
  "volCcyQuote": "5025000",
  "confirm": "1"
}
```

### OKX账户余额结构
```json
{
  "ccy": "USDT",
  "bal": "1000.00",
  "frozenBal": "0.00",
  "availBal": "1000.00",
  "eq": "1000.00",
  "upl": "0.00",
  "uTime": "*************"
}
```

### OKX持仓信息结构
```json
{
  "instId": "BTC-USDT-SWAP",
  "posId": "123456789",
  "posSide": "long",
  "pos": "0.1",
  "avgPx": "50000",
  "markPx": "51000",
  "upl": "100.00",
  "uplRatio": "0.02",
  "margin": "500.00",
  "lever": "10",
  "tdMode": "isolated",
  "cTime": "*************",
  "uTime": "*************"
}
```

### AI分析结果结构
```json
{
  "trend_lines": [
    {
      "type": "support",
      "points": [
        {"x": *************, "y": 49500},
        {"x": 1641081600000, "y": 49800}
      ],
      "strength": 0.85,
      "confidence": 0.92
    }
  ],
  "support_levels": [49500, 48800, 48000],
  "resistance_levels": [51000, 51500, 52000],
  "trend_direction": "bullish",
  "trend_strength": 0.78,
  "trading_signals": {
    "signal": "buy",
    "strength": 0.82,
    "entry_price": 50500,
    "stop_loss": 49800,
    "take_profit": 51500,
    "confidence": 0.88
  }
}
```

### 策略参数结构
```json
{
  "ma_fast": 20,
  "ma_slow": 50,
  "rsi_period": 14,
  "rsi_overbought": 70,
  "rsi_oversold": 30,
  "stop_loss": 0.02,
  "take_profit": 0.04,
  "position_size": 0.1
}
```

### 风险管理参数结构
```json
{
  "max_position_size": 0.1,
  "max_daily_loss": 0.05,
  "max_drawdown": 0.15,
  "risk_per_trade": 0.02,
  "max_open_positions": 3,
  "correlation_limit": 0.7
}
```

### 策略回测结果结构
```json
{
  "total_return": 0.15,
  "annual_return": 0.45,
  "max_drawdown": 0.08,
  "sharpe_ratio": 1.85,
  "sortino_ratio": 2.12,
  "win_rate": 0.68,
  "total_trades": 156,
  "winning_trades": 106,
  "losing_trades": 50,
  "profit_factor": 1.92,
  "avg_win": 0.025,
  "avg_loss": -0.013,
  "largest_win": 0.08,
  "largest_loss": -0.04,
  "equity_curve": [
    {"date": "2024-01-01", "equity": 10000},
    {"date": "2024-01-02", "equity": 10150}
  ]
}
```

## OKX持仓结构（部分字段）
| 字段名     | 类型    | 说明         |
| ---------- | ------- | ------------ |
| instId     | str     | 产品ID       |
| pos        | str     | 持仓量       |
| avgPx      | str     | 开仓均价     |
| lever      | str     | 杠杆倍数     |
| posSide    | str     | 持仓方向     |
| uTime      | str     | 更新时间戳   |

## OKX资金划转结构（部分字段）
| 字段名     | 类型    | 说明         |
| ---------- | ------- | ------------ |
| ccy        | str     | 币种         |
| amt        | str     | 划转数量     |
| from       | str     | 来源账户     |
| to         | str     | 目标账户     |
| type       | str     | 划转类型     |
| subAcct    | str     | 子账户（可选）|
| transId    | str     | 划转流水号   |
