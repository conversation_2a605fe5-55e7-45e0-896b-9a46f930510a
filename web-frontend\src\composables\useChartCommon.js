/**
 * @file useChartCommon.js
 * @description 图表组件的通用逻辑 Composable
 * 提供主题管理、ECharts实例管理、数据格式化等共享功能
 */

import { ref, computed, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { CHART_DATA_INDEX } from '@/constants/chartConstants'

/**
 * 图表通用逻辑 Composable
 * @param {Object} options 配置选项
 * @returns {Object} 返回图表相关的响应式数据和方法
 */
export function useChartCommon(options = {}) {
  // 响应式数据
  const chartInstance = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const isDarkTheme = ref(options.isDarkTheme || false)

  // 主题配置
  const themeConfig = computed(() => {
    if (isDarkTheme.value) {
      return {
        backgroundColor: '#1e222d',
        textColor: '#ffffff',
        borderColor: '#434651',
        gridColor: '#2a2e39',
        candlestick: {
          upColor: '#00da3c',  // 绿涨
          downColor: '#ec0000', // 红跌
          upBorderColor: '#00da3c',
          downBorderColor: '#ec0000'
        },
        volume: {
          up: '#00da3c',  // 绿涨
          down: '#ec0000'  // 红跌
        },
        ma: {
          ma5: '#FF6B6B',
          ma10: '#4ECDC4',
          ma20: '#45B7D1',
          ma50: '#FFA726',
          ma200: '#AB47BC'
        },
        indicators: {
          rsi: '#FFA726',
          macd: '#AB47BC',
          kdj: {
            k: '#2196F3',
            d: '#FF9800',
            j: '#9C27B0'
          },
          bollinger: {
            upper: '#FF5722',
            middle: '#607D8B',
            lower: '#4CAF50'
          }
        }
      }
    } else {
      return {
        backgroundColor: '#ffffff',
        textColor: '#000000',
        borderColor: '#e8e8e8',
        gridColor: '#f5f5f5',
        candlestick: {
          upColor: '#00da3c',  // 绿涨
          downColor: '#ec0000', // 红跌
          upBorderColor: '#00da3c',
          downBorderColor: '#ec0000'
        },
        volume: {
          up: '#00da3c',  // 绿涨
          down: '#ec0000'  // 红跌
        },
        ma: {
          ma5: '#FF6B6B',
          ma10: '#4ECDC4',
          ma20: '#45B7D1',
          ma50: '#FFA726',
          ma200: '#AB47BC'
        },
        indicators: {
          rsi: '#FFA726',
          macd: '#AB47BC',
          kdj: {
            k: '#2196F3',
            d: '#FF9800',
            j: '#9C27B0'
          },
          bollinger: {
            upper: '#FF5722',
            middle: '#607D8B',
            lower: '#4CAF50'
          }
        }
      }
    }
  })

  // 通用图表配置
  const getBaseChartOption = (title, customOptions = {}) => {
    const theme = themeConfig.value
    return {
      backgroundColor: theme.backgroundColor,
      title: {
        text: title,
        left: 'left',
        top: 10,
        textStyle: {
          color: theme.textColor,
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: isDarkTheme.value ? 'rgba(30, 34, 45, 0.9)' : 'rgba(255, 255, 255, 0.9)',
        borderColor: theme.borderColor,
        textStyle: {
          color: theme.textColor
        }
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '10%',
        top: '15%',
        containLabel: true,
        backgroundColor: 'transparent',
        borderColor: theme.borderColor
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLine: {
          lineStyle: {
            color: theme.borderColor
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: theme.textColor,
          fontSize: 10
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: theme.gridColor
          }
        }
      },
      yAxis: {
        type: 'value',
        position: 'right',
        scale: true,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: theme.textColor,
          fontSize: 10
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: theme.gridColor
          }
        }
      },
      ...customOptions
    }
  }

  // 初始化图表实例
  const initChart = (containerId, option) => {
    try {
      isLoading.value = true
      error.value = null

      const container = document.getElementById(containerId)
      if (!container) {
        throw new Error(`图表容器 ${containerId} 不存在`)
      }

      // 如果已存在实例，先销毁
      if (chartInstance.value) {
        chartInstance.value.dispose()
      }

      chartInstance.value = echarts.init(container)
      chartInstance.value.setOption(option)

      // 添加错误处理
      chartInstance.value.on('error', (err) => {
        console.error('ECharts 错误:', err)
        error.value = err.message || '图表渲染错误'
      })

      return chartInstance.value
    } catch (err) {
      console.error('初始化图表失败:', err)
      error.value = err.message || '初始化图表失败'
      return null
    } finally {
      isLoading.value = false
    }
  }

  // 更新图表数据
  const updateChart = (newOption, notMerge = false) => {
    if (!chartInstance.value) {
      console.warn('图表实例不存在，无法更新')
      return
    }

    try {
      chartInstance.value.setOption(newOption, notMerge)
    } catch (err) {
      console.error('更新图表失败:', err)
      error.value = err.message || '更新图表失败'
    }
  }

  // 调整图表大小
  const resizeChart = () => {
    if (chartInstance.value) {
      chartInstance.value.resize()
    }
  }

  // 销毁图表实例
  const disposeChart = () => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
      chartInstance.value = null
    }
  }

  // 切换主题
  const toggleTheme = () => {
    isDarkTheme.value = !isDarkTheme.value
  }

  // 数据格式化工具函数
  const formatChartData = {
    // 格式化K线数据
    candlestick: (data) => {
      return data.map(item => [
        item[CHART_DATA_INDEX.DATETIME],
        parseFloat(item[CHART_DATA_INDEX.OPEN]),
        parseFloat(item[CHART_DATA_INDEX.CLOSE]),
        parseFloat(item[CHART_DATA_INDEX.LOW]),
        parseFloat(item[CHART_DATA_INDEX.HIGH])
      ])
    },

    // 格式化成交量数据
    volume: (data) => {
      return data.map(item => parseFloat(item[CHART_DATA_INDEX.VOLUME]))
    },

    // 格式化时间轴数据
    timeAxis: (data) => {
      return data.map(item => {
        const timestamp = parseInt(item[CHART_DATA_INDEX.DATETIME])
        return new Date(timestamp).toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      })
    },

    // 格式化价格显示
    price: (value, decimals = 2) => {
      return parseFloat(value).toFixed(decimals)
    },

    // 格式化成交量显示
    volumeDisplay: (value) => {
      if (value >= 1000000) {
        return (value / 1000000).toFixed(1) + 'M'
      } else if (value >= 1000) {
        return (value / 1000).toFixed(1) + 'K'
      }
      return value.toFixed(0)
    },

    /**
     * 根据成交量计算K线宽度
     * @param {Array} data - K线数据
     * @param {number} index - 当前K线索引
     * @param {Object} options - 配置选项
     * @returns {string} K线宽度百分比
     */
    calculateCandlestickWidth: (data, index, options = {}) => {
      const {
        minWidth = 30,    // 最小宽度百分比
        maxWidth = 90,    // 最大宽度百分比
        baseWidth = 60    // 基础宽度百分比
      } = options

      if (!data || data.length === 0 || index < 0 || index >= data.length) {
        return `${baseWidth}%`
      }

      // 获取当前成交量
      const currentVolume = parseFloat(data[index][CHART_DATA_INDEX.VOLUME])
      
      // 计算成交量范围（使用最近20根K线或全部数据）
      const lookbackPeriod = Math.min(20, data.length)
      const startIndex = Math.max(0, index - lookbackPeriod + 1)
      const endIndex = Math.min(data.length, index + 1)
      
      const volumes = []
      for (let i = startIndex; i < endIndex; i++) {
        volumes.push(parseFloat(data[i][CHART_DATA_INDEX.VOLUME]))
      }
      
      const minVolume = Math.min(...volumes)
      const maxVolume = Math.max(...volumes)
      
      // 避免除零错误
      if (maxVolume === minVolume) {
        return `${baseWidth}%`
      }
      
      // 计算成交量相对位置 (0-1)
      const volumeRatio = (currentVolume - minVolume) / (maxVolume - minVolume)
      
      // 映射到宽度范围
      const width = minWidth + (maxWidth - minWidth) * volumeRatio
      
      return `${Math.round(width)}%`
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    disposeChart()
  })

  return {
    // 响应式数据
    chartInstance,
    isLoading,
    error,
    isDarkTheme,
    themeConfig,

    // 方法
    initChart,
    updateChart,
    resizeChart,
    disposeChart,
    toggleTheme,
    getBaseChartOption,
    formatChartData
  }
}

/**
 * 技术指标计算工具
 */
export const indicatorCalculators = {
  // 计算移动平均线
  calculateMA: (data, period) => {
    const result = []
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.push(null)
      } else {
        let sum = 0
        for (let j = 0; j < period; j++) {
          sum += parseFloat(data[i - j][CHART_DATA_INDEX.CLOSE])
        }
        result.push(sum / period)
      }
    }
    return result
  },

  // 计算EMA
  calculateEMA: (data, period) => {
    const result = []
    const multiplier = 2 / (period + 1)
    
    if (data.length === 0) return result
    
    result[0] = parseFloat(data[0][CHART_DATA_INDEX.CLOSE])
    
    for (let i = 1; i < data.length; i++) {
      const close = parseFloat(data[i][CHART_DATA_INDEX.CLOSE])
      result[i] = (close - result[i - 1]) * multiplier + result[i - 1]
    }
    
    return result
  },

  // 计算RSI
  calculateRSI: (data, period = 14) => {
    const result = []
    const gains = []
    const losses = []
    
    for (let i = 1; i < data.length; i++) {
      const change = parseFloat(data[i][CHART_DATA_INDEX.CLOSE]) - parseFloat(data[i - 1][CHART_DATA_INDEX.CLOSE])
      gains.push(change > 0 ? change : 0)
      losses.push(change < 0 ? Math.abs(change) : 0)
    }
    
    for (let i = 0; i < gains.length; i++) {
      if (i < period - 1) {
        result.push(null)
      } else {
        const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b) / period
        const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b) / period
        
        if (avgLoss === 0) {
          result.push(100)
        } else {
          const rs = avgGain / avgLoss
          result.push(100 - (100 / (1 + rs)))
        }
      }
    }
    
    return [null, ...result] // 添加第一个null值以匹配原始数据长度
  }
}