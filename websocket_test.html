<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .connect-btn {
            background-color: #28a745;
            color: white;
        }
        .disconnect-btn {
            background-color: #dc3545;
            color: white;
        }
        .subscribe-btn {
            background-color: #007bff;
            color: white;
        }
        #messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px;
        }
        .log-info {
            color: #0066cc;
        }
        .log-error {
            color: #cc0000;
            font-weight: bold;
        }
        .log-success {
            color: #009900;
        }
        .log-data {
            color: #666;
        }
    </style>
</head>
<body>
    <h1>WebSocket连接测试</h1>
    
    <div id="status" class="status disconnected">
        状态: 未连接
    </div>
    
    <div>
        <button id="connectBtn" class="connect-btn" onclick="connectWebSocket()">连接</button>
        <button id="disconnectBtn" class="disconnect-btn" onclick="disconnectWebSocket()" disabled>断开连接</button>
        <button id="subscribeBtn" class="subscribe-btn" onclick="subscribeToMarket()" disabled>订阅市场数据</button>
        <button onclick="clearMessages()">清空日志</button>
    </div>
    
    <h3>连接信息</h3>
    <p><strong>WebSocket URL:</strong> <span id="wsUrl">ws://localhost:8080/api/okx/market</span></p>
    <p><strong>重连次数:</strong> <span id="reconnectCount">0</span></p>
    <p><strong>消息计数:</strong> <span id="messageCount">0</span></p>
    
    <h3>消息日志</h3>
    <div id="messages"></div>

    <script>
        let socket = null;
        let reconnectAttempts = 0;
        let messageCount = 0;
        const maxReconnectAttempts = 5;
        const reconnectDelay = 3000;
        const wsUrl = 'ws://localhost:8080/api/okx/market';
        
        function log(message, type = 'info') {
            const messagesDiv = document.getElementById('messages');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            messagesDiv.appendChild(logEntry);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = `状态: ${status}`;
            statusDiv.className = `status ${className}`;
        }
        
        function updateUI(connected) {
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
            document.getElementById('subscribeBtn').disabled = !connected;
        }
        
        function connectWebSocket() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                log('WebSocket已经连接', 'info');
                return;
            }
            
            updateStatus('连接中...', 'connecting');
            log(`尝试连接到: ${wsUrl}`, 'info');
            
            try {
                socket = new WebSocket(wsUrl);
                
                socket.onopen = function(event) {
                    log('WebSocket连接成功建立!', 'success');
                    log(`连接状态: ${socket.readyState}`, 'info');
                    updateStatus('已连接', 'connected');
                    updateUI(true);
                    reconnectAttempts = 0;
                    document.getElementById('reconnectCount').textContent = reconnectAttempts;
                    
                    // 连接成功后自动发送订阅请求
                    setTimeout(() => {
                        subscribeToMarket();
                    }, 100);
                };
                
                socket.onmessage = function(event) {
                    messageCount++;
                    document.getElementById('messageCount').textContent = messageCount;
                    
                    try {
                        const data = JSON.parse(event.data);
                        if (data.data && Array.isArray(data.data)) {
                            log(`收到市场数据: ${data.arg?.channel} - ${data.arg?.instId}`, 'data');
                        } else {
                            log(`收到消息: ${JSON.stringify(data)}`, 'data');
                        }
                    } catch (e) {
                        log(`收到原始消息: ${event.data.substring(0, 100)}...`, 'data');
                    }
                };
                
                socket.onclose = function(event) {
                    log(`WebSocket连接关闭: 代码=${event.code}, 原因=${event.reason || '无'}`, 'error');
                    updateStatus('已断开', 'disconnected');
                    updateUI(false);
                    
                    // 自动重连逻辑
                    if (reconnectAttempts < maxReconnectAttempts && event.code !== 1000) {
                        reconnectAttempts++;
                        document.getElementById('reconnectCount').textContent = reconnectAttempts;
                        log(`${reconnectDelay/1000}秒后尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})`, 'info');
                        setTimeout(connectWebSocket, reconnectDelay);
                    } else if (reconnectAttempts >= maxReconnectAttempts) {
                        log('达到最大重连次数，停止重连', 'error');
                    }
                };
                
                socket.onerror = function(error) {
                    log(`WebSocket错误: ${error}`, 'error');
                    log(`连接状态: ${socket ? socket.readyState : 'null'}`, 'error');
                    updateStatus('连接错误', 'disconnected');
                };
                
            } catch (error) {
                log(`创建WebSocket失败: ${error}`, 'error');
                updateStatus('连接失败', 'disconnected');
            }
        }
        
        function disconnectWebSocket() {
            if (socket) {
                log('手动断开WebSocket连接', 'info');
                socket.close(1000, '用户主动断开');
                socket = null;
                reconnectAttempts = maxReconnectAttempts; // 防止自动重连
            }
        }
        
        function subscribeToMarket() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                log('WebSocket未连接，无法发送订阅请求', 'error');
                return;
            }
            
            const subscriptionRequest = {
                op: "subscribe",
                args: [
                    { channel: "tickers", instId: "BTC-USDT" },
                    { channel: "candle1m", instId: "BTC-USDT" }
                ]
            };
            
            try {
                socket.send(JSON.stringify(subscriptionRequest));
                log(`发送订阅请求: ${JSON.stringify(subscriptionRequest)}`, 'success');
            } catch (error) {
                log(`发送订阅请求失败: ${error}`, 'error');
            }
        }
        
        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
            messageCount = 0;
            document.getElementById('messageCount').textContent = messageCount;
        }
        
        // 页面加载时显示初始状态
        document.getElementById('wsUrl').textContent = wsUrl;
        log('页面加载完成，准备连接WebSocket', 'info');
    </script>
</body>
</html>