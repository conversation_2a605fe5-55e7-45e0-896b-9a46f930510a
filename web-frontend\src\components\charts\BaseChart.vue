<template>
  <a-card :title="title" size="small" :bordered="false">
    <template #extra>
      <a-space>
        <slot name="extra-controls"></slot>
        <a-tooltip title="设置">
          <a-button size="small" type="text" @click="showSettings = true">
            <SettingOutlined />
          </a-button>
        </a-tooltip>
        <a-tooltip title="信号" v-if="signals.length > 0">
          <a-badge :count="signals.length" :offset="[5, 0]">
            <a-button size="small" type="text" @click="showSignals = true">
              <BellOutlined />
            </a-button>
          </a-badge>
        </a-tooltip>
      </a-space>
    </template>

    <div ref="chartRef" class="base-chart" :style="{ height: height + 'px' }"></div>

    <a-modal v-model:open="showSettings" :title="`${title} 设置`" @ok="handleSaveSettings" @cancel="handleCancelSettings">
      <slot name="settings-form" :settings="localSettings"></slot>
    </a-modal>

    <a-modal v-model:open="showSignals" :title="`${title} 交易信号`" :footer="null" width="600px">
      <slot name="signals-list" :signals="signals"></slot>
    </a-modal>
  </a-card>
</template>

<script setup>
import { ref, toRefs } from 'vue';
import { SettingOutlined, BellOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  height: {
    type: Number,
    default: 300,
  },
  initialSettings: {
    type: Object,
    default: () => ({}),
  },
  signals: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['settings-save']);

const chartRef = ref(null);
const showSettings = ref(false);
const showSignals = ref(false);

const localSettings = ref({ ...props.initialSettings });

const handleSaveSettings = () => {
  emit('settings-save', { ...localSettings.value });
  showSettings.value = false;
};

const handleCancelSettings = () => {
  localSettings.value = { ...props.initialSettings };
  showSettings.value = false;
};

defineExpose({ chartRef });
</script>

<style scoped>
.base-chart {
  width: 100%;
}
</style>