<template>
  <div class="trading-dashboard" :class="{ 'dark-theme': chartStore.isDarkTheme }">
    <!-- 顶部控制栏 -->
    <div class="dashboard-header">
      <ChartControls />
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 左侧图表区域 -->
      <div class="charts-section">
        <!-- K线图 -->
        <div class="main-chart-container">
          <a-card title="K线图" size="small" :bordered="false">
            <template #extra>
              <a-space>
                <a-tooltip title="全屏">
                  <a-button size="small" type="text" @click="toggleFullscreen('kline')">
                    <FullscreenOutlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip title="设置">
                  <a-button size="small" type="text" @click="showAdvancedSettings = true">
                    <SettingOutlined />
                  </a-button>
                </a-tooltip>
              </a-space>
            </template>
            <div ref="klineChartRef" class="kline-chart" style="height: 400px;"></div>
          </a-card>
        </div>

        <!-- 统一指标管理器 -->
        <UnifiedIndicatorManager 
          :chart-instance="klineChart"
          @indicator-changed="handleIndicatorChanged"
          @settings-updated="handleSettingsUpdated"
        />
      </div>

      <!-- 右侧信息面板 -->
      <div class="info-panels">
        <!-- 图表交易界面 -->
        <div class="panel-item">
          <ChartTradingInterface />
        </div>
        
        <!-- 风险管理 -->
        <div class="panel-item">
          <RiskManagement />
        </div>
        
        <!-- 原有信息面板 -->
        <div class="panel-item">
          <InfoPanelSection />
        </div>
      </div>
    </div>

    <!-- 高级图表设置 -->
    <AdvancedChartSettings
      v-model:visible="showAdvancedSettings"
      :display-settings="displaySettings"
      :indicator-settings="indicatorSettings"
      :k-line-styles="kLineStyles"
      :trading-settings="tradingSettings"
      :data-settings="dataSettings"
      :update-settings="updateSettings"
      @save="handleAdvancedSettingsSave"
      @cancel="handleAdvancedSettingsCancel"
    />

    <!-- 全屏模态框 -->
    <a-modal
      v-model:open="fullscreenModal.show"
      :title="fullscreenModal.title"
      width="90vw"
      :footer="null"
      :destroy-on-close="true"
    >
      <div :ref="fullscreenModal.chartRef" style="height: 70vh;"></div>
    </a-modal>
  </div>
</template>

<script setup>
import * as klinecharts from 'klinecharts';
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { message, notification } from 'ant-design-vue'
import {
  FullscreenOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'

// 导入组件
import ChartControls from '@/components/ChartControls.vue'
import UnifiedIndicatorManager from '@/components/UnifiedIndicatorManager.vue'
import InfoPanelSection from '@/components/InfoPanelSection.vue'
import { indicatorOptimizer } from '@/utils/indicatorOptimizer'

// 导入状态管理
import { useChartStore } from '@/stores/chartStore'
import { useChartDataStore } from '@/stores/chartDataStore'
import { useChartCommon } from '@/composables/useChartCommon'
import { useKLineCharts } from '@/composables/useKLineCharts'
import ChartTradingInterface from '@/components/ChartTradingInterface.vue'
import RiskManagement from '@/components/RiskManagement.vue'
import AdvancedChartSettings from '@/components/AdvancedChartSettings.vue'
// import { CHART_DATA_INDEX } from '@/constants/chartConstants' // Removed as it's not used

// 状态管理
const chartStore = useChartStore()
const dataStore = useChartDataStore()
const { calculateMA, calculateEMA } = useChartCommon()
const { updateChartStyles } = useKLineCharts()

// 本地状态
const klineChartRef = ref(null)
const klineChart = ref(null)
const showAdvancedSettings = ref(false)

// 高级图表设置
const displaySettings = ref({
  showTechnicalIndicators: true,
  chartTheme: 'light',
  klineStyle: 'candlestick',
  showGrid: true,
  showCrosshair: true,
  showLegend: true,
  enableAnimation: true,
  showCountdown: true
})

const indicatorSettings = ref({
  ma: {
    enabled: true,
    periods: [5, 10, 20, 60],
    colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
  },
  rsi: {
    enabled: true,
    period: 14,
    overbought: 70,
    oversold: 30
  },
  macd: {
    enabled: true,
    fastPeriod: 12,
    slowPeriod: 26,
    signalPeriod: 9
  },
  boll: {
    enabled: false,
    period: 20,
    stdDev: 2
  },
  kdj: {
    enabled: false,
    period: 9,
    kPeriod: 3,
    dPeriod: 3
  }
})

const kLineStyles = ref({
  upColor: '#26a69a',
  downColor: '#ef5350',
  noChangeColor: '#888888',
  gridColor: '#e8e8e8',
  axisColor: '#DDDDDD',
  textColor: '#76808F',
  animationDuration: 1000
})

const tradingSettings = ref({
  enableTradingSignals: true,
  enablePriceAlerts: true,
  autoRefresh: true,
  showOrderBook: true
})

const dataSettings = ref({
  defaultDataSource: 'okx',
  dataCount: 1000,
  cacheTime: 300,
  retryCount: 3
})

const updateSettings = ref({
  highActivityThreshold: 100,
  mediumActivityThreshold: 50,
  updateInterval: 1000
})

// 全屏模态框状态
const fullscreenModal = reactive({
  show: false,
  title: '',
  chartRef: 'fullscreenChartRef',
  chartInstance: null
})

import { watch } from 'vue'

function initKlineChart() {
  console.log('Attempting to initialize K-line chart...');
  if (!klineChartRef.value) {
    console.error('Error: klineChartRef is not available.');
    return;
  }

  const chartContainer = klineChartRef.value;
  if (chartContainer.getBoundingClientRect().width === 0 || chartContainer.getBoundingClientRect().height === 0) {
    console.error('Error: Chart container has zero dimensions. Deferring initialization.');
    setTimeout(initKlineChart, 100);
    return;
  }

  if (!dataStore.chartData || dataStore.chartData.length === 0) {
    console.error('Error: No chart data available to initialize the chart.');
    return;
  }

  // 销毁旧实例，防止重复渲染
  if (klineChart.value) {
    klineChart.value.dispose();
  }

  try {
    // 使用新的样式配置系统
    const { getKLineThemeOptions } = useKLineCharts()
    const chartStyles = getKLineThemeOptions(chartStore.isDarkTheme, kLineStyles.value)

    klineChart.value = klinecharts.init(klineChartRef.value, {
      customApi: {
        formatDate: (timestamp) => new Date(timestamp).toLocaleString(),
      },
      styles: chartStyles
    });
    
    // 将图表实例注册到useKLineCharts中，以便倒计时更新能够访问
    const { registerChartInstance } = useKLineCharts()
    if (klineChart.value) {
      registerChartInstance('main', klineChart.value)
    }

    if (klineChart.value) {
      klineChart.value.applyNewData(dataStore.chartData);
      
      // 注意：指标现在由UnifiedIndicatorManager统一管理
      // 不在这里直接创建指标，避免重复
      
      // 支持缩放、拖拽
      klineChart.value.setZoomEnabled(true);
      klineChart.value.setScrollEnabled(true);
      // 十字线始终可见
      klineChart.value.setCrosshairVisible(displaySettings.value.showCrosshair);
      
      // 启动倒计时更新（如果启用）
      if (displaySettings.value.showCountdown) {
        const { startCountdownUpdate } = useKLineCharts()
        startCountdownUpdate()
      }
      
      // 启动实时数据更新以实现动画效果
      if (displaySettings.value.enableAnimation) {
        dataStore.startRealtimeUpdates()
      }
      
      console.log('K线图表初始化成功');
    } else {
      console.error('Error: klinecharts.init returned null or undefined.');
    }
  } catch (e) {
    console.error('An error occurred during chart initialization:', e);
  }
}



// 处理指标变化事件
const handleIndicatorChanged = (event) => {
  console.log('指标变化:', event)
  const { action, indicator } = event
  
  switch (action) {
    case 'add':
      message.success(`${indicator.displayName}已添加到图表`)
      break
    case 'remove':
      message.info(`${indicator.displayName}已从图表移除`)
      break
    case 'update':
      message.success(`${indicator.displayName}配置已更新`)
      break
    case 'visibility':
      message.info(`${indicator.displayName}可见性已${indicator.visible ? '开启' : '关闭'}`)
      break
  }
}

// 处理设置更新事件
const handleSettingsUpdated = (settings) => {
  console.log('指标设置已更新:', settings)
  message.success('指标设置已更新')
}

const handleSignalAction = (action, signal) => {
  switch (action) {
    case 'ignore':
      chartStore.removeSignal(signal.id)
      message.info('已忽略该信号')
      break
    case 'follow':
      message.success('已跟随该信号进行交易')
      break
    case 'details':
      // 显示信号详情
      break
  }
}

const handleQuickTrade = (tradeData) => {
  message.success(`提交${tradeData.side === 'buy' ? '买入' : '卖出'}订单: ${tradeData.amount} ${chartStore.selectedSymbol}`)
}

const handlePriceAlert = (alert) => {
  notification.warning({
    message: '价格预警',
    description: alert.message,
    duration: 5
  })
}

const handlePositionClick = (position) => {
  message.info(`查看 ${position.symbol} 持仓详情`)
}

const handleClosePosition = (position) => {
  message.info(`正在平仓 ${position.symbol}`)
}

const handleAdjustPosition = (position) => {
  message.info(`正在调整 ${position.symbol} 持仓`)
}

const handleCancelOrder = (order) => {
  message.info(`正在撤销订单 ${order.id}`)
}

const handleModifyOrder = (order) => {
  message.info(`正在修改订单 ${order.id}`)
}

const handleDepthPriceClick = (price, side) => {
  message.info(`点击了${side === 'buy' ? '买盘' : '卖盘'}价格: ${price}`)
}

// 全屏功能
const toggleFullscreen = (chartType) => {
  fullscreenModal.show = true
  fullscreenModal.title = getChartTitle(chartType)
  
  nextTick(() => {
    // 在模态框中重新创建图表
    initFullscreenChart(chartType)
  })
}

const getChartTitle = (chartType) => {
  const titles = {
    kline: 'K线图',
    volume: '成交量',
    rsi: 'RSI指标',
    macd: 'MACD指标',
    kdj: 'KDJ指标',
    boll: '布林带'
  }
  return titles[chartType] || '图表'
}

const initFullscreenChart = (chartType) => {
  // 这里可以根据chartType初始化不同的全屏图表
  // 为简化示例，这里只展示K线图的全屏版本
  if (chartType === 'kline') {
    // 重新初始化K线图到全屏容器
    // 实际实现中需要获取全屏容器的引用
  }
}

// 处理高级设置保存
const handleAdvancedSettingsSave = (settings) => {
  console.log('保存高级图表设置:', settings)
  
  // 更新本地设置
  Object.assign(displaySettings.value, settings.displaySettings)
  Object.assign(indicatorSettings.value, settings.indicatorSettings)
  Object.assign(kLineStyles.value, settings.kLineStyles)
  Object.assign(tradingSettings.value, settings.tradingSettings)
  Object.assign(dataSettings.value, settings.dataSettings)
  Object.assign(updateSettings.value, settings.updateSettings)
  
  // 更新图表样式
  updateChartStyles(chartStore.isDarkTheme, kLineStyles.value)
  
  // 重新应用动画和倒计时设置
  if (klineChart.value) {
    // 更新十字线显示
    klineChart.value.setCrosshairVisible(displaySettings.value.showCrosshair)
    
    // 重新启动或停止实时更新
    if (displaySettings.value.enableAnimation) {
      dataStore.startRealtimeUpdates()
    } else {
      dataStore.stopRealtimeUpdates()
    }
    
    // 重新启动或停止倒计时
    const { startCountdownUpdate, stopCountdownUpdate } = useKLineCharts()
    if (displaySettings.value.showCountdown) {
      startCountdownUpdate()
    } else {
      stopCountdownUpdate()
    }
  }
  
  message.success('图表设置已保存')
  showAdvancedSettings.value = false
}

// 处理高级设置取消
const handleAdvancedSettingsCancel = () => {
  showAdvancedSettings.value = false
}

const loadKlineSettings = () => {
  const saved = localStorage.getItem('klineSettings')
  if (saved) {
    try {
      Object.assign(klineSettings, JSON.parse(saved))
    } catch (error) {
      console.error('加载K线图设置失败:', error)
    }
  }
}

// 窗口大小调整处理
const handleResize = () => {
  if (klineChart.value) {
    klineChart.value.resize();
  }
  
  // 调整其他图表大小
  Object.values(chartStore.chartInstances).forEach(chart => {
    if (chart && typeof chart.resize === 'function') {
      chart.resize();
    }
  });
};

// 组件挂载
onMounted(async () => {
  console.log('🚀 ModularTradingDashboard onMounted 开始执行');
  console.log('🚀 当前dataStore:', dataStore);
  console.log('🚀 dataStore.fetchCandlestickData 函数:', typeof dataStore.fetchCandlestickData);
  
  try {
    console.log('🚀 开始调用 fetchCandlestickData');
    await dataStore.fetchCandlestickData();
    console.log('🚀 fetchCandlestickData 完成，开始初始化图表');
    initKlineChart();
    // 确保实时更新启动
    if (displaySettings.value.enableAnimation) {
      console.log('🚀 启动实时更新');
      dataStore.startRealtimeUpdates()
    }
  } catch (error) {
    console.error('🚀 fetchCandlestickData 失败:', error);
    console.error('🚀 错误堆栈:', error.stack);
  }
});



// 组件卸载
onUnmounted(() => {
  // 清理图表实例
  if (klineChart.value) {
    klineChart.value.dispose();
  }
  
  // 注销图表实例
  const { unregisterChartInstance, stopCountdownUpdate } = useKLineCharts()
  unregisterChartInstance('main')
  stopCountdownUpdate()
  
  // 清理事件监听
  window.removeEventListener('resize', handleResize);
  
  // 停止实时更新
  dataStore.stopRealtimeUpdates();
  
  // 清理所有图表实例
  // chartStore.disposeAllCharts(); // This might be handled elsewhere or not needed if components manage their own lifecycle
});



// 监听数据变化
watch(() => dataStore.chartData, (newData) => {
  console.log('📊 监听到chartData变化:', { hasChart: !!klineChart.value, dataLength: newData?.length });
  if (klineChart.value && newData && newData.length > 0) {
    console.log('📊 应用新数据到图表:', { latestPrice: newData[newData.length - 1]?.close });
    klineChart.value.applyNewData(newData)
  }
}, { deep: true })

// 监听主题变化
watch(() => chartStore.isDarkTheme, (isDark) => {
  if (klineChart.value) {
    updateChartStyles(isDark, kLineStyles.value)
  }
})

// 监听K线样式变化
watch(() => kLineStyles.value, (newStyles) => {
  if (klineChart.value) {
    updateChartStyles(chartStore.isDarkTheme, newStyles)
  }
}, { deep: true })
</script>

<style scoped>
.trading-dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.dashboard-header {
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  padding: 8px 16px;
  z-index: 100;
}

.dashboard-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

.charts-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 0;
}

.main-chart-container {
  flex: 1;
  min-height: 400px;
  height: 400px;
  width: 100%;
  position: relative;
}

.indicators-container {
  flex: 0 0 auto;
}

.info-panels {
  width: 350px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

.panel-item {
  flex: 0 0 auto;
  min-height: fit-content;
}

.kline-chart {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* 深色主题样式 */
.trading-dashboard.dark-theme {
  background: #1e222d;
}

.trading-dashboard.dark-theme .dashboard-header {
  background: #2a2e39;
  border-bottom-color: #434651;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .info-panels {
    width: 300px;
  }
}

@media (max-width: 992px) {
  .dashboard-content {
    flex-direction: column;
  }
  
  .info-panels {
    width: 100%;
    flex-direction: row;
    overflow-x: auto;
  }
  
  .panel-item {
    flex: 0 0 300px;
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: 8px;
    gap: 8px;
  }
  
  .info-panels {
    flex-direction: column;
  }
  
  .panel-item {
    flex: 0 0 auto;
  }
}
</style>
<!-- 删除多余的template结束标签 -->

<style scoped>
.trading-dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.dashboard-header {
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  padding: 8px 16px;
  z-index: 100;
}

.dashboard-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

.charts-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 0;
}

.main-chart-container {
  flex: 1;
  min-height: 400px;
  height: 400px;
  width: 100%;
  position: relative;
}

.indicators-container {
  flex: 0 0 auto;
}

.info-panels {
  width: 350px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

.panel-item {
  flex: 0 0 auto;
  min-height: fit-content;
}

.kline-chart {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* 深色主题样式 */
.trading-dashboard.dark-theme {
  background: #1e222d;
}

.trading-dashboard.dark-theme .dashboard-header {
  background: #2a2e39;
  border-bottom-color: #434651;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .info-panels {
    width: 300px;
  }
}

@media (max-width: 992px) {
  .dashboard-content {
    flex-direction: column;
  }
  
  .info-panels {
    width: 100%;
    flex-direction: row;
    overflow-x: auto;
  }
  
  .panel-item {
    flex: 0 0 300px;
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: 8px;
    gap: 8px;
  }
  
  .info-panels {
    flex-direction: column;
  }
  
  .panel-item {
    flex: 0 0 auto;
  }
}
</style>
<!-- 删除多余的template结束标签,因为已经在上面有一个template结束标签了 -->
