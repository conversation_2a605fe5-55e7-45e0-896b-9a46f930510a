<template>
  <div class="info-panels">
    <!-- 价格信息面板 -->
    <div class="panel-item">
      <PriceInfoPanel
        :selected-symbol="chartStore.selectedSymbol"
        :selected-timeframe="chartStore.selectedTimeframe"
        :chart-data="chartStore.chartData"
        :current-price="chartStore.realTimePriceData.price || 0"
        :price-change-direction="chartStore.realTimePriceData.direction || 'neutral'"
        @alert-triggered="handlePriceAlert"
      />
    </div>

    <!-- 交易信号面板 -->
    <div class="panel-item">
      <TradingSignalsPanel
        :signals="chartStore.tradingSignals"
        @signal-action="handleSignalAction"
        @quick-trade="handleQuickTrade"
      />
    </div>

    <!-- 持仓面板 -->
    <div class="panel-item">
      <PositionPanel
        @position-click="handlePositionClick"
        @close-position="handleClosePosition"
        @adjust-position="handleAdjustPosition"
        @cancel-order="handleCancelOrder"
        @modify-order="handleModifyOrder"
      />
    </div>

    <!-- 市场深度面板 -->
    <div class="panel-item">
      <MarketDepthPanel
        :symbol="chartStore.selectedSymbol"
        @price-click="handleDepthPriceClick"
      />
    </div>
  </div>
</template>

<script setup>
import TradingSignalsPanel from '@/components/TradingSignalsPanel.vue'
import PriceInfoPanel from '@/components/PriceInfoPanel.vue'
import PositionPanel from '@/components/PositionPanel.vue'
import MarketDepthPanel from '@/components/MarketDepthPanel.vue'
import { useChartStore } from '@/stores/chartStore'

const chartStore = useChartStore()

const handlePriceAlert = (alert) => {
  console.log('Price alert triggered:', alert)
}

const handleSignalAction = (action) => {
  console.log('Signal action:', action)
}

const handleQuickTrade = (trade) => {
  console.log('Quick trade:', trade)
}

const handlePositionClick = (position) => {
  console.log('Position clicked:', position)
}

const handleClosePosition = (position) => {
  console.log('Close position:', position)
}

const handleAdjustPosition = (position) => {
  console.log('Adjust position:', position)
}

const handleCancelOrder = (order) => {
  console.log('Cancel order:', order)
}

const handleModifyOrder = (order) => {
  console.log('Modify order:', order)
}

const handleDepthPriceClick = (price) => {
  console.log('Depth price clicked:', price)
}
</script>

<style scoped>
.info-panels {
  width: 380px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.panel-item {
  background-color: var(--component-background);
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>