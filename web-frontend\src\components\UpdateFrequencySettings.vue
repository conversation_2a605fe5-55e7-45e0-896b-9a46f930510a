<template>
  <div class="update-frequency-settings">
    <a-card title="数据更新频率设置" size="small">
      <a-space direction="vertical" style="width: 100%;">
        <!-- 动态调整开关 -->
        <a-row align="middle">
          <a-col :span="12">
            <span>智能频率调整</span>
            <a-tooltip title="根据市场活跃度自动调整更新频率">
              <QuestionCircleOutlined style="margin-left: 4px; color: #999;" />
            </a-tooltip>
          </a-col>
          <a-col :span="12">
            <a-switch 
              v-model:checked="isDynamicEnabled" 
              @change="onDynamicToggle"
            />
          </a-col>
        </a-row>

        <!-- 当前市场活跃度 -->
        <a-row align="middle" v-if="isDynamicEnabled">
          <a-col :span="12">
            <span>市场活跃度</span>
          </a-col>
          <a-col :span="12">
            <a-tag :color="getActivityColor(currentActivity)">
              {{ getActivityText(currentActivity) }}
            </a-tag>
          </a-col>
        </a-row>

        <!-- 更新频率配置 -->
        <a-divider orientation="left" style="margin: 12px 0;">更新频率配置</a-divider>
        
        <div class="frequency-list">
          <div 
            v-for="(config, type) in frequencyConfigs" 
            :key="type"
            class="frequency-item"
          >
            <a-row align="middle" :gutter="8">
              <a-col :span="8">
                <span class="frequency-label">{{ config.label }}</span>
                <a-tooltip :title="config.description">
                  <QuestionCircleOutlined style="margin-left: 4px; color: #999;" />
                </a-tooltip>
              </a-col>
              <a-col :span="10">
                <a-input-number
                  v-model:value="customIntervals[type]"
                  :min="config.min"
                  :max="config.max"
                  :step="config.step"
                  :placeholder="getInterval(type).toString()"
                  size="small"
                  style="width: 100%;"
                  @change="onIntervalChange(type, $event)"
                >
                  <template #addonAfter>ms</template>
                </a-input-number>
              </a-col>
              <a-col :span="4">
                <a-tag size="small" :color="getIntervalColor(type)">
                  {{ formatInterval(getInterval(type)) }}
                </a-tag>
              </a-col>
              <a-col :span="2">
                <a-button 
                  size="small" 
                  type="text" 
                  @click="resetInterval(type)"
                  :disabled="!customIntervals[type]"
                >
                  <ReloadOutlined />
                </a-button>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 预设配置 -->
        <a-divider orientation="left" style="margin: 12px 0;">预设配置</a-divider>
        
        <a-space>
          <a-button size="small" @click="applyPreset('conservative')">
            保守模式
          </a-button>
          <a-button size="small" @click="applyPreset('balanced')">
            平衡模式
          </a-button>
          <a-button size="small" @click="applyPreset('aggressive')">
            激进模式
          </a-button>
          <a-button size="small" @click="resetToDefaults">
            重置默认
          </a-button>
        </a-space>

        <!-- 配置摘要 -->
        <a-divider orientation="left" style="margin: 12px 0;">当前配置</a-divider>
        
        <div class="config-summary">
          <a-descriptions size="small" :column="2">
            <a-descriptions-item label="活跃度">
              {{ getActivityText(currentActivity) }}
            </a-descriptions-item>
            <a-descriptions-item label="动态调整">
              {{ isDynamicEnabled ? '启用' : '禁用' }}
            </a-descriptions-item>
            <a-descriptions-item label="自定义项">
              {{ Object.keys(customIntervals).length }}
            </a-descriptions-item>
            <a-descriptions-item label="最后成交量">
              {{ lastVolume24h ? formatVolume(lastVolume24h) : '未知' }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </a-space>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { QuestionCircleOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import { useUpdateFrequency } from '@/composables/useUpdateFrequency';
import { UPDATE_DESCRIPTIONS } from '@/constants/updateConfig';

// 使用更新频率管理
const {
  currentActivity,
  isDynamicEnabled,
  customIntervals,
  lastVolume24h,
  getInterval,
  setCustomInterval,
  clearCustomInterval,
  setDynamicEnabled,
  resetToDefaults
} = useUpdateFrequency();

// 频率配置定义
const frequencyConfigs = {
  TICKER: {
    label: '实时价格',
    description: UPDATE_DESCRIPTIONS.TICKER,
    min: 1000,
    max: 30000,
    step: 1000
  },
  CANDLESTICK: {
    label: 'K线数据',
    description: UPDATE_DESCRIPTIONS.CANDLESTICK,
    min: 5000,
    max: 60000,
    step: 5000
  },
  ORDERS: {
    label: '订单状态',
    description: UPDATE_DESCRIPTIONS.ORDERS,
    min: 2000,
    max: 30000,
    step: 1000
  },
  POSITIONS: {
    label: '持仓信息',
    description: UPDATE_DESCRIPTIONS.POSITIONS,
    min: 5000,
    max: 60000,
    step: 5000
  },
  BALANCE: {
    label: '账户余额',
    description: UPDATE_DESCRIPTIONS.BALANCE,
    min: 10000,
    max: 120000,
    step: 10000
  }
};

// 预设配置
const presets = {
  conservative: {
    TICKER: 5000,
    CANDLESTICK: 30000,
    ORDERS: 10000,
    POSITIONS: 30000,
    BALANCE: 60000
  },
  balanced: {
    TICKER: 3000,
    CANDLESTICK: 10000,
    ORDERS: 5000,
    POSITIONS: 15000,
    BALANCE: 30000
  },
  aggressive: {
    TICKER: 1000,
    CANDLESTICK: 5000,
    ORDERS: 2000,
    POSITIONS: 5000,
    BALANCE: 10000
  }
};

/**
 * 动态调整开关变化处理
 */
const onDynamicToggle = (checked) => {
  setDynamicEnabled(checked);
  message.success(checked ? '已启用智能频率调整' : '已禁用智能频率调整');
};

/**
 * 更新间隔变化处理
 */
const onIntervalChange = (type, value) => {
  if (value && value > 0) {
    setCustomInterval(type, value);
    message.success(`${frequencyConfigs[type].label}更新频率已设置为${value}ms`);
  }
};

/**
 * 重置单个间隔
 */
const resetInterval = (type) => {
  clearCustomInterval(type);
  customIntervals.value[type] = undefined;
  message.success(`${frequencyConfigs[type].label}更新频率已重置`);
};

/**
 * 应用预设配置
 */
const applyPreset = (presetName) => {
  const preset = presets[presetName];
  if (!preset) return;
  
  Object.entries(preset).forEach(([type, interval]) => {
    setCustomInterval(type, interval);
    customIntervals.value[type] = interval;
  });
  
  message.success(`已应用${getPresetName(presetName)}配置`);
};

/**
 * 获取预设名称
 */
const getPresetName = (preset) => {
  const names = {
    conservative: '保守模式',
    balanced: '平衡模式',
    aggressive: '激进模式'
  };
  return names[preset] || preset;
};

/**
 * 获取活跃度颜色
 */
const getActivityColor = (activity) => {
  const colors = {
    HIGH_ACTIVITY: 'red',
    MEDIUM_ACTIVITY: 'orange',
    LOW_ACTIVITY: 'green'
  };
  return colors[activity] || 'default';
};

/**
 * 获取活跃度文本
 */
const getActivityText = (activity) => {
  const texts = {
    HIGH_ACTIVITY: '高活跃',
    MEDIUM_ACTIVITY: '中等活跃',
    LOW_ACTIVITY: '低活跃'
  };
  return texts[activity] || '未知';
};

/**
 * 获取间隔颜色
 */
const getIntervalColor = (type) => {
  const interval = getInterval(type);
  if (interval <= 2000) return 'red';
  if (interval <= 5000) return 'orange';
  if (interval <= 15000) return 'blue';
  return 'green';
};

/**
 * 格式化间隔显示
 */
const formatInterval = (interval) => {
  if (interval >= 60000) {
    return `${(interval / 60000).toFixed(1)}分`;
  }
  if (interval >= 1000) {
    return `${(interval / 1000).toFixed(1)}秒`;
  }
  return `${interval}ms`;
};

/**
 * 格式化成交量显示
 */
const formatVolume = (volume) => {
  const vol = parseFloat(volume);
  if (vol >= 1000000) {
    return `${(vol / 1000000).toFixed(1)}M`;
  }
  if (vol >= 1000) {
    return `${(vol / 1000).toFixed(1)}K`;
  }
  return vol.toFixed(2);
};
</script>

<style scoped>
.update-frequency-settings {
  width: 100%;
}

.frequency-list {
  max-height: 300px;
  overflow-y: auto;
}

.frequency-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.frequency-item:last-child {
  border-bottom: none;
}

.frequency-label {
  font-size: 13px;
  font-weight: 500;
}

.config-summary {
  background: #fafafa;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}
</style>