<template>
  <div class="position-panel">
    <a-card title="持仓信息" size="small">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新持仓">
            <a-button size="small" type="text" @click="refreshPositions">
              <ReloadOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="持仓设置">
            <a-button size="small" type="text" @click="showSettings = true">
              <SettingOutlined />
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <div class="position-container">
        <!-- 账户概览 -->
        <div class="account-overview">
          <div class="balance-info">
            <div class="balance-item">
              <span class="balance-label">总资产:</span>
              <span class="balance-value">{{ formatCurrency(accountBalance.total) }}</span>
            </div>
            <div class="balance-item">
              <span class="balance-label">可用余额:</span>
              <span class="balance-value">{{ formatCurrency(accountBalance.available) }}</span>
            </div>
            <div class="balance-item">
              <span class="balance-label">冻结资金:</span>
              <span class="balance-value">{{ formatCurrency(accountBalance.frozen) }}</span>
            </div>
            <div class="balance-item">
              <span class="balance-label">今日盈亏:</span>
              <span :class="['balance-value', getPnlClass(accountBalance.dailyPnl)]">
                {{ formatPnl(accountBalance.dailyPnl) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 持仓列表 -->
        <div class="positions-section">
          <div class="section-header">
            <span>当前持仓</span>
            <a-badge :count="positions.length" :offset="[10, 0]" />
          </div>
          
          <div v-if="positions.length === 0" class="no-positions">
            <a-empty description="暂无持仓" size="small" />
          </div>
          
          <div v-else class="positions-list">
            <div
              v-for="position in positions"
              :key="position.symbol"
              class="position-item"
              @click="handlePositionClick(position)"
            >
              <div class="position-header">
                <div class="position-symbol">
                  {{ position.symbol }}
                  <a-tag
                    :color="position.side === 'long' ? 'green' : 'red'"
                    size="small"
                  >
                    {{ position.side === 'long' ? '多' : '空' }}
                  </a-tag>
                </div>
                <div class="position-size">
                  {{ formatAmount(position.size) }}
                </div>
              </div>
              
              <div class="position-details">
                <div class="detail-row">
                  <span class="detail-label">开仓价:</span>
                  <span class="detail-value">{{ formatPrice(position.entryPrice) }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">标记价:</span>
                  <span class="detail-value">{{ formatPrice(position.markPrice) }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">未实现盈亏:</span>
                  <span :class="['detail-value', getPnlClass(position.unrealizedPnl)]">
                    {{ formatPnl(position.unrealizedPnl) }}
                  </span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">收益率:</span>
                  <span :class="['detail-value', getPnlClass(position.unrealizedPnl)]">
                    {{ formatPercent(position.roe) }}%
                  </span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">保证金:</span>
                  <span class="detail-value">{{ formatCurrency(position.margin) }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">杠杆:</span>
                  <span class="detail-value">{{ position.leverage }}x</span>
                </div>
              </div>
              
              <div class="position-actions">
                <a-button
                  size="small"
                  type="primary"
                  @click.stop="handleClosePosition(position)"
                >
                  平仓
                </a-button>
                <a-button
                  size="small"
                  @click.stop="handleAdjustPosition(position)"
                >
                  调整
                </a-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 委托订单 -->
        <div class="orders-section">
          <div class="section-header">
            <span>委托订单</span>
            <a-badge :count="openOrders.length" :offset="[10, 0]" />
          </div>
          
          <div v-if="openOrders.length === 0" class="no-orders">
            <a-empty description="暂无委托" size="small" />
          </div>
          
          <div v-else class="orders-list">
            <div
              v-for="order in openOrders"
              :key="order.id"
              class="order-item"
            >
              <div class="order-header">
                <div class="order-symbol">
                  {{ order.symbol }}
                  <a-tag
                    :color="order.side === 'buy' ? 'green' : 'red'"
                    size="small"
                  >
                    {{ order.side === 'buy' ? '买' : '卖' }}
                  </a-tag>
                  <a-tag size="small" color="blue">
                    {{ getOrderTypeText(order.type) }}
                  </a-tag>
                </div>
                <div class="order-time">
                  {{ formatTime(order.createdAt) }}
                </div>
              </div>
              
              <div class="order-details">
                <div class="detail-row">
                  <span class="detail-label">委托价:</span>
                  <span class="detail-value">{{ formatPrice(order.price) }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">委托量:</span>
                  <span class="detail-value">{{ formatAmount(order.amount) }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">已成交:</span>
                  <span class="detail-value">{{ formatAmount(order.filled) }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">状态:</span>
                  <span :class="['detail-value', getOrderStatusClass(order.status)]">
                    {{ getOrderStatusText(order.status) }}
                  </span>
                </div>
              </div>
              
              <div class="order-actions">
                <a-button
                  size="small"
                  danger
                  @click="handleCancelOrder(order)"
                >
                  撤销
                </a-button>
                <a-button
                  size="small"
                  @click="handleModifyOrder(order)"
                >
                  修改
                </a-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 交易历史 -->
        <div class="history-section">
          <div class="section-header">
            <span>最近交易</span>
            <a-button size="small" type="link" @click="showHistoryModal = true">
              查看全部
            </a-button>
          </div>
          
          <div class="history-list">
            <div
              v-for="trade in recentTrades.slice(0, 5)"
              :key="trade.id"
              class="trade-item"
            >
              <div class="trade-info">
                <span class="trade-symbol">{{ trade.symbol }}</span>
                <span :class="['trade-side', trade.side]">
                  {{ trade.side === 'buy' ? '买入' : '卖出' }}
                </span>
              </div>
              <div class="trade-details">
                <div class="trade-price">{{ formatPrice(trade.price) }}</div>
                <div class="trade-amount">{{ formatAmount(trade.amount) }}</div>
                <div class="trade-time">{{ formatTime(trade.timestamp) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 持仓设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="持仓设置"
      @ok="saveSettings"
      @cancel="resetSettings"
    >
      <a-form :model="settings" layout="vertical">
        <a-form-item label="显示精度">
          <a-row :gutter="12">
            <a-col :span="12">
              <a-form-item label="价格精度" name="pricePrecision">
                <a-select v-model:value="settings.pricePrecision">
                  <a-select-option :value="2">2位小数</a-select-option>
                  <a-select-option :value="4">4位小数</a-select-option>
                  <a-select-option :value="6">6位小数</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="数量精度" name="amountPrecision">
                <a-select v-model:value="settings.amountPrecision">
                  <a-select-option :value="4">4位小数</a-select-option>
                  <a-select-option :value="6">6位小数</a-select-option>
                  <a-select-option :value="8">8位小数</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="settings.autoRefresh">
            自动刷新持仓 (每5秒)
          </a-checkbox>
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="settings.showUnrealizedPnl">
            显示未实现盈亏
          </a-checkbox>
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="settings.showMargin">
            显示保证金信息
          </a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 交易历史模态框 -->
    <a-modal
      v-model:open="showHistoryModal"
      title="交易历史"
      width="800px"
      :footer="null"
    >
      <a-table
        :columns="historyColumns"
        :data-source="recentTrades"
        :pagination="{ pageSize: 10 }"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'side'">
            <a-tag :color="record.side === 'buy' ? 'green' : 'red'">
              {{ record.side === 'buy' ? '买入' : '卖出' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'price'">
            {{ formatPrice(record.price) }}
          </template>
          <template v-else-if="column.key === 'amount'">
            {{ formatAmount(record.amount) }}
          </template>
          <template v-else-if="column.key === 'timestamp'">
            {{ formatFullTime(record.timestamp) }}
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

// 定义事件
const emit = defineEmits([
  'position-click',
  'close-position',
  'adjust-position',
  'cancel-order',
  'modify-order'
])

// 本地状态
const showSettings = ref(false)
const showHistoryModal = ref(false)
const refreshInterval = ref(null)

// 设置
const settings = ref({
  pricePrecision: 2,
  amountPrecision: 4,
  autoRefresh: true,
  showUnrealizedPnl: true,
  showMargin: true
})

// 模拟数据
const accountBalance = ref({
  total: 125680.50,
  available: 98450.30,
  frozen: 27230.20,
  dailyPnl: 1250.80
})

const positions = ref([
  {
    symbol: 'BTC-USDT',
    side: 'long',
    size: 0.5,
    entryPrice: 50500.00,
    markPrice: 51200.00,
    unrealizedPnl: 350.00,
    roe: 1.39,
    margin: 2525.00,
    leverage: 10
  },
  {
    symbol: 'ETH-USDT',
    side: 'short',
    size: 2.0,
    entryPrice: 3200.00,
    markPrice: 3150.00,
    unrealizedPnl: 100.00,
    roe: 3.13,
    margin: 640.00,
    leverage: 5
  }
])

const openOrders = ref([
  {
    id: '12345',
    symbol: 'BTC-USDT',
    side: 'buy',
    type: 'limit',
    price: 50000.00,
    amount: 0.1,
    filled: 0,
    status: 'open',
    createdAt: Date.now() - 300000
  },
  {
    id: '12346',
    symbol: 'ETH-USDT',
    side: 'sell',
    type: 'stop',
    price: 3300.00,
    amount: 1.0,
    filled: 0.3,
    status: 'partial',
    createdAt: Date.now() - 600000
  }
])

const recentTrades = ref([
  {
    id: '1001',
    symbol: 'BTC-USDT',
    side: 'buy',
    price: 50500.00,
    amount: 0.5,
    timestamp: Date.now() - 3600000
  },
  {
    id: '1002',
    symbol: 'ETH-USDT',
    side: 'sell',
    price: 3200.00,
    amount: 2.0,
    timestamp: Date.now() - 7200000
  },
  {
    id: '1003',
    symbol: 'BTC-USDT',
    side: 'buy',
    price: 49800.00,
    amount: 0.2,
    timestamp: Date.now() - 10800000
  }
])

// 交易历史表格列定义
const historyColumns = [
  {
    title: '交易对',
    dataIndex: 'symbol',
    key: 'symbol'
  },
  {
    title: '方向',
    dataIndex: 'side',
    key: 'side'
  },
  {
    title: '价格',
    dataIndex: 'price',
    key: 'price'
  },
  {
    title: '数量',
    dataIndex: 'amount',
    key: 'amount'
  },
  {
    title: '时间',
    dataIndex: 'timestamp',
    key: 'timestamp'
  }
]

// 格式化函数
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(amount)
}

const formatPrice = (price) => {
  return price.toFixed(settings.value.pricePrecision)
}

const formatAmount = (amount) => {
  return amount.toFixed(settings.value.amountPrecision)
}

const formatPnl = (pnl) => {
  const sign = pnl >= 0 ? '+' : ''
  return sign + formatCurrency(pnl)
}

const formatPercent = (percent) => {
  const sign = percent >= 0 ? '+' : ''
  return sign + percent.toFixed(2)
}

const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatFullTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN')
}

// 样式类函数
const getPnlClass = (pnl) => {
  if (pnl > 0) return 'profit'
  if (pnl < 0) return 'loss'
  return 'neutral'
}

const getOrderStatusClass = (status) => {
  const classes = {
    open: 'status-open',
    partial: 'status-partial',
    filled: 'status-filled',
    cancelled: 'status-cancelled'
  }
  return classes[status] || ''
}

// 文本转换函数
const getOrderTypeText = (type) => {
  const texts = {
    market: '市价',
    limit: '限价',
    stop: '止损',
    'stop-limit': '止损限价'
  }
  return texts[type] || type
}

const getOrderStatusText = (status) => {
  const texts = {
    open: '未成交',
    partial: '部分成交',
    filled: '已成交',
    cancelled: '已撤销'
  }
  return texts[status] || status
}

// 事件处理函数
const handlePositionClick = (position) => {
  emit('position-click', position)
}

const handleClosePosition = (position) => {
  emit('close-position', position)
  message.info(`正在平仓 ${position.symbol} ${position.side === 'long' ? '多头' : '空头'}持仓`)
}

const handleAdjustPosition = (position) => {
  emit('adjust-position', position)
  message.info(`正在调整 ${position.symbol} 持仓`)
}

const handleCancelOrder = (order) => {
  emit('cancel-order', order)
  message.info(`正在撤销订单 ${order.id}`)
}

const handleModifyOrder = (order) => {
  emit('modify-order', order)
  message.info(`正在修改订单 ${order.id}`)
}

const refreshPositions = () => {
  // 模拟刷新数据
  message.success('持仓数据已刷新')
}

const saveSettings = () => {
  localStorage.setItem('positionSettings', JSON.stringify(settings.value))
  
  if (settings.value.autoRefresh) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
  
  showSettings.value = false
  message.success('设置已保存')
}

const resetSettings = () => {
  settings.value = {
    pricePrecision: 2,
    amountPrecision: 4,
    autoRefresh: true,
    showUnrealizedPnl: true,
    showMargin: true
  }
}

const loadSettings = () => {
  const saved = localStorage.getItem('positionSettings')
  if (saved) {
    try {
      settings.value = { ...settings.value, ...JSON.parse(saved) }
    } catch (error) {
      console.error('加载持仓设置失败:', error)
    }
  }
}

const startAutoRefresh = () => {
  stopAutoRefresh()
  if (settings.value.autoRefresh) {
    refreshInterval.value = setInterval(() => {
      // 模拟更新持仓数据
      positions.value.forEach(position => {
        // 模拟价格变动
        const priceChange = (Math.random() - 0.5) * 100
        position.markPrice += priceChange
        
        // 重新计算未实现盈亏
        const priceDiff = position.markPrice - position.entryPrice
        position.unrealizedPnl = position.side === 'long' 
          ? priceDiff * position.size
          : -priceDiff * position.size
        
        // 重新计算收益率
        position.roe = (position.unrealizedPnl / position.margin) * 100
      })
      
      // 更新账户余额
      const totalUnrealizedPnl = positions.value.reduce((sum, pos) => sum + pos.unrealizedPnl, 0)
      accountBalance.value.dailyPnl = totalUnrealizedPnl
    }, 1000)
  }
}

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// 组件挂载
onMounted(() => {
  loadSettings()
  startAutoRefresh()
})

// 组件卸载
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.position-panel {
  height: 100%;
}

.position-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.account-overview {
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.balance-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.balance-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.balance-label {
  color: #8c8c8c;
}

.balance-value {
  font-weight: 500;
  color: #333;
}

.balance-value.profit {
  color: #52c41a;
}

.balance-value.loss {
  color: #ff4d4f;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.no-positions,
.no-orders {
  text-align: center;
  padding: 16px 0;
}

.positions-list,
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.position-item,
.order-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.position-item:hover,
.order-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.position-header,
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.position-symbol,
.order-symbol {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  color: #1890ff;
}

.position-size {
  font-size: 12px;
  color: #666;
}

.order-time {
  font-size: 11px;
  color: #8c8c8c;
}

.position-details,
.order-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
}

.detail-label {
  color: #8c8c8c;
}

.detail-value {
  font-weight: 500;
  color: #333;
}

.detail-value.profit {
  color: #52c41a;
}

.detail-value.loss {
  color: #ff4d4f;
}

.detail-value.status-open {
  color: #1890ff;
}

.detail-value.status-partial {
  color: #faad14;
}

.detail-value.status-filled {
  color: #52c41a;
}

.detail-value.status-cancelled {
  color: #8c8c8c;
}

.position-actions,
.order-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.trade-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: #fafafa;
  border-radius: 4px;
  font-size: 11px;
}

.trade-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trade-symbol {
  font-weight: 500;
  color: #1890ff;
}

.trade-side {
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
}

.trade-side.buy {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.trade-side.sell {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.trade-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 1px;
}

.trade-price {
  font-weight: 500;
  color: #333;
}

.trade-amount {
  color: #666;
}

.trade-time {
  color: #8c8c8c;
}

/* 深色主题样式 */
.position-panel.dark-theme .account-overview {
  background: #2a2e39;
}

.position-panel.dark-theme .balance-value,
.position-panel.dark-theme .section-header {
  color: #ffffff;
}

.position-panel.dark-theme .position-item,
.position-panel.dark-theme .order-item {
  background: #2a2e39;
  border-color: #434651;
}

.position-panel.dark-theme .position-item:hover,
.position-panel.dark-theme .order-item:hover {
  border-color: #2962ff;
  box-shadow: 0 2px 8px rgba(41, 98, 255, 0.1);
}

.position-panel.dark-theme .detail-value {
  color: #ffffff;
}

.position-panel.dark-theme .trade-item {
  background: #1e222d;
}

.position-panel.dark-theme .trade-price {
  color: #ffffff;
}

.position-panel.dark-theme .trade-amount {
  color: #d1d5db;
}
</style>