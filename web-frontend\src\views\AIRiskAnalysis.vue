<template>
  <div class="ai-risk-analysis">
    <h2>AI风险分析</h2>
    <a-form layout="inline" :model="query" @submit.prevent style="margin-bottom:16px">
      <a-form-item label="产品ID">
        <a-input v-model="query.instId" placeholder="如 BTC-USDT-SWAP" style="width:180px" />
      </a-form-item>
      <a-form-item label="分析类型">
        <a-select v-model="query.type" style="width:120px" allow-clear>
          <a-select-option value="position">持仓分析</a-select-option>
          <a-select-option value="leverage">杠杆分析</a-select-option>
          <a-select-option value="market">市场分析</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="fetchAnalysis" :loading="loading">分析</a-button>
      </a-form-item>
    </a-form>

    <a-row :gutter="16">
      <a-col :span="16">
        <a-card title="分析结果" :loading="loading">
          <div v-if="analysis.summary" class="analysis-summary">
            <a-alert :message="analysis.summary" type="info" show-icon style="margin-bottom:16px" />
          </div>
          
          <div v-if="analysis.risks && analysis.risks.length">
            <h3>风险项</h3>
            <a-list size="small">
              <a-list-item v-for="risk in analysis.risks" :key="risk.id">
                <template #actions>
                  <a-tag :color="getRiskColor(risk.level)">{{ getRiskText(risk.level) }}</a-tag>
                </template>
                <a-list-item-meta>
                  <template #title>{{ risk.title }}</template>
                  <template #description>{{ risk.description }}</template>
                </a-list-item-meta>
              </a-list-item>
              <template #empty>
                <div style="text-align:center; padding:32px;">暂无风险项</div>
              </template>
            </a-list>
          </div>

          <div v-if="analysis.suggestions && analysis.suggestions.length" style="margin-top:16px">
            <h3>建议措施</h3>
            <a-list size="small">
              <a-list-item v-for="suggestion in analysis.suggestions" :key="suggestion.id">
                <template #actions>
                  <a-tag :color="suggestion.priority === 'high' ? 'red' : suggestion.priority === 'medium' ? 'orange' : 'blue'">
                    {{ suggestion.priority === 'high' ? '紧急' : suggestion.priority === 'medium' ? '重要' : '建议' }}
                  </a-tag>
                </template>
                <a-list-item-meta>
                  <template #title>{{ suggestion.title }}</template>
                  <template #description>{{ suggestion.description }}</template>
                </a-list-item-meta>
              </a-list-item>
              <template #empty>
                <div style="text-align:center; padding:32px;">暂无建议</div>
              </template>
            </a-list>
          </div>
        </a-card>
      </a-col>

      <a-col :span="8">
        <a-card title="风险评分" :loading="loading">
          <template v-if="analysis.score">
            <div style="text-align:center">
              <h1 style="color:#1890ff;font-size:36px">{{ (analysis.score * 100).toFixed(0) }}</h1>
              <p>综合风险评分</p>
            </div>
            <a-progress 
              :percent="analysis.score * 100" 
              :status="getRiskStatus(analysis.score)"
              style="margin-top:16px"
            />
          </template>
        </a-card>

        <a-card title="风险分布" style="margin-top:16px" :loading="loading">
          <template v-if="analysis.distribution">
            <div v-for="(value, key) in analysis.distribution" :key="key" style="margin-bottom:8px">
              <div style="display:flex;justify-content:space-between;margin-bottom:4px">
                <span>{{ key }}</span>
                <span>{{ (value * 100).toFixed(1) }}%</span>
              </div>
              <a-progress 
                :percent="value * 100" 
                :show-info="false"
                :stroke-color="getRiskColor(key.toLowerCase())"
              />
            </div>
          </template>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'

const loading = ref(false)
const analysis = ref({})

const query = reactive({
  instId: '',
  type: undefined
})

const fetchAnalysis = () => {
  loading.value = true
  error.value = null

  fetch('/api/ai/comprehensive_risk_analysis', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      include_positions: form.include_positions,
      include_orders: form.include_orders,
      include_account: form.include_account,
      analysis_type: form.analysis_type,
    }),
  })
    .then(res => {
      if (!res.ok) {
        throw new Error('Network response was not ok');
      }
      return res.json();
    })
    .then(data => {
      analysis.value = data;
    })
    .catch(err => {
      error.value = err.message;
      console.error('There was a problem with the fetch operation:', err);
    })
    .finally(() => {
      loading.value = false;
    });
}

function getRiskColor(level) {
  switch (level.toLowerCase()) {
    case 'high': return 'red'
    case 'medium': return 'orange'
    case 'low': return 'green'
    default: return 'blue'
  }
}

function getRiskText(level) {
  switch (level.toLowerCase()) {
    case 'high': return '高风险'
    case 'medium': return '中风险'
    case 'low': return '低风险'
    default: return '未知'
  }
}

function getRiskStatus(score) {
  if (score >= 0.7) return 'exception'
  if (score >= 0.3) return 'normal'
  return 'success'
}
</script>

<style scoped>
.ai-risk-analysis {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.analysis-results {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-statistic {
  text-align: center;
}

.ant-progress {
  margin-top: 8px;
}
</style>