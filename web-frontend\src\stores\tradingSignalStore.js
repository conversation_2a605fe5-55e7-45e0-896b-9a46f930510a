import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useTradingSignalStore = defineStore('tradingSignal', () => {
  // State
  const tradingSignals = ref([]);

  // Actions
  const addTradingSignal = (signal) => {
    const signalWithId = {
      ...signal,
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      time: new Date().toLocaleTimeString('zh-CN')
    };
    
    tradingSignals.value.unshift(signalWithId);
    
    if (tradingSignals.value.length > 100) {
      tradingSignals.value = tradingSignals.value.slice(0, 100);
    }
  };

  const clearTradingSignals = () => {
    tradingSignals.value = [];
  };

  return {
    tradingSignals,
    addTradingSignal,
    clearTradingSignals
  };
});