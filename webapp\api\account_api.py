from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from db import get_db
from api.user_api import get_current_user
from sqlalchemy.orm import Session
from models import AccountBalance

router = APIRouter()


@router.get("/balance")
def get_balance(db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    """获取用户账户余额"""
    balances = db.query(AccountBalance).filter(
        AccountBalance.user_id == current_user.id).all()
    return {"code": 0, "msg": "success", "data": [
        {
            "id": b.id,
            "ccy": b.ccy,
            "balance": float(b.balance),
            "available": float(b.available),
            "frozen": float(b.frozen),
            "updated_at": b.updated_at.isoformat()
        } for b in balances
    ]}
