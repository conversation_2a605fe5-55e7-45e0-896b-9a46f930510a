<template>
  <div>
    <a-card title="OKX 行情查询" style="margin: 20px">
      <a-form layout="inline" @submit.prevent="fetchTicker">
        <a-form-item label="交易对">
          <a-input v-model="instId" placeholder="如 BTC-USDT" style="width: 200px" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="fetchTicker">查询</a-button>
        </a-form-item>
      </a-form>
      <a-table v-if="ticker" :dataSource="[ticker]" :columns="columns" rowKey="instId" style="margin-top: 20px" />
    </a-card>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import axios from 'axios'
const instId = ref('BTC-USDT')
const ticker = ref(null)
const columns = [
  { title: '交易对', dataIndex: 'instId' },
  { title: '最新价', dataIndex: 'last' },
  { title: '24h最高', dataIndex: 'high24h' },
  { title: '24h最低', dataIndex: 'low24h' },
  { title: '24h成交量', dataIndex: 'vol24h' }
]
const fetchTicker = async () => {
  const res = await axios.get('/api/okx/market/ticker', { params: { instId: instId.value } })
  if (res.data.code === '0' && res.data.data && res.data.data.length > 0) {
    ticker.value = res.data.data[0]
  } else {
    ticker.value = null
  }
}
</script>