from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from webapp.db import engine
from webapp.models import Base
from webapp.api import user_api, strategy_api, order_api, ai_api, okx_api, okx_ws, account_api

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(title="量化交易系统", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(user_api.router, prefix="/api/user", tags=["用户管理"])
app.include_router(account_api.router, prefix="/api/account", tags=["账户管理"])
app.include_router(strategy_api.router, prefix="/api/strategy", tags=["策略管理"])
app.include_router(order_api.router, prefix="/api/order", tags=["订单管理"])
app.include_router(ai_api.router, prefix="/api/ai", tags=["AI分析"])
app.include_router(okx_api.router, prefix="/api/okx", tags=["OKX交易"])
app.include_router(okx_ws.router, prefix="/api/okx/ws", tags=["OKX WebSocket"])


@app.get("/")
def read_root():
    return {"message": "量化交易系统API", "version": "1.0.0"}


@app.get("/health")
def health_check():
    return {"status": "healthy"}
