<template>
  <div class="modular-advanced-charts">
    <a-row :gutter="24">
      <!-- 主图表区域 -->
      <a-col :span="18">
        <a-card title="高级图表分析" style="margin-bottom: 24px;">
          <!-- 控制面板 -->
          <ChartControls 
            @symbol-change="handleSymbolChange"
            @timeframe-change="handleTimeframeChange"
            @theme-toggle="handleThemeToggle"
            @indicators-toggle="handleIndicatorsToggle"
            @refresh="handleRefresh"
            @settings-open="showSettingsModal = true"
          />

          <!-- K线图容器 -->
          <div id="main-candlestick-chart" style="height: 500px; width: 100%; margin-bottom: 16px;"></div>

          <!-- 技术指标图表 -->
          <div v-if="showIndicators" style="margin-top: 16px;">
            <a-row :gutter="16">
              <a-col v-if="displaySettings.showVolume" :span="8">
                <VolumeChart
                  :chart-data="chartData"
                  :is-dark-theme="isDarkTheme"
                  :height="'200px'"
                  chart-id="modular-volume-chart"
                  @chart-ready="handleChartReady('volume', $event)"
                  @chart-error="handleChartError"
                />
              </a-col>
              <a-col v-if="displaySettings.showRSI" :span="8">
                <RSIChart
                  :chart-data="chartData"
                  :is-dark-theme="isDarkTheme"
                  :height="'200px'"
                  :rsi-period="indicatorSettings.rsi.period"
                  :overbought-level="indicatorSettings.rsi.overbought"
                  :oversold-level="indicatorSettings.rsi.oversold"
                  chart-id="modular-rsi-chart"
                  @chart-ready="handleChartReady('rsi', $event)"
                  @chart-error="handleChartError"
                  @signal-generated="handleSignalGenerated"
                />
              </a-col>
              <a-col v-if="displaySettings.showMACD" :span="8">
                <MACDChart
                  :chart-data="chartData"
                  :is-dark-theme="isDarkTheme"
                  :height="'200px'"
                  :fast-period="indicatorSettings.macd.fastPeriod"
                  :slow-period="indicatorSettings.macd.slowPeriod"
                  :signal-period="indicatorSettings.macd.signalPeriod"
                  chart-id="modular-macd-chart"
                  @chart-ready="handleChartReady('macd', $event)"
                  @chart-error="handleChartError"
                  @signal-generated="handleSignalGenerated"
                />
              </a-col>
            </a-row>
            <a-row :gutter="16" style="margin-top: 16px;">
              <a-col v-if="displaySettings.showKDJ" :span="12">
                <div id="modular-kdj-chart" style="height: 200px; width: 100%;"></div>
              </a-col>
              <a-col v-if="displaySettings.showBollingerBands" :span="12">
                <div id="modular-boll-chart" style="height: 200px; width: 100%;"></div>
              </a-col>
            </a-row>
          </div>
        </a-card>

        <!-- 交易信号和快速交易 -->
        <TradingSignalsPanel 
          :trading-signals="tradingSignals"
          @quick-trade="handleQuickTrade"
        />
      </a-col>

      <!-- 右侧信息面板 -->
      <a-col :span="6">
        <PriceInfoPanel 
          :current-price="currentPrice"
          :price-change-direction="priceChangeDirection"
          :selected-symbol="selectedSymbol"
          :selected-timeframe="selectedTimeframe"
          :chart-data="chartData"
        />
        
        <MarketDepthPanel style="margin-top: 16px;" />
        
        <PositionPanel style="margin-top: 16px;" />
      </a-col>
    </a-row>

    <!-- 设置模态框 -->
    <SettingsModal 
      v-model:open="showSettingsModal"
      :indicator-settings="indicatorSettings"
      :display-settings="displaySettings"
      @settings-save="handleSettingsSave"
      @settings-reset="handleSettingsReset"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { useChartStore } from '@/stores/chartStore'
import { useChartSettingsStore } from '@/stores/chartSettingsStore'
import { useChartCommon } from '@/composables/useChartCommon'
import { CHART_DATA_INDEX } from '@/constants/chartConstants'

// 导入子组件
import VolumeChart from './charts/VolumeChart.vue'
import RSIChart from './charts/RSIChart.vue'
import MACDChart from './charts/MACDChart.vue'
import ChartControls from './ChartControls.vue'
import TradingSignalsPanel from './TradingSignalsPanel.vue'
import PriceInfoPanel from './PriceInfoPanel.vue'
import MarketDepthPanel from './MarketDepthPanel.vue'
import PositionPanel from './PositionPanel.vue'
import SettingsModal from './SettingsModal.vue'

// 使用状态管理
const chartStore = useChartStore()
const chartSettingsStore = useChartSettingsStore()
const {
  chartData,
  selectedSymbol,
  selectedTimeframe,
  isDarkTheme,
  isLoading,
  error,
  displaySettings,
  indicatorSettings,
  tradingSignals,
  currentPrice,
  priceChangeDirection
} = storeToRefs(chartStore)

// 本地状态
const showIndicators = ref(true)
const showSettingsModal = ref(false)
const priceUpdateInterval = ref(null)
const loadingChart = ref(false)

// 使用通用图表逻辑
const {
  chartInstance: mainChartInstance,
  initChart,
  updateChart,
  resizeChart,
  disposeChart,
  getBaseChartOption,
  formatChartData,
  indicatorCalculators
} = useChartCommon({ isDarkTheme: isDarkTheme.value })

// 初始化主K线图
const initMainCandlestickChart = async () => {
  await nextTick()
  
  if (!chartData.value || chartData.value.length === 0) {
    console.warn('K线图表数据为空')
    return
  }

  try {
    const theme = isDarkTheme.value ? getDarkTheme() : getLightTheme()
    
    const candlestickOption = {
      backgroundColor: theme.backgroundColor,
      title: {
        text: `${selectedSymbol.value}`,
        left: 'left',
        top: 10,
        textStyle: {
          color: theme.textColor,
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          lineStyle: {
            color: theme.borderColor,
            width: 1,
            type: 'dashed'
          }
        },
        backgroundColor: isDarkTheme.value ? 'rgba(30, 34, 45, 0.9)' : 'rgba(255, 255, 255, 0.9)',
        borderColor: theme.borderColor,
        textStyle: {
          color: theme.textColor
        },
        formatter: function(params) {
          if (params && params.length > 0) {
            const data = params[0]
            if (data.value && Array.isArray(data.value)) {
              return `
                时间: ${data.name}<br/>
                开盘: ${data.value[1]}<br/>
                最高: ${data.value[4]}<br/>
                最低: ${data.value[3]}<br/>
                收盘: ${data.value[2]}<br/>
                涨跌: ${(data.value[2] - data.value[1]).toFixed(2)}<br/>
                涨跌%: ${(((data.value[2] - data.value[1]) / data.value[1]) * 100).toFixed(2)}%
              `
            }
          }
          return ''
        }
      },
      legend: {
        data: ['K线', 'MA5', 'MA10', 'MA20'],
        top: 10,
        right: 20,
        textStyle: {
          color: theme.textColor
        },
        itemWidth: 14,
        itemHeight: 8
      },
      grid: {
        left: '3%',
        right: '8%',
        bottom: '8%',
        top: '12%',
        containLabel: true,
        backgroundColor: 'transparent',
        borderColor: theme.borderColor
      },
      xAxis: {
        type: 'category',
        data: formatChartData.timeAxis(chartData.value),
        scale: true,
        boundaryGap: false,
        axisLine: {
          onZero: false,
          lineStyle: {
            color: theme.borderColor
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: theme.textColor,
          fontSize: 11
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: theme.gridColor,
            width: 1
          }
        }
      },
      yAxis: {
        scale: true,
        position: 'right',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: theme.textColor,
          fontSize: 11,
          formatter: function(value) {
            return value.toFixed(2)
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: theme.gridColor,
            width: 1
          }
        }
      },
      dataZoom: [
        {
          type: 'inside',
          start: 70,
          end: 100,
          zoomOnMouseWheel: true,
          moveOnMouseMove: true
        },
        {
          show: true,
          type: 'slider',
          top: '92%',
          start: 70,
          end: 100,
          height: 20,
          backgroundColor: isDarkTheme.value ? '#2a2e39' : '#f5f5f5',
          borderColor: theme.borderColor,
          fillerColor: 'rgba(41, 98, 255, 0.2)',
          handleStyle: {
            color: '#2962ff',
            borderColor: '#2962ff'
          },
          textStyle: {
            color: theme.textColor
          }
        }
      ],
      series: [
        {
          name: 'K线',
          type: 'candlestick',
          data: formatChartData.candlestick(chartData.value),
          // 根据成交量动态调整K线宽度
          barWidth: (params) => {
            if (params && params.dataIndex !== undefined) {
              return formatChartData.calculateCandlestickWidth(chartData.value, params.dataIndex)
            }
            return '60%'
          },
          itemStyle: {
            color: theme.candlestick.upColor,
            color0: theme.candlestick.downColor,
            borderColor: theme.candlestick.upBorderColor,
            borderColor0: theme.candlestick.downBorderColor,
            // 根据成交量调整边框宽度
            borderWidth: (params) => {
              if (params && params.dataIndex !== undefined) {
                const volume = parseFloat(chartData.value[params.dataIndex][CHART_DATA_INDEX.VOLUME])
                const volumes = chartData.value.map(item => parseFloat(item[CHART_DATA_INDEX.VOLUME]))
                const maxVolume = Math.max(...volumes)
                const minVolume = Math.min(...volumes)
                const ratio = maxVolume > minVolume ? (volume - minVolume) / (maxVolume - minVolume) : 0.5
                return Math.max(1, Math.min(3, 1 + ratio * 2)) // 边框宽度在1-3之间
              }
              return 1
            }
          }
        },
        {
          name: 'MA5',
          type: 'line',
          data: indicatorCalculators.calculateMA(chartData.value, 5),
          smooth: true,
          lineStyle: {
            color: theme.ma.ma5,
            width: 1.5
          },
          symbol: 'none'
        },
        {
          name: 'MA10',
          type: 'line',
          data: indicatorCalculators.calculateMA(chartData.value, 10),
          smooth: true,
          lineStyle: {
            color: theme.ma.ma10,
            width: 1.5
          },
          symbol: 'none'
        },
        {
          name: 'MA20',
          type: 'line',
          data: indicatorCalculators.calculateMA(chartData.value, 20),
          smooth: true,
          lineStyle: {
            color: theme.ma.ma20,
            width: 1.5
          },
          symbol: 'none'
        }
      ]
    }

    const chart = initChart('main-candlestick-chart', candlestickOption)
    if (chart) {
      chartStore.registerChartInstance('candlestick', chart)
    }
  } catch (err) {
    console.error('初始化K线图失败:', err)
    chartStore.setError(err.message)
  }
}

// 主题配置
const getDarkTheme = () => ({
  backgroundColor: '#1e222d',
  textColor: '#ffffff',
  borderColor: '#434651',
  gridColor: '#2a2e39',
  candlestick: {
    upColor: '#00da3c',  // 绿涨
    downColor: '#ec0000', // 红跌
    upBorderColor: '#00da3c',
    downBorderColor: '#ec0000'
  },
  ma: {
    ma5: '#FF6B6B',
    ma10: '#4ECDC4',
    ma20: '#45B7D1'
  }
})

const getLightTheme = () => ({
  backgroundColor: '#ffffff',
  textColor: '#000000',
  borderColor: '#e8e8e8',
  gridColor: '#f5f5f5',
  candlestick: {
    upColor: '#00da3c',
    downColor: '#ec0000',
    upBorderColor: '#00da3c',
    downBorderColor: '#ec0000'
  },
  ma: {
    ma5: '#FF6B6B',
    ma10: '#4ECDC4',
    ma20: '#45B7D1'
  }
})

// 事件处理
const handleSymbolChange = (symbol) => {
  chartStore.setSelectedSymbol(symbol)
  loadChartData()
}

const handleTimeframeChange = (timeframe) => {
  chartStore.setSelectedTimeframe(timeframe)
  loadChartData()
}

const handleThemeToggle = () => {
  chartStore.toggleTheme()
  // 重新初始化所有图表以应用新主题
  setTimeout(() => {
    initMainCandlestickChart()
  }, 100)
}

const handleIndicatorsToggle = () => {
  showIndicators.value = !showIndicators.value
}

const handleRefresh = async () => {
  loadingChart.value = true
  try {
    await loadChartData()
  } finally {
    loadingChart.value = false
  }
}

const handleChartReady = (type, chartInstance) => {
  chartStore.registerChartInstance(type, chartInstance)
}

const handleChartError = (error) => {
  console.error('图表错误:', error)
  chartStore.setError(error)
}

const handleSignalGenerated = (signals) => {
  signals.forEach(signal => {
    chartStore.addTradingSignal(signal)
  })
}

const handleQuickTrade = (tradeData) => {
  console.log('快速交易:', tradeData)
  // 这里可以集成实际的交易API
}

const handleSettingsSave = (settings) => {
  // 更新指标设置
  if (settings.rsi) {
    chartSettingsStore.updateIndicatorSettings('rsi', settings.rsi)
  }
  if (settings.macd) {
    chartSettingsStore.updateIndicatorSettings('macd', settings.macd)
  }
  if (settings.kdj) {
    chartSettingsStore.updateIndicatorSettings('kdj', settings.kdj)
  }
  if (settings.bollingerBands) {
    chartSettingsStore.updateIndicatorSettings('bollingerBands', settings.bollingerBands)
  }
  if (settings.ma) {
    chartSettingsStore.updateIndicatorSettings('ma', settings.ma)
  }
  if (settings.ema) {
    chartSettingsStore.updateIndicatorSettings('ema', settings.ema)
  }
  if (settings.supertrend) {
    chartSettingsStore.updateIndicatorSettings('supertrend', settings.supertrend)
  }
  
  // 更新显示设置
  if (settings.display) {
    chartSettingsStore.updateDisplaySettings(settings.display)
  }
  
  // 保存设置
  chartSettingsStore.saveSettings()
  
  // 关闭设置面板
  showSettingsModal.value = false
  
  // 重新加载图表以应用新设置
  loadChartData()
}

const handleSettingsReset = () => {
  chartSettingsStore.resetIndicatorSettings()
  chartSettingsStore.saveSettings()
  // 重新加载图表以应用重置的设置
  loadChartData()
}

// 加载图表数据
const loadChartData = async () => {
  try {
    chartStore.setLoading(true)
    chartStore.clearError()
    
    // 这里应该调用实际的API获取数据
    // 现在使用模拟数据
    const mockData = generateMockData()
    chartStore.setChartData(mockData)
    
    // 初始化主图表
    await initMainCandlestickChart()
  } catch (err) {
    console.error('加载图表数据失败:', err)
    chartStore.setError(err.message)
  } finally {
    chartStore.setLoading(false)
  }
}

// 生成模拟数据
const generateMockData = () => {
  const data = []
  let basePrice = 50000
  const now = Date.now()
  
  for (let i = 0; i < 100; i++) {
    const timestamp = now - (100 - i) * 60000 // 每分钟一个数据点
    const open = basePrice + (Math.random() - 0.5) * 1000
    const close = open + (Math.random() - 0.5) * 500
    const high = Math.max(open, close) + Math.random() * 200
    const low = Math.min(open, close) - Math.random() * 200
    const volume = Math.random() * 1000000
    
    data.push([timestamp, open, high, low, close, volume])
    basePrice = close
  }
  
  return data
}

// 窗口大小变化处理
const handleResize = () => {
  chartStore.resizeAllCharts()
}

// 组件挂载
onMounted(async () => {
  // 加载保存的设置
  chartStore.loadSettings()
  
  // 加载图表数据
  await loadChartData()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  // 清理定时器
  if (priceUpdateInterval.value) {
    clearInterval(priceUpdateInterval.value)
  }
  
  // 移除事件监听
  window.removeEventListener('resize', handleResize)
  
  // 销毁所有图表实例
  chartStore.disposeAllCharts()
})
</script>

<style scoped>
.modular-advanced-charts {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.modular-advanced-charts .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modular-advanced-charts .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.modular-advanced-charts .ant-card-body {
  padding: 24px;
}

/* 深色主题样式 */
.modular-advanced-charts.dark-theme {
  background: #1e222d;
}

.modular-advanced-charts.dark-theme .ant-card {
  background: #2a2e39;
  border-color: #434651;
}

.modular-advanced-charts.dark-theme .ant-card-head {
  background: #2a2e39;
  border-bottom-color: #434651;
}

.modular-advanced-charts.dark-theme .ant-card-head-title {
  color: #ffffff;
}
</style>