<template>
  <div class="ai-risk">
    <h2>AI风险评估</h2>
    
    <a-form layout="inline" :model="query" @submit.prevent style="margin-bottom:16px">
      <a-form-item label="产品ID">
        <a-input v-model="query.instId" placeholder="如 BTC-USDT-SWAP" style="width:180px" />
      </a-form-item>
      <a-form-item label="风险类型">
        <a-select v-model="query.type" style="width:120px" allow-clear>
          <a-select-option value="position">持仓风险</a-select-option>
          <a-select-option value="leverage">杠杆风险</a-select-option>
          <a-select-option value="market">市场风险</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="fetchRisks" :loading="loading">查询</a-button>
      </a-form-item>
    </a-form>

    <a-table :dataSource="riskList" :columns="columns" :loading="loading">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'type'">
          <span>
            {{ record.type === 'position' ? '持仓风险' : 
               record.type === 'leverage' ? '杠杆风险' : '市场风险' }}
          </span>
        </template>
        <template v-else-if="column.key === 'level'">
          <a-tag :color="getRiskColor(record.level)">
            {{ getRiskText(record.level) }}
          </a-tag>
        </template>
        <template v-else-if="column.key === 'score'">
          <a-progress :percent="Number((record.score * 100).toFixed(2))" :status="getRiskStatus(record.score)" />
        </template>
        <template v-else-if="column.key === 'details'">
          <a-button type="link" @click="showRiskDetails(record)">详情</a-button>
        </template>
        <template v-else>
          {{ record[column.dataIndex] }}
        </template>
      </template>
      <template #empty>
        <div style="text-align:center; padding:32px;">暂无风险数据</div>
      </template>
    </a-table>

    <a-modal 
      title="风险详情" 
      :open="modalVisible" 
      @cancel="modalVisible = false"
      :footer="null"
    >
      <a-descriptions bordered>
        <a-descriptions-item label="产品ID">{{ currentRisk.instId }}</a-descriptions-item>
        <a-descriptions-item label="风险类型">
          {{ currentRisk.type === 'position' ? '持仓风险' : 
             currentRisk.type === 'leverage' ? '杠杆风险' : '市场风险' }}
        </a-descriptions-item>
        <a-descriptions-item label="风险等级">
          <a-tag :color="getRiskColor(currentRisk.level)">
            {{ getRiskText(currentRisk.level) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="风险分数">{{ (currentRisk.score * 100).toFixed(2) }}%</a-descriptions-item>
        <a-descriptions-item label="评估时间">{{ formatTime(currentRisk.timestamp) }}</a-descriptions-item>
        <a-descriptions-item label="风险描述">{{ currentRisk.description }}</a-descriptions-item>
        <a-descriptions-item label="建议措施">{{ currentRisk.suggestion }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const riskList = ref([])
const currentRisk = ref({})

const query = reactive({
  instId: '',
  type: undefined
})

const columns = [
  { title: '产品ID', dataIndex: 'instId', key: 'instId' },
  { title: '风险类型', dataIndex: 'type', key: 'type' },
  { title: '风险等级', dataIndex: 'level', key: 'level' },
  { title: '风险分数', dataIndex: 'score', key: 'score' },
  { title: '风险描述', dataIndex: 'description', key: 'description' },
  { title: '操作', key: 'details' }
]

function fetchRisks() {
  loading.value = true
  const params = {}
  if (query.instId) params.instId = query.instId
  if (query.type) params.type = query.type

  fetch('/api/ai/risks?' + new URLSearchParams(params))
    .then(res => res.json())
    .then(data => {
      if (data.code === 0) {
        riskList.value = data.data
      } else {
        message.error(data.msg || '查询失败')
      }
    })
    .catch(err => {
      console.error(err)
      message.error('查询失败')
    })
    .finally(() => {
      loading.value = false
    })
}

function showRiskDetails(record) {
  currentRisk.value = record
  modalVisible.value = true
}

function getRiskColor(level) {
  switch (level) {
    case 'high': return 'red'
    case 'medium': return 'orange'
    case 'low': return 'green'
    default: return 'blue'
  }
}

function getRiskText(level) {
  switch (level) {
    case 'high': return '高风险'
    case 'medium': return '中风险'
    case 'low': return '低风险'
    default: return '未知'
  }
}

function getRiskStatus(score) {
  if (score >= 0.7) return 'exception'
  if (score >= 0.3) return 'normal'
  return 'success'
}

function formatTime(timestamp) {
  if (!timestamp) return ''
  const date = new Date(Number(timestamp))
  return date.toLocaleString()
}
</script>

<style scoped>
.ai-risk {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px;
}

.risk-result {
  margin-top: 24px;
}

.active {
  border: 2px solid #1890ff;
}
</style> 