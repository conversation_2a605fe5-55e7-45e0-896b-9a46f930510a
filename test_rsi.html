<!DOCTYPE html>
<html>
<head>
    <title>RSI计算测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
    <h1>RSI计算测试</h1>
    <div id="chart" style="width: 800px; height: 400px;"></div>
    <div id="debug"></div>

    <script>
        // 标准RSI计算方法
        function calculateRSIArray(prices, period = 14) {
            const rsiArray = []
            
            if (prices.length < period + 1) {
                return prices.map(() => null)
            }

            // 计算价格变化
            const changes = []
            for (let i = 1; i < prices.length; i++) {
                changes.push(prices[i] - prices[i - 1])
            }

            // 初始化前14个为null
            for (let i = 0; i < period; i++) {
                rsiArray.push(null)
            }

            // 计算初始的平均收益和平均损失
            let gains = 0
            let losses = 0
            
            for (let i = 0; i < period; i++) {
                if (changes[i] > 0) {
                    gains += changes[i]
                } else {
                    losses += Math.abs(changes[i])
                }
            }

            let avgGain = gains / period
            let avgLoss = losses / period

            // 计算第一个RSI值
            if (avgLoss === 0) {
                rsiArray.push(avgGain === 0 ? 50 : 100)
            } else {
                const rs = avgGain / avgLoss
                const rsi = 100 - (100 / (1 + rs))
                rsiArray.push(rsi)
            }

            // 使用指数移动平均计算后续的RSI值
            for (let i = period; i < changes.length; i++) {
                const change = changes[i]
                const gain = change > 0 ? change : 0
                const loss = change < 0 ? Math.abs(change) : 0

                // 指数移动平均
                avgGain = (avgGain * (period - 1) + gain) / period
                avgLoss = (avgLoss * (period - 1) + loss) / period

                if (avgLoss === 0) {
                    rsiArray.push(avgGain === 0 ? 50 : 100)
                } else {
                    const rs = avgGain / avgLoss
                    const rsi = 100 - (100 / (1 + rs))
                    rsiArray.push(rsi)
                }
            }
            
            return rsiArray
        }

        // 生成测试数据
        function generateTestData() {
            const data = []
            let price = 50000
            
            for (let i = 0; i < 100; i++) {
                // 模拟价格波动
                const change = (Math.random() - 0.5) * 1000
                price += change
                data.push(price)
            }
            
            return data
        }

        // 测试RSI计算
        const testPrices = generateTestData()
        const rsiData = calculateRSIArray(testPrices, 14)
        
        // 过滤掉null值
        const validRSI = rsiData.filter(v => v !== null)
        
        console.log('测试数据:', {
            价格数据长度: testPrices.length,
            RSI数据长度: rsiData.length,
            有效RSI数据: validRSI.length,
            RSI范围: {
                最小: Math.min(...validRSI),
                最大: Math.max(...validRSI),
                平均: validRSI.reduce((a, b) => a + b, 0) / validRSI.length
            },
            最后10个RSI: rsiData.slice(-10)
        })

        // 显示调试信息
        document.getElementById('debug').innerHTML = `
            <h3>调试信息</h3>
            <p>价格数据长度: ${testPrices.length}</p>
            <p>RSI数据长度: ${rsiData.length}</p>
            <p>有效RSI数据: ${validRSI.length}</p>
            <p>RSI范围: ${Math.min(...validRSI).toFixed(2)} - ${Math.max(...validRSI).toFixed(2)}</p>
            <p>平均RSI: ${(validRSI.reduce((a, b) => a + b, 0) / validRSI.length).toFixed(2)}</p>
            <p>最后5个RSI: ${rsiData.slice(-5).map(v => v ? v.toFixed(2) : 'null').join(', ')}</p>
        `

        // 绘制图表
        const chart = echarts.init(document.getElementById('chart'))
        
        const option = {
            title: {
                text: 'RSI指标测试'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['价格', 'RSI']
            },
            xAxis: {
                type: 'category',
                data: testPrices.map((_, i) => i)
            },
            yAxis: [
                {
                    type: 'value',
                    name: '价格',
                    position: 'left'
                },
                {
                    type: 'value',
                    name: 'RSI',
                    position: 'right',
                    min: 0,
                    max: 100
                }
            ],
            series: [
                {
                    name: '价格',
                    type: 'line',
                    data: testPrices,
                    yAxisIndex: 0
                },
                {
                    name: 'RSI',
                    type: 'line',
                    data: rsiData,
                    yAxisIndex: 1,
                    lineStyle: {
                        color: '#ff6b6b'
                    }
                }
            ]
        }
        
        chart.setOption(option)
    </script>
</body>
</html>
