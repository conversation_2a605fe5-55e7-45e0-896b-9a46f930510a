<template>
  <div>
    <div style="margin-bottom: 20px; display: flex; align-items: center;">
      <a-avatar v-if="userStore.avatar" :src="userStore.avatar" size="large" style="margin-right: 16px" />
      <div>
        <div>用户名：{{ userStore.username }}</div>
        <div v-if="userStore.role">角色：{{ userStore.role }}</div>
        <div v-if="userStore.id">用户ID：{{ userStore.id }}</div>
      </div>
    </div>
    <a-table :dataSource="accounts" :columns="columns" rowKey="id" />
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { message } from 'ant-design-vue'
import { useUserStore } from '../stores/user'

const userStore = useUserStore()
const accounts = ref([])
const loading = ref(false)

const columns = [
  { title: '币种', dataIndex: 'ccy' },
  { title: '余额', dataIndex: 'balance' },
  { title: '可用', dataIndex: 'available' },
  { title: '冻结', dataIndex: 'frozen' },
  { title: '更新时间', dataIndex: 'updated_at' }
]

const fetchAccounts = async () => {
  try {
    loading.value = true
    const res = await axios.get('/api/account/balance')
    if (res.data.code === 0) {
      accounts.value = res.data.data
    } else {
      message.warning('获取账户信息失败')
    }
  } catch (error) {
    console.warn('账户API不可用:', error.message)
    // 不显示错误消息，只在控制台记录
  } finally {
    loading.value = false
  }
}

onMounted(fetchAccounts)
</script>