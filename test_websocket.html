<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div id="status">Disconnected</div>
    <div id="logs"></div>
    <button onclick="testConnection()">Test Connection</button>
    
    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            logs.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }
        
        function updateStatus(status) {
            document.getElementById('status').textContent = status;
        }
        
        function testConnection() {
            log('Attempting to connect to ws://localhost:8080/api/okx/market');
            updateStatus('Connecting...');
            
            const ws = new WebSocket('ws://localhost:8080/api/okx/market');
            
            ws.onopen = function(event) {
                log('WebSocket connected successfully!');
                updateStatus('Connected');
                
                // 发送测试订阅
                const testSub = {
                    op: "subscribe",
                    args: [{
                        channel: "tickers",
                        instId: "BTC-USDT"
                    }]
                };
                
                log('Sending test subscription: ' + JSON.stringify(testSub));
                ws.send(JSON.stringify(testSub));
            };
            
            ws.onmessage = function(event) {
                log('Received message: ' + event.data);
            };
            
            ws.onerror = function(error) {
                log('WebSocket error: ' + JSON.stringify(error));
                log('WebSocket readyState: ' + ws.readyState);
                updateStatus('Error');
            };
            
            ws.onclose = function(event) {
                log('WebSocket closed. Code: ' + event.code + ', Reason: ' + event.reason);
                updateStatus('Disconnected');
            };
        }
    </script>
</body>
</html>