/**
 * 更新频率管理组合式函数
 * 提供动态调整数据更新频率的功能
 */

import { ref, computed } from 'vue';
import { UPDATE_INTERVALS, DYNAMIC_INTERVALS, getUpdateInterval, getMarketActivity } from '@/constants/updateConfig';

export function useUpdateFrequency() {
  // 当前市场活跃度
  const currentActivity = ref('MEDIUM_ACTIVITY');
  
  // 是否启用动态调整
  const isDynamicEnabled = ref(false);
  
  // 自定义更新频率覆盖
  const customIntervals = ref({});
  
  // 最后一次24小时成交量
  const lastVolume24h = ref(null);
  
  /**
   * 获取指定类型的更新间隔
   * @param {string} type - 更新类型（如 'TICKER', 'CANDLESTICK' 等）
   * @returns {number} 更新间隔（毫秒）
   */
  const getInterval = (type) => {
    // 优先使用自定义间隔
    if (customIntervals.value[type]) {
      return customIntervals.value[type];
    }
    
    // 如果启用动态调整，根据市场活跃度返回间隔
    if (isDynamicEnabled.value) {
      return getUpdateInterval(type, currentActivity.value);
    }
    
    // 返回默认间隔
    return UPDATE_INTERVALS[type] || 5000;
  };
  
  /**
   * 设置自定义更新间隔
   * @param {string} type - 更新类型
   * @param {number} interval - 更新间隔（毫秒）
   */
  const setCustomInterval = (type, interval) => {
    customIntervals.value[type] = interval;
  };
  
  /**
   * 清除自定义更新间隔
   * @param {string} type - 更新类型
   */
  const clearCustomInterval = (type) => {
    delete customIntervals.value[type];
  };
  
  /**
   * 启用/禁用动态频率调整
   * @param {boolean} enabled - 是否启用
   */
  const setDynamicEnabled = (enabled) => {
    isDynamicEnabled.value = enabled;
  };
  
  /**
   * 更新市场活跃度
   * @param {string|number} volume24h - 24小时成交量
   */
  const updateMarketActivity = (volume24h) => {
    lastVolume24h.value = volume24h;
    const newActivity = getMarketActivity(volume24h);
    
    if (newActivity !== currentActivity.value) {
      console.log(`市场活跃度变化: ${currentActivity.value} -> ${newActivity}`);
      currentActivity.value = newActivity;
    }
  };
  
  /**
   * 获取所有更新间隔的配置
   * @returns {object} 包含所有类型的更新间隔
   */
  const getAllIntervals = computed(() => {
    const intervals = {};
    
    Object.keys(UPDATE_INTERVALS).forEach(type => {
      intervals[type] = getInterval(type);
    });
    
    return intervals;
  });
  
  /**
   * 获取当前配置的摘要信息
   * @returns {object} 配置摘要
   */
  const getConfigSummary = computed(() => {
    return {
      activity: currentActivity.value,
      isDynamic: isDynamicEnabled.value,
      customCount: Object.keys(customIntervals.value).length,
      lastVolume: lastVolume24h.value,
      intervals: getAllIntervals.value
    };
  });
  
  /**
   * 重置所有配置到默认值
   */
  const resetToDefaults = () => {
    currentActivity.value = 'MEDIUM_ACTIVITY';
    isDynamicEnabled.value = false;
    customIntervals.value = {};
    lastVolume24h.value = null;
  };
  
  /**
   * 创建一个定时器管理器
   * @param {string} type - 更新类型
   * @param {Function} callback - 回调函数
   * @returns {object} 定时器管理器
   */
  const createIntervalManager = (type, callback) => {
    let intervalId = null;
    let isRunning = false;
    
    const start = () => {
      if (isRunning) return;
      
      const interval = getInterval(type);
      intervalId = setInterval(callback, interval);
      isRunning = true;
      
      console.log(`启动${type}定时器，间隔: ${interval}ms`);
    };
    
    const stop = () => {
      if (intervalId) {
        clearInterval(intervalId);
        intervalId = null;
        isRunning = false;
        console.log(`停止${type}定时器`);
      }
    };
    
    const restart = () => {
      stop();
      start();
    };
    
    const updateInterval = () => {
      if (isRunning) {
        restart();
      }
    };
    
    return {
      start,
      stop,
      restart,
      updateInterval,
      isRunning: () => isRunning,
      getCurrentInterval: () => getInterval(type)
    };
  };
  
  return {
    // 状态
    currentActivity,
    isDynamicEnabled,
    customIntervals,
    lastVolume24h,
    
    // 计算属性
    getAllIntervals,
    getConfigSummary,
    
    // 方法
    getInterval,
    setCustomInterval,
    clearCustomInterval,
    setDynamicEnabled,
    updateMarketActivity,
    resetToDefaults,
    createIntervalManager
  };
}