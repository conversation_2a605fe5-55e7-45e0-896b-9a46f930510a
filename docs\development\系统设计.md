# OKX量化交易系统-系统设计

## 架构总览
- **前端**：Vue 3.4.15 + Vite 5.x + Ant Design Vue 4.2.6 + Pinia 3.0.3，SPA单页应用，组件化开发
- **图表引擎**：KLineCharts 9.8.0 + ECharts 5.6.0，专业金融图表，实时数据渲染
- **后端**：FastAPI + Uvicorn，RESTful API，模块化设计，WebSocket实时推送
- **数据库**：SQLite + SQLAlchemy ORM（支持扩展MySQL/PostgreSQL）
- **状态管理**：Pinia全局状态管理，数据持久化，实时同步
- **AI分析**：集成智能分析算法，技术指标计算，交易信号生成，TradingView风格趋势线识别
- **交易对接**：OKX API v5（REST+WebSocket），实时行情和交易，自动重连机制
- **认证系统**：JWT Token认证，安全的用户身份验证和授权

## 主要模块

### 核心业务模块
1. **用户与权限模块** - JWT认证、用户管理、API密钥配置、安全控制
2. **策略管理模块** - 交易策略创建、参数配置、回测、实盘执行
3. **订单管理模块** - 多种订单类型、订单生命周期管理、历史查询
4. **账户与资产模块** - 实时资产查询、持仓管理、杠杆设置、资金划转
5. **风险管理模块** - 实时风险评估、智能止损止盈、仓位控制、风险预警

### 数据分析模块
6. **图表分析模块** - 多时间周期K线、20+技术指标、模块化图表系统
7. **AI智能分析模块** - 智能信号生成、模式识别、市场情绪分析、趋势线识别
8. **统计看板模块** - 实时数据可视化、交易绩效分析、风险报表生成

### 技术支撑模块
9. **实时数据模块** - WebSocket双向通信、自动重连、数据缓存、状态同步
10. **OKX API集成模块** - REST API封装、WebSocket管理、错误处理、限流控制
11. **公共与安全模块** - 结构化日志、全局错误处理、安全防护、性能监控

## 前后端交互流程
- **用户认证流程**: 注册/登录 -> JWT Token验证 -> API密钥配置 -> 访问受限资源
- **交易策略流程**: 策略创建/编辑 -> 参数配置 -> 回测验证 -> 实盘执行 -> 订单生成
- **订单管理流程**: 订单创建 -> 实时状态更新 -> 修改/取消 -> 成交确认 -> 历史记录
- **账户资产流程**: 实时余额查询 -> 持仓监控 -> 杠杆调整 -> 资金划转 -> 风险评估
- **AI分析流程**: 数据采集 -> 模式识别 -> 信号生成 -> 风险评估 -> 决策建议
- **数据可视化流程**: 实时数据获取 -> 图表渲染 -> 指标计算 -> 交互操作 -> 状态同步
- **WebSocket推送**: 行情数据 -> 账户变动 -> 持仓更新 -> 订单状态 -> 系统通知

## 权限与安全设计
- JWT鉴权，接口权限控制，token过期自动跳转登录
- 用户表含api_key、api_secret、passphrase，后端自动读取签名
- 密码加密存储（bcrypt）
- 重要操作日志与风控
- 前端路由守卫，动态菜单，权限不足跳转403

## WebSocket推送架构
- **行情推送**: `/api/okx/ws/market` - 公开频道，实时K线、深度、成交数据
- **私有推送**: `/api/okx/ws/private` - 需JWT认证，账户/持仓/订单状态变更
- **系统推送**: `/api/okx/ws/system` - 系统通知、风险预警、交易信号
- **连接管理**: 自动重连机制、心跳检测、连接状态监控
- **数据处理**: 消息队列、数据压缩、增量更新、状态同步
- **频道订阅**: 支持多频道订阅、动态订阅管理、权限控制

## 自动化与文档
- 接口文档、数据结构文档自动同步
- 代码变更自动补全文档
- 支持API Mock与自动化测试

## 扩展性与可维护性
- 支持多交易所、多币种扩展
- 策略与AI模块可插件化
- 前后端均可二次开发
- 代码结构清晰，文档齐全，便于团队协作

## 典型流程图
1. 用户登录 -> 获取token -> 访问受限API
2. 下单流程：前端表单 -> REST下单接口 -> OKX官方API -> WebSocket推送订单状态
3. 持仓/账户/杠杆/划转：前端表单 -> REST接口 -> OKX官方API -> WebSocket推送实时数据
