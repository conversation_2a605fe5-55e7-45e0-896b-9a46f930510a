#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OKX Python SDK 启动示例
这是一个简单的演示程序，展示如何使用OKX SDK进行基础操作
"""

import okx.MarketData as MarketData
import okx.PublicData as PublicData
import okx.Account as Account
import okx.Trade as Trade
from webapp.okx_config import get_okx_config, is_okx_configured, config


def demo_market_data():
    """演示市场数据查询"""
    print("=== 市场数据演示 ===")

    # 创建市场数据API实例（无需API密钥）
    marketAPI = MarketData.MarketAPI(flag="1")  # flag="1" 表示模拟环境

    # 获取现货行情
    print("\n1. 获取BTC-USDT现货行情:")
    result = marketAPI.get_ticker(instId="BTC-USDT")
    if result['code'] == '0':
        data = result['data'][0]
        print(f"   最新价格: {data['last']}")
        # 注意：API返回的字段名可能不同，需要根据实际情况调整
        if 'change24h' in data:
            print(f"   24h涨跌幅: {data['change24h']}%")
        if 'vol24h' in data:
            print(f"   24h成交量: {data['vol24h']}")
    else:
        print(f"   错误: {result}")

    # 获取所有现货行情
    print("\n2. 获取所有现货行情（前5个）:")
    result = marketAPI.get_tickers(instType="SPOT")
    if result['code'] == '0':
        for i, ticker in enumerate(result['data'][:5]):
            print(f"   {i+1}. {ticker['instId']}: {ticker['last']}")
    else:
        print(f"   错误: {result}")


def demo_public_data():
    """演示公共数据查询"""
    print("\n=== 公共数据演示 ===")

    # 创建公共数据API实例
    publicAPI = PublicData.PublicAPI(flag="1")

    # 获取交易对信息
    print("\n1. 获取BTC-USDT交易对信息:")
    result = publicAPI.get_instruments(instType="SPOT", instId="BTC-USDT")
    if result['code'] == '0':
        data = result['data'][0]
        print(f"   交易对: {data['instId']}")
        print(f"   基础货币: {data['baseCcy']}")
        print(f"   计价货币: {data['quoteCcy']}")
        print(f"   最小下单量: {data['minSz']}")
        print(f"   价格精度: {data['tickSz']}")
    else:
        print(f"   错误: {result}")


def demo_with_api_key():
    """演示需要API密钥的功能"""
    print("\n=== API密钥功能演示 ===")

    # 检查配置状态
    config.print_config_status()

    if not is_okx_configured():
        print("\n⚠️  请先配置您的API密钥:")
        print("   方法1: 设置环境变量")
        print("      OKX_API_KEY=your_api_key")
        print("      OKX_SECRET_KEY=your_secret_key")
        print("      OKX_PASSPHRASE=your_passphrase")
        print("\n   方法2: 创建 config.env 文件")
        print("      复制 config.env.example 为 config.env")
        print("      并填入您的实际API密钥")
        return

    # 获取配置
    okx_config = get_okx_config()

    # 创建账户API实例
    accountAPI = Account.AccountAPI(
        api_key=okx_config['api_key'],
        api_secret_key=okx_config['secret_key'],
        passphrase=okx_config['passphrase'],
        use_server_time=False,
        flag=okx_config['flag'],
        debug=okx_config['debug']
    )

    # 获取账户余额
    print("\n1. 获取账户余额:")
    result = accountAPI.get_account_balance()
    if result['code'] == '0':
        for balance in result['data']:
            if float(balance['bal']) > 0:
                print(f"   {balance['ccy']}: {balance['bal']}")
    else:
        print(f"   错误: {result}")


def main():
    """主函数"""
    print("🚀 OKX Python SDK 启动演示")
    print("=" * 50)

    try:
        # 演示市场数据（无需API密钥）
        demo_market_data()

        # 演示公共数据（无需API密钥）
        demo_public_data()

        # 演示需要API密钥的功能
        demo_with_api_key()

        print("\n✅ 演示完成！")
        print("\n📚 更多示例请查看:")
        print("   - example/get_started_en.ipynb (现货交易)")
        print("   - example/trade_derivatives_en.ipynb (衍生品交易)")
        print("   - test/ 目录下的测试文件")

    except Exception as e:
        print(f"❌ 运行出错: {e}")
        print("请检查网络连接和API配置")


if __name__ == "__main__":
    main()
