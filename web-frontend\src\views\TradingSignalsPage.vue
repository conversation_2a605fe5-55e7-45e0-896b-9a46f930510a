<template>
  <div class="trading-signals-page">
    <a-row :gutter="24">
      <!-- 左侧：控制面板和图表 -->
      <a-col :span="16">
        <!-- 控制面板 -->
        <a-card title="交易信号分析" style="margin-bottom: 24px;">
          <div class="controls">
            <a-space>
              <a-select v-model:value="selectedSymbol" style="width: 200px;" @change="onSymbolChange">
                <a-select-option value="BTC-USDT-SWAP">BTC-USDT-SWAP</a-select-option>
                <a-select-option value="ETH-USDT-SWAP">ETH-USDT-SWAP</a-select-option>
                <a-select-option value="SOL-USDT-SWAP">SOL-USDT-SWAP</a-select-option>
                <a-select-option value="DOGE-USDT-SWAP">DOGE-USDT-SWAP</a-select-option>
              </a-select>
              
              <a-select v-model:value="selectedTimeframe" style="width: 120px;" @change="onTimeframeChange">
                <a-select-option value="1m">1分钟</a-select-option>
                <a-select-option value="5m">5分钟</a-select-option>
                <a-select-option value="15m">15分钟</a-select-option>
                <a-select-option value="30m">30分钟</a-select-option>
                <a-select-option value="1H">1小时</a-select-option>
                <a-select-option value="4H">4小时</a-select-option>
                <a-select-option value="1D">1天</a-select-option>
              </a-select>
              
              <a-button @click="refreshData" :loading="loading" type="primary">
                刷新数据
              </a-button>
              
              <a-button @click="toggleAutoUpdate" :type="autoUpdate ? 'danger' : 'default'">
                {{ autoUpdate ? '停止自动更新' : '开始自动更新' }}
              </a-button>
              
              <a-badge :dot="true" :color="autoUpdate ? 'green' : 'red'">
                <span>{{ autoUpdate ? '实时更新中' : '已停止' }}</span>
              </a-badge>
            </a-space>
          </div>
        </a-card>

        <!-- 简化的价格图表 -->
        <a-card title="价格走势" style="margin-bottom: 24px;">
          <div id="price-chart" style="width: 100%; height: 300px;"></div>
        </a-card>

        <!-- 技术指标图表 -->
        <a-card title="技术指标">
          <a-tabs>
            <a-tab-pane key="rsi" tab="RSI">
              <div id="rsi-chart" style="width: 100%; height: 200px;"></div>
            </a-tab-pane>
            <a-tab-pane key="macd" tab="MACD">
              <div id="macd-chart" style="width: 100%; height: 200px;"></div>
            </a-tab-pane>
            <a-tab-pane key="ma" tab="移动平均线">
              <div id="ma-chart" style="width: 100%; height: 200px;"></div>
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </a-col>

      <!-- 右侧：交易信号统计 -->
      <a-col :span="8">
        <!-- 当前价格 -->
        <a-card title="实时价格" style="margin-bottom: 16px;" v-if="currentPrice">
          <a-statistic
            :title="selectedSymbol"
            :value="currentPrice.last"
            :precision="2"
            suffix="USDT"
            :value-style="{
              color: parseFloat(currentPrice.sodUtc8) >= 0 ? '#52c41a' : '#ff4d4f',
              fontSize: '20px'
            }"
          />
          <a-divider />
          <a-descriptions size="small" :column="1">
            <a-descriptions-item label="24h涨跌">
              <span :style="{ color: parseFloat(currentPrice.sodUtc8) >= 0 ? '#52c41a' : '#ff4d4f' }">
                {{ currentPrice.sodUtc8 }}%
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="24h最高">{{ currentPrice.high24h }}</a-descriptions-item>
            <a-descriptions-item label="24h最低">{{ currentPrice.low24h }}</a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 交易信号统计 -->
        <TradingSignals 
          :chart-data="chartData"
          :technical-indicators="technicalIndicators"
          :current-price="currentPrice"
        />
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import axios from 'axios'
import * as echarts from 'echarts'
import TradingSignals from '../components/TradingSignals.vue'

// 响应式数据
const selectedSymbol = ref('BTC-USDT-SWAP')
const selectedTimeframe = ref('1H')
const loading = ref(false)
const autoUpdate = ref(false)
const chartData = ref([])
const currentPrice = ref(null)
const technicalIndicators = ref({
  rsi: 50,
  macd: 0,
  ma5: 0,
  ma10: 0,
  ma20: 0,
  ma50: 0,
  bbUpper: 0,
  bbLower: 0,
  kdjK: 50,
  kdjD: 50,
  kdjJ: 50
})

let priceChart = null
let rsiChart = null
let macdChart = null
let maChart = null
let updateInterval = null

// 获取K线数据
const loadChartData = async () => {
  loading.value = true
  try {
    const response = await axios.get('/api/okx/market/candles', {
      params: {
        instId: selectedSymbol.value,
        bar: selectedTimeframe.value,
        limit: 100
      }
    })

    if (response.data.code === '0' && response.data.data) {
      chartData.value = response.data.data.reverse()
      calculateTechnicalIndicators()
      updateCharts()
    } else {
      generateMockData()
    }
  } catch (error) {
    console.error('获取K线数据失败:', error)
    generateMockData()
  } finally {
    loading.value = false
  }
}

// 获取实时价格
const loadCurrentPrice = async () => {
  try {
    const response = await axios.get(`/api/okx/market/ticker?instId=${selectedSymbol.value}`)
    if (response.data.code === '0' && response.data.data) {
      currentPrice.value = response.data.data[0]
    }
  } catch (error) {
    console.warn('获取实时价格失败:', error.message)
  }
}

// 生成模拟数据
const generateMockData = () => {
  const mockData = []
  let basePrice = 50000
  
  for (let i = 0; i < 100; i++) {
    const timestamp = Date.now() - (100 - i) * 60 * 60 * 1000
    const open = basePrice + (Math.random() - 0.5) * 1000
    const close = open + (Math.random() - 0.5) * 500
    const high = Math.max(open, close) + Math.random() * 200
    const low = Math.min(open, close) - Math.random() * 200
    const volume = Math.random() * 1000000

    mockData.push([timestamp, open, high, low, close, volume])
    basePrice = close
  }

  chartData.value = mockData
  calculateTechnicalIndicators()
  updateCharts()
}

// 计算技术指标
const calculateTechnicalIndicators = () => {
  if (chartData.value.length === 0) return

  const closes = chartData.value.map(item => parseFloat(item[4]))
  const highs = chartData.value.map(item => parseFloat(item[2]))
  const lows = chartData.value.map(item => parseFloat(item[3]))

  // 计算RSI
  technicalIndicators.value.rsi = calculateRSI(closes, 14)

  // 计算移动平均线
  technicalIndicators.value.ma5 = calculateMA(closes, 5)
  technicalIndicators.value.ma10 = calculateMA(closes, 10)
  technicalIndicators.value.ma20 = calculateMA(closes, 20)
  technicalIndicators.value.ma50 = calculateMA(closes, 50)

  // 计算MACD
  technicalIndicators.value.macd = calculateMACD(closes)

  // 计算布林带
  const bb = calculateBollingerBands(closes, 20)
  technicalIndicators.value.bbUpper = bb.upper
  technicalIndicators.value.bbLower = bb.lower

  // 计算KDJ
  const kdj = calculateKDJ(highs, lows, closes, 9, 3, 3)
  technicalIndicators.value.kdjK = kdj.K
  technicalIndicators.value.kdjD = kdj.D
  technicalIndicators.value.kdjJ = kdj.J
}

// RSI计算
const calculateRSI = (prices, period = 14) => {
  if (prices.length < period + 1) return 50

  const changes = []
  for (let i = 1; i < prices.length; i++) {
    changes.push(prices[i] - prices[i - 1])
  }

  let gains = 0
  let losses = 0
  
  for (let i = 0; i < period; i++) {
    if (changes[i] > 0) {
      gains += changes[i]
    } else {
      losses += Math.abs(changes[i])
    }
  }

  const avgGain = gains / period
  const avgLoss = losses / period

  if (avgLoss === 0) {
    return avgGain === 0 ? 50 : 100
  }

  const rs = avgGain / avgLoss
  return 100 - (100 / (1 + rs))
}

// 移动平均线计算
const calculateMA = (prices, period) => {
  if (prices.length < period) return 0
  const sum = prices.slice(-period).reduce((a, b) => a + b, 0)
  return sum / period
}

// MACD计算
const calculateMACD = (prices) => {
  if (prices.length < 26) return 0
  const ema12 = calculateEMA(prices, 12)
  const ema26 = calculateEMA(prices, 26)
  return ema12 - ema26
}

// EMA计算
const calculateEMA = (prices, period) => {
  if (prices.length < period) return 0
  const multiplier = 2 / (period + 1)
  let ema = prices[0]
  
  for (let i = 1; i < prices.length; i++) {
    ema = (prices[i] - ema) * multiplier + ema
  }
  
  return ema
}

// 布林带计算
const calculateBollingerBands = (prices, period) => {
  if (prices.length < period) return { upper: 0, lower: 0 }
  
  const ma = calculateMA(prices, period)
  const slice = prices.slice(-period)
  const variance = slice.reduce((sum, price) => sum + Math.pow(price - ma, 2), 0) / period
  const stdDev = Math.sqrt(variance)
  
  return {
    upper: ma + (stdDev * 2),
    lower: ma - (stdDev * 2)
  }
}

// KDJ计算
const calculateKDJ = (highs, lows, closes, period = 9, k_period = 3, d_period = 3) => {
  if (closes.length < period) return { K: 50, D: 50, J: 50 }

  const recentHighs = highs.slice(-period)
  const recentLows = lows.slice(-period)
  const currentClose = closes[closes.length - 1]

  const highestHigh = Math.max(...recentHighs)
  const lowestLow = Math.min(...recentLows)

  const rsv = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100

  const K = rsv / k_period + (50 * (k_period - 1)) / k_period
  const D = K / d_period + (50 * (d_period - 1)) / d_period
  const J = 3 * K - 2 * D

  return { K, D, J }
}

// 初始化图表
const initCharts = () => {
  // 价格图表
  const priceContainer = document.getElementById('price-chart')
  if (priceContainer) {
    priceChart = echarts.init(priceContainer)
  }

  // RSI图表
  const rsiContainer = document.getElementById('rsi-chart')
  if (rsiContainer) {
    rsiChart = echarts.init(rsiContainer)
  }

  // MACD图表
  const macdContainer = document.getElementById('macd-chart')
  if (macdContainer) {
    macdChart = echarts.init(macdContainer)
  }

  // MA图表
  const maContainer = document.getElementById('ma-chart')
  if (maContainer) {
    maChart = echarts.init(maContainer)
  }
}

// 更新图表
const updateCharts = () => {
  if (chartData.value.length === 0) return

  const dates = chartData.value.map(item => {
    const timestamp = parseInt(item[0])
    return new Date(timestamp).toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  })

  const candleData = chartData.value.map(item => [
    parseFloat(item[1]), // open
    parseFloat(item[4]), // close
    parseFloat(item[3]), // low
    parseFloat(item[2])  // high
  ])

  // 更新价格图表
  if (priceChart) {
    priceChart.setOption({
      title: { text: `${selectedSymbol.value} 价格走势` },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: dates },
      yAxis: { type: 'value', scale: true },
      series: [{
        name: 'K线',
        type: 'candlestick',
        data: candleData,
        itemStyle: {
          color: '#ec0000',
          color0: '#00da3c',
          borderColor: '#8A0000',
          borderColor0: '#008F28'
        }
      }]
    })
  }

  // 更新RSI图表
  if (rsiChart) {
    const rsiData = chartData.value.map(() => technicalIndicators.value.rsi)
    
    rsiChart.setOption({
      title: { text: 'RSI指标' },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: dates },
      yAxis: { type: 'value', min: 0, max: 100 },
      series: [{
        name: 'RSI',
        type: 'line',
        data: rsiData,
        lineStyle: { color: '#ff4d4f' },
        markLine: {
          data: [
            { yAxis: 70, lineStyle: { color: '#ff7875' } },
            { yAxis: 30, lineStyle: { color: '#52c41a' } }
          ]
        }
      }]
    })
  }

  // 更新MACD图表
  if (macdChart) {
    const macdData = chartData.value.map(() => technicalIndicators.value.macd)
    
    macdChart.setOption({
      title: { text: 'MACD指标' },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: dates },
      yAxis: { type: 'value' },
      series: [{
        name: 'MACD',
        type: 'line',
        data: macdData,
        lineStyle: { color: '#1890ff' }
      }]
    })
  }

  // 更新MA图表
  if (maChart) {
    const ma5Data = chartData.value.map(() => technicalIndicators.value.ma5)
    const ma10Data = chartData.value.map(() => technicalIndicators.value.ma10)
    const ma20Data = chartData.value.map(() => technicalIndicators.value.ma20)
    
    maChart.setOption({
      title: { text: '移动平均线' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['MA5', 'MA10', 'MA20'] },
      xAxis: { type: 'category', data: dates },
      yAxis: { type: 'value' },
      series: [
        {
          name: 'MA5',
          type: 'line',
          data: ma5Data,
          lineStyle: { color: '#1890ff' }
        },
        {
          name: 'MA10',
          type: 'line',
          data: ma10Data,
          lineStyle: { color: '#52c41a' }
        },
        {
          name: 'MA20',
          type: 'line',
          data: ma20Data,
          lineStyle: { color: '#faad14' }
        }
      ]
    })
  }
}

// 刷新数据
const refreshData = () => {
  loadChartData()
  loadCurrentPrice()
}

// 切换自动更新
const toggleAutoUpdate = () => {
  autoUpdate.value = !autoUpdate.value
  
  if (autoUpdate.value) {
    refreshData()
    updateInterval = setInterval(() => {
      refreshData()
    }, 10000) // 每10秒更新一次
    message.success('已开启自动更新')
  } else {
    if (updateInterval) {
      clearInterval(updateInterval)
      updateInterval = null
    }
    message.info('已停止自动更新')
  }
}

// 交易对变化
const onSymbolChange = () => {
  refreshData()
}

// 时间周期变化
const onTimeframeChange = () => {
  loadChartData()
}

// 页面加载时初始化
onMounted(async () => {
  await initCharts()
  await loadChartData()
  loadCurrentPrice()
})

// 页面卸载时清理
onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
  
  if (priceChart) priceChart.dispose()
  if (rsiChart) rsiChart.dispose()
  if (macdChart) macdChart.dispose()
  if (maChart) maChart.dispose()
})
</script>

<style scoped>
.trading-signals-page {
  padding: 24px;
}

.controls {
  margin-bottom: 16px;
}
</style>
