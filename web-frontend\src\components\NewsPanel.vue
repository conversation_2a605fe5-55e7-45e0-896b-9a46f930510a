<template>
  <div class="news-panel" :class="{ 'dark-theme': isDarkTheme }">
    <a-card title="市场资讯" size="small" :bordered="false">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新资讯">
            <a-button size="small" type="text" @click="refreshNews">
              <ReloadOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="资讯设置">
            <a-button size="small" type="text" @click="showSettings = true">
              <SettingOutlined />
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <div class="news-container">
        <!-- 资讯分类标签 -->
        <div class="news-tabs">
          <a-tabs v-model:activeKey="activeTab" size="small" @change="handleTabChange">
            <a-tab-pane key="all" tab="全部" />
            <a-tab-pane key="announcement" tab="公告" />
            <a-tab-pane key="news" tab="新闻" />
            <a-tab-pane key="analysis" tab="分析" />
            <a-tab-pane key="regulation" tab="监管" />
          </a-tabs>
        </div>

        <!-- 重要公告横幅 -->
        <div v-if="importantAnnouncements.length > 0" class="important-banner">
          <a-alert
            v-for="announcement in importantAnnouncements"
            :key="announcement.id"
            :message="announcement.title"
            type="warning"
            show-icon
            closable
            @close="dismissAnnouncement(announcement.id)"
          >
            <template #description>
              <div class="announcement-content">
                <p>{{ announcement.summary }}</p>
                <a @click="viewNewsDetail(announcement)" class="read-more">查看详情</a>
              </div>
            </template>
          </a-alert>
        </div>

        <!-- 资讯列表 -->
        <div class="news-list">
          <div v-if="loading" class="loading-container">
            <a-spin size="large" />
          </div>
          
          <div v-else-if="filteredNews.length === 0" class="empty-news">
            <a-empty description="暂无资讯" size="small" />
          </div>
          
          <div v-else class="news-content">
            <div
              v-for="item in filteredNews"
              :key="item.id"
              class="news-item"
              :class="[
                `news-${item.type}`,
                { 'news-important': item.isImportant },
                { 'news-unread': !item.isRead }
              ]"
              @click="handleNewsClick(item)"
            >
              <div class="news-header">
                <div class="news-meta">
                  <a-tag
                    :color="getTypeColor(item.type)"
                    size="small"
                  >
                    {{ getTypeText(item.type) }}
                  </a-tag>
                  <span v-if="item.isImportant" class="important-badge">
                    <ExclamationCircleOutlined />
                  </span>
                  <span class="news-source">{{ item.source }}</span>
                  <span class="news-time">{{ formatTime(item.publishTime) }}</span>
                </div>
                <div class="news-actions">
                  <a-tooltip title="收藏">
                    <a-button
                      size="small"
                      type="text"
                      @click.stop="toggleFavorite(item)"
                      :class="{ 'favorited': item.isFavorited }"
                    >
                      <StarOutlined v-if="!item.isFavorited" />
                      <StarFilled v-else />
                    </a-button>
                  </a-tooltip>
                  <a-tooltip title="分享">
                    <a-button
                      size="small"
                      type="text"
                      @click.stop="shareNews(item)"
                    >
                      <ShareAltOutlined />
                    </a-button>
                  </a-tooltip>
                </div>
              </div>
              
              <div class="news-content-area">
                <h4 class="news-title">{{ item.title }}</h4>
                <p class="news-summary">{{ item.summary }}</p>
                
                <div v-if="item.tags && item.tags.length > 0" class="news-tags">
                  <a-tag
                    v-for="tag in item.tags"
                    :key="tag"
                    size="small"
                    color="blue"
                  >
                    {{ tag }}
                  </a-tag>
                </div>
                
                <div class="news-stats">
                  <span class="stat-item">
                    <EyeOutlined />
                    {{ formatNumber(item.views) }}
                  </span>
                  <span class="stat-item">
                    <LikeOutlined />
                    {{ formatNumber(item.likes) }}
                  </span>
                  <span class="stat-item">
                    <MessageOutlined />
                    {{ formatNumber(item.comments) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMore && !loading" class="load-more">
          <a-button @click="loadMore" block>
            加载更多
          </a-button>
        </div>
      </div>
    </a-card>

    <!-- 资讯设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="资讯设置"
      @ok="saveSettings"
      @cancel="resetSettings"
    >
      <a-form :model="settings" layout="vertical">
        <a-form-item label="启用推送">
          <a-switch v-model:checked="settings.enablePush" />
          <span class="setting-desc">开启/关闭资讯推送</span>
        </a-form-item>
        
        <a-form-item label="推送类型">
          <a-checkbox-group v-model:value="settings.pushTypes">
            <a-checkbox value="announcement">重要公告</a-checkbox>
            <a-checkbox value="news">市场新闻</a-checkbox>
            <a-checkbox value="analysis">技术分析</a-checkbox>
            <a-checkbox value="regulation">监管动态</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="推送频率">
          <a-radio-group v-model:value="settings.pushFrequency">
            <a-radio value="realtime">实时推送</a-radio>
            <a-radio value="hourly">每小时</a-radio>
            <a-radio value="daily">每日汇总</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="关键词过滤">
          <a-select
            v-model:value="settings.keywords"
            mode="tags"
            placeholder="输入关键词"
            style="width: 100%"
          >
            <a-select-option value="BTC">BTC</a-select-option>
            <a-select-option value="ETH">ETH</a-select-option>
            <a-select-option value="DeFi">DeFi</a-select-option>
            <a-select-option value="NFT">NFT</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="声音提醒">
          <a-switch v-model:checked="settings.soundAlert" />
          <span class="setting-desc">重要资讯时播放提示音</span>
        </a-form-item>
        
        <a-form-item label="桌面通知">
          <a-switch v-model:checked="settings.desktopNotification" />
          <span class="setting-desc">重要资讯时显示桌面通知</span>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 资讯详情模态框 -->
    <a-modal
      v-model:open="showNewsDetail"
      :title="selectedNews?.title"
      :footer="null"
      width="800px"
      class="news-detail-modal"
    >
      <div v-if="selectedNews" class="news-detail">
        <div class="detail-header">
          <div class="detail-meta">
            <a-tag :color="getTypeColor(selectedNews.type)">
              {{ getTypeText(selectedNews.type) }}
            </a-tag>
            <span class="detail-source">{{ selectedNews.source }}</span>
            <span class="detail-time">{{ formatFullTime(selectedNews.publishTime) }}</span>
          </div>
          
          <div class="detail-actions">
            <a-space>
              <a-button
                size="small"
                @click="toggleFavorite(selectedNews)"
                :type="selectedNews.isFavorited ? 'primary' : 'default'"
              >
                <StarOutlined v-if="!selectedNews.isFavorited" />
                <StarFilled v-else />
                {{ selectedNews.isFavorited ? '已收藏' : '收藏' }}
              </a-button>
              <a-button size="small" @click="shareNews(selectedNews)">
                <ShareAltOutlined />
                分享
              </a-button>
            </a-space>
          </div>
        </div>
        
        <div class="detail-content">
          <div class="detail-summary">
            <p>{{ selectedNews.summary }}</p>
          </div>
          
          <div class="detail-body">
            <div v-html="selectedNews.content"></div>
          </div>
          
          <div v-if="selectedNews.tags && selectedNews.tags.length > 0" class="detail-tags">
            <h4>相关标签</h4>
            <a-space wrap>
              <a-tag
                v-for="tag in selectedNews.tags"
                :key="tag"
                color="blue"
              >
                {{ tag }}
              </a-tag>
            </a-space>
          </div>
          
          <div class="detail-stats">
            <div class="stats-row">
              <span class="stat-item">
                <EyeOutlined />
                浏览 {{ formatNumber(selectedNews.views) }}
              </span>
              <span class="stat-item">
                <LikeOutlined />
                点赞 {{ formatNumber(selectedNews.likes) }}
              </span>
              <span class="stat-item">
                <MessageOutlined />
                评论 {{ formatNumber(selectedNews.comments) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { message, notification } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { useChartStore } from '@/stores/chartStore'
import {
  ReloadOutlined,
  SettingOutlined,
  ExclamationCircleOutlined,
  StarOutlined,
  StarFilled,
  ShareAltOutlined,
  EyeOutlined,
  LikeOutlined,
  MessageOutlined
} from '@ant-design/icons-vue'

// 定义事件
const emit = defineEmits(['news-click', 'news-share'])

// 使用状态管理
const chartStore = useChartStore()
const { isDarkTheme } = storeToRefs(chartStore)

// 本地状态
const showSettings = ref(false)
const showNewsDetail = ref(false)
const selectedNews = ref(null)
const loading = ref(false)
const hasMore = ref(true)
const activeTab = ref('all')
const updateInterval = ref(null)

// 设置
const settings = ref({
  enablePush: true,
  pushTypes: ['announcement', 'news'],
  pushFrequency: 'realtime',
  keywords: ['BTC', 'ETH'],
  soundAlert: true,
  desktopNotification: true
})

// 模拟资讯数据
const newsData = ref([
  {
    id: 1,
    type: 'announcement',
    title: 'OKX将于北京时间2024年1月15日上线新交易对',
    summary: 'OKX将新增多个主流币种交易对，包括PEPE/USDT、SHIB/USDT等，为用户提供更多交易选择。',
    content: '<p>尊敬的OKX用户：</p><p>OKX将于北京时间2024年1月15日16:00上线以下交易对：</p><ul><li>PEPE/USDT</li><li>SHIB/USDT</li><li>DOGE/USDT</li></ul><p>请注意相关风险，理性投资。</p>',
    source: 'OKX官方',
    publishTime: Date.now() - 1800000,
    isImportant: true,
    isRead: false,
    isFavorited: false,
    views: 15420,
    likes: 234,
    comments: 56,
    tags: ['交易对', '上线', '公告']
  },
  {
    id: 2,
    type: 'news',
    title: '比特币突破43000美元，创近期新高',
    summary: '比特币价格在机构资金流入推动下突破43000美元关键阻力位，市场情绪转为乐观。',
    content: '<p>比特币价格在今日亚洲交易时段突破43000美元，创下近期新高。</p><p>分析师认为，此次上涨主要受以下因素推动：</p><ul><li>机构资金持续流入</li><li>美联储政策预期转向</li><li>技术面突破关键阻力</li></ul>',
    source: 'CoinDesk',
    publishTime: Date.now() - 3600000,
    isImportant: false,
    isRead: true,
    isFavorited: true,
    views: 8920,
    likes: 156,
    comments: 89,
    tags: ['BTC', '价格', '突破']
  },
  {
    id: 3,
    type: 'analysis',
    title: '技术分析：以太坊或将测试2700美元阻力位',
    summary: '从技术图表来看，以太坊正在形成上升三角形形态，有望挑战2700美元关键阻力位。',
    content: '<p>以太坊日线图显示出明显的上升三角形形态：</p><p><strong>技术要点：</strong></p><ul><li>支撑位：2550美元</li><li>阻力位：2700美元</li><li>目标位：2850美元</li></ul><p>建议投资者关注成交量变化。</p>',
    source: 'TradingView',
    publishTime: Date.now() - 7200000,
    isImportant: false,
    isRead: false,
    isFavorited: false,
    views: 5630,
    likes: 78,
    comments: 23,
    tags: ['ETH', '技术分析', '阻力位']
  },
  {
    id: 4,
    type: 'regulation',
    title: '美国SEC主席重申对加密货币监管立场',
    summary: 'SEC主席在最新讲话中重申了对加密货币市场的监管立场，强调投资者保护的重要性。',
    content: '<p>美国证券交易委员会(SEC)主席在华盛顿的一次会议上发表讲话：</p><p><strong>主要观点：</strong></p><ul><li>加强投资者保护</li><li>明确监管框架</li><li>打击非法活动</li></ul>',
    source: 'Reuters',
    publishTime: Date.now() - 10800000,
    isImportant: true,
    isRead: false,
    isFavorited: false,
    views: 12340,
    likes: 89,
    comments: 145,
    tags: ['SEC', '监管', '政策']
  },
  {
    id: 5,
    type: 'news',
    title: 'DeFi协议总锁仓量突破500亿美元',
    summary: 'DeFi生态系统继续快速发展，总锁仓量(TVL)首次突破500亿美元大关。',
    content: '<p>根据DeFiLlama数据，DeFi协议总锁仓量已突破500亿美元：</p><p><strong>主要协议：</strong></p><ul><li>Uniswap: 45亿美元</li><li>Aave: 38亿美元</li><li>Compound: 25亿美元</li></ul>',
    source: 'DeFiPulse',
    publishTime: Date.now() - 14400000,
    isImportant: false,
    isRead: true,
    isFavorited: false,
    views: 7890,
    likes: 123,
    comments: 67,
    tags: ['DeFi', 'TVL', '里程碑']
  }
])

// 计算属性
const filteredNews = computed(() => {
  let filtered = newsData.value
  
  if (activeTab.value !== 'all') {
    filtered = filtered.filter(item => item.type === activeTab.value)
  }
  
  // 关键词过滤
  if (settings.value.keywords.length > 0) {
    filtered = filtered.filter(item => {
      const content = `${item.title} ${item.summary} ${item.tags?.join(' ') || ''}`
      return settings.value.keywords.some(keyword => 
        content.toLowerCase().includes(keyword.toLowerCase())
      )
    })
  }
  
  return filtered.sort((a, b) => {
    // 重要资讯优先
    if (a.isImportant && !b.isImportant) return -1
    if (!a.isImportant && b.isImportant) return 1
    // 按时间排序
    return b.publishTime - a.publishTime
  })
})

const importantAnnouncements = computed(() => {
  return newsData.value.filter(item => 
    item.type === 'announcement' && item.isImportant && !item.isDismissed
  )
})

/**
 * 获取类型颜色
 */
const getTypeColor = (type) => {
  switch (type) {
    case 'announcement': return 'red'
    case 'news': return 'blue'
    case 'analysis': return 'green'
    case 'regulation': return 'orange'
    default: return 'default'
  }
}

/**
 * 获取类型文本
 */
const getTypeText = (type) => {
  switch (type) {
    case 'announcement': return '公告'
    case 'news': return '新闻'
    case 'analysis': return '分析'
    case 'regulation': return '监管'
    default: return '其他'
  }
}

/**
 * 格式化时间
 */
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

/**
 * 格式化完整时间
 */
const formatFullTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN')
}

/**
 * 格式化数字
 */
const formatNumber = (num) => {
  if (num >= 10000) {
    return `${(num / 10000).toFixed(1)}万`
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}k`
  }
  return num.toString()
}

/**
 * 处理标签页切换
 */
const handleTabChange = (key) => {
  activeTab.value = key
}

/**
 * 处理资讯点击
 */
const handleNewsClick = (item) => {
  item.isRead = true
  selectedNews.value = item
  showNewsDetail.value = true
  emit('news-click', item)
}

/**
 * 切换收藏状态
 */
const toggleFavorite = (item) => {
  item.isFavorited = !item.isFavorited
  message.success(item.isFavorited ? '已收藏' : '已取消收藏')
}

/**
 * 分享资讯
 */
const shareNews = (item) => {
  if (navigator.share) {
    navigator.share({
      title: item.title,
      text: item.summary,
      url: window.location.href
    })
  } else {
    // 复制到剪贴板
    const text = `${item.title}\n${item.summary}\n${window.location.href}`
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板')
    })
  }
  emit('news-share', item)
}

/**
 * 查看资讯详情
 */
const viewNewsDetail = (item) => {
  handleNewsClick(item)
}

/**
 * 忽略公告
 */
const dismissAnnouncement = (id) => {
  const item = newsData.value.find(item => item.id === id)
  if (item) {
    item.isDismissed = true
  }
}

/**
 * 刷新资讯
 */
const refreshNews = async () => {
  loading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟新资讯
    const newItem = {
      id: Date.now(),
      type: ['news', 'analysis', 'announcement'][Math.floor(Math.random() * 3)],
      title: '最新市场动态：加密货币市场出现新变化',
      summary: '最新的市场分析显示，加密货币市场正在经历重要变化...',
      content: '<p>详细内容...</p>',
      source: '市场分析',
      publishTime: Date.now(),
      isImportant: Math.random() > 0.7,
      isRead: false,
      isFavorited: false,
      views: Math.floor(Math.random() * 10000),
      likes: Math.floor(Math.random() * 500),
      comments: Math.floor(Math.random() * 100),
      tags: ['市场', '分析']
    }
    
    newsData.value.unshift(newItem)
    
    // 限制资讯数量
    if (newsData.value.length > 50) {
      newsData.value = newsData.value.slice(0, 50)
    }
    
    message.success('资讯已刷新')
    
    // 重要资讯通知
    if (newItem.isImportant) {
      if (settings.value.soundAlert) {
        playNotificationSound()
      }
      
      if (settings.value.desktopNotification) {
        showDesktopNotification(newItem)
      }
    }
  } catch (error) {
    message.error('刷新失败')
  } finally {
    loading.value = false
  }
}

/**
 * 加载更多
 */
const loadMore = async () => {
  loading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟更多数据
    const moreData = Array.from({ length: 5 }, (_, index) => ({
      id: Date.now() + index,
      type: ['news', 'analysis'][Math.floor(Math.random() * 2)],
      title: `历史资讯 ${index + 1}`,
      summary: '这是一条历史资讯的摘要...',
      content: '<p>历史资讯内容...</p>',
      source: '历史数据',
      publishTime: Date.now() - (index + 1) * 86400000,
      isImportant: false,
      isRead: Math.random() > 0.5,
      isFavorited: false,
      views: Math.floor(Math.random() * 5000),
      likes: Math.floor(Math.random() * 200),
      comments: Math.floor(Math.random() * 50),
      tags: ['历史']
    }))
    
    newsData.value.push(...moreData)
    
    // 模拟没有更多数据
    if (newsData.value.length > 30) {
      hasMore.value = false
    }
  } catch (error) {
    message.error('加载失败')
  } finally {
    loading.value = false
  }
}

/**
 * 播放通知音
 */
const playNotificationSound = () => {
  try {
    const audio = new Audio('/notification.mp3')
    audio.play().catch(() => {
      // 忽略播放失败
    })
  } catch (error) {
    // 忽略音频错误
  }
}

/**
 * 显示桌面通知
 */
const showDesktopNotification = (item) => {
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification('重要资讯', {
      body: item.title,
      icon: '/favicon.ico'
    })
  }
}

/**
 * 保存设置
 */
const saveSettings = () => {
  localStorage.setItem('newsSettings', JSON.stringify(settings.value))
  showSettings.value = false
  message.success('设置已保存')
  
  // 请求通知权限
  if (settings.value.desktopNotification && 'Notification' in window) {
    Notification.requestPermission()
  }
}

/**
 * 重置设置
 */
const resetSettings = () => {
  settings.value = {
    enablePush: true,
    pushTypes: ['announcement', 'news'],
    pushFrequency: 'realtime',
    keywords: ['BTC', 'ETH'],
    soundAlert: true,
    desktopNotification: true
  }
}

/**
 * 加载设置
 */
const loadSettings = () => {
  const saved = localStorage.getItem('newsSettings')
  if (saved) {
    try {
      settings.value = { ...settings.value, ...JSON.parse(saved) }
    } catch (error) {
      console.error('加载资讯设置失败:', error)
    }
  }
}

/**
 * 开始自动刷新
 */
const startAutoRefresh = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
  }
  
  updateInterval.value = setInterval(() => {
    if (settings.value.enablePush && settings.value.pushFrequency === 'realtime') {
      if (Math.random() > 0.8) {
        refreshNews()
      }
    }
  }, 60000) // 1分钟检查一次
}

/**
 * 停止自动刷新
 */
const stopAutoRefresh = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
    updateInterval.value = null
  }
}

// 监听设置变化
watch(() => settings.value.enablePush, (enabled) => {
  if (enabled) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})

// 生命周期
onMounted(() => {
  loadSettings()
  if (settings.value.enablePush) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.news-panel {
  height: 100%;
}

.news-panel .ant-card {
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.news-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.news-tabs {
  border-bottom: 1px solid #f0f0f0;
}

.news-tabs .ant-tabs {
  margin-bottom: 0;
}

.important-banner {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.announcement-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.announcement-content p {
  margin: 0;
  flex: 1;
}

.read-more {
  color: #1890ff;
  cursor: pointer;
  margin-left: 12px;
}

.read-more:hover {
  text-decoration: underline;
}

.news-list {
  flex: 1;
  overflow: hidden;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-news {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.news-content {
  height: 100%;
  overflow-y: auto;
}

.news-item {
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.news-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.news-item.news-important {
  border-left: 4px solid #ff4d4f;
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.05), rgba(255, 77, 79, 0.02));
}

.news-item.news-unread {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05), rgba(24, 144, 255, 0.02));
}

.news-item.news-unread::before {
  content: '';
  position: absolute;
  top: 12px;
  right: 12px;
  width: 8px;
  height: 8px;
  background: #1890ff;
  border-radius: 50%;
}

.news-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.news-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.important-badge {
  color: #ff4d4f;
  font-size: 14px;
}

.news-source {
  font-size: 12px;
  color: #666;
}

.news-time {
  font-size: 11px;
  color: #999;
}

.news-actions {
  display: flex;
  gap: 4px;
}

.news-actions .ant-btn.favorited {
  color: #faad14;
}

.news-content-area {
  margin-bottom: 12px;
}

.news-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.news-summary {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-tags {
  margin-bottom: 12px;
}

.news-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #999;
}

.load-more {
  padding: 16px 0;
}

.setting-desc {
  margin-left: 8px;
  font-size: 12px;
  color: #999;
}

.news-detail-modal .ant-modal-body {
  padding: 0;
}

.news-detail {
  padding: 24px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.detail-source {
  font-size: 13px;
  color: #666;
}

.detail-time {
  font-size: 12px;
  color: #999;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.detail-summary {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
}

.detail-summary p {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.detail-body {
  font-size: 14px;
  line-height: 1.8;
  color: #333;
}

.detail-body :deep(h1),
.detail-body :deep(h2),
.detail-body :deep(h3),
.detail-body :deep(h4) {
  margin-top: 24px;
  margin-bottom: 12px;
  color: #333;
}

.detail-body :deep(p) {
  margin-bottom: 16px;
}

.detail-body :deep(ul),
.detail-body :deep(ol) {
  margin-bottom: 16px;
  padding-left: 24px;
}

.detail-body :deep(li) {
  margin-bottom: 8px;
}

.detail-tags h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
}

.detail-stats {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stats-row {
  display: flex;
  gap: 24px;
}

.stats-row .stat-item {
  font-size: 13px;
  color: #666;
}

/* 深色主题样式 */
.news-panel.dark-theme .ant-card {
  background: #1f1f1f;
  border-color: #434343;
}

.news-panel.dark-theme .news-tabs {
  border-bottom-color: #434343;
}

.news-panel.dark-theme .news-item {
  background: #2a2a2a;
  border-color: #434343;
}

.news-panel.dark-theme .news-item:hover {
  background: #333;
}

.news-panel.dark-theme .news-title {
  color: #fff;
}

.news-panel.dark-theme .news-summary {
  color: #d1d5db;
}

.news-panel.dark-theme .news-source,
.news-panel.dark-theme .news-time,
.news-panel.dark-theme .stat-item {
  color: #999;
}

.news-panel.dark-theme .detail-summary {
  background: #333;
}

.news-panel.dark-theme .detail-summary p {
  color: #d1d5db;
}

.news-panel.dark-theme .detail-body {
  color: #d1d5db;
}

.news-panel.dark-theme .detail-stats {
  background: #333;
}

.news-panel.dark-theme .detail-tags h4 {
  color: #fff;
}
</style>