<template>
  <div class="market-depth-panel">
    <a-card title="市场深度" size="small">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新深度数据">
            <a-button size="small" type="text" @click="refreshDepthData">
              <ReloadOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="深度设置">
            <a-button size="small" type="text" @click="showSettings = true">
              <SettingOutlined />
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <div class="depth-container">
        <!-- 深度图表 -->
        <div class="depth-chart">
          <div id="depth-chart-container" style="height: 120px; width: 100%;"></div>
        </div>

        <!-- 买卖盘数据 -->
        <div class="orderbook">
          <!-- 卖盘 -->
          <div class="asks-section">
            <div class="section-header asks-header">
              <span class="price-label">卖价</span>
              <span class="amount-label">数量</span>
              <span class="total-label">累计</span>
            </div>
            <div class="orders-list asks-list">
              <div
                v-for="(ask, index) in displayedAsks"
                :key="'ask-' + index"
                class="order-row ask-row"
                @click="handlePriceClick(ask.price, 'sell')"
              >
                <div class="order-background ask-bg" :style="{ width: ask.percentage + '%' }"></div>
                <span class="order-price ask-price">{{ formatPrice(ask.price) }}</span>
                <span class="order-amount">{{ formatAmount(ask.amount) }}</span>
                <span class="order-total">{{ formatAmount(ask.total) }}</span>
              </div>
            </div>
          </div>

          <!-- 价差显示 -->
          <div class="spread-info">
            <div class="spread-value">
              价差: {{ formatPrice(spread) }}
              <span class="spread-percent">({{ spreadPercent.toFixed(3) }}%)</span>
            </div>
          </div>

          <!-- 买盘 -->
          <div class="bids-section">
            <div class="orders-list bids-list">
              <div
                v-for="(bid, index) in displayedBids"
                :key="'bid-' + index"
                class="order-row bid-row"
                @click="handlePriceClick(bid.price, 'buy')"
              >
                <div class="order-background bid-bg" :style="{ width: bid.percentage + '%' }"></div>
                <span class="order-price bid-price">{{ formatPrice(bid.price) }}</span>
                <span class="order-amount">{{ formatAmount(bid.amount) }}</span>
                <span class="order-total">{{ formatAmount(bid.total) }}</span>
              </div>
            </div>
            <div class="section-header bids-header">
              <span class="price-label">买价</span>
              <span class="amount-label">数量</span>
              <span class="total-label">累计</span>
            </div>
          </div>
        </div>

        <!-- 深度统计 -->
        <div class="depth-stats">
          <div class="stat-row">
            <span class="stat-label">买盘总量:</span>
            <span class="stat-value bid-color">{{ formatAmount(totalBidsAmount) }}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">卖盘总量:</span>
            <span class="stat-value ask-color">{{ formatAmount(totalAsksAmount) }}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">买卖比:</span>
            <span class="stat-value">{{ bidAskRatio.toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 深度设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="深度设置"
      @ok="saveSettings"
      @cancel="resetSettings"
    >
      <a-form :model="settings" layout="vertical">
        <a-form-item label="显示档位数量">
          <a-slider
            v-model:value="settings.depthLevels"
            :min="5"
            :max="20"
            :marks="{ 5: '5', 10: '10', 15: '15', 20: '20' }"
          />
        </a-form-item>
        <a-form-item label="价格精度">
          <a-select v-model:value="settings.pricePrecision">
            <a-select-option :value="0">整数</a-select-option>
            <a-select-option :value="1">1位小数</a-select-option>
            <a-select-option :value="2">2位小数</a-select-option>
            <a-select-option :value="4">4位小数</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="数量精度">
          <a-select v-model:value="settings.amountPrecision">
            <a-select-option :value="2">2位小数</a-select-option>
            <a-select-option :value="4">4位小数</a-select-option>
            <a-select-option :value="6">6位小数</a-select-option>
            <a-select-option :value="8">8位小数</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="settings.showDepthChart">
            显示深度图表
          </a-checkbox>
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="settings.autoRefresh">
            自动刷新 (每3秒)
          </a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import {
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

// 定义事件
const emit = defineEmits(['price-click'])

// 本地状态
const showSettings = ref(false)
const depthChart = ref(null)
const refreshInterval = ref(null)

// 设置
const settings = ref({
  depthLevels: 10,
  pricePrecision: 2,
  amountPrecision: 4,
  showDepthChart: true,
  autoRefresh: true
})

// 模拟深度数据
const rawBids = ref([])
const rawAsks = ref([])

// 计算属性
const displayedBids = computed(() => {
  return processOrderData(rawBids.value, 'bid').slice(0, settings.value.depthLevels)
})

const displayedAsks = computed(() => {
  return processOrderData(rawAsks.value, 'ask').slice(0, settings.value.depthLevels)
})

const spread = computed(() => {
  if (displayedAsks.value.length === 0 || displayedBids.value.length === 0) return 0
  return displayedAsks.value[0].price - displayedBids.value[0].price
})

const spreadPercent = computed(() => {
  if (displayedBids.value.length === 0) return 0
  return (spread.value / displayedBids.value[0].price) * 100
})

const totalBidsAmount = computed(() => {
  return displayedBids.value.reduce((sum, bid) => sum + bid.amount, 0)
})

const totalAsksAmount = computed(() => {
  return displayedAsks.value.reduce((sum, ask) => sum + ask.amount, 0)
})

const bidAskRatio = computed(() => {
  if (totalAsksAmount.value === 0) return 0
  return totalBidsAmount.value / totalAsksAmount.value
})

// 数据处理函数
const processOrderData = (orders, type) => {
  if (!orders || orders.length === 0) return []
  
  let total = 0
  const maxAmount = Math.max(...orders.map(order => order.amount))
  
  return orders.map(order => {
    total += order.amount
    return {
      price: order.price,
      amount: order.amount,
      total: total,
      percentage: (order.amount / maxAmount) * 100
    }
  })
}

// 格式化函数
const formatPrice = (price) => {
  return price.toFixed(settings.value.pricePrecision)
}

const formatAmount = (amount) => {
  if (amount >= 1000) {
    return (amount / 1000).toFixed(1) + 'K'
  }
  return amount.toFixed(settings.value.amountPrecision)
}

// 生成模拟数据
const generateMockDepthData = () => {
  const basePrice = 51000
  const bids = []
  const asks = []
  
  // 生成买盘数据
  for (let i = 0; i < 20; i++) {
    const price = basePrice - (i + 1) * 10
    const amount = Math.random() * 50 + 10
    bids.push({ price, amount })
  }
  
  // 生成卖盘数据
  for (let i = 0; i < 20; i++) {
    const price = basePrice + (i + 1) * 10
    const amount = Math.random() * 50 + 10
    asks.push({ price, amount })
  }
  
  rawBids.value = bids.sort((a, b) => b.price - a.price) // 买盘按价格降序
  rawAsks.value = asks.sort((a, b) => a.price - b.price) // 卖盘按价格升序
}

// 初始化深度图表
const initDepthChart = async () => {
  if (!settings.value.showDepthChart) return
  
  await nextTick()
  
  const container = document.getElementById('depth-chart-container')
  if (!container) return
  
  if (depthChart.value) {
    depthChart.value.dispose()
  }
  
  depthChart.value = echarts.init(container)
  
  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '3%',
      right: '3%',
      bottom: '3%',
      top: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        fontSize: 10,
        color: '#8c8c8c'
      },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        fontSize: 10,
        color: '#8c8c8c'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          width: 1
        }
      }
    },
    series: [
      {
        name: '买盘深度',
        type: 'line',
        data: displayedBids.value.map(bid => [bid.price, bid.total]),
        lineStyle: {
          color: '#52c41a',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: 'rgba(82, 196, 26, 0.3)' },
              { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }
            ]
          }
        },
        symbol: 'none'
      },
      {
        name: '卖盘深度',
        type: 'line',
        data: displayedAsks.value.map(ask => [ask.price, ask.total]),
        lineStyle: {
          color: '#ff4d4f',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: 'rgba(255, 77, 79, 0.1)' },
              { offset: 1, color: 'rgba(255, 77, 79, 0.3)' }
            ]
          }
        },
        symbol: 'none'
      }
    ],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff',
        fontSize: 11
      },
      formatter: function(params) {
        if (params && params.length > 0) {
          const data = params[0]
          return `价格: ${formatPrice(data.value[0])}<br/>累计: ${formatAmount(data.value[1])}`
        }
        return ''
      }
    }
  }
  
  depthChart.value.setOption(option)
}

// 更新深度图表
const updateDepthChart = () => {
  if (!depthChart.value || !settings.value.showDepthChart) return
  
  const option = {
    series: [
      {
        data: displayedBids.value.map(bid => [bid.price, bid.total])
      },
      {
        data: displayedAsks.value.map(ask => [ask.price, ask.total])
      }
    ]
  }
  
  depthChart.value.setOption(option)
}

// 事件处理
const handlePriceClick = (price, side) => {
  emit('price-click', { price, side })
  message.info(`已选择${side === 'buy' ? '买入' : '卖出'}价格: ${formatPrice(price)}`)
}

const refreshDepthData = () => {
  generateMockDepthData()
  updateDepthChart()
  message.success('深度数据已刷新')
}

const saveSettings = () => {
  // 保存设置到本地存储
  localStorage.setItem('marketDepthSettings', JSON.stringify(settings.value))
  
  // 重新初始化图表
  if (settings.value.showDepthChart) {
    initDepthChart()
  } else if (depthChart.value) {
    depthChart.value.dispose()
    depthChart.value = null
  }
  
  // 设置自动刷新
  if (settings.value.autoRefresh) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
  
  showSettings.value = false
  message.success('设置已保存')
}

const resetSettings = () => {
  settings.value = {
    depthLevels: 10,
    pricePrecision: 2,
    amountPrecision: 4,
    showDepthChart: true,
    autoRefresh: true
  }
}

const loadSettings = () => {
  const saved = localStorage.getItem('marketDepthSettings')
  if (saved) {
    try {
      settings.value = { ...settings.value, ...JSON.parse(saved) }
    } catch (error) {
      console.error('加载深度设置失败:', error)
    }
  }
}

const startAutoRefresh = () => {
  stopAutoRefresh()
  if (settings.value.autoRefresh) {
    refreshInterval.value = setInterval(() => {
      generateMockDepthData()
      updateDepthChart()
    }, 1000)
  }
}

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// 组件挂载
onMounted(() => {
  loadSettings()
  generateMockDepthData()
  initDepthChart()
  startAutoRefresh()
})

// 组件卸载
onUnmounted(() => {
  stopAutoRefresh()
  if (depthChart.value) {
    depthChart.value.dispose()
  }
})
</script>

<style scoped>
.market-depth-panel {
  height: 100%;
}

.depth-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.depth-chart {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.orderbook {
  display: flex;
  flex-direction: column;
}

.section-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  padding: 4px 8px;
  font-size: 11px;
  font-weight: 500;
  color: #8c8c8c;
  background: #fafafa;
  border-radius: 4px;
}

.asks-section {
  margin-bottom: 8px;
}

.orders-list {
  display: flex;
  flex-direction: column;
}

.asks-list {
  flex-direction: column-reverse;
}

.order-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  padding: 2px 8px;
  font-size: 11px;
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s ease;
}

.order-row:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

.order-background {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  transition: width 0.3s ease;
}

.ask-bg {
  background-color: #ff4d4f;
}

.bid-bg {
  background-color: #52c41a;
}

.order-price {
  font-weight: 500;
  z-index: 1;
  position: relative;
}

.ask-price {
  color: #ff4d4f;
}

.bid-price {
  color: #52c41a;
}

.order-amount,
.order-total {
  text-align: right;
  z-index: 1;
  position: relative;
  color: #666;
}

.spread-info {
  padding: 8px;
  text-align: center;
  background: #f0f0f0;
  border-radius: 4px;
  margin: 4px 0;
}

.spread-value {
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.spread-percent {
  color: #8c8c8c;
  margin-left: 4px;
}

.depth-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
}

.stat-label {
  color: #8c8c8c;
}

.stat-value {
  font-weight: 500;
  color: #333;
}

.bid-color {
  color: #52c41a;
}

.ask-color {
  color: #ff4d4f;
}

/* 深色主题样式 */
.market-depth-panel.dark-theme .section-header {
  background: #2a2e39;
  color: #9ca3af;
}

.market-depth-panel.dark-theme .order-row:hover {
  background-color: rgba(41, 98, 255, 0.1);
}

.market-depth-panel.dark-theme .spread-info,
.market-depth-panel.dark-theme .depth-stats {
  background: #2a2e39;
}

.market-depth-panel.dark-theme .spread-value,
.market-depth-panel.dark-theme .stat-value {
  color: #ffffff;
}

.market-depth-panel.dark-theme .order-amount,
.market-depth-panel.dark-theme .order-total {
  color: #d1d5db;
}

.market-depth-panel.dark-theme .depth-chart {
  border-bottom-color: #434651;
}
</style>