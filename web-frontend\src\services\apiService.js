/**
 * apiService.js
 * API 请求服务
 *
 * 该模块封装了所有与后端 API 的交互。
 */

// 处理API响应的通用函数
const handleResponse = async (response) => {
  const data = await response.json()
  
  // 检查后端统一响应格式 {code, msg, data}
  // 后端返回的code为数字类型：0表示成功，1表示失败
  if (data.code !== 0) {
    const error = new Error(data.msg || '请求失败')
    error.response = { status: response.status, data }
    throw error
  }
  
  return data.data
}

// 处理网络错误
const handleNetworkError = (error, url) => {
  console.error(`API请求失败 [${url}]:`, error)
  
  // 如果存在全局错误处理函数，调用它
  if (window.handleGlobalError) {
    window.handleGlobalError(error)
  }
  
  throw error
}

/**
 * 获取认证头部
 * @returns {object} - 包含Authorization头的对象
 */
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
}

// 获取K线数据
export async function getCandlesticks(instId, bar = '1H', limit = 100) {
  const url = `/api/okx/market/candles?instId=${instId}&bar=${bar}&limit=${limit}`
  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    const data = await handleResponse(response)
    
    console.log('apiService: 从后端获取的K线数据样本:', data.slice(0, 2));
    
    // 转换数据格式以适配图表库
    return data.map(item => ({
      time: parseInt(item[0]),
      open: parseFloat(item[1]),
      high: parseFloat(item[2]),
      low: parseFloat(item[3]),
      close: parseFloat(item[4]),
      volume: parseFloat(item[5])
    }))
  } catch (error) {
    handleNetworkError(error, url)
  }
}

// 获取最新的Ticker数据
export async function getTicker(instId) {
  const url = `/api/okx/market/ticker?instId=${instId}`
  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    const data = await handleResponse(response)
    
    if (data && data.length > 0) {
      const ticker = data[0]
      return {
        instId: ticker.instId,
        last: parseFloat(ticker.last),
        lastSz: parseFloat(ticker.lastSz),
        askPx: parseFloat(ticker.askPx),
        askSz: parseFloat(ticker.askSz),
        bidPx: parseFloat(ticker.bidPx),
        bidSz: parseFloat(ticker.bidSz),
        open24h: parseFloat(ticker.open24h),
        high24h: parseFloat(ticker.high24h),
        low24h: parseFloat(ticker.low24h),
        vol24h: parseFloat(ticker.vol24h),
        ts: parseInt(ticker.ts)
      }
    }
    return null
  } catch (error) {
    handleNetworkError(error, url)
  }
}

// ==================== 用户相关API ====================

/**
 * 获取当前用户信息
 * @returns {Promise<object>} - 用户信息对象
 * @throws {Error} - 当API请求失败时抛出
 */
export async function getCurrentUser() {
    try {
        const response = await fetch('/api/user/me', {
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            }
        });
        const data = await handleResponse(response);
        return data.data;
    } catch (error) {
        console.error('Error fetching current user:', error);
        throw error;
    }
}

/**
 * 获取用户设置
 * @returns {Promise<object>} - 用户设置对象
 * @throws {Error} - 当API请求失败时抛出
 */
export async function getUserSettings() {
    try {
        const response = await fetch('/api/user/settings', {
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            }
        });
        const data = await handleResponse(response);
        return data.data;
    } catch (error) {
        console.error('Error fetching user settings:', error);
        throw error;
    }
}

/**
 * 保存用户设置
 * @param {object} settings - 用户设置对象
 * @returns {Promise<object>} - 保存结果
 * @throws {Error} - 当API请求失败时抛出
 */
export async function saveUserSettings(settings) {
    try {
        const response = await fetch('/api/user/settings', {
            method: 'POST',
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        });
        const data = await handleResponse(response);
        return data.data;
    } catch (error) {
        console.error('Error saving user settings:', error);
        throw error;
    }
}

/**
 * 获取用户每日统计数据
 * @returns {Promise<object>} - 统计数据对象
 * @throws {Error} - 当API请求失败时抛出
 */
export async function getDailyStats() {
    try {
        const response = await fetch('/api/user/daily-stats', {
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            }
        });
        const data = await handleResponse(response);
        return data.data;
    } catch (error) {
        console.error('Error fetching daily stats:', error);
        throw error;
    }
}

/**
 * 获取用户统计信息
 * @returns {Promise<object>} - 统计信息对象
 * @throws {Error} - 当API请求失败时抛出
 */
export async function getUserStats() {
    try {
        const response = await fetch('/api/user/stats', {
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            }
        });
        const data = await handleResponse(response);
        return data.data;
    } catch (error) {
        console.error('Error fetching user stats:', error);
        throw error;
    }
}

// ==================== API配置相关 ====================

/**
 * 获取API配置
 * @returns {Promise<object>} - API配置对象
 * @throws {Error} - 当API请求失败时抛出
 */
export async function getApiConfig() {
    try {
        const response = await fetch('/api/user/api-config', {
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            }
        });
        const data = await handleResponse(response);
        return data.data;
    } catch (error) {
        console.error('Error fetching API config:', error);
        throw error;
    }
}

/**
 * 保存API配置
 * @param {object} config - API配置对象
 * @returns {Promise<object>} - 保存结果
 * @throws {Error} - 当API请求失败时抛出
 */
export async function saveApiConfig(config) {
    try {
        const response = await fetch('/api/user/api-config', {
            method: 'POST',
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });
        const data = await handleResponse(response);
        return data.data;
    } catch (error) {
        console.error('Error saving API config:', error);
        throw error;
    }
}

/**
 * 测试API连接
 * @returns {Promise<object>} - 测试结果
 * @throws {Error} - 当API请求失败时抛出
 */
export async function testApiConnection() {
    try {
        const response = await fetch('/api/user/test-api', {
            method: 'POST',
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            }
        });
        const data = await handleResponse(response);
        return data.data;
    } catch (error) {
        console.error('Error testing API connection:', error);
        throw error;
    }
}

/**
 * 清除API配置
 * @returns {Promise<void>} - 清除结果
 * @throws {Error} - 当API请求失败时抛出
 */
export async function clearApiConfig() {
    try {
        const response = await fetch('/api/user/api-config', {
            method: 'DELETE',
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            }
        });
        await handleResponse(response);
    } catch (error) {
        console.error('Error clearing API config:', error);
        throw error;
    }
}