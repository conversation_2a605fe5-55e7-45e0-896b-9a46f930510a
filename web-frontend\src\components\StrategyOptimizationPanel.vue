<template>
  <div class="strategy-optimization-panel" :class="{ 'dark-theme': isDarkTheme }">
    <a-card title="策略优化" size="small" :bordered="false">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新数据">
            <a-button size="small" type="text" @click="refreshData">
              <ReloadOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="优化设置">
            <a-button size="small" type="text" @click="showSettings = true">
              <SettingOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="新建优化">
            <a-button size="small" type="primary" @click="showCreateOptimization = true">
              <PlusOutlined /> 新建优化
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <div class="optimization-container">
        <!-- 优化概览 -->
        <div class="optimization-overview">
          <div class="overview-stats">
            <div class="stat-item">
              <div class="stat-value">{{ optimizationResults.length }}</div>
              <div class="stat-label">总优化数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value positive">{{ formatPercentage(bestReturn) }}</div>
              <div class="stat-label">最佳收益率</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ bestSharpeRatio.toFixed(2) }}</div>
              <div class="stat-label">最佳夏普比率</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ runningOptimizations }}</div>
              <div class="stat-label">运行中</div>
            </div>
          </div>
        </div>

        <!-- 优化列表 -->
        <div class="optimization-list">
          <div class="section-header">
            <h4>优化列表</h4>
            <a-space>
              <a-select
                v-model:value="statusFilter"
                size="small"
                style="width: 120px"
                @change="filterOptimizations"
              >
                <a-select-option value="all">全部</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
                <a-select-option value="running">运行中</a-select-option>
                <a-select-option value="failed">失败</a-select-option>
              </a-select>
              <a-input-search
                v-model:value="searchKeyword"
                placeholder="搜索优化"
                size="small"
                style="width: 200px"
                @search="searchOptimizations"
              />
            </a-space>
          </div>
          
          <a-table
            :columns="optimizationColumns"
            :data-source="filteredOptimizations"
            size="small"
            :pagination="{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }"
            :scroll="{ x: 1200 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'bestReturn'">
                <span :class="record.bestReturn >= 0 ? 'positive' : 'negative'">
                  {{ formatPercentage(record.bestReturn) }}
                </span>
              </template>
              <template v-else-if="column.key === 'bestSharpe'">
                <span :class="record.bestSharpe >= 1 ? 'positive' : 'negative'">
                  {{ record.bestSharpe.toFixed(2) }}
                </span>
              </template>
              <template v-else-if="column.key === 'progress'">
                <a-progress 
                  :percent="record.progress" 
                  size="small"
                  :status="record.status === 'failed' ? 'exception' : 'normal'"
                />
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button size="small" @click="viewOptimizationDetail(record)">
                    详情
                  </a-button>
                  <a-button 
                    size="small" 
                    type="primary"
                    @click="applyBestParams(record)"
                    v-if="record.status === 'completed'"
                  >
                    应用
                  </a-button>
                  <a-dropdown>
                    <a-button size="small" type="text">
                      <MoreOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu @click="handleOptimizationAction($event, record)">
                        <a-menu-item key="clone">
                          <CopyOutlined /> 克隆
                        </a-menu-item>
                        <a-menu-item key="export">
                          <ExportOutlined /> 导出
                        </a-menu-item>
                        <a-menu-item key="stop" v-if="record.status === 'running'">
                          <StopOutlined /> 停止
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item key="delete" danger>
                          <DeleteOutlined /> 删除
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 参数分析 -->
        <div class="parameter-analysis" v-if="selectedOptimization">
          <div class="section-header">
            <h4>参数分析 - {{ selectedOptimization.name }}</h4>
            <a-button size="small" @click="selectedOptimization = null">
              关闭
            </a-button>
          </div>
          
          <div class="analysis-content">
            <div class="parameter-heatmap">
              <div class="chart-title">参数热力图</div>
              <div ref="heatmapChart" class="chart-container"></div>
            </div>
            
            <div class="parameter-surface">
              <div class="chart-title">参数曲面图</div>
              <div ref="surfaceChart" class="chart-container"></div>
            </div>
            
            <div class="optimization-path">
              <div class="chart-title">优化路径</div>
              <div ref="pathChart" class="chart-container"></div>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 新建优化模态框 -->
    <a-modal
      v-model:open="showCreateOptimization"
      title="新建策略优化"
      @ok="createOptimization"
      @cancel="resetCreateForm"
      width="800px"
    >
      <a-form :model="createForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="优化名称" required>
              <a-input v-model:value="createForm.name" placeholder="请输入优化名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="策略类型" required>
              <a-select v-model:value="createForm.strategyType" placeholder="选择策略类型">
                <a-select-option value="grid">网格交易</a-select-option>
                <a-select-option value="dca">定投策略</a-select-option>
                <a-select-option value="momentum">动量策略</a-select-option>
                <a-select-option value="arbitrage">套利策略</a-select-option>
                <a-select-option value="custom">自定义策略</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="交易对" required>
              <a-select v-model:value="createForm.symbol" placeholder="选择交易对">
                <a-select-option value="BTC-USDT">BTC-USDT</a-select-option>
                <a-select-option value="ETH-USDT">ETH-USDT</a-select-option>
                <a-select-option value="BNB-USDT">BNB-USDT</a-select-option>
                <a-select-option value="ADA-USDT">ADA-USDT</a-select-option>
                <a-select-option value="SOL-USDT">SOL-USDT</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="优化目标">
              <a-select v-model:value="createForm.objective" placeholder="选择优化目标">
                <a-select-option value="return">最大化收益</a-select-option>
                <a-select-option value="sharpe">最大化夏普比率</a-select-option>
                <a-select-option value="calmar">最大化卡玛比率</a-select-option>
                <a-select-option value="sortino">最大化索提诺比率</a-select-option>
                <a-select-option value="drawdown">最小化回撤</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="开始时间" required>
              <a-date-picker
                v-model:value="createForm.startDate"
                style="width: 100%"
                placeholder="选择开始时间"
                :disabled-date="disabledStartDate"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="结束时间" required>
              <a-date-picker
                v-model:value="createForm.endDate"
                style="width: 100%"
                placeholder="选择结束时间"
                :disabled-date="disabledEndDate"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="优化参数">
          <div class="optimization-params">
            <div v-for="(param, index) in createForm.parameters" :key="index" class="param-row">
              <a-input
                v-model:value="param.name"
                placeholder="参数名"
                style="width: 20%"
              />
              <a-input-number
                v-model:value="param.min"
                placeholder="最小值"
                style="width: 18%"
                :step="param.step || 0.1"
              />
              <a-input-number
                v-model:value="param.max"
                placeholder="最大值"
                style="width: 18%"
                :step="param.step || 0.1"
              />
              <a-input-number
                v-model:value="param.step"
                placeholder="步长"
                style="width: 15%"
                :min="0.001"
                :step="0.001"
              />
              <a-input
                v-model:value="param.description"
                placeholder="参数描述"
                style="width: 24%"
              />
              <a-button 
                type="text" 
                danger 
                @click="removeParameter(index)"
                style="width: 5%"
              >
                <DeleteOutlined />
              </a-button>
            </div>
            <a-button type="dashed" @click="addParameter" style="width: 100%">
              <PlusOutlined /> 添加参数
            </a-button>
          </div>
        </a-form-item>
        
        <a-form-item label="优化算法">
          <a-radio-group v-model:value="createForm.algorithm">
            <a-radio value="grid">网格搜索</a-radio>
            <a-radio value="random">随机搜索</a-radio>
            <a-radio value="genetic">遗传算法</a-radio>
            <a-radio value="bayesian">贝叶斯优化</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="最大迭代次数">
              <a-input-number
                v-model:value="createForm.maxIterations"
                :min="10"
                :max="10000"
                style="width: 100%"
                placeholder="最大迭代次数"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="并行进程数">
              <a-input-number
                v-model:value="createForm.parallelJobs"
                :min="1"
                :max="16"
                style="width: 100%"
                placeholder="并行进程数"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="高级设置">
          <a-space direction="vertical" style="width: 100%">
            <div>
              <a-switch v-model:checked="createForm.enableEarlyStopping" />
              <span class="setting-label">启用早停机制</span>
            </div>
            <div v-if="createForm.enableEarlyStopping" style="margin-left: 24px;">
              <a-form-item label="早停耐心值" style="margin-bottom: 8px">
                <a-input-number
                  v-model:value="createForm.patience"
                  :min="5"
                  :max="100"
                  style="width: 200px"
                />
              </a-form-item>
            </div>
            
            <div>
              <a-switch v-model:checked="createForm.enableCrossValidation" />
              <span class="setting-label">启用交叉验证</span>
            </div>
            <div v-if="createForm.enableCrossValidation" style="margin-left: 24px;">
              <a-form-item label="折数" style="margin-bottom: 8px">
                <a-input-number
                  v-model:value="createForm.cvFolds"
                  :min="2"
                  :max="10"
                  style="width: 200px"
                />
              </a-form-item>
            </div>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 优化详情模态框 -->
    <a-modal
      v-model:open="showOptimizationDetail"
      :title="selectedOptimization?.name"
      :footer="null"
      width="1200px"
    >
      <div v-if="selectedOptimization" class="optimization-detail">
        <a-tabs>
          <a-tab-pane key="overview" tab="概览">
            <div class="detail-overview">
              <div class="overview-metrics">
                <div class="metric-card">
                  <div class="metric-title">最佳收益率</div>
                  <div class="metric-value" :class="selectedOptimization.bestReturn >= 0 ? 'positive' : 'negative'">
                    {{ formatPercentage(selectedOptimization.bestReturn) }}
                  </div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">最佳夏普比率</div>
                  <div class="metric-value" :class="selectedOptimization.bestSharpe >= 1 ? 'positive' : 'negative'">
                    {{ selectedOptimization.bestSharpe.toFixed(2) }}
                  </div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">总测试次数</div>
                  <div class="metric-value">
                    {{ selectedOptimization.totalTests }}
                  </div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">优化时间</div>
                  <div class="metric-value">
                    {{ formatDuration(selectedOptimization.duration) }}
                  </div>
                </div>
              </div>
              
              <div class="best-parameters">
                <h4>最佳参数组合</h4>
                <a-descriptions :column="2" bordered>
                  <a-descriptions-item 
                    v-for="param in selectedOptimization.bestParameters" 
                    :key="param.name"
                    :label="param.description || param.name"
                  >
                    {{ param.value }}
                  </a-descriptions-item>
                </a-descriptions>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="results" tab="优化结果">
            <a-table
              :columns="resultColumns"
              :data-source="selectedOptimization.results"
              size="small"
              :pagination="{ pageSize: 20, showSizeChanger: true }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'return'">
                  <span :class="record.return >= 0 ? 'positive' : 'negative'">
                    {{ formatPercentage(record.return) }}
                  </span>
                </template>
                <template v-else-if="column.key === 'sharpe'">
                  <span :class="record.sharpe >= 1 ? 'positive' : 'negative'">
                    {{ record.sharpe.toFixed(2) }}
                  </span>
                </template>
                <template v-else-if="column.key === 'drawdown'">
                  <span class="negative">{{ formatPercentage(record.drawdown) }}</span>
                </template>
                <template v-else-if="column.key === 'parameters'">
                  <a-tooltip>
                    <template #title>
                      <div v-for="param in record.parameters" :key="param.name">
                        {{ param.name }}: {{ param.value }}
                      </div>
                    </template>
                    <a-tag>查看参数</a-tag>
                  </a-tooltip>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="analysis" tab="分析报告">
            <div class="analysis-report">
              <div class="report-section">
                <h4>参数敏感性分析</h4>
                <div class="sensitivity-analysis">
                  <div v-for="param in selectedOptimization.sensitivityAnalysis" :key="param.name" class="sensitivity-item">
                    <div class="param-name">{{ param.name }}</div>
                    <div class="sensitivity-score">
                      <a-progress 
                        :percent="param.sensitivity * 100" 
                        size="small"
                        :stroke-color="getSensitivityColor(param.sensitivity)"
                      />
                      <span class="score-text">{{ (param.sensitivity * 100).toFixed(1) }}%</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="report-section">
                <h4>优化建议</h4>
                <div class="optimization-suggestions">
                  <ul>
                    <li v-for="suggestion in selectedOptimization.suggestions" :key="suggestion">
                      {{ suggestion }}
                    </li>
                  </ul>
                </div>
              </div>
              
              <div class="report-section">
                <h4>稳定性分析</h4>
                <div class="stability-metrics">
                  <div class="stability-item">
                    <span class="stability-label">参数稳定性:</span>
                    <a-rate v-model:value="selectedOptimization.parameterStability" disabled />
                  </div>
                  <div class="stability-item">
                    <span class="stability-label">收益稳定性:</span>
                    <a-rate v-model:value="selectedOptimization.returnStability" disabled />
                  </div>
                  <div class="stability-item">
                    <span class="stability-label">风险稳定性:</span>
                    <a-rate v-model:value="selectedOptimization.riskStability" disabled />
                  </div>
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>

    <!-- 设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="优化设置"
      @ok="saveSettings"
      @cancel="resetSettings"
      width="600px"
    >
      <a-form :model="settings" layout="vertical">
        <a-form-item label="默认优化算法">
          <a-radio-group v-model:value="settings.defaultAlgorithm">
            <a-radio value="grid">网格搜索</a-radio>
            <a-radio value="random">随机搜索</a-radio>
            <a-radio value="genetic">遗传算法</a-radio>
            <a-radio value="bayesian">贝叶斯优化</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="默认最大迭代次数">
          <a-input-number
            v-model:value="settings.defaultMaxIterations"
            :min="10"
            :max="10000"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="默认并行进程数">
          <a-input-number
            v-model:value="settings.defaultParallelJobs"
            :min="1"
            :max="16"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="性能优化">
          <a-space direction="vertical" style="width: 100%">
            <div>
              <a-switch v-model:checked="settings.enableGPU" />
              <span class="setting-label">启用GPU加速</span>
            </div>
            <div>
              <a-switch v-model:checked="settings.enableCache" />
              <span class="setting-label">启用结果缓存</span>
            </div>
            <div>
              <a-switch v-model:checked="settings.enableMemoryOptimization" />
              <span class="setting-label">启用内存优化</span>
            </div>
          </a-space>
        </a-form-item>
        
        <a-form-item label="通知设置">
          <a-checkbox-group v-model:value="settings.notifications">
            <a-checkbox value="progress">进度通知</a-checkbox>
            <a-checkbox value="complete">完成通知</a-checkbox>
            <a-checkbox value="error">错误通知</a-checkbox>
            <a-checkbox value="milestone">里程碑通知</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { useChartStore } from '@/stores/chartStore'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import {
  ReloadOutlined,
  SettingOutlined,
  PlusOutlined,
  MoreOutlined,
  CopyOutlined,
  ExportOutlined,
  StopOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// 定义事件
const emit = defineEmits(['optimization-created', 'optimization-completed', 'parameters-applied'])

// 使用状态管理
const chartStore = useChartStore()
const { isDarkTheme } = storeToRefs(chartStore)

// 本地状态
const showSettings = ref(false)
const showCreateOptimization = ref(false)
const showOptimizationDetail = ref(false)
const selectedOptimization = ref(null)
const heatmapChart = ref(null)
const surfaceChart = ref(null)
const pathChart = ref(null)
const updateInterval = ref(null)

// 过滤和搜索
const statusFilter = ref('all')
const searchKeyword = ref('')

// 优化数据
const optimizationResults = ref([
  {
    id: 1,
    name: 'BTC网格策略参数优化_20240301',
    strategyType: '网格交易',
    symbol: 'BTC-USDT',
    objective: 'sharpe',
    status: 'completed',
    progress: 100,
    bestReturn: 28.5,
    bestSharpe: 2.15,
    totalTests: 1000,
    duration: 3600000, // 1小时
    bestParameters: [
      { name: 'gridSize', value: '0.8%', description: '网格间距' },
      { name: 'gridCount', value: '15', description: '网格数量' },
      { name: 'baseAmount', value: '120', description: '基础下单量' }
    ],
    results: [
      { id: 1, return: 28.5, sharpe: 2.15, drawdown: 8.2, parameters: [{ name: 'gridSize', value: '0.8%' }] },
      { id: 2, return: 25.2, sharpe: 1.98, drawdown: 9.1, parameters: [{ name: 'gridSize', value: '0.7%' }] }
    ],
    sensitivityAnalysis: [
      { name: 'gridSize', sensitivity: 0.85 },
      { name: 'gridCount', sensitivity: 0.62 },
      { name: 'baseAmount', sensitivity: 0.34 }
    ],
    suggestions: [
      '网格间距对策略收益影响最大，建议重点关注',
      '当前参数组合在历史数据上表现稳定',
      '建议在实盘前进行小资金测试'
    ],
    parameterStability: 4,
    returnStability: 4,
    riskStability: 3
  },
  {
    id: 2,
    name: 'ETH动量策略参数优化_20240315',
    strategyType: '动量策略',
    symbol: 'ETH-USDT',
    objective: 'return',
    status: 'running',
    progress: 65,
    bestReturn: 22.1,
    bestSharpe: 1.78,
    totalTests: 650,
    duration: 2400000, // 40分钟
    bestParameters: [
      { name: 'period', value: '12', description: '动量周期' },
      { name: 'threshold', value: '0.025', description: '信号阈值' }
    ],
    results: [],
    sensitivityAnalysis: [],
    suggestions: [],
    parameterStability: 0,
    returnStability: 0,
    riskStability: 0
  }
])

// 表单数据
const createForm = ref({
  name: '',
  strategyType: '',
  symbol: '',
  objective: 'sharpe',
  startDate: null,
  endDate: null,
  parameters: [],
  algorithm: 'grid',
  maxIterations: 1000,
  parallelJobs: 4,
  enableEarlyStopping: false,
  patience: 20,
  enableCrossValidation: false,
  cvFolds: 5
})

// 设置
const settings = ref({
  defaultAlgorithm: 'grid',
  defaultMaxIterations: 1000,
  defaultParallelJobs: 4,
  enableGPU: false,
  enableCache: true,
  enableMemoryOptimization: true,
  notifications: ['complete', 'error']
})

// 表格列定义
const optimizationColumns = [
  { title: '优化名称', dataIndex: 'name', key: 'name', width: 200, fixed: 'left' },
  { title: '策略类型', dataIndex: 'strategyType', key: 'strategyType', width: 100 },
  { title: '交易对', dataIndex: 'symbol', key: 'symbol', width: 100 },
  { title: '优化目标', dataIndex: 'objective', key: 'objective', width: 100 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '进度', dataIndex: 'progress', key: 'progress', width: 120 },
  { title: '最佳收益率', dataIndex: 'bestReturn', key: 'bestReturn', width: 100 },
  { title: '最佳夏普', dataIndex: 'bestSharpe', key: 'bestSharpe', width: 100 },
  { title: '测试次数', dataIndex: 'totalTests', key: 'totalTests', width: 100 },
  { title: '操作', key: 'actions', width: 200, fixed: 'right' }
]

const resultColumns = [
  { title: '序号', dataIndex: 'id', key: 'id', width: 60 },
  { title: '收益率', dataIndex: 'return', key: 'return', width: 100 },
  { title: '夏普比率', dataIndex: 'sharpe', key: 'sharpe', width: 100 },
  { title: '最大回撤', dataIndex: 'drawdown', key: 'drawdown', width: 100 },
  { title: '参数组合', dataIndex: 'parameters', key: 'parameters', width: 150 }
]

// 计算属性
const bestReturn = computed(() => {
  const completed = optimizationResults.value.filter(o => o.status === 'completed')
  if (completed.length === 0) return 0
  return Math.max(...completed.map(o => o.bestReturn))
})

const bestSharpeRatio = computed(() => {
  const completed = optimizationResults.value.filter(o => o.status === 'completed')
  if (completed.length === 0) return 0
  return Math.max(...completed.map(o => o.bestSharpe))
})

const runningOptimizations = computed(() => {
  return optimizationResults.value.filter(o => o.status === 'running').length
})

const filteredOptimizations = computed(() => {
  let filtered = optimizationResults.value
  
  // 状态过滤
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(o => o.status === statusFilter.value)
  }
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(o => 
      o.name.toLowerCase().includes(keyword) ||
      o.strategyType.toLowerCase().includes(keyword) ||
      o.symbol.toLowerCase().includes(keyword)
    )
  }
  
  return filtered
})

/**
 * 获取状态颜色
 */
const getStatusColor = (status) => {
  switch (status) {
    case 'completed': return 'green'
    case 'running': return 'blue'
    case 'failed': return 'red'
    default: return 'gray'
  }
}

/**
 * 获取状态文本
 */
const getStatusText = (status) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'running': return '运行中'
    case 'failed': return '失败'
    default: return '未知'
  }
}

/**
 * 获取敏感性颜色
 */
const getSensitivityColor = (sensitivity) => {
  if (sensitivity >= 0.8) return '#f5222d'
  if (sensitivity >= 0.6) return '#fa8c16'
  if (sensitivity >= 0.4) return '#fadb14'
  return '#52c41a'
}

/**
 * 格式化百分比
 */
const formatPercentage = (value) => {
  return `${value.toFixed(1)}%`
}

/**
 * 格式化持续时间
 */
const formatDuration = (ms) => {
  const hours = Math.floor(ms / 3600000)
  const minutes = Math.floor((ms % 3600000) / 60000)
  const seconds = Math.floor((ms % 60000) / 1000)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds}秒`
  } else {
    return `${seconds}秒`
  }
}

/**
 * 禁用开始日期
 */
const disabledStartDate = (current) => {
  return current && current > dayjs().endOf('day')
}

/**
 * 禁用结束日期
 */
const disabledEndDate = (current) => {
  return current && (current > dayjs().endOf('day') || current < createForm.value.startDate)
}

/**
 * 过滤优化
 */
const filterOptimizations = () => {
  // 过滤逻辑在计算属性中处理
}

/**
 * 搜索优化
 */
const searchOptimizations = () => {
  // 搜索逻辑在计算属性中处理
}

/**
 * 处理优化操作
 */
const handleOptimizationAction = ({ key }, optimization) => {
  switch (key) {
    case 'clone':
      cloneOptimization(optimization)
      break
    case 'export':
      exportOptimization(optimization)
      break
    case 'stop':
      stopOptimization(optimization)
      break
    case 'delete':
      deleteOptimization(optimization)
      break
  }
}

/**
 * 克隆优化
 */
const cloneOptimization = (optimization) => {
  const newOptimization = {
    ...optimization,
    id: Date.now(),
    name: `${optimization.name} (副本)`,
    status: 'completed',
    progress: 100
  }
  optimizationResults.value.unshift(newOptimization)
  message.success('优化已克隆')
}

/**
 * 导出优化
 */
const exportOptimization = (optimization) => {
  message.info(`正在导出优化: ${optimization.name}`)
  // 这里可以实现导出逻辑
}

/**
 * 停止优化
 */
const stopOptimization = (optimization) => {
  optimization.status = 'failed'
  message.success(`优化 ${optimization.name} 已停止`)
}

/**
 * 删除优化
 */
const deleteOptimization = (optimization) => {
  const index = optimizationResults.value.findIndex(o => o.id === optimization.id)
  if (index > -1) {
    optimizationResults.value.splice(index, 1)
    message.success('优化已删除')
  }
}

/**
 * 查看优化详情
 */
const viewOptimizationDetail = (optimization) => {
  selectedOptimization.value = optimization
  showOptimizationDetail.value = true
  
  nextTick(() => {
    initAnalysisCharts()
  })
}

/**
 * 应用最佳参数
 */
const applyBestParams = (optimization) => {
  message.success(`已应用 ${optimization.name} 的最佳参数`)
  emit('parameters-applied', optimization.bestParameters)
}

/**
 * 创建优化
 */
const createOptimization = () => {
  if (!createForm.value.name || !createForm.value.strategyType || !createForm.value.symbol || 
      !createForm.value.startDate || !createForm.value.endDate) {
    message.error('请填写必要信息')
    return
  }
  
  if (createForm.value.parameters.length === 0) {
    message.error('请至少添加一个优化参数')
    return
  }
  
  const newOptimization = {
    id: Date.now(),
    name: createForm.value.name,
    strategyType: createForm.value.strategyType,
    symbol: createForm.value.symbol,
    objective: createForm.value.objective,
    status: 'running',
    progress: 0,
    bestReturn: 0,
    bestSharpe: 0,
    totalTests: 0,
    duration: 0,
    bestParameters: [],
    results: [],
    sensitivityAnalysis: [],
    suggestions: [],
    parameterStability: 0,
    returnStability: 0,
    riskStability: 0
  }
  
  optimizationResults.value.unshift(newOptimization)
  showCreateOptimization.value = false
  resetCreateForm()
  message.success('优化已创建，正在运行中...')
  emit('optimization-created', newOptimization)
  
  // 模拟优化进度
  simulateOptimizationProgress(newOptimization)
}

/**
 * 模拟优化进度
 */
const simulateOptimizationProgress = (optimization) => {
  const progressInterval = setInterval(() => {
    optimization.progress += Math.random() * 5
    optimization.totalTests += Math.floor(Math.random() * 10) + 1
    
    // 更新最佳结果
    if (Math.random() > 0.7) {
      optimization.bestReturn = Math.max(optimization.bestReturn, Math.random() * 30)
      optimization.bestSharpe = Math.max(optimization.bestSharpe, Math.random() * 2 + 0.5)
    }
    
    if (optimization.progress >= 100) {
      optimization.progress = 100
      optimization.status = 'completed'
      
      // 生成最终结果
      optimization.bestParameters = createForm.value.parameters.map(param => ({
        name: param.name,
        value: (Math.random() * (param.max - param.min) + param.min).toFixed(3),
        description: param.description
      }))
      
      optimization.sensitivityAnalysis = createForm.value.parameters.map(param => ({
        name: param.name,
        sensitivity: Math.random()
      }))
      
      optimization.suggestions = [
        '优化结果显示当前参数组合表现良好',
        '建议在不同市场环境下进行验证',
        '可以考虑进一步细化参数范围'
      ]
      
      optimization.parameterStability = Math.floor(Math.random() * 2) + 3
      optimization.returnStability = Math.floor(Math.random() * 2) + 3
      optimization.riskStability = Math.floor(Math.random() * 2) + 3
      
      clearInterval(progressInterval)
      message.success(`优化 ${optimization.name} 已完成`)
      emit('optimization-completed', optimization)
    }
  }, 2000)
}

/**
 * 重置创建表单
 */
const resetCreateForm = () => {
  createForm.value = {
    name: '',
    strategyType: '',
    symbol: '',
    objective: 'sharpe',
    startDate: null,
    endDate: null,
    parameters: [],
    algorithm: 'grid',
    maxIterations: 1000,
    parallelJobs: 4,
    enableEarlyStopping: false,
    patience: 20,
    enableCrossValidation: false,
    cvFolds: 5
  }
}

/**
 * 添加参数
 */
const addParameter = () => {
  createForm.value.parameters.push({
    name: '',
    min: 0,
    max: 1,
    step: 0.1,
    description: ''
  })
}

/**
 * 移除参数
 */
const removeParameter = (index) => {
  createForm.value.parameters.splice(index, 1)
}

/**
 * 初始化分析图表
 */
const initAnalysisCharts = () => {
  initHeatmapChart()
  initSurfaceChart()
  initPathChart()
}

/**
 * 初始化热力图
 */
const initHeatmapChart = () => {
  if (!heatmapChart.value) return
  
  const chart = echarts.init(heatmapChart.value)
  
  // 生成模拟数据
  const data = []
  for (let i = 0; i < 10; i++) {
    for (let j = 0; j < 10; j++) {
      data.push([i, j, Math.random() * 100])
    }
  }
  
  const option = {
    tooltip: {
      position: 'top'
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 10 }, (_, i) => `P${i + 1}`),
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: Array.from({ length: 10 }, (_, i) => `P${i + 1}`),
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: 100,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%'
    },
    series: [
      {
        name: '参数组合收益',
        type: 'heatmap',
        data: data,
        label: {
          show: true
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

/**
 * 初始化曲面图
 */
const initSurfaceChart = () => {
  if (!surfaceChart.value) return
  
  const chart = echarts.init(surfaceChart.value)
  
  // 生成模拟数据
  const data = []
  for (let i = 0; i < 20; i++) {
    for (let j = 0; j < 20; j++) {
      const x = i / 10
      const y = j / 10
      const z = Math.sin(x) * Math.cos(y) * 50 + 50
      data.push([x, y, z])
    }
  }
  
  const option = {
    tooltip: {},
    visualMap: {
      show: false,
      dimension: 2,
      min: 0,
      max: 100,
      inRange: {
        color: [
          '#313695',
          '#4575b4',
          '#74add1',
          '#abd9e9',
          '#e0f3f8',
          '#ffffcc',
          '#fee090',
          '#fdae61',
          '#f46d43',
          '#d73027',
          '#a50026'
        ]
      }
    },
    xAxis3D: {
      type: 'value'
    },
    yAxis3D: {
      type: 'value'
    },
    zAxis3D: {
      type: 'value'
    },
    grid3D: {
      viewControl: {
        projection: 'orthographic'
      }
    },
    series: [
      {
        type: 'surface',
        data: data
      }
    ]
  }
  
  chart.setOption(option)
}

/**
 * 初始化路径图
 */
const initPathChart = () => {
  if (!pathChart.value) return
  
  const chart = echarts.init(pathChart.value)
  
  // 生成模拟优化路径数据
  const pathData = []
  let currentValue = 10
  
  for (let i = 0; i < 50; i++) {
    currentValue += (Math.random() - 0.3) * 5
    pathData.push([i, Math.max(0, currentValue)])
  }
  
  const option = {
    title: {
      text: '优化收敛路径',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: isDarkTheme.value ? '#fff' : '#333'
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '迭代次数',
      axisLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#434343' : '#e8e8e8'
        }
      },
      axisLabel: {
        color: isDarkTheme.value ? '#999' : '#666'
      }
    },
    yAxis: {
      type: 'value',
      name: '目标函数值',
      axisLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#434343' : '#e8e8e8'
        }
      },
      axisLabel: {
        color: isDarkTheme.value ? '#999' : '#666'
      },
      splitLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#434343' : '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: '最佳值',
        type: 'line',
        data: pathData,
        smooth: true,
        lineStyle: {
          color: '#1890ff',
          width: 2
        },
        symbol: 'circle',
        symbolSize: 4
      }
    ]
  }
  
  chart.setOption(option)
}

/**
 * 刷新数据
 */
const refreshData = () => {
  message.success('数据已刷新')
  // 这里可以实现实际的数据刷新逻辑
}

/**
 * 保存设置
 */
const saveSettings = () => {
  localStorage.setItem('optimization-settings', JSON.stringify(settings.value))
  showSettings.value = false
  message.success('设置已保存')
}

/**
 * 重置设置
 */
const resetSettings = () => {
  settings.value = {
    defaultAlgorithm: 'grid',
    defaultMaxIterations: 1000,
    defaultParallelJobs: 4,
    enableGPU: false,
    enableCache: true,
    enableMemoryOptimization: true,
    notifications: ['complete', 'error']
  }
}

/**
 * 加载设置
 */
const loadSettings = () => {
  const saved = localStorage.getItem('optimization-settings')
  if (saved) {
    try {
      settings.value = { ...settings.value, ...JSON.parse(saved) }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }
}

/**
 * 启动自动刷新
 */
const startAutoRefresh = () => {
  if (updateInterval.value) return
  
  updateInterval.value = setInterval(() => {
    // 更新运行中的优化进度
    optimizationResults.value.forEach(optimization => {
      if (optimization.status === 'running' && optimization.progress < 100) {
        optimization.progress = Math.min(100, optimization.progress + Math.random() * 3)
        optimization.totalTests += Math.floor(Math.random() * 5) + 1
        
        // 随机更新最佳结果
        if (Math.random() > 0.8) {
          optimization.bestReturn = Math.max(optimization.bestReturn, Math.random() * 30)
          optimization.bestSharpe = Math.max(optimization.bestSharpe, Math.random() * 2 + 0.5)
        }
        
        if (optimization.progress >= 100) {
          optimization.status = 'completed'
          message.success(`优化 ${optimization.name} 已完成`)
          emit('optimization-completed', optimization)
        }
      }
    })
  }, 5000)
}

/**
 * 停止自动刷新
 */
const stopAutoRefresh = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
    updateInterval.value = null
  }
}

// 生命周期
onMounted(() => {
  loadSettings()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.strategy-optimization-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.optimization-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.optimization-overview {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.optimization-list {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.parameter-analysis {
  margin-top: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
}

.analysis-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 16px;
  height: 600px;
}

.parameter-heatmap {
  grid-column: 1;
  grid-row: 1;
}

.parameter-surface {
  grid-column: 2;
  grid-row: 1;
}

.optimization-path {
  grid-column: 1 / 3;
  grid-row: 2;
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  text-align: center;
}

.chart-container {
  width: 100%;
  height: calc(100% - 30px);
}

.optimization-params {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-row {
  display: flex;
  gap: 8px;
  align-items: center;
}

.optimization-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-overview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.overview-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.metric-card {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.metric-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
}

.best-parameters {
  margin-top: 16px;
}

.best-parameters h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.analysis-report {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.report-section {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.report-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
}

.sensitivity-analysis {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sensitivity-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.param-name {
  font-size: 12px;
  font-weight: 500;
  min-width: 100px;
}

.sensitivity-score {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-text {
  font-size: 12px;
  color: #666;
  min-width: 40px;
}

.optimization-suggestions ul {
  margin: 0;
  padding-left: 20px;
}

.optimization-suggestions li {
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.5;
}

.stability-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stability-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stability-label {
  font-size: 13px;
  font-weight: 500;
  min-width: 100px;
}

.setting-label {
  margin-left: 8px;
  font-size: 13px;
}

.positive {
  color: #52c41a;
}

.negative {
  color: #f5222d;
}

/* 深色主题 */
.dark-theme .optimization-overview {
  background: #1f1f1f;
}

.dark-theme .stat-item {
  background: #262626;
  color: #fff;
}

.dark-theme .stat-label {
  color: #999;
}

.dark-theme .parameter-analysis {
  border-color: #434343;
  background: #1f1f1f;
}

.dark-theme .metric-card {
  background: #262626;
  border-color: #434343;
  color: #fff;
}

.dark-theme .metric-title {
  color: #999;
}

.dark-theme .report-section {
  background: #262626;
  border-color: #434343;
  color: #fff;
}

.dark-theme .param-name {
  color: #fff;
}

.dark-theme .stability-label {
  color: #fff;
}

.dark-theme .setting-label {
  color: #fff;
}
</style>