#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OKX API 配置管理模块
用于安全地管理API密钥和配置信息
"""

import os
from pathlib import Path
import requests
import time
import hmac
import base64
import hashlib


class OKXConfig:
    """OKX API 配置管理类"""

    def __init__(self, config_file=None):
        """
        初始化配置

        Args:
            config_file: 配置文件路径，默认为 config.env
        """
        self.config_file = config_file or "config.env"
        self.config = {}
        self.load_config()

    def load_config(self):
        """加载配置文件"""
        # 首先尝试从环境变量读取
        self.config = {
            'api_key': os.getenv('OKX_API_KEY', ''),
            'secret_key': os.getenv('OKX_SECRET_KEY', ''),
            'passphrase': os.getenv('OKX_PASSPHRASE', ''),
            'flag': os.getenv('OKX_FLAG', '1'),
            'domain': os.getenv('OKX_DOMAIN', 'https://www.okx.com'),
            'debug': os.getenv('OKX_DEBUG', 'false').lower() == 'true'
        }

        # 如果环境变量为空，尝试从配置文件读取
        if not self.config['api_key'] and Path(self.config_file).exists():
            self.load_from_file()

    def load_from_file(self):
        """从配置文件读取配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()

                        if key == 'OKX_API_KEY':
                            self.config['api_key'] = value
                        elif key == 'OKX_SECRET_KEY':
                            self.config['secret_key'] = value
                        elif key == 'OKX_PASSPHRASE':
                            self.config['passphrase'] = value
                        elif key == 'OKX_FLAG':
                            self.config['flag'] = value
                        elif key == 'OKX_DOMAIN':
                            self.config['domain'] = value
                        elif key == 'OKX_DEBUG':
                            self.config['debug'] = value.lower() == 'true'
        except Exception as e:
            print(f"读取配置文件失败: {e}")

    def get_config(self):
        """获取配置信息"""
        return self.config.copy()

    def is_configured(self):
        """检查是否已配置API密钥"""
        return bool(self.config['api_key'] and self.config['secret_key'] and self.config['passphrase'])

    def print_config_status(self):
        """打印配置状态"""
        print("=== OKX API 配置状态 ===")
        print(f"API密钥已配置: {'✅' if self.is_configured() else '❌'}")
        print(f"交易环境: {'模拟盘' if self.config['flag'] == '1' else '实盘'}")
        print(f"调试模式: {'开启' if self.config['debug'] else '关闭'}")

        if not self.is_configured():
            print("\n⚠️  请配置您的API密钥:")
            print("   方法1: 设置环境变量")
            print("      OKX_API_KEY=your_api_key")
            print("      OKX_SECRET_KEY=your_secret_key")
            print("      OKX_PASSPHRASE=your_passphrase")
            print("\n   方法2: 创建 config.env 文件")
            print("      复制 config.env.example 为 config.env")
            print("      并填入您的实际API密钥")

        print("=" * 30)


# 全局配置实例
_config_instance = None


def get_config_instance():
    """获取全局唯一的OKXConfig实例"""
    global _config_instance
    if _config_instance is None:
        _config_instance = OKXConfig()
    return _config_instance


def get_okx_config():
    """获取OKX配置的便捷函数"""
    return get_config_instance().get_config()


def is_okx_configured():
    """检查OKX是否已配置的便捷函数"""
    return get_config_instance().is_configured()


def make_okx_request(method, endpoint, api_key, secret_key, passphrase, is_sandbox=False, body=""):
    """
    通用OKX API请求函数
    :param method: 'GET' 或 'POST'
    :param endpoint: API路径，如 '/api/v5/account/balance'
    :param api_key: OKX API KEY
    :param secret_key: OKX SECRET KEY
    :param passphrase: OKX PASSPHRASE
    :param is_sandbox: 是否为模拟盘
    :param body: POST请求体（字符串）
    :return: 返回json数据
    """
    base_url = "https://www.okx.com"  # 如需沙盒环境可调整
    url = base_url + endpoint
    timestamp = str(time.time())
    prehash = f"{timestamp}{method.upper()}{endpoint}{body}"
    sign = base64.b64encode(
        hmac.new(secret_key.encode(), prehash.encode(),
                 hashlib.sha256).digest()
    ).decode()
    headers = {
        "OK-ACCESS-KEY": api_key,
        "OK-ACCESS-SIGN": sign,
        "OK-ACCESS-TIMESTAMP": timestamp,
        "OK-ACCESS-PASSPHRASE": passphrase,
        "Content-Type": "application/json"
    }
    if method.upper() == "GET":
        resp = requests.get(url, headers=headers)
    else:
        resp = requests.post(url, headers=headers, data=body)
    try:
        return resp.json()
    except Exception:
        return {"code": "-1", "msg": "响应解析失败", "raw": resp.text}
