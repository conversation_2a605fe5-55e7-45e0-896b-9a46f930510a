<template>
  <div class="ai-predict">
    <h2>AI行情预测</h2>

    <!-- 预测表单 -->
    <a-form layout="inline" :model="query" @submit.prevent style="margin-bottom: 16px">
      <a-form-item label="产品ID">
        <a-input v-model="query.instId" placeholder="如 BTC-USDT-SWAP" style="width: 180px" />
      </a-form-item>
      <a-form-item label="预测类型">
        <a-select v-model="query.type" style="width: 120px" allow-clear>
          <a-select-option value="price">价格预测</a-select-option>
          <a-select-option value="trend">趋势预测</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="fetchPredictions" :loading="loading">查询</a-button>
      </a-form-item>
    </a-form>

    <a-table :dataSource="predictionList" :columns="columns" :loading="loading">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'type'">
          <span>{{ record.type === 'price' ? '价格预测' : '趋势预测' }}</span>
        </template>
        <template v-else-if="column.key === 'confidence'">
          <a-progress :percent="Number((record.confidence * 100).toFixed(2))" :status="getConfidenceStatus(record.confidence)" />
        </template>
        <template v-else-if="column.key === 'details'">
          <a-button type="link" @click="showPredictionDetails(record)">详情</a-button>
        </template>
        <template v-else>
          {{ record[column.dataIndex] }}
        </template>
      </template>
      <template #empty>
        <div style="text-align:center; padding:32px;">暂无预测</div>
      </template>
    </a-table>

    <a-modal
      title="预测详情"
      :open="modalVisible"
      @update:open="modalVisible = $event"
      :footer="null"
      @cancel="modalVisible = false"
    >
      <a-descriptions bordered>
        <a-descriptions-item label="产品ID">{{ currentPrediction.instId }}</a-descriptions-item>
        <a-descriptions-item label="预测类型">{{ currentPrediction.type === 'price' ? '价格预测' : '趋势预测' }}</a-descriptions-item>
        <a-descriptions-item label="当前价格">{{ currentPrediction.currentPrice }}</a-descriptions-item>
        <a-descriptions-item label="预测价格">{{ currentPrediction.predictedPrice }}</a-descriptions-item>
        <a-descriptions-item label="预测趋势">{{ currentPrediction.predictedTrend }}</a-descriptions-item>
        <a-descriptions-item label="置信度">{{ (currentPrediction.confidence * 100).toFixed(2) }}%</a-descriptions-item>
        <a-descriptions-item label="预测时间">{{ formatTime(currentPrediction.timestamp) }}</a-descriptions-item>
        <a-descriptions-item label="分析依据">{{ currentPrediction.analysis }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, reactive } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import axios from 'axios'

const loading = ref(false)
const modalVisible = ref(false)
const predictionList = ref([])
const currentPrediction = ref({})

const query = reactive({
  instId: '',
  type: undefined
})

const columns = [
  { title: '产品ID', dataIndex: 'instId', key: 'instId' },
  { title: '预测类型', dataIndex: 'type', key: 'type' },
  { title: '当前价格', dataIndex: 'currentPrice', key: 'currentPrice' },
  { title: '预测价格', dataIndex: 'predictedPrice', key: 'predictedPrice' },
  { title: '预测趋势', dataIndex: 'predictedTrend', key: 'predictedTrend' },
  { title: '置信度', dataIndex: 'confidence', key: 'confidence' },
  { title: '操作', key: 'details' }
]

async function fetchPredictions() {
  loading.value = true
  try {
    const params = {}
    if (query.instId) params.instId = query.instId
    if (query.type) params.type = query.type

    const response = await axios.get('/api/ai/predictions', { params })
    const data = response.data

    if (data.code === 0) {
      predictionList.value = data.data
    } else {
      message.error(data.msg || '查询失败')
    }
  } catch (error) {
    console.error('AI预测API调用失败:', error)
    if (error.response && error.response.status === 401) {
      message.error('请先登录')
    } else {
      message.warning('AI预测服务暂时不可用')
    }
  } finally {
    loading.value = false
  }
}

function showPredictionDetails(record) {
  currentPrediction.value = record
  modalVisible.value = true
}

function getConfidenceStatus(confidence) {
  if (confidence >= 0.8) return 'success'
  if (confidence >= 0.6) return 'normal'
  return 'exception'
}

function formatTime(timestamp) {
  if (!timestamp) return ''
  const date = new Date(Number(timestamp))
  return date.toLocaleString()
}

onMounted(() => {
  // 页面加载时自动预测
  fetchPredictions()
})
</script>

<style scoped>
.ai-predict {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px;
}

.prediction-result {
  margin-top: 24px;
}
</style>