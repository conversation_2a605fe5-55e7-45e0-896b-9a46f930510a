<template>
  <div class="strategy-backtest-panel" :class="{ 'dark-theme': isDarkTheme }">
    <a-card title="策略回测" size="small" :bordered="false">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新数据">
            <a-button size="small" type="text" @click="refreshData">
              <ReloadOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="回测设置">
            <a-button size="small" type="text" @click="showSettings = true">
              <SettingOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="新建回测">
            <a-button size="small" type="primary" @click="showCreateBacktest = true">
              <PlusOutlined /> 新建回测
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <div class="backtest-container">
        <!-- 回测概览 -->
        <div class="backtest-overview">
          <div class="overview-stats">
            <div class="stat-item">
              <div class="stat-value">{{ backtestResults.length }}</div>
              <div class="stat-label">总回测数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value positive">{{ formatPercentage(avgReturn) }}</div>
              <div class="stat-label">平均收益率</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ avgSharpeRatio.toFixed(2) }}</div>
              <div class="stat-label">平均夏普比率</div>
            </div>
            <div class="stat-item">
              <div class="stat-value negative">{{ formatPercentage(avgMaxDrawdown) }}</div>
              <div class="stat-label">平均最大回撤</div>
            </div>
          </div>
        </div>

        <!-- 回测列表 -->
        <div class="backtest-list">
          <div class="section-header">
            <h4>回测列表</h4>
            <a-space>
              <a-select
                v-model:value="statusFilter"
                size="small"
                style="width: 120px"
                @change="filterBacktests"
              >
                <a-select-option value="all">全部</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
                <a-select-option value="running">运行中</a-select-option>
                <a-select-option value="failed">失败</a-select-option>
              </a-select>
              <a-input-search
                v-model:value="searchKeyword"
                placeholder="搜索回测"
                size="small"
                style="width: 200px"
                @search="searchBacktests"
              />
            </a-space>
          </div>
          
          <a-table
            :columns="backtestColumns"
            :data-source="filteredBacktests"
            size="small"
            :pagination="{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }"
            :scroll="{ x: 1200 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'totalReturn'">
                <span :class="record.totalReturn >= 0 ? 'positive' : 'negative'">
                  {{ formatPercentage(record.totalReturn) }}
                </span>
              </template>
              <template v-else-if="column.key === 'annualReturn'">
                <span :class="record.annualReturn >= 0 ? 'positive' : 'negative'">
                  {{ formatPercentage(record.annualReturn) }}
                </span>
              </template>
              <template v-else-if="column.key === 'sharpeRatio'">
                <span :class="record.sharpeRatio >= 1 ? 'positive' : 'negative'">
                  {{ record.sharpeRatio.toFixed(2) }}
                </span>
              </template>
              <template v-else-if="column.key === 'maxDrawdown'">
                <span class="negative">{{ formatPercentage(record.maxDrawdown) }}</span>
              </template>
              <template v-else-if="column.key === 'winRate'">
                <span :class="record.winRate >= 50 ? 'positive' : 'negative'">
                  {{ formatPercentage(record.winRate) }}
                </span>
              </template>
              <template v-else-if="column.key === 'progress'">
                <a-progress 
                  :percent="record.progress" 
                  size="small"
                  :status="record.status === 'failed' ? 'exception' : 'normal'"
                />
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button size="small" @click="viewBacktestDetail(record)">
                    详情
                  </a-button>
                  <a-button 
                    size="small" 
                    @click="compareBacktest(record)"
                    v-if="record.status === 'completed'"
                  >
                    对比
                  </a-button>
                  <a-button 
                    size="small" 
                    type="primary"
                    @click="deployStrategy(record)"
                    v-if="record.status === 'completed' && record.totalReturn > 0"
                  >
                    部署
                  </a-button>
                  <a-dropdown>
                    <a-button size="small" type="text">
                      <MoreOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu @click="handleBacktestAction($event, record)">
                        <a-menu-item key="clone">
                          <CopyOutlined /> 克隆
                        </a-menu-item>
                        <a-menu-item key="export">
                          <ExportOutlined /> 导出
                        </a-menu-item>
                        <a-menu-item key="share">
                          <ShareAltOutlined /> 分享
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item key="delete" danger>
                          <DeleteOutlined /> 删除
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 回测对比 -->
        <div class="backtest-comparison" v-if="comparisonBacktests.length > 0">
          <div class="section-header">
            <h4>回测对比</h4>
            <a-button size="small" @click="clearComparison">
              清空对比
            </a-button>
          </div>
          
          <div class="comparison-chart">
            <div ref="comparisonChart" class="chart-container"></div>
          </div>
          
          <div class="comparison-table">
            <a-table
              :columns="comparisonColumns"
              :data-source="comparisonBacktests"
              size="small"
              :pagination="false"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'totalReturn'">
                  <span :class="record.totalReturn >= 0 ? 'positive' : 'negative'">
                    {{ formatPercentage(record.totalReturn) }}
                  </span>
                </template>
                <template v-else-if="column.key === 'sharpeRatio'">
                  <span :class="record.sharpeRatio >= 1 ? 'positive' : 'negative'">
                    {{ record.sharpeRatio.toFixed(2) }}
                  </span>
                </template>
                <template v-else-if="column.key === 'maxDrawdown'">
                  <span class="negative">{{ formatPercentage(record.maxDrawdown) }}</span>
                </template>
                <template v-else-if="column.key === 'winRate'">
                  <span :class="record.winRate >= 50 ? 'positive' : 'negative'">
                    {{ formatPercentage(record.winRate) }}
                  </span>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 新建回测模态框 -->
    <a-modal
      v-model:open="showCreateBacktest"
      title="新建回测"
      @ok="createBacktest"
      @cancel="resetCreateForm"
      width="800px"
    >
      <a-form :model="createForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="回测名称" required>
              <a-input v-model:value="createForm.name" placeholder="请输入回测名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="策略类型" required>
              <a-select v-model:value="createForm.strategyType" placeholder="选择策略类型">
                <a-select-option value="grid">网格交易</a-select-option>
                <a-select-option value="dca">定投策略</a-select-option>
                <a-select-option value="momentum">动量策略</a-select-option>
                <a-select-option value="arbitrage">套利策略</a-select-option>
                <a-select-option value="custom">自定义策略</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="交易对" required>
              <a-select v-model:value="createForm.symbol" placeholder="选择交易对">
                <a-select-option value="BTC-USDT">BTC-USDT</a-select-option>
                <a-select-option value="ETH-USDT">ETH-USDT</a-select-option>
                <a-select-option value="BNB-USDT">BNB-USDT</a-select-option>
                <a-select-option value="ADA-USDT">ADA-USDT</a-select-option>
                <a-select-option value="SOL-USDT">SOL-USDT</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="时间周期">
              <a-select v-model:value="createForm.timeframe" placeholder="选择时间周期">
                <a-select-option value="1m">1分钟</a-select-option>
                <a-select-option value="5m">5分钟</a-select-option>
                <a-select-option value="15m">15分钟</a-select-option>
                <a-select-option value="1h">1小时</a-select-option>
                <a-select-option value="4h">4小时</a-select-option>
                <a-select-option value="1d">1天</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="开始时间" required>
              <a-date-picker
                v-model:value="createForm.startDate"
                style="width: 100%"
                placeholder="选择开始时间"
                :disabled-date="disabledStartDate"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="结束时间" required>
              <a-date-picker
                v-model:value="createForm.endDate"
                style="width: 100%"
                placeholder="选择结束时间"
                :disabled-date="disabledEndDate"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="初始资金">
              <a-input-number
                v-model:value="createForm.initialCapital"
                :min="1000"
                :max="1000000"
                style="width: 100%"
                placeholder="初始资金"
                :formatter="value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手续费率">
              <a-input-number
                v-model:value="createForm.feeRate"
                :min="0"
                :max="1"
                :step="0.0001"
                style="width: 100%"
                placeholder="手续费率"
                :formatter="value => `${(value * 100).toFixed(2)}%`"
                :parser="value => value.replace('%', '') / 100"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="策略参数">
          <div class="strategy-params">
            <div v-for="(param, index) in createForm.parameters" :key="index" class="param-row">
              <a-input
                v-model:value="param.key"
                placeholder="参数名"
                style="width: 25%"
              />
              <a-select
                v-model:value="param.type"
                placeholder="类型"
                style="width: 20%"
              >
                <a-select-option value="number">数字</a-select-option>
                <a-select-option value="string">字符串</a-select-option>
                <a-select-option value="boolean">布尔值</a-select-option>
              </a-select>
              <a-input
                v-model:value="param.value"
                placeholder="参数值"
                style="width: 25%"
              />
              <a-input
                v-model:value="param.description"
                placeholder="参数描述"
                style="width: 25%"
              />
              <a-button 
                type="text" 
                danger 
                @click="removeParameter(index)"
                style="width: 5%"
              >
                <DeleteOutlined />
              </a-button>
            </div>
            <a-button type="dashed" @click="addParameter" style="width: 100%">
              <PlusOutlined /> 添加参数
            </a-button>
          </div>
        </a-form-item>
        
        <a-form-item label="高级设置">
          <a-space direction="vertical" style="width: 100%">
            <a-checkbox v-model:checked="createForm.enableSlippage">
              启用滑点模拟
            </a-checkbox>
            <div v-if="createForm.enableSlippage" style="margin-left: 24px;">
              <a-form-item label="滑点率" style="margin-bottom: 8px">
                <a-slider
                  v-model:value="createForm.slippageRate"
                  :min="0"
                  :max="1"
                  :step="0.001"
                  :marks="{ 0.001: '0.1%', 0.005: '0.5%', 0.01: '1%' }"
                />
              </a-form-item>
            </div>
            
            <a-checkbox v-model:checked="createForm.enableBenchmark">
              启用基准对比
            </a-checkbox>
            <div v-if="createForm.enableBenchmark" style="margin-left: 24px;">
              <a-select v-model:value="createForm.benchmark" placeholder="选择基准" style="width: 200px">
                <a-select-option value="buy_hold">买入持有</a-select-option>
                <a-select-option value="btc">BTC</a-select-option>
                <a-select-option value="eth">ETH</a-select-option>
              </a-select>
            </div>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 回测详情模态框 -->
    <a-modal
      v-model:open="showBacktestDetail"
      :title="selectedBacktest?.name"
      :footer="null"
      width="1200px"
    >
      <div v-if="selectedBacktest" class="backtest-detail">
        <a-tabs>
          <a-tab-pane key="overview" tab="概览">
            <div class="detail-overview">
              <div class="overview-metrics">
                <div class="metric-card">
                  <div class="metric-title">总收益率</div>
                  <div class="metric-value" :class="selectedBacktest.totalReturn >= 0 ? 'positive' : 'negative'">
                    {{ formatPercentage(selectedBacktest.totalReturn) }}
                  </div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">年化收益率</div>
                  <div class="metric-value" :class="selectedBacktest.annualReturn >= 0 ? 'positive' : 'negative'">
                    {{ formatPercentage(selectedBacktest.annualReturn) }}
                  </div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">最大回撤</div>
                  <div class="metric-value negative">
                    {{ formatPercentage(selectedBacktest.maxDrawdown) }}
                  </div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">夏普比率</div>
                  <div class="metric-value" :class="selectedBacktest.sharpeRatio >= 1 ? 'positive' : 'negative'">
                    {{ selectedBacktest.sharpeRatio.toFixed(2) }}
                  </div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">胜率</div>
                  <div class="metric-value" :class="selectedBacktest.winRate >= 50 ? 'positive' : 'negative'">
                    {{ formatPercentage(selectedBacktest.winRate) }}
                  </div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">盈亏比</div>
                  <div class="metric-value" :class="selectedBacktest.profitLossRatio >= 1 ? 'positive' : 'negative'">
                    {{ selectedBacktest.profitLossRatio.toFixed(2) }}
                  </div>
                </div>
              </div>
              
              <div class="performance-chart">
                <div ref="performanceChart" class="chart-container"></div>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="trades" tab="交易记录">
            <a-table
              :columns="tradeColumns"
              :data-source="selectedBacktest.trades"
              size="small"
              :pagination="{ pageSize: 20, showSizeChanger: true }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'side'">
                  <a-tag :color="record.side === 'buy' ? 'green' : 'red'">
                    {{ record.side === 'buy' ? '买入' : '卖出' }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'pnl'">
                  <span :class="record.pnl >= 0 ? 'positive' : 'negative'">
                    {{ formatCurrency(record.pnl) }}
                  </span>
                </template>
                <template v-else-if="column.key === 'time'">
                  {{ formatFullTime(record.time) }}
                </template>
                <template v-else-if="column.key === 'price'">
                  ${{ record.price.toLocaleString() }}
                </template>
                <template v-else-if="column.key === 'amount'">
                  {{ record.amount.toFixed(6) }}
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="analysis" tab="分析报告">
            <div class="analysis-report">
              <div class="report-section">
                <h4>收益分析</h4>
                <div class="analysis-metrics">
                  <div class="analysis-item">
                    <span class="analysis-label">总交易次数:</span>
                    <span class="analysis-value">{{ selectedBacktest.totalTrades }}</span>
                  </div>
                  <div class="analysis-item">
                    <span class="analysis-label">盈利交易:</span>
                    <span class="analysis-value positive">{{ selectedBacktest.profitTrades }}</span>
                  </div>
                  <div class="analysis-item">
                    <span class="analysis-label">亏损交易:</span>
                    <span class="analysis-value negative">{{ selectedBacktest.lossTrades }}</span>
                  </div>
                  <div class="analysis-item">
                    <span class="analysis-label">平均盈利:</span>
                    <span class="analysis-value positive">{{ formatCurrency(selectedBacktest.avgProfit) }}</span>
                  </div>
                  <div class="analysis-item">
                    <span class="analysis-label">平均亏损:</span>
                    <span class="analysis-value negative">{{ formatCurrency(selectedBacktest.avgLoss) }}</span>
                  </div>
                  <div class="analysis-item">
                    <span class="analysis-label">最大单笔盈利:</span>
                    <span class="analysis-value positive">{{ formatCurrency(selectedBacktest.maxProfit) }}</span>
                  </div>
                  <div class="analysis-item">
                    <span class="analysis-label">最大单笔亏损:</span>
                    <span class="analysis-value negative">{{ formatCurrency(selectedBacktest.maxLoss) }}</span>
                  </div>
                </div>
              </div>
              
              <div class="report-section">
                <h4>风险分析</h4>
                <div class="analysis-metrics">
                  <div class="analysis-item">
                    <span class="analysis-label">波动率:</span>
                    <span class="analysis-value">{{ formatPercentage(selectedBacktest.volatility) }}</span>
                  </div>
                  <div class="analysis-item">
                    <span class="analysis-label">VaR (95%):</span>
                    <span class="analysis-value negative">{{ formatPercentage(selectedBacktest.var95) }}</span>
                  </div>
                  <div class="analysis-item">
                    <span class="analysis-label">最大连续亏损:</span>
                    <span class="analysis-value negative">{{ selectedBacktest.maxConsecutiveLoss }}</span>
                  </div>
                  <div class="analysis-item">
                    <span class="analysis-label">回撤持续时间:</span>
                    <span class="analysis-value">{{ selectedBacktest.drawdownDuration }}天</span>
                  </div>
                </div>
              </div>
              
              <div class="report-section">
                <h4>策略评价</h4>
                <div class="strategy-rating">
                  <div class="rating-item">
                    <span class="rating-label">收益性:</span>
                    <a-rate v-model:value="selectedBacktest.profitabilityRating" disabled />
                  </div>
                  <div class="rating-item">
                    <span class="rating-label">稳定性:</span>
                    <a-rate v-model:value="selectedBacktest.stabilityRating" disabled />
                  </div>
                  <div class="rating-item">
                    <span class="rating-label">风险控制:</span>
                    <a-rate v-model:value="selectedBacktest.riskControlRating" disabled />
                  </div>
                </div>
                
                <div class="strategy-recommendation">
                  <h5>策略建议</h5>
                  <ul>
                    <li v-for="suggestion in selectedBacktest.suggestions" :key="suggestion">
                      {{ suggestion }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="parameters" tab="参数配置">
            <div class="parameter-config">
              <a-descriptions :column="2" bordered>
                <a-descriptions-item 
                  v-for="param in selectedBacktest.parameters" 
                  :key="param.key"
                  :label="param.description || param.key"
                >
                  {{ param.value }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>

    <!-- 设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="回测设置"
      @ok="saveSettings"
      @cancel="resetSettings"
      width="600px"
    >
      <a-form :model="settings" layout="vertical">
        <a-form-item label="默认回测周期">
          <a-radio-group v-model:value="settings.defaultPeriod">
            <a-radio value="1m">1个月</a-radio>
            <a-radio value="3m">3个月</a-radio>
            <a-radio value="6m">6个月</a-radio>
            <a-radio value="1y">1年</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="默认初始资金">
          <a-input-number
            v-model:value="settings.defaultCapital"
            :min="1000"
            :max="1000000"
            style="width: 100%"
            :formatter="value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
          />
        </a-form-item>
        
        <a-form-item label="默认手续费率">
          <a-input-number
            v-model:value="settings.defaultFeeRate"
            :min="0"
            :max="1"
            :step="0.0001"
            style="width: 100%"
            :formatter="value => `${(value * 100).toFixed(2)}%`"
            :parser="value => value.replace('%', '') / 100"
          />
        </a-form-item>
        
        <a-form-item label="性能优化">
          <a-space direction="vertical" style="width: 100%">
            <div>
              <a-switch v-model:checked="settings.enableParallel" />
              <span class="setting-label">启用并行计算</span>
            </div>
            <div>
              <a-switch v-model:checked="settings.enableCache" />
              <span class="setting-label">启用结果缓存</span>
            </div>
            <div>
              <a-switch v-model:checked="settings.enableOptimization" />
              <span class="setting-label">启用参数优化</span>
            </div>
          </a-space>
        </a-form-item>
        
        <a-form-item label="通知设置">
          <a-checkbox-group v-model:value="settings.notifications">
            <a-checkbox value="complete">回测完成通知</a-checkbox>
            <a-checkbox value="error">错误通知</a-checkbox>
            <a-checkbox value="report">报告生成通知</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { useChartStore } from '@/stores/chartStore'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import {
  ReloadOutlined,
  SettingOutlined,
  PlusOutlined,
  MoreOutlined,
  CopyOutlined,
  ExportOutlined,
  ShareAltOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// 定义事件
const emit = defineEmits(['backtest-created', 'backtest-completed', 'strategy-deployed'])

// 使用状态管理
const chartStore = useChartStore()
const { isDarkTheme } = storeToRefs(chartStore)

// 本地状态
const showSettings = ref(false)
const showCreateBacktest = ref(false)
const showBacktestDetail = ref(false)
const selectedBacktest = ref(null)
const performanceChart = ref(null)
const comparisonChart = ref(null)
const updateInterval = ref(null)

// 过滤和搜索
const statusFilter = ref('all')
const searchKeyword = ref('')

// 对比回测
const comparisonBacktests = ref([])

// 回测数据
const backtestResults = ref([
  {
    id: 1,
    name: 'BTC网格策略回测_20240301',
    strategyType: '网格交易',
    symbol: 'BTC-USDT',
    timeframe: '1h',
    period: '2024-01-01 ~ 2024-03-01',
    status: 'completed',
    progress: 100,
    totalReturn: 25.8,
    annualReturn: 45.2,
    sharpeRatio: 1.85,
    maxDrawdown: 8.5,
    winRate: 68.5,
    profitLossRatio: 1.8,
    totalTrades: 156,
    profitTrades: 107,
    lossTrades: 49,
    avgProfit: 125.50,
    avgLoss: -68.20,
    maxProfit: 580.30,
    maxLoss: -245.80,
    volatility: 15.2,
    var95: 12.5,
    maxConsecutiveLoss: 5,
    drawdownDuration: 12,
    profitabilityRating: 4,
    stabilityRating: 4,
    riskControlRating: 3,
    suggestions: [
      '建议适当增加网格间距以减少交易频率',
      '可以考虑在趋势明显时暂停策略',
      '建议设置止损机制以控制最大回撤'
    ],
    parameters: [
      { key: 'gridSize', value: '0.5%', description: '网格间距' },
      { key: 'gridCount', value: '20', description: '网格数量' },
      { key: 'baseAmount', value: '100', description: '基础下单量' }
    ],
    trades: [
      { id: 1, time: Date.now() - 86400000, side: 'buy', price: 43250, amount: 0.1, pnl: 25.5 },
      { id: 2, time: Date.now() - 82800000, side: 'sell', price: 43500, amount: 0.1, pnl: 45.2 }
    ]
  },
  {
    id: 2,
    name: 'ETH动量策略回测_20240315',
    strategyType: '动量策略',
    symbol: 'ETH-USDT',
    timeframe: '4h',
    period: '2024-02-01 ~ 2024-03-15',
    status: 'running',
    progress: 75,
    totalReturn: 18.2,
    annualReturn: 32.1,
    sharpeRatio: 1.45,
    maxDrawdown: 12.3,
    winRate: 72.3,
    profitLossRatio: 2.1,
    totalTrades: 89,
    profitTrades: 64,
    lossTrades: 25,
    avgProfit: 180.20,
    avgLoss: -85.60,
    maxProfit: 720.50,
    maxLoss: -320.80,
    volatility: 18.5,
    var95: 15.2,
    maxConsecutiveLoss: 3,
    drawdownDuration: 8,
    profitabilityRating: 4,
    stabilityRating: 3,
    riskControlRating: 4,
    suggestions: [
      '策略表现良好，建议继续运行',
      '可以考虑增加仓位以提高收益',
      '建议关注市场波动率变化'
    ],
    parameters: [
      { key: 'period', value: '14', description: '动量周期' },
      { key: 'threshold', value: '0.02', description: '信号阈值' }
    ],
    trades: []
  },
  {
    id: 3,
    name: 'BNB定投策略回测_20240320',
    strategyType: '定投策略',
    symbol: 'BNB-USDT',
    timeframe: '1d',
    period: '2023-12-01 ~ 2024-03-20',
    status: 'completed',
    progress: 100,
    totalReturn: 12.5,
    annualReturn: 18.5,
    sharpeRatio: 2.15,
    maxDrawdown: 3.8,
    winRate: 85.2,
    profitLossRatio: 3.2,
    totalTrades: 45,
    profitTrades: 38,
    lossTrades: 7,
    avgProfit: 95.30,
    avgLoss: -29.80,
    maxProfit: 280.60,
    maxLoss: -85.20,
    volatility: 8.5,
    var95: 6.2,
    maxConsecutiveLoss: 2,
    drawdownDuration: 5,
    profitabilityRating: 3,
    stabilityRating: 5,
    riskControlRating: 5,
    suggestions: [
      '策略风险控制良好，适合长期投资',
      '建议保持当前参数设置',
      '可以考虑增加定投频率'
    ],
    parameters: [
      { key: 'interval', value: '24h', description: '定投间隔' },
      { key: 'amount', value: '50', description: '定投金额' }
    ],
    trades: []
  }
])

// 表单数据
const createForm = ref({
  name: '',
  strategyType: '',
  symbol: '',
  timeframe: '1h',
  startDate: null,
  endDate: null,
  initialCapital: 10000,
  feeRate: 0.001,
  parameters: [],
  enableSlippage: false,
  slippageRate: 0.001,
  enableBenchmark: false,
  benchmark: 'buy_hold'
})

// 设置
const settings = ref({
  defaultPeriod: '3m',
  defaultCapital: 10000,
  defaultFeeRate: 0.001,
  enableParallel: true,
  enableCache: true,
  enableOptimization: false,
  notifications: ['complete', 'error']
})

// 表格列定义
const backtestColumns = [
  { title: '回测名称', dataIndex: 'name', key: 'name', width: 200, fixed: 'left' },
  { title: '策略类型', dataIndex: 'strategyType', key: 'strategyType', width: 100 },
  { title: '交易对', dataIndex: 'symbol', key: 'symbol', width: 100 },
  { title: '周期', dataIndex: 'timeframe', key: 'timeframe', width: 80 },
  { title: '回测周期', dataIndex: 'period', key: 'period', width: 180 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '进度', dataIndex: 'progress', key: 'progress', width: 120 },
  { title: '总收益率', dataIndex: 'totalReturn', key: 'totalReturn', width: 100 },
  { title: '年化收益率', dataIndex: 'annualReturn', key: 'annualReturn', width: 100 },
  { title: '夏普比率', dataIndex: 'sharpeRatio', key: 'sharpeRatio', width: 100 },
  { title: '最大回撤', dataIndex: 'maxDrawdown', key: 'maxDrawdown', width: 100 },
  { title: '胜率', dataIndex: 'winRate', key: 'winRate', width: 80 },
  { title: '操作', key: 'actions', width: 200, fixed: 'right' }
]

const comparisonColumns = [
  { title: '回测名称', dataIndex: 'name', key: 'name' },
  { title: '总收益率', dataIndex: 'totalReturn', key: 'totalReturn' },
  { title: '夏普比率', dataIndex: 'sharpeRatio', key: 'sharpeRatio' },
  { title: '最大回撤', dataIndex: 'maxDrawdown', key: 'maxDrawdown' },
  { title: '胜率', dataIndex: 'winRate', key: 'winRate' }
]

const tradeColumns = [
  { title: '时间', dataIndex: 'time', key: 'time', width: 150 },
  { title: '方向', dataIndex: 'side', key: 'side', width: 80 },
  { title: '价格', dataIndex: 'price', key: 'price', width: 120 },
  { title: '数量', dataIndex: 'amount', key: 'amount', width: 120 },
  { title: '盈亏', dataIndex: 'pnl', key: 'pnl', width: 120 }
]

// 计算属性
const avgReturn = computed(() => {
  const completed = backtestResults.value.filter(b => b.status === 'completed')
  if (completed.length === 0) return 0
  return completed.reduce((sum, b) => sum + b.totalReturn, 0) / completed.length
})

const avgSharpeRatio = computed(() => {
  const completed = backtestResults.value.filter(b => b.status === 'completed')
  if (completed.length === 0) return 0
  return completed.reduce((sum, b) => sum + b.sharpeRatio, 0) / completed.length
})

const avgMaxDrawdown = computed(() => {
  const completed = backtestResults.value.filter(b => b.status === 'completed')
  if (completed.length === 0) return 0
  return completed.reduce((sum, b) => sum + b.maxDrawdown, 0) / completed.length
})

const filteredBacktests = computed(() => {
  let filtered = backtestResults.value
  
  // 状态过滤
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(b => b.status === statusFilter.value)
  }
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(b => 
      b.name.toLowerCase().includes(keyword) ||
      b.strategyType.toLowerCase().includes(keyword) ||
      b.symbol.toLowerCase().includes(keyword)
    )
  }
  
  return filtered
})

/**
 * 获取状态颜色
 */
const getStatusColor = (status) => {
  switch (status) {
    case 'completed': return 'green'
    case 'running': return 'blue'
    case 'failed': return 'red'
    default: return 'gray'
  }
}

/**
 * 获取状态文本
 */
const getStatusText = (status) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'running': return '运行中'
    case 'failed': return '失败'
    default: return '未知'
  }
}

/**
 * 格式化百分比
 */
const formatPercentage = (value) => {
  return `${value.toFixed(1)}%`
}

/**
 * 格式化货币
 */
const formatCurrency = (value) => {
  const sign = value >= 0 ? '+' : ''
  return `${sign}$${Math.abs(value).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`
}

/**
 * 格式化完整时间
 */
const formatFullTime = (timestamp) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 禁用开始日期
 */
const disabledStartDate = (current) => {
  return current && current > dayjs().endOf('day')
}

/**
 * 禁用结束日期
 */
const disabledEndDate = (current) => {
  return current && (current > dayjs().endOf('day') || current < createForm.value.startDate)
}

/**
 * 过滤回测
 */
const filterBacktests = () => {
  // 过滤逻辑在计算属性中处理
}

/**
 * 搜索回测
 */
const searchBacktests = () => {
  // 搜索逻辑在计算属性中处理
}

/**
 * 处理回测操作
 */
const handleBacktestAction = ({ key }, backtest) => {
  switch (key) {
    case 'clone':
      cloneBacktest(backtest)
      break
    case 'export':
      exportBacktest(backtest)
      break
    case 'share':
      shareBacktest(backtest)
      break
    case 'delete':
      deleteBacktest(backtest)
      break
  }
}

/**
 * 克隆回测
 */
const cloneBacktest = (backtest) => {
  const newBacktest = {
    ...backtest,
    id: Date.now(),
    name: `${backtest.name} (副本)`,
    status: 'completed',
    progress: 100
  }
  backtestResults.value.unshift(newBacktest)
  message.success('回测已克隆')
}

/**
 * 导出回测
 */
const exportBacktest = (backtest) => {
  message.info(`正在导出回测: ${backtest.name}`)
  // 这里可以实现导出逻辑
}

/**
 * 分享回测
 */
const shareBacktest = (backtest) => {
  message.info(`正在分享回测: ${backtest.name}`)
  // 这里可以实现分享逻辑
}

/**
 * 删除回测
 */
const deleteBacktest = (backtest) => {
  const index = backtestResults.value.findIndex(b => b.id === backtest.id)
  if (index > -1) {
    backtestResults.value.splice(index, 1)
    message.success('回测已删除')
  }
}

/**
 * 查看回测详情
 */
const viewBacktestDetail = (backtest) => {
  selectedBacktest.value = backtest
  showBacktestDetail.value = true
  
  nextTick(() => {
    initPerformanceChart()
  })
}

/**
 * 对比回测
 */
const compareBacktest = (backtest) => {
  if (comparisonBacktests.value.length >= 5) {
    message.warning('最多只能对比5个回测')
    return
  }
  
  if (comparisonBacktests.value.find(b => b.id === backtest.id)) {
    message.warning('该回测已在对比列表中')
    return
  }
  
  comparisonBacktests.value.push(backtest)
  message.success(`已添加 ${backtest.name} 到对比列表`)
  
  nextTick(() => {
    initComparisonChart()
  })
}

/**
 * 清空对比
 */
const clearComparison = () => {
  comparisonBacktests.value = []
  message.success('已清空对比列表')
}

/**
 * 部署策略
 */
const deployStrategy = (backtest) => {
  message.success(`策略 ${backtest.name} 已部署到实盘`)
  emit('strategy-deployed', backtest)
}

/**
 * 创建回测
 */
const createBacktest = () => {
  if (!createForm.value.name || !createForm.value.strategyType || !createForm.value.symbol || 
      !createForm.value.startDate || !createForm.value.endDate) {
    message.error('请填写必要信息')
    return
  }
  
  const newBacktest = {
    id: Date.now(),
    name: createForm.value.name,
    strategyType: createForm.value.strategyType,
    symbol: createForm.value.symbol,
    timeframe: createForm.value.timeframe,
    period: `${dayjs(createForm.value.startDate).format('YYYY-MM-DD')} ~ ${dayjs(createForm.value.endDate).format('YYYY-MM-DD')}`,
    status: 'running',
    progress: 0,
    totalReturn: 0,
    annualReturn: 0,
    sharpeRatio: 0,
    maxDrawdown: 0,
    winRate: 0,
    profitLossRatio: 0,
    totalTrades: 0,
    profitTrades: 0,
    lossTrades: 0,
    avgProfit: 0,
    avgLoss: 0,
    maxProfit: 0,
    maxLoss: 0,
    volatility: 0,
    var95: 0,
    maxConsecutiveLoss: 0,
    drawdownDuration: 0,
    profitabilityRating: 0,
    stabilityRating: 0,
    riskControlRating: 0,
    suggestions: [],
    parameters: [...createForm.value.parameters],
    trades: []
  }
  
  backtestResults.value.unshift(newBacktest)
  showCreateBacktest.value = false
  resetCreateForm()
  message.success('回测已创建，正在运行中...')
  emit('backtest-created', newBacktest)
  
  // 模拟回测进度
  simulateBacktestProgress(newBacktest)
}

/**
 * 模拟回测进度
 */
const simulateBacktestProgress = (backtest) => {
  const progressInterval = setInterval(() => {
    backtest.progress += Math.random() * 10
    
    if (backtest.progress >= 100) {
      backtest.progress = 100
      backtest.status = 'completed'
      
      // 生成模拟结果
      backtest.totalReturn = Math.random() * 40 - 10 // -10% to 30%
      backtest.annualReturn = backtest.totalReturn * 2
      backtest.sharpeRatio = Math.random() * 2 + 0.5
      backtest.maxDrawdown = Math.random() * 20
      backtest.winRate = Math.random() * 40 + 40 // 40% to 80%
      backtest.profitLossRatio = Math.random() * 2 + 1
      backtest.totalTrades = Math.floor(Math.random() * 200) + 50
      backtest.profitTrades = Math.floor(backtest.totalTrades * backtest.winRate / 100)
      backtest.lossTrades = backtest.totalTrades - backtest.profitTrades
      
      clearInterval(progressInterval)
      message.success(`回测 ${backtest.name} 已完成`)
      emit('backtest-completed', backtest)
    }
  }, 1000)
}

/**
 * 重置创建表单
 */
const resetCreateForm = () => {
  createForm.value = {
    name: '',
    strategyType: '',
    symbol: '',
    timeframe: '1h',
    startDate: null,
    endDate: null,
    initialCapital: 10000,
    feeRate: 0.001,
    parameters: [],
    enableSlippage: false,
    slippageRate: 0.001,
    enableBenchmark: false,
    benchmark: 'buy_hold'
  }
}

/**
 * 添加参数
 */
const addParameter = () => {
  createForm.value.parameters.push({
    key: '',
    type: 'number',
    value: '',
    description: ''
  })
}

/**
 * 移除参数
 */
const removeParameter = (index) => {
  createForm.value.parameters.splice(index, 1)
}

/**
 * 初始化性能图表
 */
const initPerformanceChart = () => {
  if (!performanceChart.value) return
  
  const chart = echarts.init(performanceChart.value)
  
  // 生成模拟数据
  const dates = []
  const values = []
  const baseValue = 10000
  let currentValue = baseValue
  
  for (let i = 0; i < 60; i++) {
    dates.push(dayjs().subtract(59 - i, 'day').format('MM-DD'))
    currentValue += (Math.random() - 0.45) * 200
    values.push(currentValue)
  }
  
  const option = {
    title: {
      text: '回测对比',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: isDarkTheme.value ? '#fff' : '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: comparisonBacktests.value.map(b => b.name),
      bottom: 0,
      textStyle: {
        color: isDarkTheme.value ? '#fff' : '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#434343' : '#e8e8e8'
        }
      },
      axisLabel: {
        color: isDarkTheme.value ? '#999' : '#666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#434343' : '#e8e8e8'
        }
      },
      axisLabel: {
        color: isDarkTheme.value ? '#999' : '#666',
        formatter: '${value}'
      },
      splitLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#434343' : '#f0f0f0'
        }
      }
    },
    series: series
  }
  
  chart.setOption(option)
  
  // 响应式调整
  const resizeChart = () => {
    chart.resize()
  }
  
  window.addEventListener('resize', resizeChart)
  
  return () => {
    window.removeEventListener('resize', resizeChart)
    chart.dispose()
  }
}

/**
 * 刷新数据
 */
const refreshData = () => {
  message.success('数据已刷新')
  // 这里可以实现实际的数据刷新逻辑
}

/**
 * 保存设置
 */
const saveSettings = () => {
  localStorage.setItem('backtest-settings', JSON.stringify(settings.value))
  showSettings.value = false
  message.success('设置已保存')
}

/**
 * 重置设置
 */
const resetSettings = () => {
  settings.value = {
    defaultPeriod: '3m',
    defaultCapital: 10000,
    defaultFeeRate: 0.001,
    enableParallel: true,
    enableCache: true,
    enableOptimization: false,
    notifications: ['complete', 'error']
  }
}

/**
 * 加载设置
 */
const loadSettings = () => {
  const saved = localStorage.getItem('backtest-settings')
  if (saved) {
    try {
      settings.value = { ...settings.value, ...JSON.parse(saved) }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }
}

/**
 * 启动自动刷新
 */
const startAutoRefresh = () => {
  if (updateInterval.value) return
  
  updateInterval.value = setInterval(() => {
    // 更新运行中的回测进度
    backtestResults.value.forEach(backtest => {
      if (backtest.status === 'running' && backtest.progress < 100) {
        backtest.progress = Math.min(100, backtest.progress + Math.random() * 5)
        
        if (backtest.progress >= 100) {
          backtest.status = 'completed'
          // 生成模拟结果
          backtest.totalReturn = Math.random() * 40 - 10
          backtest.annualReturn = backtest.totalReturn * 2
          backtest.sharpeRatio = Math.random() * 2 + 0.5
          backtest.maxDrawdown = Math.random() * 20
          backtest.winRate = Math.random() * 40 + 40
          backtest.profitLossRatio = Math.random() * 2 + 1
          
          message.success(`回测 ${backtest.name} 已完成`)
          emit('backtest-completed', backtest)
        }
      }
    })
  }, 3000)
}

/**
 * 停止自动刷新
 */
const stopAutoRefresh = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
    updateInterval.value = null
  }
}

// 生命周期
onMounted(() => {
  loadSettings()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.strategy-backtest-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.backtest-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.backtest-overview {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.backtest-list {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.backtest-comparison {
  margin-top: 16px;
}

.comparison-chart {
  margin-bottom: 16px;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.comparison-table {
  margin-top: 16px;
}

.strategy-params {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-row {
  display: flex;
  gap: 8px;
  align-items: center;
}

.backtest-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-overview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.overview-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.metric-card {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.metric-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
}

.performance-chart {
  margin-top: 16px;
}

.analysis-report {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.report-section {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.report-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
}

.analysis-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.analysis-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.analysis-item:last-child {
  border-bottom: none;
}

.analysis-label {
  font-size: 12px;
  color: #666;
}

.analysis-value {
  font-size: 12px;
  font-weight: 500;
}

.strategy-rating {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rating-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rating-label {
  font-size: 12px;
  color: #666;
  min-width: 80px;
}

.strategy-recommendation {
  margin-top: 16px;
}

.strategy-recommendation h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 600;
}

.strategy-recommendation ul {
  margin: 0;
  padding-left: 16px;
}

.strategy-recommendation li {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.parameter-config {
  padding: 16px;
}

.setting-label {
  margin-left: 8px;
  font-size: 12px;
}

.positive {
  color: #52c41a;
}

.negative {
  color: #f5222d;
}

/* 深色主题 */
.dark-theme .backtest-overview {
  background: #1f1f1f;
}

.dark-theme .stat-item {
  background: #262626;
  color: #fff;
}

.dark-theme .stat-label {
  color: #999;
}

.dark-theme .metric-card {
  background: #1f1f1f;
  border-color: #434343;
  color: #fff;
}

.dark-theme .metric-title {
  color: #999;
}

.dark-theme .report-section {
  background: #1f1f1f;
  border-color: #434343;
  color: #fff;
}

.dark-theme .analysis-item {
  border-color: #434343;
}

.dark-theme .analysis-label {
  color: #999;
}

.dark-theme .strategy-recommendation li {
  color: #999;
}

.dark-theme .setting-label {
  color: #fff;
}
</style>
    title: {
      text: '回测收益曲线',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: isDarkTheme.value ? '#fff' : '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const value = params[0].value
        const profit = value - baseValue
        const profitRate = ((profit / baseValue) * 100).toFixed(2)
        return `${params[0].name}<br/>净值: $${value.toFixed(2)}<br/>收益: $${profit.toFixed(2)} (${profitRate}%)`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#434343' : '#e8e8e8'
        }
      },
      axisLabel: {
        color: isDarkTheme.value ? '#999' : '#666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#434343' : '#e8e8e8'
        }
      },
      axisLabel: {
        color: isDarkTheme.value ? '#999' : '#666',
        formatter: '${value}'
      },
      splitLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#434343' : '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: '净值',
        type: 'line',
        data: values,
        smooth: true,
        lineStyle: {
          color: '#1890ff',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.05)' }
            ]
          }
        },
        symbol: 'none'
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式调整
  const resizeChart = () => {
    chart.resize()
  }
  
  window.addEventListener('resize', resizeChart)
  
  return () => {
    window.removeEventListener('resize', resizeChart)
    chart.dispose()
  }
}

/**
 * 初始化对比图表
 */
const initComparisonChart = () => {
  if (!comparisonChart.value || comparisonBacktests.value.length === 0) return
  
  const chart = echarts.init(comparisonChart.value)
  
  const dates = []
  for (let i = 0; i < 30; i++) {
    dates.push(dayjs().subtract(29 - i, 'day').format('MM-DD'))
  }
  
  const series = comparisonBacktests.value.map((backtest, index) => {
    const values = []
    let currentValue = 10000
    
    for (let i = 0; i < 30; i++) {
      currentValue += (Math.random() - 0.45) * 100
      values.push(currentValue)
    }
    
    const colors = ['#1890ff', '#52c41a', '#fa8c16', '#f5222d', '#722ed1']
    
    return {
      name: backtest.name,
      type: 'line',
      data: values,
      smooth: true,
      lineStyle: {
        color: colors[index % colors.length],
        width: 2
      },
      symbol: 'none'
    }
  })
  
  const option =