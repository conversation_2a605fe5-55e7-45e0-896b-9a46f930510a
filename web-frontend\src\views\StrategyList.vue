<template>
  <div>
    <a-table :dataSource="strategies" :columns="columns" rowKey="id">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-button type="link" @click="onEdit(record)">编辑</a-button>
          <a-popconfirm title="确定删除该策略吗？" @confirm="onDelete(record.id)">
            <a-button type="link" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <a-modal :open="editVisible" @update:open="editVisible = $event" title="编辑策略" @ok="submitEdit">
      <a-form :model="editForm">
        <a-form-item label="名称">
          <a-input v-model="editForm.name" />
        </a-form-item>
        <a-form-item label="参数(JSON)">
          <a-input v-model="editForm.params" />
        </a-form-item>
        <a-form-item label="状态">
          <a-select v-model="editForm.status">
            <a-select-option value="回测">回测</a-select-option>
            <a-select-option value="实盘">实盘</a-select-option>
            <a-select-option value="停止">停止</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
const strategies = ref([])
const columns = [
  { title: 'ID', dataIndex: 'id' },
  { title: '名称', dataIndex: 'name' },
  { title: '参数', dataIndex: 'params' },
  { title: '状态', dataIndex: 'status' },
  { title: '创建时间', dataIndex: 'created_at' },
  { title: '操作', key: 'action' }
]
const editVisible = ref(false)
const editForm = ref({ id: null, name: '', params: '', status: '' })
async function fetchStrategies() {
  const res = await axios.get('/api/strategy/list')
  if (res.data.code === 0) {
    strategies.value = res.data.data
  }
}
function onEdit(record) {
  editForm.value = {
    id: record.id,
    name: record.name,
    params: JSON.stringify(record.params),
    status: record.status
  }
  editVisible.value = true
}
async function submitEdit() {
  try {
    await axios.put(`/api/strategy/edit/${editForm.value.id}`, {
      name: editForm.value.name,
      params: JSON.parse(editForm.value.params),
      status: editForm.value.status
    })
    editVisible.value = false
    fetchStrategies()
  } catch (e) {
    alert('编辑失败')
  }
}
async function onDelete(id) {
  try {
    await axios.delete(`/api/strategy/delete/${id}`)
    fetchStrategies()
  } catch (e) {
    alert('删除失败')
  }
}
onMounted(fetchStrategies)
</script> 