# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
PIPFILE.lock
python_okx.egg-info/

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.iws
*.iml
*.ipr
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3
db.sqlite3
okx_trading.db

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config.env

# API Keys and Secrets
*api_key*
*secret*
*passphrase*
api_config.py
id_rsa*

# Node.js (Frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Frontend build
web-frontend/dist/
web-frontend/build/

# Frontend cache
web-frontend/.cache/
web-frontend/.parcel-cache/
web-frontend/.vite/

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Temporary files
*.tmp
*.temp
*.bak
*.backup
test_*.py
test_*.js
debug_*.py
debug_*.js
temp_*
*.old

# Java/Spring (legacy)
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
/nbproject/private/
/nbbuild/
/nbdist/
/.nb-gradle/
!**/src/main/**/build/
!**/src/test/**/build/