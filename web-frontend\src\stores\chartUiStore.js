import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useChartUiStore = defineStore('chartUi', () => {
  // State
  const chartInstances = ref({
    candlestick: null,
    volume: null,
    rsi: null,
    macd: null,
    kdj: null,
    bollinger: null,
    supertrend: null
  });

  // Actions
  const registerChartInstance = (type, instance) => {
    if (Object.prototype.hasOwnProperty.call(chartInstances.value, type)) {
      chartInstances.value[type] = instance;
    }
  };

  const getChartInstance = (type) => {
    return chartInstances.value[type];
  };

  const disposeAllCharts = () => {
    Object.keys(chartInstances.value).forEach(type => {
      const instance = chartInstances.value[type];
      if (instance && typeof instance.dispose === 'function') {
        instance.dispose();
        chartInstances.value[type] = null;
      }
    });
  };

  const resizeAllCharts = () => {
    Object.values(chartInstances.value).forEach(instance => {
      if (instance && typeof instance.resize === 'function') {
        instance.resize();
      }
    });
  };

  return {
    chartInstances,
    registerChartInstance,
    getChartInstance,
    disposeAllCharts,
    resizeAllCharts
  };
});