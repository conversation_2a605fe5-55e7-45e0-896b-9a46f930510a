<template>
  <div class="algorithm-trading-panel" :class="{ 'dark-theme': isDarkTheme }">
    <a-card title="算法交易" size="small" :bordered="false">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新数据">
            <a-button size="small" type="text" @click="refreshData">
              <ReloadOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="策略设置">
            <a-button size="small" type="text" @click="showSettings = true">
              <SettingOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="新建策略">
            <a-button size="small" type="primary" @click="showCreateStrategy = true">
              <PlusOutlined /> 新建
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <div class="algorithm-container">
        <!-- 策略概览 -->
        <div class="strategy-overview">
          <div class="overview-stats">
            <div class="stat-item">
              <div class="stat-value">{{ activeStrategies.length }}</div>
              <div class="stat-label">运行中策略</div>
            </div>
            <div class="stat-item">
              <div class="stat-value positive">{{ formatCurrency(totalProfit) }}</div>
              <div class="stat-label">总盈亏</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ formatPercentage(winRate) }}</div>
              <div class="stat-label">胜率</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ totalTrades }}</div>
              <div class="stat-label">总交易次数</div>
            </div>
          </div>
        </div>

        <!-- 策略列表 -->
        <div class="strategy-list">
          <div class="section-header">
            <h4>策略列表</h4>
            <a-space>
              <a-select
                v-model:value="strategyFilter"
                size="small"
                style="width: 120px"
                @change="filterStrategies"
              >
                <a-select-option value="all">全部</a-select-option>
                <a-select-option value="running">运行中</a-select-option>
                <a-select-option value="stopped">已停止</a-select-option>
                <a-select-option value="paused">已暂停</a-select-option>
              </a-select>
              <a-input-search
                v-model:value="searchKeyword"
                placeholder="搜索策略"
                size="small"
                style="width: 200px"
                @search="searchStrategies"
              />
            </a-space>
          </div>
          
          <div class="strategies-grid">
            <div 
              v-for="strategy in filteredStrategies"
              :key="strategy.id"
              class="strategy-card"
              :class="`status-${strategy.status}`"
            >
              <div class="strategy-header">
                <div class="strategy-info">
                  <div class="strategy-name">{{ strategy.name }}</div>
                  <div class="strategy-type">{{ strategy.type }}</div>
                </div>
                <div class="strategy-status">
                  <a-tag 
                    :color="getStatusColor(strategy.status)"
                    size="small"
                  >
                    {{ getStatusText(strategy.status) }}
                  </a-tag>
                  <a-dropdown>
                    <a-button size="small" type="text">
                      <MoreOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu @click="handleStrategyAction($event, strategy)">
                        <a-menu-item key="start" v-if="strategy.status !== 'running'">
                          <PlayCircleOutlined /> 启动
                        </a-menu-item>
                        <a-menu-item key="pause" v-if="strategy.status === 'running'">
                          <PauseCircleOutlined /> 暂停
                        </a-menu-item>
                        <a-menu-item key="stop" v-if="strategy.status !== 'stopped'">
                          <StopOutlined /> 停止
                        </a-menu-item>
                        <a-menu-item key="edit">
                          <EditOutlined /> 编辑
                        </a-menu-item>
                        <a-menu-item key="backtest">
                          <BarChartOutlined /> 回测
                        </a-menu-item>
                        <a-menu-item key="clone">
                          <CopyOutlined /> 克隆
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item key="delete" danger>
                          <DeleteOutlined /> 删除
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
              </div>
              
              <div class="strategy-metrics">
                <div class="metric-row">
                  <span class="metric-label">交易对:</span>
                  <span class="metric-value">{{ strategy.symbol }}</span>
                </div>
                <div class="metric-row">
                  <span class="metric-label">今日盈亏:</span>
                  <span 
                    class="metric-value" 
                    :class="strategy.todayPnl >= 0 ? 'positive' : 'negative'"
                  >
                    {{ formatCurrency(strategy.todayPnl) }}
                  </span>
                </div>
                <div class="metric-row">
                  <span class="metric-label">总盈亏:</span>
                  <span 
                    class="metric-value" 
                    :class="strategy.totalPnl >= 0 ? 'positive' : 'negative'"
                  >
                    {{ formatCurrency(strategy.totalPnl) }}
                  </span>
                </div>
                <div class="metric-row">
                  <span class="metric-label">胜率:</span>
                  <span class="metric-value">{{ formatPercentage(strategy.winRate) }}</span>
                </div>
                <div class="metric-row">
                  <span class="metric-label">运行时间:</span>
                  <span class="metric-value">{{ formatDuration(strategy.runTime) }}</span>
                </div>
              </div>
              
              <div class="strategy-progress">
                <div class="progress-header">
                  <span class="progress-label">今日表现</span>
                  <span class="progress-value">{{ formatPercentage(strategy.todayReturn) }}</span>
                </div>
                <a-progress 
                  :percent="Math.abs(strategy.todayReturn) * 10" 
                  :status="strategy.todayReturn >= 0 ? 'success' : 'exception'"
                  :show-info="false"
                  size="small"
                />
              </div>
              
              <div class="strategy-actions">
                <a-button 
                  size="small" 
                  @click="viewStrategyDetail(strategy)"
                >
                  查看详情
                </a-button>
                <a-button 
                  size="small" 
                  type="primary"
                  @click="optimizeStrategy(strategy)"
                >
                  优化参数
                </a-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 回测结果 -->
        <div class="backtest-results">
          <div class="section-header">
            <h4>回测结果</h4>
            <a-button size="small" @click="showBacktestModal = true">
              新建回测
            </a-button>
          </div>
          
          <div class="backtest-list">
            <a-table
              :columns="backtestColumns"
              :data-source="backtestResults"
              size="small"
              :pagination="{ pageSize: 5, size: 'small' }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="getBacktestStatusColor(record.status)">
                    {{ getBacktestStatusText(record.status) }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'totalReturn'">
                  <span :class="record.totalReturn >= 0 ? 'positive' : 'negative'">
                    {{ formatPercentage(record.totalReturn) }}
                  </span>
                </template>
                <template v-else-if="column.key === 'sharpeRatio'">
                  <span :class="record.sharpeRatio >= 1 ? 'positive' : 'negative'">
                    {{ record.sharpeRatio.toFixed(2) }}
                  </span>
                </template>
                <template v-else-if="column.key === 'maxDrawdown'">
                  <span class="negative">{{ formatPercentage(record.maxDrawdown) }}</span>
                </template>
                <template v-else-if="column.key === 'actions'">
                  <a-space>
                    <a-button size="small" @click="viewBacktestDetail(record)">详情</a-button>
                    <a-button size="small" @click="deployStrategy(record)">部署</a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </div>

        <!-- 策略模板 -->
        <div class="strategy-templates">
          <div class="section-header">
            <h4>策略模板</h4>
            <a-button size="small" @click="showTemplateModal = true">
              管理模板
            </a-button>
          </div>
          
          <div class="templates-grid">
            <div 
              v-for="template in strategyTemplates"
              :key="template.id"
              class="template-card"
              @click="useTemplate(template)"
            >
              <div class="template-icon">
                <component :is="template.icon" />
              </div>
              <div class="template-info">
                <div class="template-name">{{ template.name }}</div>
                <div class="template-desc">{{ template.description }}</div>
                <div class="template-stats">
                  <span class="template-stat">
                    <TrophyOutlined /> {{ formatPercentage(template.avgReturn) }}
                  </span>
                  <span class="template-stat">
                    <UserOutlined /> {{ template.users }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 新建策略模态框 -->
    <a-modal
      v-model:open="showCreateStrategy"
      title="新建策略"
      @ok="createStrategy"
      @cancel="resetCreateForm"
      width="800px"
    >
      <a-form :model="createForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="策略名称" required>
              <a-input v-model:value="createForm.name" placeholder="请输入策略名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="策略类型" required>
              <a-select v-model:value="createForm.type" placeholder="选择策略类型">
                <a-select-option value="grid">网格交易</a-select-option>
                <a-select-option value="dca">定投策略</a-select-option>
                <a-select-option value="momentum">动量策略</a-select-option>
                <a-select-option value="arbitrage">套利策略</a-select-option>
                <a-select-option value="custom">自定义策略</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="交易对" required>
              <a-select v-model:value="createForm.symbol" placeholder="选择交易对">
                <a-select-option value="BTC-USDT">BTC-USDT</a-select-option>
                <a-select-option value="ETH-USDT">ETH-USDT</a-select-option>
                <a-select-option value="BNB-USDT">BNB-USDT</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="投入资金">
              <a-input-number
                v-model:value="createForm.capital"
                :min="100"
                :max="100000"
                style="width: 100%"
                placeholder="投入资金"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="策略描述">
          <a-textarea
            v-model:value="createForm.description"
            :rows="3"
            placeholder="请输入策略描述"
          />
        </a-form-item>
        
        <a-form-item label="策略参数">
          <div class="strategy-params">
            <div v-for="(param, index) in createForm.parameters" :key="index" class="param-row">
              <a-input
                v-model:value="param.key"
                placeholder="参数名"
                style="width: 30%"
              />
              <a-input
                v-model:value="param.value"
                placeholder="参数值"
                style="width: 30%"
              />
              <a-input
                v-model:value="param.description"
                placeholder="参数描述"
                style="width: 30%"
              />
              <a-button 
                type="text" 
                danger 
                @click="removeParameter(index)"
                style="width: 10%"
              >
                <DeleteOutlined />
              </a-button>
            </div>
            <a-button type="dashed" @click="addParameter" style="width: 100%">
              <PlusOutlined /> 添加参数
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 回测模态框 -->
    <a-modal
      v-model:open="showBacktestModal"
      title="新建回测"
      @ok="runBacktest"
      @cancel="resetBacktestForm"
      width="600px"
    >
      <a-form :model="backtestForm" layout="vertical">
        <a-form-item label="选择策略" required>
          <a-select v-model:value="backtestForm.strategyId" placeholder="选择要回测的策略">
            <a-select-option 
              v-for="strategy in strategies" 
              :key="strategy.id" 
              :value="strategy.id"
            >
              {{ strategy.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="开始时间" required>
              <a-date-picker
                v-model:value="backtestForm.startDate"
                style="width: 100%"
                placeholder="选择开始时间"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="结束时间" required>
              <a-date-picker
                v-model:value="backtestForm.endDate"
                style="width: 100%"
                placeholder="选择结束时间"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="初始资金">
          <a-input-number
            v-model:value="backtestForm.initialCapital"
            :min="1000"
            :max="1000000"
            style="width: 100%"
            placeholder="初始资金"
          />
        </a-form-item>
        
        <a-form-item label="手续费率">
          <a-input-number
            v-model:value="backtestForm.feeRate"
            :min="0"
            :max="1"
            :step="0.001"
            style="width: 100%"
            placeholder="手续费率"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 策略详情模态框 -->
    <a-modal
      v-model:open="showStrategyDetail"
      :title="selectedStrategy?.name"
      :footer="null"
      width="1000px"
    >
      <div v-if="selectedStrategy" class="strategy-detail">
        <a-tabs>
          <a-tab-pane key="overview" tab="概览">
            <div class="detail-overview">
              <div class="overview-metrics">
                <div class="metric-card">
                  <div class="metric-title">总收益率</div>
                  <div class="metric-value" :class="selectedStrategy.totalReturn >= 0 ? 'positive' : 'negative'">
                    {{ formatPercentage(selectedStrategy.totalReturn) }}
                  </div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">年化收益率</div>
                  <div class="metric-value" :class="selectedStrategy.annualReturn >= 0 ? 'positive' : 'negative'">
                    {{ formatPercentage(selectedStrategy.annualReturn) }}
                  </div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">最大回撤</div>
                  <div class="metric-value negative">
                    {{ formatPercentage(selectedStrategy.maxDrawdown) }}
                  </div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">夏普比率</div>
                  <div class="metric-value" :class="selectedStrategy.sharpeRatio >= 1 ? 'positive' : 'negative'">
                    {{ selectedStrategy.sharpeRatio.toFixed(2) }}
                  </div>
                </div>
              </div>
              
              <div class="performance-chart">
                <div ref="performanceChart" class="chart-container"></div>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="trades" tab="交易记录">
            <a-table
              :columns="tradeColumns"
              :data-source="selectedStrategy.trades"
              size="small"
              :pagination="{ pageSize: 10 }"
            />
          </a-tab-pane>
          
          <a-tab-pane key="parameters" tab="参数配置">
            <div class="parameter-config">
              <a-form layout="vertical">
                <div v-for="param in selectedStrategy.parameters" :key="param.key" class="param-item">
                  <a-form-item :label="param.description || param.key">
                    <a-input v-model:value="param.value" />
                  </a-form-item>
                </div>
              </a-form>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="logs" tab="运行日志">
            <div class="strategy-logs">
              <div v-for="log in selectedStrategy.logs" :key="log.id" class="log-item">
                <div class="log-time">{{ formatFullTime(log.timestamp) }}</div>
                <div class="log-level" :class="`level-${log.level}`">{{ log.level.toUpperCase() }}</div>
                <div class="log-message">{{ log.message }}</div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>

    <!-- 设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="算法交易设置"
      @ok="saveSettings"
      @cancel="resetSettings"
      width="600px"
    >
      <a-form :model="settings" layout="vertical">
        <a-form-item label="默认交易模式">
          <a-radio-group v-model:value="settings.defaultMode">
            <a-radio value="paper">模拟交易</a-radio>
            <a-radio value="live">实盘交易</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="风险控制">
          <a-space direction="vertical" style="width: 100%">
            <div>
              <a-switch v-model:checked="settings.riskControl.enabled" />
              <span class="setting-label">启用风险控制</span>
            </div>
            <div v-if="settings.riskControl.enabled">
              <a-form-item label="最大单日损失" style="margin-bottom: 8px">
                <a-slider
                  v-model:value="settings.riskControl.maxDailyLoss"
                  :min="1"
                  :max="20"
                  :marks="{ 5: '5%', 10: '10%', 15: '15%' }"
                />
              </a-form-item>
              <a-form-item label="最大持仓比例" style="margin-bottom: 8px">
                <a-slider
                  v-model:value="settings.riskControl.maxPositionRatio"
                  :min="10"
                  :max="100"
                  :marks="{ 25: '25%', 50: '50%', 75: '75%' }"
                />
              </a-form-item>
            </div>
          </a-space>
        </a-form-item>
        
        <a-form-item label="通知设置">
          <a-checkbox-group v-model:value="settings.notifications">
            <a-checkbox value="trade">交易通知</a-checkbox>
            <a-checkbox value="profit">盈利通知</a-checkbox>
            <a-checkbox value="loss">亏损通知</a-checkbox>
            <a-checkbox value="error">错误通知</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="自动优化">
          <a-switch v-model:checked="settings.autoOptimization" />
          <span class="setting-desc">定期自动优化策略参数</span>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { useChartStore } from '@/stores/chartStore'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import {
  ReloadOutlined,
  SettingOutlined,
  PlusOutlined,
  MoreOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  EditOutlined,
  BarChartOutlined,
  CopyOutlined,
  DeleteOutlined,
  TrophyOutlined,
  UserOutlined,
  RocketOutlined,
  ThunderboltOutlined,
  DashboardOutlined,
  ApiOutlined
} from '@ant-design/icons-vue'

// 定义事件
const emit = defineEmits(['strategy-created', 'strategy-updated', 'strategy-deleted'])

// 使用状态管理
const chartStore = useChartStore()
const { isDarkTheme } = storeToRefs(chartStore)

// 本地状态
const showSettings = ref(false)
const showCreateStrategy = ref(false)
const showBacktestModal = ref(false)
const showTemplateModal = ref(false)
const showStrategyDetail = ref(false)
const selectedStrategy = ref(null)
const performanceChart = ref(null)
const updateInterval = ref(null)

// 过滤和搜索
const strategyFilter = ref('all')
const searchKeyword = ref('')

// 策略数据
const strategies = ref([
  {
    id: 1,
    name: 'BTC网格策略',
    type: '网格交易',
    symbol: 'BTC-USDT',
    status: 'running',
    todayPnl: 125.80,
    totalPnl: 2580.45,
    winRate: 68.5,
    runTime: Date.now() - 86400000 * 7,
    todayReturn: 2.5,
    totalReturn: 15.8,
    annualReturn: 45.2,
    maxDrawdown: 8.5,
    sharpeRatio: 1.85,
    parameters: [
      { key: 'gridSize', value: '0.5', description: '网格间距(%)' },
      { key: 'gridCount', value: '20', description: '网格数量' },
      { key: 'baseAmount', value: '100', description: '基础下单量' }
    ],
    trades: [
      { id: 1, time: Date.now() - 3600000, side: 'buy', price: 43250, amount: 0.1, pnl: 25.5 },
      { id: 2, time: Date.now() - 7200000, side: 'sell', price: 43500, amount: 0.1, pnl: 45.2 }
    ],
    logs: [
      { id: 1, timestamp: Date.now() - 1800000, level: 'info', message: '网格订单已成交，价格: 43250' },
      { id: 2, timestamp: Date.now() - 3600000, level: 'warning', message: '价格波动较大，建议调整网格间距' }
    ]
  },
  {
    id: 2,
    name: 'ETH动量策略',
    type: '动量策略',
    symbol: 'ETH-USDT',
    status: 'paused',
    todayPnl: -45.20,
    totalPnl: 890.15,
    winRate: 72.3,
    runTime: Date.now() - 86400000 * 3,
    todayReturn: -1.2,
    totalReturn: 8.9,
    annualReturn: 32.1,
    maxDrawdown: 12.3,
    sharpeRatio: 1.45,
    parameters: [
      { key: 'period', value: '14', description: '动量周期' },
      { key: 'threshold', value: '0.02', description: '信号阈值' }
    ],
    trades: [],
    logs: []
  },
  {
    id: 3,
    name: 'BNB定投策略',
    type: '定投策略',
    symbol: 'BNB-USDT',
    status: 'stopped',
    todayPnl: 0,
    totalPnl: 156.78,
    winRate: 85.2,
    runTime: Date.now() - 86400000 * 30,
    todayReturn: 0,
    totalReturn: 5.2,
    annualReturn: 18.5,
    maxDrawdown: 3.8,
    sharpeRatio: 2.15,
    parameters: [
      { key: 'interval', value: '24', description: '定投间隔(小时)' },
      { key: 'amount', value: '50', description: '定投金额' }
    ],
    trades: [],
    logs: []
  }
])

// 回测结果
const backtestResults = ref([
  {
    id: 1,
    strategyName: 'BTC网格策略',
    period: '2024-01-01 ~ 2024-03-01',
    totalReturn: 25.8,
    sharpeRatio: 1.85,
    maxDrawdown: 8.5,
    trades: 156,
    status: 'completed'
  },
  {
    id: 2,
    strategyName: 'ETH动量策略',
    period: '2024-02-01 ~ 2024-03-01',
    totalReturn: 18.2,
    sharpeRatio: 1.45,
    maxDrawdown: 12.3,
    trades: 89,
    status: 'running'
  }
])

// 策略模板
const strategyTemplates = ref([
  {
    id: 1,
    name: '经典网格',
    description: '适合震荡行情的网格交易策略',
    icon: 'DashboardOutlined',
    avgReturn: 15.5,
    users: 1250
  },
  {
    id: 2,
    name: '趋势跟踪',
    description: '基于技术指标的趋势跟踪策略',
    icon: 'RocketOutlined',
    avgReturn: 22.8,
    users: 890
  },
  {
    id: 3,
    name: '套利策略',
    description: '跨交易所价差套利策略',
    icon: 'ThunderboltOutlined',
    avgReturn: 8.5,
    users: 456
  },
  {
    id: 4,
    name: '智能定投',
    description: '基于市场情绪的智能定投策略',
    icon: 'ApiOutlined',
    avgReturn: 18.2,
    users: 2100
  }
])

// 表单数据
const createForm = ref({
  name: '',
  type: '',
  symbol: '',
  capital: 1000,
  description: '',
  parameters: []
})

const backtestForm = ref({
  strategyId: null,
  startDate: null,
  endDate: null,
  initialCapital: 10000,
  feeRate: 0.001
})

// 设置
const settings = ref({
  defaultMode: 'paper',
  riskControl: {
    enabled: true,
    maxDailyLoss: 10,
    maxPositionRatio: 50
  },
  notifications: ['trade', 'error'],
  autoOptimization: false
})

// 表格列定义
const backtestColumns = [
  { title: '策略名称', dataIndex: 'strategyName', key: 'strategyName' },
  { title: '回测周期', dataIndex: 'period', key: 'period' },
  { title: '总收益率', dataIndex: 'totalReturn', key: 'totalReturn' },
  { title: '夏普比率', dataIndex: 'sharpeRatio', key: 'sharpeRatio' },
  { title: '最大回撤', dataIndex: 'maxDrawdown', key: 'maxDrawdown' },
  { title: '交易次数', dataIndex: 'trades', key: 'trades' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '操作', key: 'actions' }
]

const tradeColumns = [
  { title: '时间', dataIndex: 'time', key: 'time' },
  { title: '方向', dataIndex: 'side', key: 'side' },
  { title: '价格', dataIndex: 'price', key: 'price' },
  { title: '数量', dataIndex: 'amount', key: 'amount' },
  { title: '盈亏', dataIndex: 'pnl', key: 'pnl' }
]

// 计算属性
const activeStrategies = computed(() => {
  return strategies.value.filter(s => s.status === 'running')
})

const totalProfit = computed(() => {
  return strategies.value.reduce((sum, s) => sum + s.totalPnl, 0)
})

const winRate = computed(() => {
  const totalWinRate = strategies.value.reduce((sum, s) => sum + s.winRate, 0)
  return strategies.value.length > 0 ? totalWinRate / strategies.value.length : 0
})

const totalTrades = computed(() => {
  return strategies.value.reduce((sum, s) => sum + (s.trades?.length || 0), 0)
})

const filteredStrategies = computed(() => {
  let filtered = strategies.value
  
  // 状态过滤
  if (strategyFilter.value !== 'all') {
    filtered = filtered.filter(s => s.status === strategyFilter.value)
  }
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(s => 
      s.name.toLowerCase().includes(keyword) ||
      s.type.toLowerCase().includes(keyword) ||
      s.symbol.toLowerCase().includes(keyword)
    )
  }
  
  return filtered
})

/**
 * 获取状态颜色
 */
const getStatusColor = (status) => {
  switch (status) {
    case 'running': return 'green'
    case 'paused': return 'orange'
    case 'stopped': return 'red'
    default: return 'gray'
  }
}

/**
 * 获取状态文本
 */
const getStatusText = (status) => {
  switch (status) {
    case 'running': return '运行中'
    case 'paused': return '已暂停'
    case 'stopped': return '已停止'
    default: return '未知'
  }
}

/**
 * 获取回测状态颜色
 */
const getBacktestStatusColor = (status) => {
  switch (status) {
    case 'completed': return 'green'
    case 'running': return 'blue'
    case 'failed': return 'red'
    default: return 'gray'
  }
}

/**
 * 获取回测状态文本
 */
const getBacktestStatusText = (status) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'running': return '运行中'
    case 'failed': return '失败'
    default: return '未知'
  }
}

/**
 * 格式化百分比
 */
const formatPercentage = (value) => {
  return `${value.toFixed(1)}%`
}

/**
 * 格式化货币
 */
const formatCurrency = (value) => {
  const sign = value >= 0 ? '+' : ''
  return `${sign}$${Math.abs(value).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`
}

/**
 * 格式化持续时间
 */
const formatDuration = (startTime) => {
  const duration = Date.now() - startTime
  const days = Math.floor(duration / (1000 * 60 * 60 * 24))
  const hours = Math.floor((duration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  
  if (days > 0) {
    return `${days}天${hours}小时`
  } else {
    return `${hours}小时`
  }
}

/**
 * 格式化完整时间
 */
const formatFullTime = (timestamp) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 过滤策略
 */
const filterStrategies = () => {
  // 过滤逻辑在计算属性中处理
}

/**
 * 搜索策略
 */
const searchStrategies = () => {
  // 搜索逻辑在计算属性中处理
}

/**
 * 处理策略操作
 */
const handleStrategyAction = ({ key }, strategy) => {
  switch (key) {
    case 'start':
      strategy.status = 'running'
      message.success(`策略 ${strategy.name} 已启动`)
      break
    case 'pause':
      strategy.status = 'paused'
      message.success(`策略 ${strategy.name} 已暂停`)
      break
    case 'stop':
      strategy.status = 'stopped'
      message.success(`策略 ${strategy.name} 已停止`)
      break
    case 'edit':
      editStrategy(strategy)
      break
    case 'backtest':
      backtestForm.value.strategyId = strategy.id
      showBacktestModal.value = true
      break
    case 'clone':
      cloneStrategy(strategy)
      break
    case 'delete':
      deleteStrategy(strategy)
      break
  }
}

/**
 * 编辑策略
 */
const editStrategy = (strategy) => {
  createForm.value = {
    name: strategy.name,
    type: strategy.type,
    symbol: strategy.symbol,
    capital: 1000,
    description: '',
    parameters: [...strategy.parameters]
  }
  showCreateStrategy.value = true
}

/**
 * 克隆策略
 */
const cloneStrategy = (strategy) => {
  const newStrategy = {
    ...strategy,
    id: Date.now(),
    name: `${strategy.name} (副本)`,
    status: 'stopped',
    todayPnl: 0,
    totalPnl: 0
  }
  strategies.value.push(newStrategy)
  message.success('策略已克隆')
}

/**
 * 删除策略
 */
const deleteStrategy = (strategy) => {
  const index = strategies.value.findIndex(s => s.id === strategy.id)
  if (index > -1) {
    strategies.value.splice(index, 1)
    message.success('策略已删除')
    emit('strategy-deleted', strategy)
  }
}

/**
 * 查看策略详情
 */
const viewStrategyDetail = (strategy) => {
  selectedStrategy.value = strategy
  showStrategyDetail.value = true
  
  nextTick(() => {
    initPerformanceChart()
  })
}

/**
 * 优化策略
 */
const optimizeStrategy = (strategy) => {
  message.info(`正在优化策略: ${strategy.name}`)
  // 这里可以实现参数优化逻辑
}

/**
 * 使用模板
 */
const useTemplate = (template) => {
  createForm.value = {
    name: `${template.name}_${Date.now()}`,
    type: template.name,
    symbol: 'BTC-USDT',
    capital: 1000,
    description: template.description,
    parameters: []
  }
  showCreateStrategy.value = true
}

/**
 * 创建策略
 */
const createStrategy = () => {
  if (!createForm.value.name || !createForm.value.type || !createForm.value.symbol) {
    message.error('请填写必要信息')
    return
  }
  
  const newStrategy = {
    id: Date.now(),
    ...createForm.value,
    status: 'stopped',
    todayPnl: 0,
    totalPnl: 0,
    winRate: 0,
    runTime: Date.now(),
    todayReturn: 0,
    totalReturn: 0,
    annualReturn: 0,
    maxDrawdown: 0,
    sharpeRatio: 0,
    trades: [],
    logs: []
  }
  
  strategies.value.push(newStrategy)
  showCreateStrategy.value = false
  resetCreateForm()
  message.success('策略创建成功')
  emit('strategy-created', newStrategy)
}

/**
 * 重置创建表单
 */
const resetCreateForm = () => {
  createForm.value = {
    name: '',
    type: '',
    symbol: '',
    capital: 1000,
    description: '',
    parameters: []
  }
}

/**
 * 添加参数
 */
const addParameter = () => {
  createForm.value.parameters.push({
    key: '',
    value: '',
    description: ''
  })
}

/**
 * 移除参数
 */
const removeParameter = (index) => {
  createForm.value.parameters.splice(index, 1)
}

/**
 * 运行回测
 */
const runBacktest = () => {
  if (!backtestForm.value.strategyId || !backtestForm.value.startDate || !backtestForm.value.endDate) {
    message.error('请填写必要信息')
    return
  }
  
  const strategy = strategies.value.find(s => s.id === backtestForm.value.strategyId)
  const newBacktest = {
    id: Date.now(),
    strategyName: strategy.name,
    period: `${dayjs(backtestForm.value.startDate).format('YYYY-MM-DD')} ~ ${dayjs(backtestForm.value.endDate).format('YYYY-MM-DD')}`,
    totalReturn: Math.random() * 30 - 5, // 模拟结果
    sharpeRatio: Math.random() * 2 + 0.5,
    maxDrawdown: Math.random() * 15,
    trades: Math.floor(Math.random() * 200) + 50,
    status: 'running'
  }
  
  backtestResults.value.unshift(newBacktest)
  showBacktestModal.value = false
  resetBacktestForm()
  message.success('回测已启动')
  
  // 模拟回测完成
  setTimeout(() => {
    newBacktest.status = 'completed'
    message.success('回测已完成')
  }, 5000)
}

/**
 * 重置回测表单
 */
const resetBacktestForm = () => {
  backtestForm.value = {
    strategyId: null,
    startDate: null,
    endDate: null,
    initialCapital: 10000,
    feeRate: 0.001
  }
}

/**
 * 查看回测详情
 */
const viewBacktestDetail = (backtest) => {
  message.info(`查看回测详情: ${backtest.strategyName}`)
}

/**
 * 部署策略
 */
const deployStrategy = (backtest) => {
  message.success(`策略 ${backtest.strategyName} 已部署到实盘`)
}

/**
 * 初始化性能图表
 */
const initPerformanceChart = () => {
  if (!performanceChart.value) return
  
  const chart = echarts.init(performanceChart.value)
  
  // 生成模拟数据
  const dates = []
  const values = []
  const baseValue = 10000
  let currentValue = baseValue
  
  for (let i = 0; i < 30; i++) {
    dates.push(dayjs().subtract(29 - i, 'day').format('MM-DD'))
    currentValue += (Math.random() - 0.45) * 200
    values.push(currentValue)
  }
  
  const option = {
    title: {
      text: '策略收益曲线',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: isDarkTheme.value ? '#fff' : '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const value = params[0].value
        const profit = value - baseValue
        const profitRate = ((profit / baseValue) * 100).toFixed(2)
        return `${params[0].name}<br/>净值: $${value.toFixed(2)}<br/>收益: $${profit.toFixed(2)} (${profitRate}%)`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#434343' : '#e8e8e8'
        }
      },
      axisLabel: {
        color: isDarkTheme.value ? '#999' : '#666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#434343' : '#e8e8e8'
        }
      },
      axisLabel: {
        color: isDarkTheme.value ? '#999' : '#666',
        formatter: '${value}'
      },
      splitLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#434343' : '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: '净值',
        type: 'line',
        data: values,
        smooth: true,
        lineStyle: {
          color: '#1890ff',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.05)' }
            ]
          }
        },
        symbol: 'none'
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式调整
  const resizeChart = () => {
    chart.resize()
  }
  
  window.addEventListener('resize', resizeChart)
  
  return () => {
    window.removeEventListener('resize', resizeChart)
    chart.dispose()
  }
}

/**
 * 刷新数据
 */
const refreshData = () => {
  // 模拟数据更新
  strategies.value.forEach(strategy => {
    if (strategy.status === 'running') {
      strategy.todayPnl += (Math.random() - 0.5) * 50
      strategy.totalPnl += (Math.random() - 0.5) * 20
      strategy.todayReturn = (strategy.todayPnl / 1000) * 100
    }
  })
  
  message.success('数据已刷新')
}

/**
 * 保存设置
 */
const saveSettings = () => {
  localStorage.setItem('algorithmTradingSettings', JSON.stringify(settings.value))
  showSettings.value = false
  message.success('设置已保存')
}

/**
 * 重置设置
 */
const resetSettings = () => {
  settings.value = {
    defaultMode: 'paper',
    riskControl: {
      enabled: true,
      maxDailyLoss: 10,
      maxPositionRatio: 50
    },
    notifications: ['trade', 'error'],
    autoOptimization: false
  }
}

/**
 * 加载设置
 */
const loadSettings = () => {
  const saved = localStorage.getItem('algorithmTradingSettings')
  if (saved) {
    try {
      settings.value = { ...settings.value, ...JSON.parse(saved) }
    } catch (error) {
      console.error('加载算法交易设置失败:', error)
    }
  }
}

/**
 * 开始自动更新
 */
const startAutoUpdate = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
  }
  
  updateInterval.value = setInterval(() => {
    // 更新运行中的策略数据
    strategies.value.forEach(strategy => {
      if (strategy.status === 'running') {
        const change = (Math.random() - 0.5) * 10
        strategy.todayPnl += change
        strategy.totalPnl += change * 0.1
        strategy.todayReturn = (strategy.todayPnl / 1000) * 100
      }
    })
  }, 10000) // 10秒更新一次
}

/**
 * 停止自动更新
 */
const stopAutoUpdate = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
    updateInterval.value = null
  }
}

// 生命周期
onMounted(() => {
  loadSettings()
  startAutoUpdate()
})

onUnmounted(() => {
  stopAutoUpdate()
})
</script>

<style scoped>
.algorithm-trading-panel {
  height: 100%;
}

.algorithm-trading-panel .ant-card {
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.algorithm-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  overflow-y: auto;
}

.strategy-overview {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-value.positive {
  color: #52c41a;
}

.stat-value.negative {
  color: #f5222d;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.strategies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.strategy-card {
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.strategy-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.strategy-card.status-running {
  border-left: 4px solid #52c41a;
}

.strategy-card.status-paused {
  border-left: 4px solid #fa8c16;
}

.strategy-card.status-stopped {
  border-left: 4px solid #f5222d;
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.strategy-info {
  flex: 1;
}

.strategy-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.strategy-type {
  font-size: 12px;
  color: #666;
}

.strategy-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.strategy-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 12px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.metric-label {
  color: #666;
}

.metric-value {
  font-weight: 500;
  color: #333;
}

.metric-value.positive {
  color: #52c41a;
}

.metric-value.negative {
  color: #f5222d;
}

.strategy-progress {
  margin-bottom: 12px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 12px;
  color: #666;
}

.progress-value {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.strategy-actions {
  display: flex;
  gap: 8px;
}

.strategy-actions .ant-btn {
  flex: 1;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.template-card {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-card:hover {
  background: #e6f7ff;
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.template-icon {
  font-size: 24px;
  color: #1890ff;
  margin-bottom: 12px;
}

.template-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.template-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.template-stats {
  display: flex;
  gap: 12px;
}

.template-stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #999;
}

.strategy-params {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
}

.param-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

.param-item {
  margin-bottom: 12px;
}

.strategy-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-overview {
  padding: 16px 0;
}

.overview-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.metric-card {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.metric-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.metric-card .metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.metric-card .metric-value.positive {
  color: #52c41a;
}

.metric-card .metric-value.negative {
  color: #f5222d;
}

.performance-chart {
  margin-top: 16px;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.parameter-config {
  padding: 16px 0;
}

.strategy-logs {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px 0;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
}

.log-time {
  color: #999;
  white-space: nowrap;
  min-width: 140px;
}

.log-level {
  font-weight: 600;
  text-transform: uppercase;
  min-width: 60px;
}

.log-level.level-info {
  color: #1890ff;
}

.log-level.level-warning {
  color: #fa8c16;
}

.log-level.level-error {
  color: #f5222d;
}

.log-message {
  flex: 1;
  color: #333;
}

.setting-label {
  margin-left: 8px;
  font-size: 14px;
}

.setting-desc {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.positive {
  color: #52c41a;
}

.negative {
  color: #f5222d;
}

/* 深色主题 */
.algorithm-trading-panel.dark-theme .ant-card {
  background: #1f1f1f;
  border-color: #434343;
}

.algorithm-trading-panel.dark-theme .strategy-overview {
  background: #262626;
}

.algorithm-trading-panel.dark-theme .stat-item {
  background: #1f1f1f;
  border-color: #434343;
}

.algorithm-trading-panel.dark-theme .stat-value {
  color: #fff;
}

.algorithm-trading-panel.dark-theme .stat-label {
  color: #999;
}

.algorithm-trading-panel.dark-theme .section-header h4 {
  color: #fff;
}

.algorithm-trading-panel.dark-theme .strategy-card {
  background: #1f1f1f;
  border-color: #434343;
}

.algorithm-trading-panel.dark-theme .strategy-name {
  color: #fff;
}

.algorithm-trading-panel.dark-theme .strategy-type {
  color: #999;
}

.algorithm-trading-panel.dark-theme .metric-label {
  color: #999;
}

.algorithm-trading-panel.dark-theme .metric-value {
  color: #fff;
}

.algorithm-trading-panel.dark-theme .progress-label {
  color: #999;
}

.algorithm-trading-panel.dark-theme .progress-value {
  color: #fff;
}

.algorithm-trading-panel.dark-theme .template-card {
  background: #262626;
  border-color: #434343;
}

.algorithm-trading-panel.dark-theme .template-card:hover {
  background: #1f1f1f;
  border-color: #1890ff;
}

.algorithm-trading-panel.dark-theme .template-name {
  color: #fff;
}

.algorithm-trading-panel.dark-theme .template-desc {
  color: #999;
}

.algorithm-trading-panel.dark-theme .template-stat {
  color: #666;
}

.algorithm-trading-panel.dark-theme .strategy-params {
  border-color: #434343;
}

.algorithm-trading-panel.dark-theme .metric-card {
  background: #262626;
  border-color: #434343;
}

.algorithm-trading-panel.dark-theme .metric-title {
  color: #999;
}

.algorithm-trading-panel.dark-theme .metric-card .metric-value {
  color: #fff;
}

.algorithm-trading-panel.dark-theme .log-item {
  border-bottom-color: #434343;
}

.algorithm-trading-panel.dark-theme .log-time {
  color: #666;
}

.algorithm-trading-panel.dark-theme .log-message {
  color: #fff;
}
</style>