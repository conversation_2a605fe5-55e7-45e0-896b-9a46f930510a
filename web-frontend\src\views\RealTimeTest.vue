<template>
  <div class="realtime-test">
    <a-card title="实时数据测试">
      <a-space direction="vertical" style="width: 100%;">
        <!-- 控制面板 -->
        <a-card size="small" title="控制面板">
          <a-space>
            <a-select v-model:value="selectedSymbol" style="width: 200px;" @change="onSymbolChange">
              <a-select-option value="BTC-USDT-SWAP">BTC-USDT-SWAP</a-select-option>
              <a-select-option value="ETH-USDT-SWAP">ETH-USDT-SWAP</a-select-option>
              <a-select-option value="SOL-USDT-SWAP">SOL-USDT-SWAP</a-select-option>
            </a-select>
            
            <a-select v-model:value="selectedTimeframe" style="width: 120px;" @change="onTimeframeChange">
              <a-select-option value="1m">1分钟</a-select-option>
              <a-select-option value="5m">5分钟</a-select-option>
              <a-select-option value="15m">15分钟</a-select-option>
              <a-select-option value="1H">1小时</a-select-option>
              <a-select-option value="1D">1天</a-select-option>
            </a-select>
            
            <a-button @click="fetchData" :loading="loading" type="primary">
              获取数据
            </a-button>
            
            <a-button @click="toggleAutoUpdate" :type="autoUpdate ? 'danger' : 'default'">
              {{ autoUpdate ? '停止自动更新' : '开始自动更新' }}
            </a-button>
            
            <a-badge :dot="true" :color="autoUpdate ? 'green' : 'red'">
              <span>{{ autoUpdate ? '实时更新中' : '已停止' }}</span>
            </a-badge>
          </a-space>
        </a-card>
        
        <!-- 数据信息 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card size="small" title="数据统计">
              <a-descriptions size="small" :column="1">
                <a-descriptions-item label="数据条数">{{ chartData.length }}</a-descriptions-item>
                <a-descriptions-item label="最新时间">{{ latestTime }}</a-descriptions-item>
                <a-descriptions-item label="最早时间">{{ earliestTime }}</a-descriptions-item>
                <a-descriptions-item label="更新次数">{{ updateCount }}</a-descriptions-item>
                <a-descriptions-item label="最后更新">{{ lastUpdateTime }}</a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>
          
          <a-col :span="12">
            <a-card size="small" title="实时价格">
              <a-descriptions size="small" :column="1" v-if="currentPrice">
                <a-descriptions-item label="最新价格">{{ currentPrice.last }}</a-descriptions-item>
                <a-descriptions-item label="24h涨跌">{{ currentPrice.sodUtc8 }}%</a-descriptions-item>
                <a-descriptions-item label="24h最高">{{ currentPrice.high24h }}</a-descriptions-item>
                <a-descriptions-item label="24h最低">{{ currentPrice.low24h }}</a-descriptions-item>
                <a-descriptions-item label="24h成交量">{{ currentPrice.vol24h }}</a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>
        </a-row>
        
        <!-- 简单图表 -->
        <a-card title="价格走势图">
          <div id="realtime-chart" style="width: 100%; height: 400px;"></div>
        </a-card>
        
        <!-- 原始数据 -->
        <a-card title="原始数据（最新10条）">
          <a-table 
            :dataSource="recentData" 
            :columns="dataColumns" 
            size="small"
            :pagination="false"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'timestamp'">
                {{ formatTime(record.timestamp) }}
              </template>
              <template v-else-if="column.key === 'change'">
                <span :style="{ color: record.change >= 0 ? '#52c41a' : '#ff4d4f' }">
                  {{ record.change >= 0 ? '+' : '' }}{{ record.change.toFixed(2) }}%
                </span>
              </template>
            </template>
          </a-table>
        </a-card>
        
        <!-- 调试信息 -->
        <a-card title="调试信息">
          <pre>{{ debugInfo }}</pre>
        </a-card>
      </a-space>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import axios from 'axios'
import * as echarts from 'echarts'

// 响应式数据
const selectedSymbol = ref('BTC-USDT-SWAP')
const selectedTimeframe = ref('1H')
const loading = ref(false)
const autoUpdate = ref(false)
const chartData = ref([])
const currentPrice = ref(null)
const updateCount = ref(0)
const lastUpdateTime = ref('')
const debugInfo = ref({})

let chart = null
let updateInterval = null

// 计算属性
const latestTime = computed(() => {
  if (chartData.value.length === 0) return '-'
  const latest = chartData.value[chartData.value.length - 1]
  return formatTime(parseInt(latest[0]))
})

const earliestTime = computed(() => {
  if (chartData.value.length === 0) return '-'
  const earliest = chartData.value[0]
  return formatTime(parseInt(earliest[0]))
})

const recentData = computed(() => {
  return chartData.value.slice(-10).reverse().map((item, index) => {
    const open = parseFloat(item[1])
    const close = parseFloat(item[4])
    const change = ((close - open) / open) * 100
    
    return {
      key: index,
      timestamp: parseInt(item[0]),
      open: parseFloat(item[1]).toFixed(2),
      high: parseFloat(item[2]).toFixed(2),
      low: parseFloat(item[3]).toFixed(2),
      close: parseFloat(item[4]).toFixed(2),
      volume: parseFloat(item[5]).toFixed(0),
      change: change
    }
  })
})

// 表格列定义
const dataColumns = [
  { title: '时间', dataIndex: 'timestamp', key: 'timestamp' },
  { title: '开盘', dataIndex: 'open', key: 'open' },
  { title: '最高', dataIndex: 'high', key: 'high' },
  { title: '最低', dataIndex: 'low', key: 'low' },
  { title: '收盘', dataIndex: 'close', key: 'close' },
  { title: '成交量', dataIndex: 'volume', key: 'volume' },
  { title: '涨跌幅', key: 'change' }
]

// 时间格式化
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 获取K线数据
const fetchData = async () => {
  loading.value = true
  try {
    console.log('开始获取数据...', {
      symbol: selectedSymbol.value,
      timeframe: selectedTimeframe.value
    })
    
    const response = await axios.get('/api/okx/market/candles', {
      params: {
        instId: selectedSymbol.value,
        bar: selectedTimeframe.value,
        limit: 100
      }
    })
    
    debugInfo.value = {
      requestTime: new Date().toLocaleString(),
      responseCode: response.data.code,
      responseMsg: response.data.msg,
      dataLength: response.data.data ? response.data.data.length : 0,
      firstItem: response.data.data ? response.data.data[0] : null,
      lastItem: response.data.data ? response.data.data[response.data.data.length - 1] : null
    }
    
    if (response.data.code === '0' && response.data.data) {
      // OKX返回的数据是按时间倒序的，需要反转
      chartData.value = response.data.data.reverse()
      updateCount.value++
      lastUpdateTime.value = new Date().toLocaleString()
      
      // 更新图表
      updateChart()
      
      message.success(`成功获取${chartData.value.length}条数据`)
    } else {
      message.error('获取数据失败: ' + (response.data.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    message.error('获取数据失败: ' + error.message)
    debugInfo.value = {
      error: error.message,
      errorTime: new Date().toLocaleString()
    }
  } finally {
    loading.value = false
  }
}

// 获取实时价格
const fetchCurrentPrice = async () => {
  try {
    const response = await axios.get(`/api/okx/market/ticker?instId=${selectedSymbol.value}`)
    if (response.data.code === '0' && response.data.data) {
      currentPrice.value = response.data.data[0]
    }
  } catch (error) {
    console.warn('获取实时价格失败:', error.message)
  }
}

// 更新图表
const updateChart = () => {
  if (!chart || chartData.value.length === 0) return
  
  const dates = chartData.value.map(item => {
    const timestamp = parseInt(item[0])
    return new Date(timestamp).toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  })
  
  const candleData = chartData.value.map(item => [
    parseFloat(item[1]), // open
    parseFloat(item[4]), // close
    parseFloat(item[3]), // low
    parseFloat(item[2])  // high
  ])
  
  chart.setOption({
    xAxis: {
      data: dates
    },
    series: [{
      data: candleData
    }]
  })
}

// 初始化图表
const initChart = () => {
  const container = document.getElementById('realtime-chart')
  if (!container) return
  
  chart = echarts.init(container)
  
  const option = {
    title: {
      text: `${selectedSymbol.value} 实时K线`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const data = params[0].data
        return `
          开盘: ${data[0]}<br/>
          收盘: ${data[1]}<br/>
          最低: ${data[2]}<br/>
          最高: ${data[3]}
        `
      }
    },
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: {
      type: 'value',
      scale: true
    },
    dataZoom: [
      {
        type: 'inside',
        start: 70,
        end: 100
      }
    ],
    series: [{
      name: 'K线',
      type: 'candlestick',
      data: [],
      itemStyle: {
        color: '#ec0000',
        color0: '#00da3c',
        borderColor: '#8A0000',
        borderColor0: '#008F28'
      }
    }]
  }
  
  chart.setOption(option)
}

// 切换自动更新
const toggleAutoUpdate = () => {
  autoUpdate.value = !autoUpdate.value
  
  if (autoUpdate.value) {
    // 立即获取一次数据
    fetchData()
    fetchCurrentPrice()
    
    // 设置定时更新
    updateInterval = setInterval(() => {
      fetchData()
      fetchCurrentPrice()
    }, 10000) // 每10秒更新一次
    
    message.success('已开启自动更新')
  } else {
    if (updateInterval) {
      clearInterval(updateInterval)
      updateInterval = null
    }
    message.info('已停止自动更新')
  }
}

// 交易对变化
const onSymbolChange = () => {
  fetchData()
  fetchCurrentPrice()
}

// 时间周期变化
const onTimeframeChange = () => {
  fetchData()
}

// 页面加载时初始化
onMounted(() => {
  initChart()
  fetchData()
  fetchCurrentPrice()
})

// 页面卸载时清理
onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
  if (chart) {
    chart.dispose()
  }
})
</script>

<style scoped>
.realtime-test {
  padding: 24px;
}

pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow: auto;
  max-height: 200px;
  font-size: 12px;
}
</style>
