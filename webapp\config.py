import os
from dotenv import load_dotenv

# 从 .env 文件加载环境变量
load_dotenv()

class Settings:
    """
    应用配置类，从环境变量加载配置。
    """
    # JWT 密钥
    SECRET_KEY: str = os.getenv("SECRET_KEY", "e59c9a5e-b9a4-4b9a-a9a4-e59c9a5eb9a4")
    
    # 数据库URL
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./db.sqlite3")

    # 访问令牌过期时间（秒）
    ACCESS_TOKEN_EXPIRE_SECONDS: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_SECONDS", 3600))

    # 算法
    ALGORITHM: str = "HS256"

settings = Settings()