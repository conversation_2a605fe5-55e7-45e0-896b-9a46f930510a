<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .connect-btn { background-color: #28a745; color: white; }
        .disconnect-btn { background-color: #dc3545; color: white; }
        .subscribe-btn { background-color: #007bff; color: white; }
    </style>
</head>
<body>
    <h1>WebSocket连接调试</h1>
    
    <div id="status" class="status disconnected">未连接</div>
    
    <div>
        <button id="connectBtn" class="connect-btn" onclick="testConnect()">连接测试</button>
        <button id="disconnectBtn" class="disconnect-btn" onclick="testDisconnect()" disabled>断开连接</button>
        <button id="subscribeBtn" class="subscribe-btn" onclick="testSubscribe()" disabled>订阅测试</button>
    </div>
    
    <h3>连接信息</h3>
    <p><strong>WebSocket URL:</strong> <span id="wsUrl">ws://localhost:8080/api/okx/market</span></p>
    <p><strong>连接状态:</strong> <span id="readyState">-</span></p>
    <p><strong>重连次数:</strong> <span id="reconnectCount">0</span></p>
    
    <h3>日志</h3>
    <div id="log" class="log"></div>
    
    <script>
        let socket = null;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;
        const reconnectDelay = 3000;
        const wsUrl = 'ws://localhost:8080/api/okx/market';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function updateStatus(status, className) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = status;
            statusElement.className = `status ${className}`;
        }
        
        function updateReadyState() {
            const readyStateElement = document.getElementById('readyState');
            if (socket) {
                const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
                readyStateElement.textContent = `${socket.readyState} (${states[socket.readyState]})`;
            } else {
                readyStateElement.textContent = 'null';
            }
        }
        
        function updateReconnectCount() {
            document.getElementById('reconnectCount').textContent = reconnectAttempts;
        }
        
        function testConnect() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                log('WebSocket已经连接', 'info');
                return;
            }
            
            log('开始连接WebSocket...', 'info');
            updateStatus('连接中...', 'connecting');
            
            try {
                socket = new WebSocket(wsUrl);
                updateReadyState();
                
                socket.onopen = function(event) {
                    log('WebSocket连接成功建立!', 'success');
                    updateStatus('已连接', 'connected');
                    updateReadyState();
                    reconnectAttempts = 0;
                    updateReconnectCount();
                    
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    document.getElementById('subscribeBtn').disabled = false;
                    
                    log(`连接详情: readyState=${socket.readyState}, url=${socket.url}`, 'info');
                };
                
                socket.onmessage = function(event) {
                    log(`收到消息: ${event.data}`, 'success');
                    try {
                        const data = JSON.parse(event.data);
                        log(`解析后的数据: ${JSON.stringify(data, null, 2)}`, 'info');
                    } catch (e) {
                        log(`消息解析失败: ${e.message}`, 'error');
                    }
                };
                
                socket.onclose = function(event) {
                    log(`WebSocket连接关闭: 代码=${event.code}, 原因=${event.reason || '无'}`, 'error');
                    updateStatus('已断开', 'disconnected');
                    updateReadyState();
                    
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    document.getElementById('subscribeBtn').disabled = true;
                    
                    // 自动重连逻辑
                    if (reconnectAttempts < maxReconnectAttempts && !event.wasClean) {
                        reconnectAttempts++;
                        updateReconnectCount();
                        log(`${reconnectDelay/1000}秒后尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})`, 'info');
                        setTimeout(testConnect, reconnectDelay);
                    } else if (reconnectAttempts >= maxReconnectAttempts) {
                        log('达到最大重连次数，停止重连', 'error');
                    }
                };
                
                socket.onerror = function(error) {
                    log(`WebSocket错误: ${error}`, 'error');
                    log(`错误详情: readyState=${socket?.readyState}, url=${wsUrl}`, 'error');
                    updateStatus('连接错误', 'disconnected');
                    updateReadyState();
                };
                
            } catch (error) {
                log(`创建WebSocket失败: ${error}`, 'error');
                updateStatus('连接失败', 'disconnected');
            }
        }
        
        function testDisconnect() {
            if (socket) {
                log('手动断开WebSocket连接', 'info');
                socket.close(1000, 'Manual disconnect');
                socket = null;
                reconnectAttempts = 0;
                updateReconnectCount();
            }
        }
        
        function testSubscribe() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                log('WebSocket未连接，无法发送订阅请求', 'error');
                return;
            }
            
            const subscriptionRequest = {
                op: "subscribe",
                args: [
                    {
                        channel: "tickers",
                        instId: "BTC-USDT"
                    },
                    {
                        channel: "candle1m",
                        instId: "BTC-USDT"
                    }
                ]
            };
            
            log(`发送订阅请求: ${JSON.stringify(subscriptionRequest)}`, 'info');
            socket.send(JSON.stringify(subscriptionRequest));
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('页面加载完成，准备进行WebSocket调试', 'info');
            updateReadyState();
            updateReconnectCount();
        });
    </script>
</body>
</html>