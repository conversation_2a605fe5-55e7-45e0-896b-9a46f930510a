// V2 - Final Version

document.addEventListener('DOMContentLoaded', () => {
    // --- STATE MANAGEMENT ---
    // Centralized state for the application
    const state = {
        ws: null,
        wsConnected: false,
        wsSubscriptions: new Set(),
        favorites: new Set(JSON.parse(localStorage.getItem('okxFavorites') || '[]')),
        marketData: { // To hold instrument lists for different types
            spot: [],
            swap: [],
            option: [],
            futures: [],
        },
        tickers: {}, // To hold real-time ticker data, keyed by instId
        activeMarketTab: 'favorites', // 'favorites', 'swap', 'spot', 'option'
        activeTradeTab: 'positions',  // 'positions', 'pending-orders', 'history-orders'
        orderHistory: [],
        orderHistoryTotal: 0,
        orderHistoryPage: 1,
        orderHistoryPageSize: 10,
        orderHistoryDetails: {}, // ordId -> detail
        orderHistoryAfter: '',
    };

    // --- DOM ELEMENTS CACHE ---
    // Caching frequently accessed DOM elements for performance
    const dom = {
        appContainer: document.getElementById('app-container'),
        // Sidebar
        balanceContent: document.getElementById('balance-content'),
        statusContent: document.getElementById('status-content'),
        refreshStatusBtn: document.getElementById('refresh-status-btn'),
        // Market Data
        marketTabs: document.getElementById('market-tabs'),
        marketListContent: document.getElementById('market-list-content'),
        marketSearchInput: document.getElementById('market-search-input'),
        quoteFilterTags: document.getElementById('quote-filter-tags'),
        // Trade Details
        tradeTabs: document.getElementById('trade-tabs'),
        tradeContent: document.getElementById('trade-content'),
        tradeFilters: document.getElementById('trade-filters'),
        // Order Form
        orderForm: document.getElementById('orderForm'),
        instTypeSelect: document.getElementById('instType-select'),
        instIdSelect: document.getElementById('instId-select'),
        ordTypeSelect: document.getElementById('ordType-select'),
        pxInput: document.getElementById('px-input'),
        orderResultBox: document.getElementById('order-result-box'),
        // Trade Form
        modeSelect: document.getElementById('mode-select'),
        orderTypeSelect: document.getElementById('order-type-select'),
        tdModeSelect: document.getElementById('tdMode-select'),
        leverSelect: document.getElementById('lever-select'),
        priceInput: document.getElementById('price-input'),
        amountInput: document.getElementById('amount-input'),
        availBal: document.getElementById('availBal'),
        canBuy: document.getElementById('canBuy'),
        canSell: document.getElementById('canSell'),
        liqPx: document.getElementById('liqPx'),
        buyBtn: document.getElementById('buy-btn'),
        sellBtn: document.getElementById('sell-btn'),
    };

    // --- UTILITY FUNCTIONS ---
    // Reusable helper functions
    const utils = {
        renderSpinner: (element) => {
            element.innerHTML = '<div class="spinner"></div>';
        },
        formatNumber: (num, precision = 2) => {
            const n = parseFloat(num);
            if (isNaN(n) || num === null || num === undefined) return '-';
            return n.toLocaleString(undefined, {
                minimumFractionDigits: precision,
                maximumFractionDigits: precision,
            });
        },
        formatLargeNumber: (num) => {
            const n = parseFloat(num);
            if (isNaN(n)) return '-';
            if (n >= 1_000_000_000) return `${(n / 1_000_000_000).toFixed(2)}B`;
            if (n >= 1_000_000) return `${(n / 1_000_000).toFixed(2)}M`;
            if (n >= 1_000) return `${(n / 1_000).toFixed(2)}K`;
            return n.toString();
        },
        getChangeClass: (change) => {
            const n = parseFloat(change);
            if (n > 0) return 'positive';
            if (n < 0) return 'negative';
            return '';
        },
    };

    // --- API & WEBSOCKET HANDLERS ---
    const api = {
        get: async (endpoint, params = {}) => {
            try {
                const response = await axios.get(`/api${endpoint}`, { params });
                if (response.data.code !== "0" && response.data.code !== 0) { // OKX sometimes returns string "0"
                    throw new Error(response.data.msg || `API Error code ${response.data.code}`);
                }
                return response.data.data;
            } catch (error) {
                console.error(`Error fetching ${endpoint}:`, error);
                const message = error.response?.data?.msg || error.message;
                // Maybe show a global error notification here in the future
                throw new Error(message);
            }
        },
        post: async (endpoint, body = {}) => {
            try {
                const response = await axios.post(`/api${endpoint}`, body);
                if (response.data.code !== "0" && response.data.code !== 0) {
                     throw new Error(response.data.msg || `API Error code ${response.data.code}`);
                }
                return response.data.data;
            } catch (error) {
                console.error(`Error posting to ${endpoint}:`, error);
                const message = error.response?.data?.msg || error.message;
                throw new Error(message);
            }
        }
    };
    
    const ws = {
        connect: () => {
            if (state.ws && state.ws.readyState === WebSocket.OPEN) return;

            state.ws = new WebSocket('ws://' + window.location.host + '/ws/proxy');

            state.ws.onopen = () => {
                console.log("WebSocket connected.");
                state.wsConnected = true;
                if (state.wsSubscriptions.size > 0) {
                    const subs = Array.from(state.wsSubscriptions).map(s => JSON.parse(s));
                    ws.send('subscribe', subs);
                }
            };

            state.ws.onmessage = (event) => {
                const message = JSON.parse(event.data);
                ws.handleMessage(message);
            };

            state.ws.onclose = () => {
                console.log("WebSocket disconnected. Reconnecting...");
                state.wsConnected = false;
                setTimeout(ws.connect, 5000);
            };

            state.ws.onerror = (error) => {
                console.error("WebSocket error:", error);
                state.ws.close(); // This will trigger onclose and the reconnect logic
            };
        },
        send: (op, args) => {
            if (state.wsConnected) {
                state.ws.send(JSON.stringify({ op, args }));
            } else {
                console.warn("WS not connected. Message queued or dropped.", {op, args});
            }
        },
        subscribe: (channels) => {
            const newChannels = channels.filter(c => !state.wsSubscriptions.has(JSON.stringify(c)));
            if (newChannels.length > 0) {
                ws.send('subscribe', newChannels);
                newChannels.forEach(c => state.wsSubscriptions.add(JSON.stringify(c)));
            }
        },
        unsubscribe: (channels) => {
             ws.send('unsubscribe', channels);
             channels.forEach(c => state.wsSubscriptions.delete(JSON.stringify(c)));
        },
        handleMessage: (message) => {
            // Placeholder for message handling logic
            // console.log("WS Message:", message);
            if (message.arg?.channel === 'tickers' && message.data) {
                const ticker = message.data[0];
                const existingTicker = state.tickers[ticker.instId];
                if (existingTicker) {
                    // Update state
                    state.tickers[ticker.instId] = { ...existingTicker, ...ticker };
                    // Update UI non-destructively
                    renderers.updateMarketRow(ticker.instId);
                }
            }
        }
    };

    // --- RENDER FUNCTIONS ---
    const renderers = {
        renderBalance: async () => {
            try {
                const data = await api.get('/balance');
                if (data && data[0] && data[0].details) {
                    dom.balanceContent.innerHTML = data[0].details
                        .map(asset => `<p><strong>${asset.ccy}:</strong> ${utils.formatNumber(asset.eq, 4)}</p>`)
                        .join('');
                } else {
                    dom.balanceContent.innerHTML = '<p>无资产信息</p>';
                }
            } catch (error) {
                dom.balanceContent.innerHTML = `<p class="error">无法加载余额: ${error.message}</p>`;
            }
        },
        renderStatus: async () => {
            utils.renderSpinner(dom.statusContent);
            try {
                const data = await api.get('/status');
                // OKX returns an empty array when system is normal
                if (!data || data.length === 0) {
                     dom.statusContent.innerHTML = `
                        <p><strong>状态:</strong> <span class="positive">正常</span></p>
                        <p>所有系统均运行正常。</p>
                    `;
                    return;
                }
                const status = data[0];
                dom.statusContent.innerHTML = `
                    <p><strong>状态:</strong> ${status.state === '0' ? '<span class="positive">正常</span>' : '<span class="negative">维护中</span>'}</p>
                    <p><strong>标题:</strong> ${status.title}</p>
                    ${status.href ? `<a href="${status.href}" target="_blank" class="sidebar-button">查看详情</a>` : ''}
                `;
            } catch (error) {
                dom.statusContent.innerHTML = `<p class="error">无法加载系统状态: ${error.message}</p>`;
            }
        },
        renderMarketList: (instType, searchTerm = '', quoteFilter = 'ALL') => {
            const lowerCaseSearchTerm = searchTerm.toLowerCase();
            
            let instruments;
            if (instType === 'favorites') {
                instruments = Array.from(state.favorites)
                    .map(instId => state.tickers[instId])
                    .filter(Boolean); // Filter out any undefined tickers
            } else {
                instruments = state.marketData[instType.toLowerCase()] || [];
            }

            const filtered = instruments.filter(inst => {
                const searchMatch = inst.instId.toLowerCase().includes(lowerCaseSearchTerm);
                const quoteMatch = quoteFilter === 'ALL' || inst.quoteCcy === quoteFilter;
                return searchMatch && quoteMatch;
            });

            if (filtered.length === 0) {
                dom.marketListContent.innerHTML = '<p style="text-align: center; padding: 20px;">无匹配数据</p>';
                return;
            }

            dom.marketListContent.innerHTML = filtered.map(inst => renderers.getMarketRowHTML(inst.instId)).join('');

            // Subscribe to WS updates for visible items
            const channels = filtered.map(inst => ({ channel: 'tickers', instId: inst.instId }));
            ws.subscribe(channels);
        },
        getMarketRowHTML: (instId) => {
            const ticker = state.tickers[instId];
            if (!ticker) return '';

            const changePct = parseFloat(ticker.sodUtc8) > 0 
                ? (((ticker.last / ticker.sodUtc8) - 1) * 100).toFixed(2)
                : '0.00';
            const isFavorite = state.favorites.has(instId);

            return `
                <div class="market-row" id="market-row-${instId}" data-inst-id="${instId}">
                    <div class="instrument-info">
                        <span class="favorite-star ${isFavorite ? 'favorited' : ''}" data-inst-id="${instId}">★</span>
                        <div>
                            <div class="instrument-name">${ticker.instId}</div>
                            <div class="instrument-vol">24h Vol: ${utils.formatLargeNumber(ticker.volCcy24h)} ${ticker.quoteCcy}</div>
                        </div>
                    </div>
                    <div class="price">${utils.formatNumber(ticker.last, 2)}</div>
                    <div class="change ${utils.getChangeClass(changePct)}">${changePct}%</div>
                </div>
            `;
        },
        updateMarketRow: (instId) => {
            const row = document.getElementById(`market-row-${instId}`);
            if (!row) return;

            const ticker = state.tickers[instId];
            const priceEl = row.querySelector('.price');
            const changeEl = row.querySelector('.change');
            const volEl = row.querySelector('.instrument-vol');

            const changePct = parseFloat(ticker.sodUtc8) > 0
                ? (((ticker.last / ticker.sodUtc8) - 1) * 100).toFixed(2)
                : '0.00';
            
            // Flash effect only if price changed
            if (priceEl.textContent !== utils.formatNumber(ticker.last, 2)) {
                priceEl.classList.add('flash');
                // A bit longer to be more noticeable
                setTimeout(() => priceEl.classList.remove('flash'), 700);
            }

            priceEl.textContent = utils.formatNumber(ticker.last, 2);
            changeEl.textContent = `${changePct}%`;
            changeEl.className = `change ${utils.getChangeClass(changePct)}`;
            volEl.textContent = `24h Vol: ${utils.formatLargeNumber(ticker.volCcy24h)} ${ticker.quoteCcy}`;
        },
        renderQuoteFilters: (instType) => {
            const instruments = instType === 'favorites'
                ? Array.from(state.favorites).map(id => state.tickers[id]).filter(Boolean)
                : state.marketData[instType.toLowerCase()] || [];

            const quotes = [...new Set(instruments.map(inst => inst.quoteCcy))].sort();
            if (quotes.length <= 1) {
                dom.quoteFilterTags.innerHTML = '';
                return;
            }

            dom.quoteFilterTags.innerHTML = ['ALL', ...quotes].map(q => 
                `<button class="filter-tag ${q === 'ALL' ? 'active' : ''}" data-quote="${q}">${q}</button>`
            ).join('');
        },
        renderTradeTable: (data, columns) => {
            if (!data || data.length === 0) {
                dom.tradeContent.innerHTML = '<p style="text-align: center; padding: 20px;">无数据</p>';
                return;
            }

            const head = `<thead><tr>${columns.map(c => `<th>${c.title}</th>`).join('')}</tr></thead>`;
            const body = `<tbody>${data.map(row => `
                <tr>
                    ${columns.map(col => {
                        const value = row[col.key];
                        const content = col.render ? col.render(row)
                                      : col.format ? col.format(value) 
                                      : value;
                        return `<td>${content}</td>`;
                    }).join('')}
                </tr>
            `).join('')}</tbody>`;

            dom.tradeContent.innerHTML = `<table>${head}${body}</table>`;
        },
        renderHistoryOrdersTable: (data, total) => {
            const validData = Array.isArray(data) ? data.filter(row => row && typeof row === 'object' && row.ordId) : [];
            if (!validData.length) {
                dom.tradeContent.innerHTML = '<p style="text-align: center; padding: 20px;">无数据</p>';
                return;
            }
            const columns = [
                { key: 'instId', title: '交易品种', render: row => `${row.instId || '--'}<br><span class=\"leverage-tag\">${row.lever || '--'}x</span>` },
                { key: 'status', title: '合约状态' },
                { key: 'openTime', title: '开仓时间' },
                { key: 'actions', title: '操作', render: (v, row) => `<button class="detail-btn" data-ordid="${row && row.ordId ? row.ordId : '--'}">详情</button>` },
            ];
            dom.tradeContent.innerHTML = '';
            const head = `<thead><tr>${columns.map(c => `<th>${c.title}</th>`).join('')}</tr></thead>`;
            const body = `<tbody>${validData.map(row => `
                <tr data-ordid="${row && row.ordId ? row.ordId : '--'}">
                    ${columns.map(col => {
                        let value = row[col.key];
                        let content = col.render ? col.render(row) : (value || '--');
                        return `<td>${content}</td>`;
                    }).join('')}
                </tr>
                <tr class="order-detail-row" id="detail-${row && row.ordId ? row.ordId : '--'}" style="display:none;"><td colspan="${columns.length}"></td></tr>
            `).join('')}</tbody>`;
            const table = document.createElement('table');
            table.className = 'history-orders-table';
            table.innerHTML = head + body;
            dom.tradeContent.appendChild(table);
            renderers.renderOrderHistoryPagination(total);
            dom.tradeContent.querySelectorAll('.detail-btn').forEach(btn => {
                btn.addEventListener('click', async (e) => {
                    const ordId = btn.getAttribute('data-ordid');
                    if (!ordId || ordId === '--') return;
                    const detailRow = document.getElementById(`detail-${ordId}`);
                    if (!detailRow) return;
                    if (detailRow.style.display === 'none') {
                        if (!state.orderHistoryDetails[ordId]) {
                            detailRow.querySelector('td').innerHTML = '加载中...';
                            try {
                                const res = await api.get('/order_fills', { ordId });
                                if (res) {
                                    state.orderHistoryDetails[ordId] = res;
                                }
                            } catch (err) {
                                detailRow.querySelector('td').innerHTML = `<span class="error">加载详情失败: ${err.message}</span>`;
                                return;
                            }
                        }
                        // 渲染详情
                        const d = state.orderHistoryDetails[ordId].data || {};
                        detailRow.querySelector('td').innerHTML = renderers.renderOrderDetail(d);
                        detailRow.style.display = '';
                        btn.textContent = '收起';
                    } else {
                        detailRow.style.display = 'none';
                        btn.textContent = '详情';
                    }
                });
            });
        },
        renderOrderDetail: (d) => {
            // 展示开/平仓均价、收益、收益率、最大持仓量、已平仓量、开/平时间、撮合明细表
            return `
                <div class="order-detail-box">
                    <div class="order-detail-fields">
                        <span><b>开仓均价：</b> ${d.openAvgPx || '--'}</span>
                        <span><b>平仓均价：</b> ${d.closeAvgPx || '--'}</span>
                        <span><b>已实现收益：</b> <span class="${utils.getChangeClass(d.realizedPnl)}">${d.realizedPnl || '--'} USDT</span></span>
                        <span><b>已实现收益率：</b> <span class="${utils.getChangeClass(d.realizedPnlRate)}">${d.realizedPnlRate || '--'}%</span></span>
                        <span><b>最大持仓量：</b> ${d.maxPos || '--'} USDT</span>
                        <span><b>已平仓量：</b> ${d.closeVol || '--'} USDT</span>
                        <span><b>开仓时间：</b> ${d.openTime || '--'}</span>
                        <span><b>平仓时间：</b> ${d.closeTime || '--'}</span>
                    </div>
                    <div class="order-fills-table-box">
                        <table class="order-fills-table">
                            <thead><tr><th>成交时间</th><th>方向</th><th>价格</th><th>数量</th><th>收益</th></tr></thead>
                            <tbody>
                                ${(d.fills||[]).length === 0 ? '<tr><td colspan="5">--</td></tr>' : (d.fills||[]).map(f => `
                                    <tr>
                                        <td>${f.fillTime ? new Date(parseInt(f.fillTime)).toLocaleString() : '--'}</td>
                                        <td>${f.side || '--'}</td>
                                        <td>${f.fillPx || '--'}</td>
                                        <td>${f.fillSz || '--'}</td>
                                        <td>${f.pnl || '--'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        },
        renderOrderHistoryPagination: (total) => {
            // 游标分页：只显示上一页/下一页按钮
            let html = `<div class="pagination">`;
            html += `<button class="page-btn" data-direction="prev">上一页</button>`;
            html += `<button class="page-btn" data-direction="next">下一页</button>`;
            html += `</div>`;
            const div = document.createElement('div');
            div.innerHTML = html;
            dom.tradeContent.appendChild(div);
            div.querySelectorAll('.page-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const direction = btn.getAttribute('data-direction');
                    handlers.fetchOrderHistory(direction);
                });
            });
        },
        renderPositionsTable: (positions) => {
            if (!positions || positions.length === 0) {
                dom.tradeContent.innerHTML = '<p style="text-align: center; padding: 20px;">无持仓</p>';
                return;
            }
            const columns = [
                { key: 'instId', title: '交易品种', render: row => `<div><b>${row.instId}</b> <span class="pos-type">${row.posSide === 'long' ? '多头' : row.posSide === 'short' ? '空头' : ''}</span></div><div class="lever-tag">${row.lever}x</div>` },
                { key: 'pos', title: '持仓量', render: row => `<span class="pos-qty">${row.pos} USDT</span>` },
                { key: 'markPx', title: '标记价格' },
                { key: 'avgPx', title: '开仓均价' },
                { key: 'upl', title: '浮动盈亏', render: row => `<span class="${parseFloat(row.upl) >= 0 ? 'positive' : 'negative'}">${row.upl} USDT</span>` },
                { key: 'mgnRatio', title: '维持保证金率', render: row => `<span>${row.mgnRatio || '--'}%</span>` },
                { key: 'margin', title: '保证金', render: row => `<span>${row.margin || '--'} USDT</span>` },
                { key: 'lever', title: '杠杆', render: row => `<span class="lever-tag">${row.lever}x <span class="edit-icon">✎</span></span>` },
                { key: 'actions', title: '操作', render: row => `<button class="close-btn">平仓</button>` },
            ];
            let html = `<table class="positions-table"><thead><tr>${columns.map(c => `<th>${c.title}</th>`).join('')}</tr></thead><tbody>`;
            html += positions.map(row => `<tr>${columns.map(col => col.render ? col.render(row) : (row[col.key] || '--')).map(cell => `<td>${cell}</td>`).join('')}</tr>`).join('');
            html += '</tbody></table>';
            dom.tradeContent.innerHTML = html;
        },
    };
    
    // --- EVENT LISTENERS & LOGIC ---
    const handlers = {
        initSideBar: () => {
            dom.refreshStatusBtn.addEventListener('click', renderers.renderStatus);
        },
        initMarketData: async () => {
            // Setup Event Listeners for Market module first
            dom.marketTabs.addEventListener('click', handlers.handleMarketTabClick);
            dom.marketSearchInput.addEventListener('input', (e) => {
                const activeTab = state.activeMarketTab;
                const activeQuoteFilter = dom.quoteFilterTags.querySelector('.active')?.dataset.quote || 'ALL';
                renderers.renderMarketList(activeTab, e.target.value, activeQuoteFilter);
            });
            dom.quoteFilterTags.addEventListener('click', handlers.handleQuoteFilterClick);
            dom.marketListContent.addEventListener('click', handlers.handleMarketListClick);

            // Then load initial data
            utils.renderSpinner(dom.marketListContent);
            try {
                // We start by loading the 'favorites' tab.
                // This handler will internally ensure necessary base data (like SWAP) is loaded first.
                await handlers.loadMarketDataForTab('favorites');
                renderers.renderMarketList('favorites');
                renderers.renderQuoteFilters('favorites');

            } catch (error) {
                dom.marketListContent.innerHTML = `<p class="error">无法加载市场数据: ${error.message}</p>`;
            }
        },
        handleMarketTabClick: async (e) => {
            if (!e.target.classList.contains('tab-link')) return;

            const tab = e.target;
            const instType = tab.dataset.tab;

            dom.marketTabs.querySelector('.active').classList.remove('active');
            tab.classList.add('active');
            state.activeMarketTab = instType;
            dom.marketSearchInput.value = ''; // Clear search

            utils.renderSpinner(dom.marketListContent);
            await handlers.loadMarketDataForTab(instType);
            
            renderers.renderMarketList(instType);
            renderers.renderQuoteFilters(instType);
        },
        handleQuoteFilterClick: (e) => {
            if (!e.target.classList.contains('filter-tag')) return;
            
            const button = e.target;
            const quote = button.dataset.quote;

            dom.quoteFilterTags.querySelector('.active')?.classList.remove('active');
            button.classList.add('active');

            const searchTerm = dom.marketSearchInput.value;
            renderers.renderMarketList(state.activeMarketTab, searchTerm, quote);
        },
        handleMarketListClick: (e) => {
            const target = e.target;
            if (target.classList.contains('favorite-star')) {
                e.stopPropagation(); // Prevent row click from firing
                const instId = target.dataset.instId;
                if (state.favorites.has(instId)) {
                    state.favorites.delete(instId);
                    target.classList.remove('favorited');
                } else {
                    state.favorites.add(instId);
                    target.classList.add('favorited');
                }
                localStorage.setItem('okxFavorites', JSON.stringify(Array.from(state.favorites)));

                if (state.activeMarketTab === 'favorites') {
                    renderers.renderMarketList('favorites');
                    renderers.renderQuoteFilters('favorites');
                }
            } else {
                 const row = target.closest('.market-row');
                 if (row) {
                     const instId = row.dataset.instId;
                     const ticker = state.tickers[instId];
                     if (ticker) {
                         // 只用tradeState赋值并刷新交易信息
                         tradeState.instId = ticker.instId;
                         tradeState.instType = ticker.instType;
                         refreshTradeInfo();
                     }
                 }
            }
        },
        loadInstrumentsForOrderForm: (instType, selectedInstId = null) => {
            const instruments = state.marketData[instType.toLowerCase()] || [];
            
            if (instruments.length === 0) {
                dom.instIdSelect.innerHTML = '<option value="">-- 请先从左侧加载该类型数据 --</option>';
                return;
            }

            dom.instIdSelect.innerHTML = instruments
                .map(i => `<option value="${i.instId}" ${i.instId === selectedInstId ? 'selected' : ''}>${i.instId}</option>`)
                .join('');
        },
        handleOrderSubmit: async (e) => {
            e.preventDefault();
            const formData = new FormData(dom.orderForm);
            const data = Object.fromEntries(formData.entries());
            // 如果是现货，移除posSide
            if (data.instType === 'SPOT') {
                delete data.posSide;
            }
            dom.orderResultBox.innerHTML = '正在提交...';
            dom.orderResultBox.className = 'form-result';
            try {
                const result = await api.post('/order', data);
                const orderRes = result[0];
                if (orderRes.sCode === "0") {
                     dom.orderResultBox.innerHTML = `<strong>成功!</strong> 订单ID: ${orderRes.ordId}`;
                     dom.orderResultBox.classList.add('success');
                     // Refresh positions/pending orders after a short delay
                     setTimeout(() => {
                         if (handlers.fetchPositions) handlers.fetchPositions();
                         if (handlers.fetchPendingOrders) handlers.fetchPendingOrders();
                     }, 1500);
                } else {
                    throw new Error(`[${orderRes.sCode}] ${orderRes.sMsg}`);
                }
            } catch (error) {
                dom.orderResultBox.innerHTML = `<strong>失败:</strong> ${error.message}`;
                dom.orderResultBox.classList.add('error');
            }
        },
        loadMarketDataForTab: async (instType) => {
            const type = instType.toLowerCase();
            if (type === 'favorites') {
                const favArr = Array.from(state.favorites);
                // Find which types we need to load based on favorited instruments
                const typesToLoad = new Set(favArr.map(id => state.tickers[id]?.instType).filter(Boolean).map(t => t.toLowerCase()));
                
                // Always ensure SWAP is loaded as a base, as it's a common type
                if (state.marketData.swap.length === 0) typesToLoad.add('swap');

                // Load all necessary types in parallel
                await Promise.all(Array.from(typesToLoad).map(t => handlers.loadMarketDataForTab(t)));
                return;
            }

            // Skip if already loaded
            if (state.marketData[type] && state.marketData[type].length > 0) {
                return;
            }

            try {
                 const [products, tickers] = await Promise.all([
                    api.get('/products', { instType: instType.toUpperCase() }),
                    api.get('/tickers', { instType: instType.toUpperCase() })
                ]);
                
                const combinedData = tickers.map(t => {
                    const p = products.find(p => p.instId === t.instId) || {};
                    const existingData = state.tickers[t.instId] || {};
                    return { ...p, ...existingData, ...t }; // Ensure we don't lose product info
                });

                combinedData.forEach(d => {
                    state.tickers[d.instId] = d;
                });
                
                // Store as a separate list for filtering
                state.marketData[type] = combinedData;

            } catch (error) {
                console.error(`Failed to load data for ${instType}:`, error);
                // Don't overwrite the whole view if one tab fails
                if (state.activeMarketTab === instType) {
                    dom.marketListContent.innerHTML = `<p class="error" style="text-align: center; padding: 20px;">无法加载 ${instType} 数据: ${error.message}</p>`;
                }
            }
        },

        // --- Trade Details ---
        initTradeDetails: () => {
            dom.tradeTabs.addEventListener('click', handlers.handleTradeTabClick);
            dom.tradeContent.addEventListener('click', handlers.handleCancelOrderClick);

            // Initial load
            handlers.fetchPositions();
        },

        handleTradeTabClick: async (e) => {
            if (!e.target.classList.contains('tab-link')) return;
            const tab = e.target;
            const type = tab.dataset.tab;
            dom.tradeTabs.querySelector('.active').classList.remove('active');
            tab.classList.add('active');
            state.activeTradeTab = type;
            switch(type) {
                case 'positions':
                    await handlers.fetchPositions();
                    break;
                case 'pending-orders':
                    await handlers.fetchPendingOrders();
                    break;
                case 'history-orders':
                    await handlers.fetchOrderHistory('next');
                    break;
            }
        },
        
        fetchPositions: async () => {
            utils.renderSpinner(dom.tradeContent);
            try {
                const data = await api.get('/positions');
                renderers.renderPositionsTable(data);
            } catch (error) {
                dom.tradeContent.innerHTML = `<p class="error">无法加载持仓: ${error.message}</p>`;
            }
        },

        fetchPendingOrders: async () => {
            utils.renderSpinner(dom.tradeContent);
            try {
                const data = await api.get('/orders_pending');
                const columns = [
                    { key: 'instId', title: '产品ID' },
                    { key: 'side', title: '方向' },
                    { key: 'ordType', title: '类型' },
                    { key: 'px', title: '价格' },
                    { key: 'sz', title: '数量' },
                    { key: 'state', title: '状态' },
                    { key: 'cTime', title: '时间', format: (ts) => new Date(parseInt(ts)).toLocaleString() },
                    { key: 'actions', title: '操作', render: (row) => `<button class="cancel-btn" data-inst-id="${row.instId}" data-ord-id="${row.ordId}">撤销</button>` }
                ];
                renderers.renderTradeTable(data, columns);
            } catch (error) {
                dom.tradeContent.innerHTML = `<p class="error">无法加载挂单: ${error.message}</p>`;
            }
        },

        fetchOrderHistory: async (direction = 'next') => {
            utils.renderSpinner(dom.tradeContent);
            try {
                const pageSize = state.orderHistoryPageSize;
                let after = '';
                if (direction === 'next' && state.orderHistory.length > 0) {
                    after = state.orderHistory[state.orderHistory.length - 1].ordId;
                } else if (direction === 'prev' && state.orderHistory.length > 0) {
                    after = state.orderHistory[0].ordId;
                }
                const res = await api.get('/order_history', { after, limit: pageSize });
                state.orderHistory = res;
                renderers.renderHistoryOrdersTable(res, res.length);
                // 记录当前游标
                state.orderHistoryAfter = after;
            } catch (error) {
                dom.tradeContent.innerHTML = `<p class="error">无法加载历史订单: ${error.message}</p>`;
            }
        },

        handleCancelOrderClick: async (e) => {
            if (!e.target.classList.contains('cancel-btn')) return;

            const btn = e.target;
            const instId = btn.dataset.instId;
            const ordId = btn.dataset.ordId;

            if (confirm(`确定要撤销订单 ${ordId} 吗?`)) {
                try {
                    btn.textContent = '...';
                    btn.disabled = true;
                    await api.post('/cancel_order', { instId, ordId });
                    // Refresh the list after cancellation
                    await handlers.fetchPendingOrders();
                } catch (error) {
                    alert(`撤销失败: ${error.message}`);
                    btn.textContent = '撤销';
                    btn.disabled = false;
                }
            }
        }
    }

    // --- TRADE FORM LOGIC ---
    const tradeState = {
        mode: 'open', // open/close
        orderType: 'market', // market/limit/tp-sl
        tdMode: 'cross',
        lever: 50,
        instType: 'SWAP',
        instId: '',
        posSide: 'long',
    };

    // 初始化杠杆下拉
    function renderLeverOptions(min=1, max=50) {
        let html = '';
        for(let i=min; i<=max; i++) {
            html += `<option value="${i}" ${i===50?'selected':''}>${i}.00x</option>`;
        }
        dom.leverSelect.innerHTML = html;
    }

    // 切换Tab
    document.querySelectorAll('.trade-tab').forEach(btn => {
        btn.addEventListener('click', e => {
            document.querySelectorAll('.trade-tab').forEach(b=>b.classList.remove('active'));
            btn.classList.add('active');
            tradeState.mode = btn.dataset.mode;
        });
    });
    // 切换委托类型
    document.querySelectorAll('.order-type-tab').forEach(btn => {
        btn.addEventListener('click', e => {
            document.querySelectorAll('.order-type-tab').forEach(b=>b.classList.remove('active'));
            btn.classList.add('active');
            tradeState.orderType = btn.dataset.type;
            if(tradeState.orderType==='market') {
                dom.priceInput.value = '';
                dom.priceInput.disabled = true;
                dom.priceInput.placeholder = '以当前市场最优价格下单';
                document.getElementById('tp-sl-box').style.display = 'none';
            } else if(tradeState.orderType==='limit') {
                dom.priceInput.disabled = false;
                dom.priceInput.placeholder = '价格(USDT)';
                document.getElementById('tp-sl-box').style.display = 'none';
            } else if(tradeState.orderType==='tp-sl') {
                dom.priceInput.disabled = false;
                dom.priceInput.placeholder = '价格(USDT)';
                document.getElementById('tp-sl-box').style.display = '';
            }
        });
    });
    // 全仓/逐仓切换
    if(dom.tdModeSelect) dom.tdModeSelect.addEventListener('change', e => {
        tradeState.tdMode = e.target.value;
    });
    // 杠杆切换
    if(dom.leverSelect) dom.leverSelect.addEventListener('change', e => {
        tradeState.lever = e.target.value;
        refreshTradeInfo();
    });
    // 实时刷新交易信息
    async function refreshTradeInfo() {
        if(!tradeState.instId) return;
        try {
            const info = await api.get('/trade_info', { instId: tradeState.instId, instType: tradeState.instType, lever: tradeState.lever });
            // 获取base币种
            const baseCcy = tradeState.instId ? tradeState.instId.split('-')[0] : '';
            if(info && info.availBal !== undefined) {
                dom.availBal.textContent = '可用余额: ' + info.availBal + ' USDT';
                dom.canBuy.textContent = '可开多: ' + info.canBuy + (tradeState.instType === 'SPOT' ? ' ' + baseCcy : ' 张');
                dom.canSell.textContent = '可开空: ' + info.canSell + (tradeState.instType === 'SPOT' ? ' ' + baseCcy : ' 张');
                dom.liqPx.textContent = '强平价: ' + info.liqPx;
            }
            renderLeverOptions(info.leverRange ? info.leverRange[0] : 1, info.leverRange ? info.leverRange[1] : 50);
        } catch(e) {
            dom.availBal.textContent = '可用余额: --';
            dom.canBuy.textContent = '可开多: --';
            dom.canSell.textContent = '可开空: --';
            dom.liqPx.textContent = '强平价: --';
        }
    }
    // 假设初始instId/instType
    tradeState.instId = 'ETH-USDT-SWAP';
    tradeState.instType = 'SWAP';
    refreshTradeInfo();

    // 下单按钮
    if(dom.buyBtn) dom.buyBtn.onclick = async function() {
        await submitOrder('long');
    };
    if(dom.sellBtn) dom.sellBtn.onclick = async function() {
        await submitOrder('short');
    };
    async function submitOrder(posSide) {
        const data = {
            instId: tradeState.instId,
            instType: tradeState.instType,
            tdMode: tradeState.tdMode,
            lever: tradeState.lever,
            posSide: posSide,
            side: posSide==='long' ? 'buy' : 'sell',
            ordType: tradeState.orderType==='market' ? 'market' : (tradeState.orderType==='limit' ? 'limit' : 'conditional'),
            sz: dom.amountInput.value,
            px: dom.priceInput.value
        };
        // 止盈止损参数
        if(tradeState.orderType==='tp-sl') {
            data.tpTriggerPx = document.querySelector('[name="tpTriggerPx"]').value;
            data.tpOrdPx = document.querySelector('[name="tpOrdPx"]').value;
            data.slTriggerPx = document.querySelector('[name="slTriggerPx"]').value;
            data.slOrdPx = document.querySelector('[name="slOrdPx"]').value;
        }
        dom.orderResultBox.innerHTML = '正在提交...';
        dom.orderResultBox.className = 'form-result';
        try {
            const result = await api.post('/order', data);
            dom.orderResultBox.innerHTML = `<strong>成功!</strong> 订单ID: ${result[0]?.ordId || '--'}`;
            dom.orderResultBox.classList.add('success');
            await refreshTradeInfo(); // 下单后自动刷新
        } catch (error) {
            dom.orderResultBox.innerHTML = `<strong>失败:</strong> ${error.message}`;
            dom.orderResultBox.classList.add('error');
        }
    }

    // --- INITIALIZATION ---
    const init = () => {
        console.log("Initializing OKX Terminal...");
        // Initial data load
        renderers.renderBalance();
        renderers.renderStatus();
        // Setup event listeners
        handlers.initSideBar();
        handlers.initMarketData();
        // Order Form listeners
        if(dom.instTypeSelect) dom.instTypeSelect.addEventListener('change', (e) => {
            const type = e.target.value;
            const posSideRow = document.getElementById('posSide-select');
            if (posSideRow) {
                if (["SWAP", "FUTURES", "OPTION"].includes(type)) {
                    posSideRow.style.display = '';
                } else {
                    posSideRow.style.display = 'none';
                }
            }
            handlers.loadInstrumentsForOrderForm(type);
        });
        // 初始化时根据默认类型显示/隐藏
        (() => {
            if(!dom.instTypeSelect) return;
            const type = dom.instTypeSelect.value;
            const posSideRow = document.getElementById('posSide-select');
            if (posSideRow) {
                if (["SWAP", "FUTURES", "OPTION"].includes(type)) {
                    posSideRow.style.display = '';
                } else {
                    posSideRow.style.display = 'none';
                }
            }
        })();
        if(dom.orderForm) dom.orderForm.addEventListener('submit', handlers.handleOrderSubmit);
        // Trade Details listener
        handlers.initTradeDetails();
        // Connect WebSocket
        ws.connect();
    };

    // Run the app
    init();

    // 行情区点击币对切换交易区产品
    dom.marketListContent.addEventListener('click', (e) => {
        const row = e.target.closest('.market-row');
        if (row) {
            const instId = row.getAttribute('data-inst-id');
            // 尝试从ticker里获取instType
            let instType = 'SWAP';
            if (state.tickers[instId] && state.tickers[instId].instType) {
                instType = state.tickers[instId].instType;
            }
            tradeState.instId = instId;
            tradeState.instType = instType;
            refreshTradeInfo();
        }
    });
}); 