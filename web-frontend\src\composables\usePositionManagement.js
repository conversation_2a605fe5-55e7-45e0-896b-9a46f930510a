import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 仓位管理组合式函数
 * 提供交易仓位的创建、更新、关闭和风险控制功能
 */
export function usePositionManagement() {
  // 仓位列表
  const positions = ref([])
  const isLoading = ref(false)
  const isUpdating = ref(false)

  // 仓位统计
  const totalPositions = computed(() => positions.value.length)
  const openPositions = computed(() => positions.value.filter(p => p.status === 'open'))
  const closedPositions = computed(() => positions.value.filter(p => p.status === 'closed'))
  
  const totalUnrealizedPnL = computed(() => {
    return openPositions.value.reduce((total, position) => {
      return total + (position.unrealizedPnL || 0)
    }, 0)
  })
  
  const totalRealizedPnL = computed(() => {
    return positions.value.reduce((total, position) => {
      return total + (position.realizedPnL || 0)
    }, 0)
  })
  
  const totalMargin = computed(() => {
    return openPositions.value.reduce((total, position) => {
      return total + (position.margin || 0)
    }, 0)
  })

  const longPositions = computed(() => {
    return openPositions.value.filter(p => p.side === 'long')
  })
  
  const shortPositions = computed(() => {
    return openPositions.value.filter(p => p.side === 'short')
  })

  /**
   * 添加新仓位
   * @param {Object} positionData - 仓位数据
   * @returns {Promise<Object>} 操作结果
   */
  async function addPosition(positionData) {
    try {
      isLoading.value = true

      // 验证仓位数据
      const validatedData = validatePositionData(positionData)
      
      // 检查风险限制
      const riskCheck = await checkPositionRisk(validatedData)
      if (!riskCheck.allowed) {
        return {
          success: false,
          reason: riskCheck.reason,
          riskLevel: riskCheck.riskLevel
        }
      }

      // 创建仓位对象
      const position = {
        id: generatePositionId(),
        ...validatedData,
        status: 'open',
        openTime: new Date(),
        updateTime: new Date(),
        unrealizedPnL: 0,
        realizedPnL: 0,
        fees: 0,
        risk: calculatePositionRisk(validatedData),
        margin: calculateMargin(validatedData),
        liquidationPrice: calculateLiquidationPrice(validatedData),
        roi: 0,
        holdingTime: 0
      }

      // 添加到仓位列表
      positions.value.push(position)
      
      message.success(`成功开仓 ${position.symbol} ${position.side === 'long' ? '多头' : '空头'}`)
      
      return {
        success: true,
        position,
        positionId: position.id
      }
    } catch (error) {
      console.error('添加仓位失败:', error)
      message.error('开仓失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 更新仓位信息
   * @param {string} positionId - 仓位ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 操作结果
   */
  async function updatePosition(positionId, updateData) {
    try {
      isUpdating.value = true

      const position = positions.value.find(p => p.id === positionId)
      if (!position) {
        throw new Error('仓位不存在')
      }

      if (position.status === 'closed') {
        throw new Error('无法更新已关闭的仓位')
      }

      // 更新仓位数据
      const updatedPosition = {
        ...position,
        ...updateData,
        updateTime: new Date()
      }

      // 重新计算相关指标
      if (updateData.currentPrice) {
        updatedPosition.unrealizedPnL = calculateUnrealizedPnL(updatedPosition)
        updatedPosition.roi = calculateROI(updatedPosition)
        updatedPosition.holdingTime = Date.now() - new Date(updatedPosition.openTime).getTime()
      }

      // 更新仓位
      const index = positions.value.findIndex(p => p.id === positionId)
      positions.value[index] = updatedPosition

      return {
        success: true,
        position: updatedPosition
      }
    } catch (error) {
      console.error('更新仓位失败:', error)
      message.error('更新仓位失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    } finally {
      isUpdating.value = false
    }
  }

  /**
   * 关闭仓位
   * @param {string} positionId - 仓位ID
   * @param {number} closePrice - 平仓价格
   * @param {string} reason - 关闭原因
   * @returns {Promise<Object>} 操作结果
   */
  async function closePosition(positionId, closePrice, reason = 'manual') {
    try {
      const position = positions.value.find(p => p.id === positionId)
      if (!position) {
        throw new Error('仓位不存在')
      }

      if (position.status === 'closed') {
        throw new Error('仓位已关闭')
      }

      // 计算平仓盈亏
      const realizedPnL = calculateRealizedPnL(position, closePrice)
      const fees = calculateCloseFees(position, closePrice)
      const netPnL = realizedPnL - fees

      // 更新仓位状态
      const closedPosition = {
        ...position,
        status: 'closed',
        closePrice,
        closeTime: new Date(),
        closeReason: reason,
        realizedPnL: netPnL,
        fees: position.fees + fees,
        roi: calculateFinalROI(position, closePrice, fees),
        holdingTime: Date.now() - new Date(position.openTime).getTime()
      }

      // 更新仓位
      const index = positions.value.findIndex(p => p.id === positionId)
      positions.value[index] = closedPosition

      const pnlText = netPnL >= 0 ? `盈利 ${netPnL.toFixed(2)}` : `亏损 ${Math.abs(netPnL).toFixed(2)}`
      message.success(`成功平仓 ${position.symbol}，${pnlText}`)

      return {
        success: true,
        position: closedPosition,
        realizedPnL: netPnL
      }
    } catch (error) {
      console.error('关闭仓位失败:', error)
      message.error('平仓失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 设置止损价格
   * @param {string} positionId - 仓位ID
   * @param {number} stopLossPrice - 止损价格
   * @returns {Promise<Object>} 操作结果
   */
  async function setStopLoss(positionId, stopLossPrice) {
    try {
      const position = positions.value.find(p => p.id === positionId)
      if (!position) {
        throw new Error('仓位不存在')
      }

      if (position.status !== 'open') {
        throw new Error('只能为开仓状态的仓位设置止损')
      }

      // 验证止损价格
      const validation = validateStopLossPrice(position, stopLossPrice)
      if (!validation.valid) {
        throw new Error(validation.reason)
      }

      // 更新止损价格
      await updatePosition(positionId, {
        stopLoss: stopLossPrice,
        stopLossType: 'manual'
      })

      message.success(`已设置止损价格: ${stopLossPrice}`)
      
      return {
        success: true,
        stopLossPrice
      }
    } catch (error) {
      console.error('设置止损失败:', error)
      message.error('设置止损失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 设置止盈价格
   * @param {string} positionId - 仓位ID
   * @param {number} takeProfitPrice - 止盈价格
   * @returns {Promise<Object>} 操作结果
   */
  async function setTakeProfit(positionId, takeProfitPrice) {
    try {
      const position = positions.value.find(p => p.id === positionId)
      if (!position) {
        throw new Error('仓位不存在')
      }

      if (position.status !== 'open') {
        throw new Error('只能为开仓状态的仓位设置止盈')
      }

      // 验证止盈价格
      const validation = validateTakeProfitPrice(position, takeProfitPrice)
      if (!validation.valid) {
        throw new Error(validation.reason)
      }

      // 更新止盈价格
      await updatePosition(positionId, {
        takeProfit: takeProfitPrice,
        takeProfitType: 'manual'
      })

      message.success(`已设置止盈价格: ${takeProfitPrice}`)
      
      return {
        success: true,
        takeProfitPrice
      }
    } catch (error) {
      console.error('设置止盈失败:', error)
      message.error('设置止盈失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 批量设置止损
   * @param {number} stopLossPercentage - 止损百分比
   * @param {Array} positionIds - 仓位ID列表（可选）
   * @returns {Promise<Object>} 操作结果
   */
  async function batchSetStopLoss(stopLossPercentage, positionIds = null) {
    try {
      const targetPositions = positionIds 
        ? positions.value.filter(p => positionIds.includes(p.id) && p.status === 'open')
        : openPositions.value

      if (targetPositions.length === 0) {
        throw new Error('没有可设置止损的仓位')
      }

      const results = []
      for (const position of targetPositions) {
        const stopLossPrice = calculateStopLossPrice(position, stopLossPercentage)
        const result = await setStopLoss(position.id, stopLossPrice)
        results.push({
          positionId: position.id,
          symbol: position.symbol,
          ...result
        })
      }

      const successCount = results.filter(r => r.success).length
      message.success(`成功为 ${successCount} 个仓位设置止损`)

      return {
        success: true,
        results,
        successCount
      }
    } catch (error) {
      console.error('批量设置止损失败:', error)
      message.error('批量设置止损失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 自动调整止损位
   * @param {string} positionId - 仓位ID
   * @param {number} trailingPercentage - 跟踪止损百分比
   * @returns {Promise<Object>} 操作结果
   */
  async function adjustTrailingStopLoss(positionId, trailingPercentage = 0.05) {
    try {
      const position = positions.value.find(p => p.id === positionId)
      if (!position) {
        throw new Error('仓位不存在')
      }

      if (position.status !== 'open') {
        return { success: false, reason: '仓位已关闭' }
      }

      const currentPrice = position.currentPrice
      const entryPrice = position.entryPrice
      const currentStopLoss = position.stopLoss

      let newStopLoss
      if (position.side === 'long') {
        // 多头：价格上涨时向上调整止损
        if (currentPrice > entryPrice) {
          newStopLoss = currentPrice * (1 - trailingPercentage)
          if (!currentStopLoss || newStopLoss > currentStopLoss) {
            await setStopLoss(positionId, newStopLoss)
            return { success: true, newStopLoss, adjusted: true }
          }
        }
      } else {
        // 空头：价格下跌时向下调整止损
        if (currentPrice < entryPrice) {
          newStopLoss = currentPrice * (1 + trailingPercentage)
          if (!currentStopLoss || newStopLoss < currentStopLoss) {
            await setStopLoss(positionId, newStopLoss)
            return { success: true, newStopLoss, adjusted: true }
          }
        }
      }

      return { success: true, adjusted: false }
    } catch (error) {
      console.error('调整跟踪止损失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 检查止损止盈触发
   * @param {Object} priceData - 价格数据
   */
  function checkStopLossTakeProfit(priceData) {
    openPositions.value.forEach(async (position) => {
      const currentPrice = priceData[position.symbol]
      if (!currentPrice) return

      // 更新当前价格
      await updatePosition(position.id, { currentPrice })

      // 检查止损触发
      if (position.stopLoss) {
        const shouldTriggerStopLoss = 
          (position.side === 'long' && currentPrice <= position.stopLoss) ||
          (position.side === 'short' && currentPrice >= position.stopLoss)

        if (shouldTriggerStopLoss) {
          await closePosition(position.id, currentPrice, 'stop-loss')
          return
        }
      }

      // 检查止盈触发
      if (position.takeProfit) {
        const shouldTriggerTakeProfit = 
          (position.side === 'long' && currentPrice >= position.takeProfit) ||
          (position.side === 'short' && currentPrice <= position.takeProfit)

        if (shouldTriggerTakeProfit) {
          await closePosition(position.id, currentPrice, 'take-profit')
          return
        }
      }

      // 自动调整跟踪止损
      if (position.trailingStopLoss) {
        await adjustTrailingStopLoss(position.id, position.trailingPercentage || 0.05)
      }
    })
  }

  /**
   * 获取指定交易对的仓位
   * @param {string} symbol - 交易对
   * @returns {Array} 仓位列表
   */
  function getPositionsBySymbol(symbol) {
    return positions.value.filter(p => p.symbol === symbol)
  }

  /**
   * 计算仓位风险
   * @param {Object} position - 仓位数据
   * @returns {number} 风险值
   */
  function calculatePositionRisk(position) {
    const leverage = position.leverage || 1
    const size = Math.abs(position.size)
    const price = position.entryPrice || position.currentPrice
    
    // 基础风险 = 仓位价值 * 杠杆 / 总资金
    const positionValue = size * price
    const leveragedValue = positionValue * leverage
    
    // 简化计算，实际应该基于总资金
    const totalCapital = 100000 // 假设总资金
    const baseRisk = leveragedValue / totalCapital
    
    // 考虑波动率风险
    const volatilityMultiplier = 1 + (position.volatility || 0.2)
    
    return Math.min(baseRisk * volatilityMultiplier, 1)
  }

  /**
   * 计算保证金
   * @param {Object} position - 仓位数据
   * @returns {number} 保证金金额
   */
  function calculateMargin(position) {
    const leverage = position.leverage || 1
    const size = Math.abs(position.size)
    const price = position.entryPrice || position.currentPrice
    
    return (size * price) / leverage
  }

  /**
   * 计算强平价格
   * @param {Object} position - 仓位数据
   * @returns {number} 强平价格
   */
  function calculateLiquidationPrice(position) {
    const leverage = position.leverage || 1
    const entryPrice = position.entryPrice
    const maintenanceMarginRate = 0.005 // 维持保证金率0.5%
    
    if (position.side === 'long') {
      return entryPrice * (1 - (1 / leverage) + maintenanceMarginRate)
    } else {
      return entryPrice * (1 + (1 / leverage) - maintenanceMarginRate)
    }
  }

  /**
   * 计算未实现盈亏
   * @param {Object} position - 仓位数据
   * @returns {number} 未实现盈亏
   */
  function calculateUnrealizedPnL(position) {
    const size = position.size
    const entryPrice = position.entryPrice
    const currentPrice = position.currentPrice
    
    if (!currentPrice) return 0
    
    if (position.side === 'long') {
      return size * (currentPrice - entryPrice)
    } else {
      return size * (entryPrice - currentPrice)
    }
  }

  /**
   * 计算已实现盈亏
   * @param {Object} position - 仓位数据
   * @param {number} closePrice - 平仓价格
   * @returns {number} 已实现盈亏
   */
  function calculateRealizedPnL(position, closePrice) {
    const size = position.size
    const entryPrice = position.entryPrice
    
    if (position.side === 'long') {
      return size * (closePrice - entryPrice)
    } else {
      return size * (entryPrice - closePrice)
    }
  }

  /**
   * 计算ROI
   * @param {Object} position - 仓位数据
   * @returns {number} ROI百分比
   */
  function calculateROI(position) {
    const margin = position.margin || calculateMargin(position)
    const unrealizedPnL = position.unrealizedPnL || calculateUnrealizedPnL(position)
    
    if (margin === 0) return 0
    return (unrealizedPnL / margin) * 100
  }

  /**
   * 计算最终ROI（包含手续费）
   * @param {Object} position - 仓位数据
   * @param {number} closePrice - 平仓价格
   * @param {number} fees - 手续费
   * @returns {number} 最终ROI百分比
   */
  function calculateFinalROI(position, closePrice, fees) {
    const margin = position.margin || calculateMargin(position)
    const realizedPnL = calculateRealizedPnL(position, closePrice)
    const netPnL = realizedPnL - fees
    
    if (margin === 0) return 0
    return (netPnL / margin) * 100
  }

  /**
   * 计算平仓手续费
   * @param {Object} position - 仓位数据
   * @param {number} closePrice - 平仓价格
   * @returns {number} 手续费
   */
  function calculateCloseFees(position, closePrice) {
    const feeRate = 0.001 // 0.1%手续费率
    const size = Math.abs(position.size)
    return size * closePrice * feeRate
  }

  /**
   * 计算止损价格
   * @param {Object} position - 仓位数据
   * @param {number} percentage - 止损百分比
   * @returns {number} 止损价格
   */
  function calculateStopLossPrice(position, percentage) {
    const entryPrice = position.entryPrice
    
    if (position.side === 'long') {
      return entryPrice * (1 - percentage)
    } else {
      return entryPrice * (1 + percentage)
    }
  }

  /**
   * 验证仓位数据
   * @param {Object} positionData - 仓位数据
   * @returns {Object} 验证后的数据
   */
  function validatePositionData(positionData) {
    const required = ['symbol', 'side', 'size', 'entryPrice']
    
    for (const field of required) {
      if (!positionData[field]) {
        throw new Error(`缺少必要字段: ${field}`)
      }
    }

    if (!['long', 'short'].includes(positionData.side)) {
      throw new Error('仓位方向必须是 long 或 short')
    }

    if (positionData.size <= 0) {
      throw new Error('仓位大小必须大于0')
    }

    if (positionData.entryPrice <= 0) {
      throw new Error('入场价格必须大于0')
    }

    if (positionData.leverage && (positionData.leverage < 1 || positionData.leverage > 100)) {
      throw new Error('杠杆倍数必须在1-100之间')
    }

    return {
      ...positionData,
      leverage: positionData.leverage || 1,
      currentPrice: positionData.currentPrice || positionData.entryPrice
    }
  }

  /**
   * 检查仓位风险
   * @param {Object} positionData - 仓位数据
   * @returns {Promise<Object>} 风险检查结果
   */
  async function checkPositionRisk(positionData) {
    // 检查最大开仓数量
    if (openPositions.value.length >= 10) { // 假设最大10个仓位
      return {
        allowed: false,
        reason: '已达到最大开仓数量限制',
        riskLevel: 'high'
      }
    }

    // 检查单仓位风险
    const positionRisk = calculatePositionRisk(positionData)
    if (positionRisk > 0.2) { // 单仓位风险不超过20%
      return {
        allowed: false,
        reason: '单仓位风险过高',
        riskLevel: 'high'
      }
    }

    // 检查同向仓位集中度
    const sameDirectionPositions = openPositions.value.filter(p => p.side === positionData.side)
    const sameDirectionRisk = sameDirectionPositions.reduce((total, p) => total + p.risk, 0)
    
    if (sameDirectionRisk + positionRisk > 0.6) { // 同向仓位总风险不超过60%
      return {
        allowed: false,
        reason: '同向仓位风险过于集中',
        riskLevel: 'medium'
      }
    }

    return {
      allowed: true,
      riskLevel: 'low'
    }
  }

  /**
   * 验证止损价格
   * @param {Object} position - 仓位数据
   * @param {number} stopLossPrice - 止损价格
   * @returns {Object} 验证结果
   */
  function validateStopLossPrice(position, stopLossPrice) {
    if (stopLossPrice <= 0) {
      return { valid: false, reason: '止损价格必须大于0' }
    }

    const entryPrice = position.entryPrice
    
    if (position.side === 'long') {
      if (stopLossPrice >= entryPrice) {
        return { valid: false, reason: '多头止损价格必须低于入场价格' }
      }
      
      const lossPercentage = (entryPrice - stopLossPrice) / entryPrice
      if (lossPercentage > 0.5) {
        return { valid: false, reason: '止损幅度不能超过50%' }
      }
    } else {
      if (stopLossPrice <= entryPrice) {
        return { valid: false, reason: '空头止损价格必须高于入场价格' }
      }
      
      const lossPercentage = (stopLossPrice - entryPrice) / entryPrice
      if (lossPercentage > 0.5) {
        return { valid: false, reason: '止损幅度不能超过50%' }
      }
    }

    return { valid: true }
  }

  /**
   * 验证止盈价格
   * @param {Object} position - 仓位数据
   * @param {number} takeProfitPrice - 止盈价格
   * @returns {Object} 验证结果
   */
  function validateTakeProfitPrice(position, takeProfitPrice) {
    if (takeProfitPrice <= 0) {
      return { valid: false, reason: '止盈价格必须大于0' }
    }

    const entryPrice = position.entryPrice
    
    if (position.side === 'long') {
      if (takeProfitPrice <= entryPrice) {
        return { valid: false, reason: '多头止盈价格必须高于入场价格' }
      }
    } else {
      if (takeProfitPrice >= entryPrice) {
        return { valid: false, reason: '空头止盈价格必须低于入场价格' }
      }
    }

    return { valid: true }
  }

  /**
   * 生成仓位ID
   * @returns {string} 仓位ID
   */
  function generatePositionId() {
    return `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 导出仓位数据
   */
  function exportPositions() {
    const data = {
      timestamp: new Date().toISOString(),
      positions: positions.value,
      summary: {
        totalPositions: totalPositions.value,
        openPositions: openPositions.value.length,
        totalUnrealizedPnL: totalUnrealizedPnL.value,
        totalRealizedPnL: totalRealizedPnL.value,
        totalMargin: totalMargin.value
      }
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `positions-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    
    URL.revokeObjectURL(url)
    message.success('仓位数据已导出')
  }

  // 监听仓位变化，自动更新统计信息
  watch(
    () => positions.value,
    () => {
      // 可以在这里添加自动保存逻辑
    },
    { deep: true }
  )

  return {
    // 状态
    positions,
    isLoading,
    isUpdating,
    
    // 计算属性
    totalPositions,
    openPositions,
    closedPositions,
    totalUnrealizedPnL,
    totalRealizedPnL,
    totalMargin,
    longPositions,
    shortPositions,
    
    // 方法
    addPosition,
    updatePosition,
    closePosition,
    setStopLoss,
    setTakeProfit,
    batchSetStopLoss,
    adjustTrailingStopLoss,
    checkStopLossTakeProfit,
    getPositionsBySymbol,
    exportPositions,
    
    // 计算方法
    calculatePositionRisk,
    calculateMargin,
    calculateLiquidationPrice,
    calculateUnrealizedPnL,
    calculateRealizedPnL,
    calculateROI,
    calculateStopLossPrice,
    
    // 验证方法
    validatePositionData,
    validateStopLossPrice,
    validateTakeProfitPrice
  }
}