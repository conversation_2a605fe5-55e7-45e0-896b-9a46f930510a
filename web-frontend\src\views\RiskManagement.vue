<template>
  <div class="risk-management">
    <a-row :gutter="24">
      <!-- 左侧：风险设置 -->
      <a-col :span="12">
        <a-card title="风险管理设置" style="margin-bottom: 24px;">
          <a-form
            :model="riskSettings"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
            @finish="saveRiskSettings"
          >
            <a-form-item
              label="最大仓位比例"
              name="maxPositionRatio"
              :rules="[{ required: true, message: '请输入最大仓位比例' }]"
            >
              <a-slider
                v-model:value="riskSettings.maxPositionRatio"
                :min="1"
                :max="100"
                :marks="{ 10: '10%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }"
              />
              <div style="text-align: center; margin-top: 8px;">
                {{ riskSettings.maxPositionRatio }}%
              </div>
            </a-form-item>
            
            <a-form-item
              label="止损比例"
              name="stopLossRatio"
              :rules="[{ required: true, message: '请输入止损比例' }]"
            >
              <a-slider
                v-model:value="riskSettings.stopLossRatio"
                :min="1"
                :max="20"
                :marks="{ 2: '2%', 5: '5%', 10: '10%', 15: '15%', 20: '20%' }"
              />
              <div style="text-align: center; margin-top: 8px;">
                {{ riskSettings.stopLossRatio }}%
              </div>
            </a-form-item>
            
            <a-form-item
              label="止盈比例"
              name="takeProfitRatio"
              :rules="[{ required: true, message: '请输入止盈比例' }]"
            >
              <a-slider
                v-model:value="riskSettings.takeProfitRatio"
                :min="5"
                :max="50"
                :marks="{ 10: '10%', 20: '20%', 30: '30%', 40: '40%', 50: '50%' }"
              />
              <div style="text-align: center; margin-top: 8px;">
                {{ riskSettings.takeProfitRatio }}%
              </div>
            </a-form-item>
            
            <a-form-item
              label="最大杠杆倍数"
              name="maxLeverage"
            >
              <a-select v-model:value="riskSettings.maxLeverage">
                <a-select-option :value="1">1x (现货)</a-select-option>
                <a-select-option :value="2">2x</a-select-option>
                <a-select-option :value="3">3x</a-select-option>
                <a-select-option :value="5">5x</a-select-option>
                <a-select-option :value="10">10x</a-select-option>
                <a-select-option :value="20">20x</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item
              label="日最大亏损"
              name="dailyMaxLoss"
            >
              <a-input-number
                v-model:value="riskSettings.dailyMaxLoss"
                placeholder="日最大亏损金额(USDT)"
                style="width: 100%"
                :min="0"
              />
            </a-form-item>
            
            <a-form-item
              label="启用自动止损"
              name="autoStopLoss"
            >
              <a-switch v-model:checked="riskSettings.autoStopLoss" />
            </a-form-item>
            
            <a-form-item
              label="启用自动止盈"
              name="autoTakeProfit"
            >
              <a-switch v-model:checked="riskSettings.autoTakeProfit" />
            </a-form-item>
            
            <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
              <a-space>
                <a-button type="primary" html-type="submit" :loading="saving">
                  保存设置
                </a-button>
                <a-button @click="resetSettings">重置</a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>
        
        <!-- 风险监控规则 -->
        <a-card title="风险监控规则">
          <a-list
            :dataSource="riskRules"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>{{ item.name }}</template>
                  <template #description>{{ item.description }}</template>
                </a-list-item-meta>
                <template #actions>
                  <a-switch
                    v-model:checked="item.enabled"
                    @change="updateRiskRule(item)"
                  />
                </template>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
      
      <!-- 右侧：风险监控 -->
      <a-col :span="12">
        <!-- 风险指标 -->
        <a-card title="当前风险指标" style="margin-bottom: 24px;">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-statistic
                title="总资产(USDT)"
                :value="riskMetrics.totalAssets"
                :precision="2"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :span="12">
              <a-statistic
                title="可用余额(USDT)"
                :value="riskMetrics.availableBalance"
                :precision="2"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
          </a-row>
          
          <a-divider />
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-statistic
                title="持仓价值(USDT)"
                :value="riskMetrics.positionValue"
                :precision="2"
                :value-style="{ color: '#faad14' }"
              />
            </a-col>
            <a-col :span="12">
              <a-statistic
                title="未实现盈亏(USDT)"
                :value="riskMetrics.unrealizedPnl"
                :precision="2"
                :value-style="{ color: riskMetrics.unrealizedPnl >= 0 ? '#52c41a' : '#ff4d4f' }"
              />
            </a-col>
          </a-row>
          
          <a-divider />
          
          <!-- 风险等级 -->
          <div style="text-align: center;">
            <div style="margin-bottom: 16px;">
              <span style="font-size: 16px; font-weight: bold;">当前风险等级</span>
            </div>
            <a-progress
              type="circle"
              :percent="riskMetrics.riskLevel"
              :stroke-color="getRiskColor(riskMetrics.riskLevel)"
              :format="percent => getRiskLabel(percent)"
            />
          </div>
        </a-card>
        
        <!-- 风险警告 -->
        <a-card title="风险警告" v-if="riskWarnings.length > 0">
          <a-list
            :dataSource="riskWarnings"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-alert
                  :message="item.title"
                  :description="item.description"
                  :type="item.type"
                  show-icon
                  style="width: 100%"
                />
              </a-list-item>
            </template>
          </a-list>
        </a-card>
        
        <!-- 今日交易统计 -->
        <a-card title="今日交易统计" style="margin-top: 24px;">
          <a-descriptions bordered size="small">
            <a-descriptions-item label="交易次数">{{ dailyStats.tradeCount }}</a-descriptions-item>
            <a-descriptions-item label="胜率">{{ dailyStats.winRate }}%</a-descriptions-item>
            <a-descriptions-item label="今日盈亏">
              <span :style="{ color: dailyStats.dailyPnl >= 0 ? '#52c41a' : '#ff4d4f' }">
                {{ dailyStats.dailyPnl >= 0 ? '+' : '' }}{{ dailyStats.dailyPnl }} USDT
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="最大回撤">{{ dailyStats.maxDrawdown }}%</a-descriptions-item>
            <a-descriptions-item label="平均持仓时间">{{ dailyStats.avgHoldTime }}</a-descriptions-item>
            <a-descriptions-item label="风险调整收益">{{ dailyStats.sharpeRatio }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import axios from 'axios'

// 响应式数据
const saving = ref(false)

// 风险设置
const riskSettings = ref({
  maxPositionRatio: 25,
  stopLossRatio: 5,
  takeProfitRatio: 15,
  maxLeverage: 3,
  dailyMaxLoss: 1000,
  autoStopLoss: true,
  autoTakeProfit: true
})

// 风险监控规则
const riskRules = ref([
  {
    id: 1,
    name: '仓位过重警告',
    description: '当单个仓位超过总资产的设定比例时发出警告',
    enabled: true
  },
  {
    id: 2,
    name: '连续亏损警告',
    description: '当连续亏损次数超过3次时发出警告',
    enabled: true
  },
  {
    id: 3,
    name: '日亏损限制',
    description: '当日亏损达到设定金额时停止交易',
    enabled: true
  },
  {
    id: 4,
    name: '杠杆过高警告',
    description: '当使用杠杆超过设定倍数时发出警告',
    enabled: true
  },
  {
    id: 5,
    name: '市场波动警告',
    description: '当市场波动率异常时发出警告',
    enabled: false
  }
])

// 风险指标
const riskMetrics = ref({
  totalAssets: 10000,
  availableBalance: 7500,
  positionValue: 2500,
  unrealizedPnl: 150,
  riskLevel: 35
})

// 风险警告
const riskWarnings = ref([
  {
    title: '仓位风险提醒',
    description: 'BTC-USDT-SWAP 仓位占总资产比例已达到20%，接近设定的25%上限',
    type: 'warning'
  }
])

// 今日交易统计
const dailyStats = ref({
  tradeCount: 8,
  winRate: 62.5,
  dailyPnl: 245.50,
  maxDrawdown: 3.2,
  avgHoldTime: '2小时15分',
  sharpeRatio: 1.85
})

// 获取风险颜色
const getRiskColor = (level) => {
  if (level < 30) return '#52c41a'
  if (level < 60) return '#faad14'
  if (level < 80) return '#ff7a45'
  return '#ff4d4f'
}

// 获取风险标签
const getRiskLabel = (percent) => {
  if (percent < 30) return '低风险'
  if (percent < 60) return '中风险'
  if (percent < 80) return '高风险'
  return '极高风险'
}

// 保存风险设置
const saveRiskSettings = async () => {
  saving.value = true
  try {
    const response = await axios.post('/api/user/risk-settings', riskSettings.value)
    
    if (response.data.code === 0) {
      message.success('风险设置保存成功')
      updateRiskMetrics()
    } else {
      message.error(response.data.msg || '保存失败')
    }
  } catch (error) {
    message.error('保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

// 重置设置
const resetSettings = () => {
  riskSettings.value = {
    maxPositionRatio: 25,
    stopLossRatio: 5,
    takeProfitRatio: 15,
    maxLeverage: 3,
    dailyMaxLoss: 1000,
    autoStopLoss: true,
    autoTakeProfit: true
  }
  message.info('设置已重置')
}

// 更新风险规则
const updateRiskRule = async (rule) => {
  try {
    const response = await axios.put(`/api/user/risk-rules/${rule.id}`, {
      enabled: rule.enabled
    })
    
    if (response.data.code === 0) {
      message.success(`${rule.name} ${rule.enabled ? '已启用' : '已禁用'}`)
    } else {
      message.error('更新失败')
      rule.enabled = !rule.enabled // 回滚状态
    }
  } catch (error) {
    message.error('更新失败: ' + error.message)
    rule.enabled = !rule.enabled // 回滚状态
  }
}

// 加载风险设置
const loadRiskSettings = async () => {
  try {
    const response = await axios.get('/api/user/risk-settings')
    if (response.data.code === 0 && response.data.data) {
      riskSettings.value = { ...riskSettings.value, ...response.data.data }
    }
  } catch (error) {
    console.warn('加载风险设置失败:', error.message)
  }
}

// 更新风险指标
const updateRiskMetrics = async () => {
  try {
    // 获取账户余额
    const balanceResponse = await axios.get('/api/okx/account/balance')
    if (balanceResponse.data.code === 0) {
      const balanceData = balanceResponse.data.data[0]
      if (balanceData) {
        riskMetrics.value.totalAssets = parseFloat(balanceData.totalEq || 0)
        riskMetrics.value.availableBalance = parseFloat(balanceData.availEq || 0)
      }
    }
    
    // 获取持仓信息
    const positionsResponse = await axios.get('/api/okx/account/positions')
    if (positionsResponse.data.code === 0) {
      const positions = positionsResponse.data.data
      let totalPositionValue = 0
      let totalUnrealizedPnl = 0
      
      positions.forEach(position => {
        if (parseFloat(position.pos) !== 0) {
          totalPositionValue += Math.abs(parseFloat(position.notionalUsd || 0))
          totalUnrealizedPnl += parseFloat(position.upl || 0)
        }
      })
      
      riskMetrics.value.positionValue = totalPositionValue
      riskMetrics.value.unrealizedPnl = totalUnrealizedPnl
      
      // 计算风险等级
      const positionRatio = (totalPositionValue / riskMetrics.value.totalAssets) * 100
      let riskLevel = Math.min(positionRatio * 2, 100) // 简化的风险计算
      
      // 根据未实现盈亏调整风险等级
      if (totalUnrealizedPnl < 0) {
        const lossRatio = Math.abs(totalUnrealizedPnl) / riskMetrics.value.totalAssets
        riskLevel += lossRatio * 100
      }
      
      riskMetrics.value.riskLevel = Math.min(Math.max(riskLevel, 0), 100)
    }
    
    // 更新风险警告
    updateRiskWarnings()
    
  } catch (error) {
    console.warn('更新风险指标失败:', error.message)
  }
}

// 更新风险警告
const updateRiskWarnings = () => {
  const warnings = []
  
  // 检查仓位比例
  const positionRatio = (riskMetrics.value.positionValue / riskMetrics.value.totalAssets) * 100
  if (positionRatio > riskSettings.value.maxPositionRatio * 0.8) {
    warnings.push({
      title: '仓位风险提醒',
      description: `当前仓位占总资产比例为${positionRatio.toFixed(1)}%，接近设定的${riskSettings.value.maxPositionRatio}%上限`,
      type: 'warning'
    })
  }
  
  // 检查未实现亏损
  if (riskMetrics.value.unrealizedPnl < -riskSettings.value.dailyMaxLoss * 0.5) {
    warnings.push({
      title: '亏损警告',
      description: `当前未实现亏损为${riskMetrics.value.unrealizedPnl.toFixed(2)} USDT，已达到日最大亏损限制的50%`,
      type: 'error'
    })
  }
  
  // 检查风险等级
  if (riskMetrics.value.riskLevel > 70) {
    warnings.push({
      title: '高风险警告',
      description: '当前风险等级较高，建议减少仓位或设置更严格的止损',
      type: 'error'
    })
  }
  
  riskWarnings.value = warnings
}

// 加载今日交易统计
const loadDailyStats = async () => {
  try {
    const response = await axios.get('/api/user/daily-stats')
    if (response.data.code === 0 && response.data.data) {
      dailyStats.value = { ...dailyStats.value, ...response.data.data }
    }
  } catch (error) {
    console.warn('加载今日统计失败:', error.message)
  }
}

// 页面加载时初始化
onMounted(async () => {
  await loadRiskSettings()
  await updateRiskMetrics()
  await loadDailyStats()
})
</script>

<style scoped>
.risk-management {
  padding: 24px;
}

.ant-slider {
  margin-bottom: 0;
}

.ant-progress-circle {
  margin: 16px 0;
}
</style>
