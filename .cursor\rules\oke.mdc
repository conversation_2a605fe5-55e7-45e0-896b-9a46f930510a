---
description: 
globs: 
alwaysApply: false
---
Project rules（项目规则）一般用于团队协作、开源项目或公司内部项目，目的是统一开发规范、流程和行为准则，提高协作效率，减少沟通成本。下面给出一个适用于你当前项目（前端 Vue3+Vite+Ant Design Vue，后端 FastAPI+SQLAlchemy+SQLite，AI分析和OKX API对接）的 project rules 示例模板，并附有中文说明：

---

# 项目规则（Project Rules）

## 1. 代码规范

- 前端代码遵循 [Vue3 官方风格指南](https://cn.vuejs.org/style-guide/)。
- 后端代码遵循 [PEP8](https://peps.python.org/pep-0008/) Python 编码规范。
- 统一使用 4 个空格缩进，不允许使用 Tab。
- 变量、函数、类命名需语义清晰，禁止拼音、缩写（常用缩写除外）。

## 2. 分支管理

- 主分支（master/main）仅用于发布稳定版本。
- 功能开发请在 feature/xxx 分支进行，bug 修复请在 fix/xxx 分支进行。
- 合并代码前需通过代码评审（Pull Request）。

## 3. 提交信息

- 提交信息需简明扼要，建议使用英文（如需中文请保持简洁）。
- 格式建议：`[类型] 简要描述`，如 `[fix] 修复登录接口异常`。

## 4. 依赖管理

- 前端依赖通过 `package.json` 统一管理，禁止私自修改 lock 文件。
- 后端依赖通过 `requirements.txt` 或 `pyproject.toml` 统一管理。

## 5. 代码审查

- 所有合并到主分支的代码必须经过至少一名成员的 Review。
- 发现安全隐患、性能问题、可读性差的代码需及时提出并修正。

## 6. 测试与部署

- 新功能需配套单元测试，测试文件放在 `test/` 目录下。
- 部署前需确保所有测试通过，无严重警告或报错。

## 7. 文档与注释

- 重要模块、复杂逻辑需有详细注释。
- 项目文档需及时更新，接口变更需同步更新文档。

## 8. 其他约定

- 禁止在代码中留有敏感信息（如 API Key、密码等）。
- 遵守公司/团队的保密协议和开源协议。

---

你可以将上述内容保存为 `PROJECT_RULES.md` 或 `CONTRIBUTING.md` 文件，放在项目根目录，便于团队成员查阅和遵守。

如需更详细或定制化的规则，请告知你的具体需求！