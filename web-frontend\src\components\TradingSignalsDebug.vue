<template>
  <div class="trading-signals-debug">
    <a-card title="交易信号调试信息" size="small">
      <!-- 原始数据显示 -->
      <div class="debug-section">
        <h4>原始数据</h4>
        <pre>{{ JSON.stringify(debugData, null, 2) }}</pre>
      </div>

      <!-- 计算结果显示 -->
      <div class="debug-section">
        <h4>计算结果</h4>
        <div class="signal-grid">
          <div class="signal-item">
            <span class="label">EMA交叉:</span>
            <a-tag :color="emaSignalColor">{{ emaSignalText }}</a-tag>
          </div>
          
          <div class="signal-item">
            <span class="label">EMA慢线:</span>
            <a-tag :color="emaSlowColor">{{ emaSlowText }}</a-tag>
            <span class="value">{{ emaSlowValue }}</span>
          </div>
          
          <div class="signal-item">
            <span class="label">MA快线:</span>
            <a-tag :color="maFastColor">{{ maFastText }}</a-tag>
            <span class="value">{{ maFastValue }}</span>
          </div>
          
          <div class="signal-item">
            <span class="label">RSI状态:</span>
            <a-tag :color="rsiColor">{{ rsiText }}</a-tag>
            <span class="value">{{ rsiValue }}</span>
          </div>
        </div>
      </div>

      <!-- 实时更新按钮 -->
      <div class="debug-section">
        <a-button @click="generateMockData" type="primary">生成模拟数据</a-button>
        <a-button @click="clearData" style="margin-left: 8px;">清空数据</a-button>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  technicalIndicators: {
    type: Object,
    default: () => ({})
  },
  currentPrice: {
    type: Object,
    default: () => null
  }
})

// 调试数据
const debugData = computed(() => ({
  technicalIndicators: props.technicalIndicators,
  currentPrice: props.currentPrice,
  timestamp: new Date().toLocaleString()
}))

// EMA信号计算
const emaSignalText = computed(() => {
  const ema5 = props.technicalIndicators.ema5
  const ema10 = props.technicalIndicators.ema10
  
  if (!ema5 || !ema10 || ema5 === 0 || ema10 === 0) {
    return '无数据'
  }
  
  return ema5 > ema10 ? '金叉' : '死叉'
})

const emaSignalColor = computed(() => {
  return emaSignalText.value === '金叉' ? 'green' : 
         emaSignalText.value === '死叉' ? 'red' : 'default'
})

// EMA慢线状态
const emaSlowText = computed(() => {
  const currentPrice = parseFloat(props.currentPrice?.last || 0)
  const ema20 = props.technicalIndicators.ema20 || 0
  
  if (!currentPrice || !ema20 || ema20 === 0) {
    return '无数据'
  }
  
  return currentPrice > ema20 ? '多头' : '空头'
})

const emaSlowColor = computed(() => {
  return emaSlowText.value === '多头' ? 'green' : 
         emaSlowText.value === '空头' ? 'red' : 'default'
})

const emaSlowValue = computed(() => {
  const ema20 = props.technicalIndicators.ema20 || 0
  return ema20 ? ema20.toFixed(2) : '-'
})

// MA快线状态
const maFastText = computed(() => {
  const currentPrice = parseFloat(props.currentPrice?.last || 0)
  const ma5 = props.technicalIndicators.ma5 || 0
  
  if (!currentPrice || !ma5 || ma5 === 0) {
    return '无数据'
  }
  
  return currentPrice > ma5 ? '多头' : '空头'
})

const maFastColor = computed(() => {
  return maFastText.value === '多头' ? 'green' : 
         maFastText.value === '空头' ? 'red' : 'default'
})

const maFastValue = computed(() => {
  const ma5 = props.technicalIndicators.ma5 || 0
  return ma5 ? ma5.toFixed(2) : '-'
})

// RSI状态
const rsiText = computed(() => {
  const rsi = props.technicalIndicators.rsi || 50
  
  if (rsi > 70) {
    return 'RSI超买'
  } else if (rsi < 30) {
    return 'RSI超卖'
  } else {
    return 'RSI中立'
  }
})

const rsiColor = computed(() => {
  return rsiText.value === 'RSI超买' ? 'orange' : 
         rsiText.value === 'RSI超卖' ? 'green' : 'blue'
})

const rsiValue = computed(() => {
  const rsi = props.technicalIndicators.rsi || 50
  return rsi.toFixed(2)
})

// 监控数据变化
watch(() => props.technicalIndicators, (newVal) => {
  console.log('调试组件收到技术指标:', newVal)
}, { deep: true, immediate: true })

watch(() => props.currentPrice, (newVal) => {
  console.log('调试组件收到价格:', newVal)
}, { immediate: true })

// 生成模拟数据（用于测试）
const emit = defineEmits(['update-data'])

const generateMockData = () => {
  const mockPrice = 50000 + (Math.random() - 0.5) * 10000
  
  const mockIndicators = {
    ma5: mockPrice * (0.98 + Math.random() * 0.04),
    ma10: mockPrice * (0.97 + Math.random() * 0.04),
    ma20: mockPrice * (0.96 + Math.random() * 0.04),
    ema5: mockPrice * (0.985 + Math.random() * 0.03),
    ema10: mockPrice * (0.975 + Math.random() * 0.03),
    ema20: mockPrice * (0.965 + Math.random() * 0.03),
    rsi: 30 + Math.random() * 40,
    macd: (Math.random() - 0.5) * 100
  }
  
  const mockCurrentPrice = {
    last: mockPrice.toString(),
    sodUtc8: ((Math.random() - 0.5) * 10).toFixed(2),
    high24h: (mockPrice * 1.05).toString(),
    low24h: (mockPrice * 0.95).toString()
  }
  
  emit('update-data', { indicators: mockIndicators, price: mockCurrentPrice })
}

const clearData = () => {
  emit('update-data', { indicators: {}, price: null })
}
</script>

<style scoped>
.trading-signals-debug {
  width: 100%;
}

.debug-section {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 4px;
}

.debug-section h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.debug-section pre {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.signal-grid {
  display: grid;
  gap: 8px;
}

.signal-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  min-width: 80px;
  font-size: 13px;
  color: #666;
}

.value {
  font-size: 12px;
  color: #999;
}
</style>
