[tool:pytest]
testpaths = test
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10
    --cov=webapp
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-fail-under=80
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    okx: marks tests as OKX API tests
    user: marks tests as user API tests
    websocket: marks tests as WebSocket tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning 