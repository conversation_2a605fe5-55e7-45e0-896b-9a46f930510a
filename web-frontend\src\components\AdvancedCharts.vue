<template>
  <div class="advanced-charts-container">
    <div ref="mainChartRef" class="chart-container"></div>
    <div ref="rsiChartRef" class="chart-container"></div>
    <div ref="macdChartRef" class="chart-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useChartCoordinator } from '@/composables/useChartCoordinator';

const props = defineProps({
  selectedSymbol: {
    type: String,
    required: true,
  },
  selectedTimeframe: {
    type: String,
    required: true,
  },
});

const mainChartRef = ref(null);
const rsiChartRef = ref(null);
const macdChartRef = ref(null);

const indicatorSettings = ref({
  ma: { periods: [5, 10, 20, 60], enabled: true },
  rsi: { period: 14, enabled: true },
  macd: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9, enabled: true },
  bollingerBands: { period: 20, stdDev: 2, enabled: true },
  kdj: { period: 9, kPeriod: 3, dPeriod: 3, enabled: true },
  supertrend: { period: 10, multiplier: 3, enabled: true },
  tradingSignals: { useRSI: true, useMACD: true, useSupertrend: false, rsi: { overbought: 70, oversold: 30 } },
});

const displaySettings = ref({
  showMA: true,
  showBollingerBands: true,
  showSupertrend: true,
  showTradingSignals: true,
});

const { 
  loadingChart, 
  chartData, 
  currentPrice, 
  technicalIndicators, 
  marketSentiment, 
  tradingSignals, 
  dataSource, 
  initializeCoordinator, 
  update: updateAllCharts, 
  dispose: disposeAllCharts 
} = useChartCoordinator({
  selectedSymbol: ref(props.selectedSymbol),
  selectedTimeframe: ref(props.selectedTimeframe),
  refs: { mainChartRef, rsiChartRef, macdChartRef },
  indicatorSettings,
  displaySettings,
});

onMounted(() => {
  initializeCoordinator();
});

onUnmounted(() => {
  disposeAllCharts();
});

watch(() => [props.selectedSymbol, props.selectedTimeframe], () => {
  initializeCoordinator();
});

watch(chartData, () => {
  updateAllCharts();
}, { deep: true });

</script>

<style scoped>
.advanced-charts-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.chart-container {
  width: 100%;
  flex: 1;
}
</style>