<template>
  <a-modal
    v-model:open="visible"
    title="高级图表设置"
    width="1200px"
    :footer="null"
    :resizable="true"
    :draggable="true"
    style="top: 20px;"
    @cancel="handleCancel"
  >
    <div class="settings-container">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 显示设置 -->
        <a-tab-pane key="display" tab="显示设置">
          <div class="settings-section">
            <h4>技术指标显示</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="6">
                <a-checkbox v-model:checked="localDisplaySettings.showMA">
                  移动平均线 (MA)
                </a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox v-model:checked="localDisplaySettings.showEMA">
                  指数移动平均线 (EMA)
                </a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox v-model:checked="localDisplaySettings.showBollingerBands">
                  布林带 (BOLL)
                </a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox v-model:checked="localDisplaySettings.showSupertrend">
                  超级趋势 (SuperTrend)
                </a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox v-model:checked="localDisplaySettings.showVolume">
                  成交量 (VOL)
                </a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox v-model:checked="localDisplaySettings.showRSI">
                  相对强弱指数 (RSI)
                </a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox v-model:checked="localDisplaySettings.showMACD">
                  MACD
                </a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox v-model:checked="localDisplaySettings.showKDJ">
                  KDJ
                </a-checkbox>
              </a-col>
            </a-row>
          </div>

          <a-divider />

          <div class="settings-section">
            <h4>图表主题</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <a-form-item label="主题模式">
                  <a-select v-model:value="localDisplaySettings.theme" style="width: 100%">
                    <a-select-option value="light">浅色主题</a-select-option>
                    <a-select-option value="dark">深色主题</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="K线样式">
                  <a-select v-model:value="localDisplaySettings.candlestickStyle" style="width: 100%">
                    <a-select-option value="candle">蜡烛图</a-select-option>
                    <a-select-option value="ohlc">OHLC</a-select-option>
                    <a-select-option value="area">面积图</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <a-divider />

          <div class="settings-section">
            <h4>图表元素</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="6">
                <a-checkbox v-model:checked="localDisplaySettings.showGrid">
                  显示网格
                </a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox v-model:checked="localDisplaySettings.showCrosshair">
                  显示十字线
                </a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox v-model:checked="localDisplaySettings.showLegend">
                  显示图例
                </a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox v-model:checked="localDisplaySettings.enableAnimation">
                  启用动画
                </a-checkbox>
              </a-col>
              <a-col :span="6">
                <a-checkbox v-model:checked="localDisplaySettings.showCountdown">
                  显示倒计时
                </a-checkbox>
              </a-col>
            </a-row>
          </div>

          <a-divider />

          <div class="settings-section">
            <h4>K线图样式配置</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="8">
                <a-form-item label="上涨颜色">
                  <a-input 
                    v-model:value="localKLineStyles.upColor" 
                    type="color" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="下跌颜色">
                  <a-input 
                    v-model:value="localKLineStyles.downColor" 
                    type="color" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="无变化颜色">
                  <a-input 
                    v-model:value="localKLineStyles.noChangeColor" 
                    type="color" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="网格颜色">
                  <a-input 
                    v-model:value="localKLineStyles.gridColor" 
                    type="color" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="轴线颜色">
                  <a-input 
                    v-model:value="localKLineStyles.axisColor" 
                    type="color" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="文字颜色">
                  <a-input 
                    v-model:value="localKLineStyles.textColor" 
                    type="color" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <a-divider />

          <div class="settings-section">
            <h4>动画配置</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <a-form-item label="动画时长 (毫秒)">
                  <a-input-number
                    v-model:value="localKLineStyles.animationDuration"
                    :min="0"
                    :max="5000"
                    :step="100"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="动画类型">
                  <a-select v-model:value="localKLineStyles.animationType" style="width: 100%">
                    <a-select-option value="linear">线性</a-select-option>
                    <a-select-option value="easeInOut">缓入缓出</a-select-option>
                    <a-select-option value="easeIn">缓入</a-select-option>
                    <a-select-option value="easeOut">缓出</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 指标参数 -->
        <a-tab-pane key="indicators" tab="指标参数">
          <div class="settings-section">
            <h4>移动平均线设置</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="6">
                <a-form-item label="MA5">
                  <a-input-number
                    v-model:value="localIndicatorSettings.ma.periods[0]"
                    :min="1"
                    :max="200"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="MA10">
                  <a-input-number
                    v-model:value="localIndicatorSettings.ma.periods[1]"
                    :min="1"
                    :max="200"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="MA20">
                  <a-input-number
                    v-model:value="localIndicatorSettings.ma.periods[2]"
                    :min="1"
                    :max="200"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="MA60">
                  <a-input-number
                    v-model:value="localIndicatorSettings.ma.periods[3]"
                    :min="1"
                    :max="200"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <a-divider />

          <div class="settings-section">
            <h4>RSI设置</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="8">
                <a-form-item label="周期">
                  <a-input-number
                    v-model:value="localIndicatorSettings.rsi.period"
                    :min="2"
                    :max="100"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="超买线">
                  <a-input-number
                    v-model:value="localIndicatorSettings.rsi.overbought"
                    :min="50"
                    :max="100"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="超卖线">
                  <a-input-number
                    v-model:value="localIndicatorSettings.rsi.oversold"
                    :min="0"
                    :max="50"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <a-divider />

          <div class="settings-section">
            <h4>MACD设置</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="8">
                <a-form-item label="快线周期">
                  <a-input-number
                    v-model:value="localIndicatorSettings.macd.fastPeriod"
                    :min="1"
                    :max="50"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="慢线周期">
                  <a-input-number
                    v-model:value="localIndicatorSettings.macd.slowPeriod"
                    :min="1"
                    :max="100"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="信号线周期">
                  <a-input-number
                    v-model:value="localIndicatorSettings.macd.signalPeriod"
                    :min="1"
                    :max="50"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <a-divider />

          <div class="settings-section">
            <h4>布林带设置</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <a-form-item label="周期">
                  <a-input-number
                    v-model:value="localIndicatorSettings.bollingerBands.period"
                    :min="2"
                    :max="100"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="标准差倍数">
                  <a-input-number
                    v-model:value="localIndicatorSettings.bollingerBands.stdDev"
                    :min="0.1"
                    :max="5"
                    :step="0.1"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <a-divider />

          <div class="settings-section">
            <h4>KDJ设置</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="8">
                <a-form-item label="K周期">
                  <a-input-number
                    v-model:value="localIndicatorSettings.kdj.kPeriod"
                    :min="1"
                    :max="100"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="D周期">
                  <a-input-number
                    v-model:value="localIndicatorSettings.kdj.dPeriod"
                    :min="1"
                    :max="100"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="J周期">
                  <a-input-number
                    v-model:value="localIndicatorSettings.kdj.jPeriod"
                    :min="1"
                    :max="100"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 交易设置 -->
        <a-tab-pane key="trading" tab="交易设置">
          <div class="settings-section">
            <h4>交易信号设置</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <a-checkbox v-model:checked="localTradingSettings.enableSignals">
                  启用交易信号
                </a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox v-model:checked="localTradingSettings.enableAlerts">
                  启用价格提醒
                </a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox v-model:checked="localTradingSettings.autoRefresh">
                  自动刷新数据
                </a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox v-model:checked="localTradingSettings.showOrderBook">
                  显示订单簿
                </a-checkbox>
              </a-col>
            </a-row>
          </div>

          <a-divider />

          <div class="settings-section">
            <h4>风险管理</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <a-form-item label="止损百分比 (%)">
                  <a-input-number
                    v-model:value="localTradingSettings.stopLossPercent"
                    :min="0.1"
                    :max="50"
                    :step="0.1"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="止盈百分比 (%)">
                  <a-input-number
                    v-model:value="localTradingSettings.takeProfitPercent"
                    :min="0.1"
                    :max="100"
                    :step="0.1"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="最大仓位 (%)">
                  <a-input-number
                    v-model:value="localTradingSettings.maxPositionPercent"
                    :min="1"
                    :max="100"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="刷新间隔 (秒)">
                  <a-input-number
                    v-model:value="localTradingSettings.refreshInterval"
                    :min="1"
                    :max="300"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 更新频率设置 -->
        <a-tab-pane key="update" tab="更新频率">
          <div class="settings-section">
            <h4>实时数据更新</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <a-form-item label="实时价格 (秒)">
                  <a-input-number
                    v-model:value="localUpdateSettings.tickerInterval"
                    :min="1"
                    :max="60"
                    style="width: 100%"
                  />
                  <div class="setting-description">影响价格显示的实时性</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="K线数据 (秒)">
                  <a-input-number
                    v-model:value="localUpdateSettings.candlestickInterval"
                    :min="5"
                    :max="300"
                    style="width: 100%"
                  />
                  <div class="setting-description">影响图表和技术分析的准确性</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="市场深度 (秒)">
                  <a-input-number
                    v-model:value="localUpdateSettings.depthInterval"
                    :min="1"
                    :max="30"
                    style="width: 100%"
                  />
                  <div class="setting-description">影响买卖盘显示的实时性</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="成交记录 (秒)">
                  <a-input-number
                    v-model:value="localUpdateSettings.tradesInterval"
                    :min="1"
                    :max="60"
                    style="width: 100%"
                  />
                  <div class="setting-description">影响市场活动的监控</div>
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <a-divider />

          <div class="settings-section">
            <h4>账户数据更新</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <a-form-item label="账户余额 (秒)">
                  <a-input-number
                    v-model:value="localUpdateSettings.balanceInterval"
                    :min="10"
                    :max="300"
                    style="width: 100%"
                  />
                  <div class="setting-description">影响资金显示的准确性</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="持仓信息 (秒)">
                  <a-input-number
                    v-model:value="localUpdateSettings.positionsInterval"
                    :min="5"
                    :max="120"
                    style="width: 100%"
                  />
                  <div class="setting-description">影响仓位管理的实时性</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="订单状态 (秒)">
                  <a-input-number
                    v-model:value="localUpdateSettings.ordersInterval"
                    :min="2"
                    :max="60"
                    style="width: 100%"
                  />
                  <div class="setting-description">影响交易执行的监控</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="技术指标 (秒)">
                  <a-input-number
                    v-model:value="localUpdateSettings.indicatorsInterval"
                    :min="5"
                    :max="300"
                    style="width: 100%"
                  />
                  <div class="setting-description">影响分析工具的准确性</div>
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <a-divider />

          <div class="settings-section">
            <h4>动态调整设置</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="24">
                <a-checkbox v-model:checked="localUpdateSettings.enableDynamicAdjustment">
                  启用根据市场活跃度动态调整更新频率
                </a-checkbox>
                <div class="setting-description">根据交易量自动调整更新频率，高活跃度时更频繁更新</div>
              </a-col>
            </a-row>
            
            <div v-if="localUpdateSettings.enableDynamicAdjustment" style="margin-top: 16px;">
              <h5>活跃度阈值设置</h5>
              <a-row :gutter="[16, 16]">
                <a-col :span="12">
                  <a-form-item label="高活跃度阈值">
                    <a-input-number
                      v-model:value="localUpdateSettings.highActivityThreshold"
                      :min="100000"
                      :max="10000000"
                      :step="100000"
                      style="width: 100%"
                      :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                      :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                    />
                    <div class="setting-description">24小时交易量超过此值时使用高频更新</div>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="中活跃度阈值">
                    <a-input-number
                      v-model:value="localUpdateSettings.mediumActivityThreshold"
                      :min="10000"
                      :max="1000000"
                      :step="10000"
                      style="width: 100%"
                      :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                      :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                    />
                    <div class="setting-description">24小时交易量超过此值时使用中频更新</div>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-tab-pane>

        <!-- 数据设置 -->
        <a-tab-pane key="data" tab="数据设置">
          <div class="settings-section">
            <h4>数据源设置</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <a-form-item label="默认数据源">
                  <a-select v-model:value="localDataSettings.defaultSource" style="width: 100%">
                    <a-select-option value="real">真实数据</a-select-option>
                    <a-select-option value="mock">模拟数据</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="数据条数">
                  <a-input-number
                    v-model:value="localDataSettings.dataLimit"
                    :min="50"
                    :max="1000"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="缓存时间 (分钟)">
                  <a-input-number
                    v-model:value="localDataSettings.cacheTime"
                    :min="1"
                    :max="60"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="重试次数">
                  <a-input-number
                    v-model:value="localDataSettings.retryCount"
                    :min="1"
                    :max="10"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <a-divider />

          <div class="settings-section">
            <h4>性能设置</h4>
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <a-checkbox v-model:checked="localDataSettings.enableCache">
                  启用数据缓存
                </a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox v-model:checked="localDataSettings.enableCompression">
                  启用数据压缩
                </a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox v-model:checked="localDataSettings.enableLazyLoad">
                  启用懒加载
                </a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox v-model:checked="localDataSettings.enablePreload">
                  启用预加载
                </a-checkbox>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>
      </a-tabs>

      <!-- 操作按钮 -->
      <div class="settings-actions">
        <a-space>
          <a-button @click="resetToDefaults">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置默认
          </a-button>
          <a-button @click="handleCancel">
            取消
          </a-button>
          <a-button type="primary" @click="handleSave">
            <template #icon>
              <SaveOutlined />
            </template>
            保存设置
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined, SaveOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  displaySettings: {
    type: Object,
    required: true
  },
  indicatorSettings: {
    type: Object,
    required: true
  },
  kLineStyles: {
    type: Object,
    default: () => ({})
  },
  tradingSettings: {
    type: Object,
    default: () => ({})
  },
  dataSettings: {
    type: Object,
    default: () => ({})
  },
  updateSettings: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:open', 'save', 'cancel'])

// 响应式状态
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const activeTab = ref('display')

// 本地设置副本
const localDisplaySettings = reactive({ ...props.displaySettings })
const localIndicatorSettings = reactive({ ...props.indicatorSettings })
const localKLineStyles = reactive({
  upColor: '#26a69a',
  downColor: '#ef5350',
  noChangeColor: '#757575',
  gridColor: '#e0e0e0',
  axisColor: '#bdbdbd',
  textColor: '#424242',
  animationDuration: 1000,
  animationType: 'easeInOut',
  ...props.kLineStyles
})
const localTradingSettings = reactive({
  enableSignals: true,
  enableAlerts: true,
  autoRefresh: true,
  showOrderBook: false,
  stopLossPercent: 2.0,
  takeProfitPercent: 5.0,
  maxPositionPercent: 10,
  refreshInterval: 1,
  ...props.tradingSettings
})
const localDataSettings = reactive({
  defaultSource: 'real',
  dataLimit: 200,
  cacheTime: 5,
  retryCount: 3,
  enableCache: true,
  enableCompression: false,
  enableLazyLoad: true,
  enablePreload: false,
  ...props.dataSettings
})

const localUpdateSettings = reactive({
  // 实时数据更新间隔（秒）
  tickerInterval: 3,
  candlestickInterval: 30,
  depthInterval: 2,
  tradesInterval: 5,
  // 账户数据更新间隔（秒）
  balanceInterval: 30,
  positionsInterval: 15,
  ordersInterval: 5,
  indicatorsInterval: 15,
  // 动态调整设置
  enableDynamicAdjustment: false,
  highActivityThreshold: 1000000,
  mediumActivityThreshold: 100000,
  ...props.updateSettings
})

// 默认设置
const defaultSettings = {
  display: {
    showMA: true,
    showEMA: false,
    showBollingerBands: true,
    showSupertrend: false,
    showVolume: true,
    showRSI: true,
    showMACD: true,
    showKDJ: false,
    theme: 'light',
    candlestickStyle: 'candle',
    showGrid: true,
    showCrosshair: true,
    showLegend: true,
    enableAnimation: true,
    showCountdown: true
  },
  indicators: {
    ma: {
      periods: [5, 10, 20, 60]
    },
    ema: {
      periods: [5, 10, 20, 60]
    },
    rsi: {
      period: 14,
      overbought: 70,
      oversold: 30
    },
    macd: {
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9
    },
    bollingerBands: {
      period: 20,
      stdDev: 2
    },
    kdj: {
      kPeriod: 9,
      dPeriod: 3,
      jPeriod: 3
    },
    supertrend: {
      period: 10,
      multiplier: 3
    }
  },
  trading: {
    enableSignals: true,
    enableAlerts: true,
    autoRefresh: true,
    showOrderBook: false,
    stopLossPercent: 2.0,
    takeProfitPercent: 5.0,
    maxPositionPercent: 10,
    refreshInterval: 1
  },
  data: {
    defaultSource: 'real',
    dataLimit: 200,
    cacheTime: 5,
    retryCount: 3,
    enableCache: true,
    enableCompression: false,
    enableLazyLoad: true,
    enablePreload: false
  },
  update: {
    tickerInterval: 3,
    candlestickInterval: 30,
    depthInterval: 2,
    tradesInterval: 5,
    balanceInterval: 30,
    positionsInterval: 15,
    ordersInterval: 5,
    indicatorsInterval: 15,
    enableDynamicAdjustment: false,
    highActivityThreshold: 1000000,
    mediumActivityThreshold: 100000
  }
}

// 监听props变化，更新本地设置
watch(
  () => props.displaySettings,
  (newSettings) => {
    Object.assign(localDisplaySettings, newSettings)
  },
  { deep: true }
)

watch(
  () => props.indicatorSettings,
  (newSettings) => {
    Object.assign(localIndicatorSettings, newSettings)
  },
  { deep: true }
)

watch(
  () => props.tradingSettings,
  (newSettings) => {
    Object.assign(localTradingSettings, newSettings)
  },
  { deep: true }
)

watch(
  () => props.dataSettings,
  (newSettings) => {
    Object.assign(localDataSettings, newSettings)
  },
  { deep: true }
)

watch(
  () => props.updateSettings,
  (newSettings) => {
    Object.assign(localUpdateSettings, newSettings)
  },
  { deep: true }
)

watch(
  () => props.kLineStyles,
  (newSettings) => {
    Object.assign(localKLineStyles, newSettings)
  },
  { deep: true }
)

// 方法
const handleSave = () => {
  try {
    // 验证设置
    if (!validateSettings()) {
      return
    }

    // 发送保存事件
    emit('save', {
      displaySettings: { ...localDisplaySettings },
      indicatorSettings: { ...localIndicatorSettings },
      kLineStyles: { ...localKLineStyles },
      tradingSettings: { ...localTradingSettings },
      dataSettings: { ...localDataSettings },
      updateSettings: { ...localUpdateSettings }
    })

  } catch (error) {
    console.error('保存设置失败:', error)
    message.error('保存设置失败')
  }
}

const handleCancel = () => {
  // 恢复原始设置
  Object.assign(localDisplaySettings, props.displaySettings)
  Object.assign(localIndicatorSettings, props.indicatorSettings)
  Object.assign(localKLineStyles, props.kLineStyles)
  Object.assign(localTradingSettings, props.tradingSettings)
  Object.assign(localDataSettings, props.dataSettings)
  Object.assign(localUpdateSettings, props.updateSettings)
  
  emit('cancel')
}

const resetToDefaults = () => {
  try {
    Object.assign(localDisplaySettings, defaultSettings.display)
    Object.assign(localIndicatorSettings, defaultSettings.indicators)
    Object.assign(localKLineStyles, {
      upColor: '#26a69a',
      downColor: '#ef5350',
      noChangeColor: '#757575',
      gridColor: '#e0e0e0',
      axisColor: '#bdbdbd',
      textColor: '#424242',
      animationDuration: 1000,
      animationType: 'easeInOut'
    })
    Object.assign(localTradingSettings, defaultSettings.trading)
    Object.assign(localDataSettings, defaultSettings.data)
    Object.assign(localUpdateSettings, defaultSettings.update)
    
    message.success('已重置为默认设置')
  } catch (error) {
    console.error('重置设置失败:', error)
    message.error('重置设置失败')
  }
}

const validateSettings = () => {
  // 验证MA周期
  const maPeriods = localIndicatorSettings.ma.periods
  if (maPeriods.some(period => period < 1 || period > 200)) {
    message.error('MA周期必须在1-200之间')
    return false
  }

  // 验证RSI设置
  const rsi = localIndicatorSettings.rsi
  if (rsi.period < 2 || rsi.period > 100) {
    message.error('RSI周期必须在2-100之间')
    return false
  }
  if (rsi.overbought <= rsi.oversold) {
    message.error('RSI超买线必须大于超卖线')
    return false
  }

  // 验证MACD设置
  const macd = localIndicatorSettings.macd
  if (macd.fastPeriod >= macd.slowPeriod) {
    message.error('MACD快线周期必须小于慢线周期')
    return false
  }

  // 验证布林带设置
  const bb = localIndicatorSettings.bollingerBands
  if (bb.period < 2 || bb.period > 100) {
    message.error('布林带周期必须在2-100之间')
    return false
  }
  if (bb.stdDev < 0.1 || bb.stdDev > 5) {
    message.error('布林带标准差倍数必须在0.1-5之间')
    return false
  }

  // 验证交易设置
  const trading = localTradingSettings
  if (trading.stopLossPercent >= trading.takeProfitPercent) {
    message.error('止盈百分比必须大于止损百分比')
    return false
  }

  return true
}
</script>

<style scoped>
.settings-container {
  max-height: 600px;
  overflow-y: auto;
}

.settings-section {
  margin-bottom: 24px;
}

.settings-section h4 {
  margin-bottom: 16px;
  color: #1890ff;
  font-weight: 600;
}

.settings-section h5 {
  margin-bottom: 12px;
  color: #595959;
  font-weight: 500;
}

.setting-description {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
  line-height: 1.4;
}

.settings-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

:deep(.ant-tabs-content-holder) {
  padding: 16px 0;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-checkbox-wrapper) {
  font-weight: 500;
}

:deep(.ant-divider) {
  margin: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-container {
    max-height: 500px;
  }
  
  :deep(.ant-col) {
    margin-bottom: 8px;
  }
}
</style>