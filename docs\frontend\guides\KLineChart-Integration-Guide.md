# KLineChart 项目集成指南

## 🎯 项目概述

本项目是一个基于 Vue 3 + KLineChart 的专业量化交易系统，提供完整的K线图表分析功能。本指南详细说明了如何在项目中集成和使用 KLineChart。

## 🏗️ 架构设计

### 技术栈

- **前端框架**: Vue 3 (Composition API)
- **图表库**: KLineChart v9.x
- **状态管理**: Pinia
- **UI组件**: Ant Design Vue
- **HTTP客户端**: Axios
- **WebSocket**: 原生 WebSocket API
- **构建工具**: Vite

### 目录结构

```
web-frontend/
├── src/
│   ├── components/
│   │   ├── charts/                 # 图表组件
│   │   │   ├── KLineChart.vue     # 主K线图组件
│   │   │   ├── VolumeChart.vue    # 成交量图表
│   │   │   └── TechnicalIndicators.vue # 技术指标
│   │   ├── trading/               # 交易相关组件
│   │   └── panels/                # 面板组件
│   ├── composables/               # 组合式函数
│   │   ├── useKLineCharts.js     # K线图表逻辑
│   │   ├── useDataFetching.js    # 数据获取
│   │   ├── useWebSocket.js       # WebSocket管理
│   │   └── useIndicators.js      # 技术指标管理
│   ├── stores/                   # 状态管理
│   │   ├── chartDataStore.js     # 图表数据
│   │   └── chartSettingsStore.js # 图表设置
│   ├── config/                   # 配置文件
│   │   └── chartConfig.js        # 图表配置
│   ├── constants/                # 常量定义
│   │   └── updateConfig.js       # 更新频率配置
│   └── utils/                    # 工具函数
│       ├── formatters.js         # 格式化工具
│       └── validators.js         # 数据验证
└── docs/                         # 文档
    ├── KLineChart-Usage-Guide.md
    ├── KLineChart-API-Reference.md
    └── KLineChart-Integration-Guide.md
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 或使用项目提供的批处理文件
.\start-vite.bat
```

### 2. 基础配置

#### 图表配置 (`src/config/chartConfig.js`)

```javascript
/**
 * 图表基础配置
 */
export const chartConfig = {
  // 默认主题
  theme: 'light',
  
  // 默认时间周期
  defaultTimeframe: '1m',
  
  // 支持的时间周期
  timeframes: [
    { value: '1m', label: '1分钟' },
    { value: '5m', label: '5分钟' },
    { value: '15m', label: '15分钟' },
    { value: '30m', label: '30分钟' },
    { value: '1H', label: '1小时' },
    { value: '4H', label: '4小时' },
    { value: '1D', label: '1天' }
  ],
  
  // 默认指标
  defaultIndicators: {
    main: ['MA', 'BOLL'],
    sub: ['MACD', 'RSI']
  },
  
  // 更新频率配置
  updateIntervals: {
    kline: 30000,    // K线数据 30秒
    ticker: 3000,    // 行情数据 3秒
    depth: 2000      // 深度数据 2秒
  }
}
```

#### 更新频率配置 (`src/constants/updateConfig.js`)

```javascript
/**
 * 数据更新频率配置
 * 统一管理各种数据的更新间隔
 */
export const UPDATE_INTERVALS = {
  TICKER: 3000,        // 实时价格数据
  CANDLESTICK: 30000,  // K线数据
  BALANCE: 30000,      // 账户余额
  POSITIONS: 15000,    // 持仓信息
  ORDERS: 5000,        // 订单状态
  DEPTH: 2000,         // 市场深度
  TRADES: 5000,        // 成交记录
  INDICATORS: 15000,   // 技术指标
  VOLUME: 10000,       // 实时成交量
  HEARTBEAT: 10000,    // WebSocket心跳
  RECONNECT: 5000      // 重连间隔
}

/**
 * 根据市场活跃度动态调整的配置
 */
export const DYNAMIC_INTERVALS = {
  HIGH_ACTIVITY: {
    TICKER: 1000,
    CANDLESTICK: 5000,
    DEPTH: 1000
  },
  MEDIUM_ACTIVITY: {
    TICKER: 3000,
    CANDLESTICK: 10000,
    DEPTH: 2000
  },
  LOW_ACTIVITY: {
    TICKER: 5000,
    CANDLESTICK: 15000,
    DEPTH: 5000
  }
}
```

## 📊 核心组件使用

### 1. KLineChart 主图表组件

#### 基础使用

```vue
<template>
  <div class="kline-chart-container">
    <div ref="chartContainer" class="chart-container"></div>
    
    <!-- 图表控制面板 -->
    <div class="chart-controls">
      <a-select v-model:value="selectedTimeframe" @change="onTimeframeChange">
        <a-select-option v-for="tf in timeframes" :key="tf.value" :value="tf.value">
          {{ tf.label }}
        </a-select-option>
      </a-select>
      
      <a-button @click="toggleTheme">切换主题</a-button>
      <a-button @click="resetChart">重置图表</a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useKLineCharts } from '@/composables/useKLineCharts'
import { useDataFetching } from '@/composables/useDataFetching'
import { useWebSocket } from '@/composables/useWebSocket'
import { chartConfig } from '@/config/chartConfig'

// Props
const props = defineProps({
  symbol: {
    type: String,
    default: 'BTC-USDT'
  },
  height: {
    type: Number,
    default: 400
  }
})

// 响应式数据
const chartContainer = ref(null)
const selectedTimeframe = ref(chartConfig.defaultTimeframe)
const timeframes = chartConfig.timeframes

// 组合式函数
const {
  chartInstances,
  initializeChart,
  updateChart,
  setSymbol,
  addIndicator,
  removeIndicator,
  setTheme
} = useKLineCharts()

const {
  fetchKlineData,
  klineData,
  loading
} = useDataFetching()

const {
  connect,
  subscribeKline,
  subscribeTicker,
  disconnect
} = useWebSocket()

// 初始化图表
const initChart = async () => {
  if (!chartContainer.value) return
  
  try {
    // 初始化图表实例
    await initializeChart('main', chartContainer.value, {
      symbol: props.symbol,
      timeframe: selectedTimeframe.value,
      height: props.height
    })
    
    // 获取初始数据
    await fetchKlineData(props.symbol, selectedTimeframe.value)
    
    // 更新图表数据
    if (klineData.value.length > 0) {
      updateChart('main', klineData.value)
    }
    
    // 连接WebSocket并订阅实时数据
    connect()
    subscribeKline(props.symbol, selectedTimeframe.value)
    subscribeTicker(props.symbol)
    
  } catch (error) {
    console.error('初始化图表失败:', error)
  }
}

// 时间周期变化处理
const onTimeframeChange = async (newTimeframe) => {
  try {
    // 获取新时间周期的数据
    await fetchKlineData(props.symbol, newTimeframe)
    
    // 更新图表
    if (klineData.value.length > 0) {
      updateChart('main', klineData.value)
    }
    
    // 重新订阅WebSocket
    subscribeKline(props.symbol, newTimeframe)
    
  } catch (error) {
    console.error('切换时间周期失败:', error)
  }
}

// 主题切换
const toggleTheme = () => {
  const currentTheme = chartInstances.value.main?.getStyles()?.theme || 'light'
  const newTheme = currentTheme === 'light' ? 'dark' : 'light'
  setTheme(newTheme)
}

// 重置图表
const resetChart = () => {
  if (chartInstances.value.main) {
    chartInstances.value.main.scrollToRealTime()
  }
}

// 监听交易对变化
watch(() => props.symbol, async (newSymbol) => {
  if (newSymbol) {
    setSymbol(newSymbol)
    await fetchKlineData(newSymbol, selectedTimeframe.value)
    if (klineData.value.length > 0) {
      updateChart('main', klineData.value)
    }
    subscribeKline(newSymbol, selectedTimeframe.value)
    subscribeTicker(newSymbol)
  }
})

// 生命周期
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  disconnect()
})
</script>

<style scoped>
.kline-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-container {
  flex: 1;
  min-height: 400px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.chart-controls {
  display: flex;
  gap: 8px;
  padding: 8px 0;
  align-items: center;
}
</style>
```

### 2. 技术指标管理组件

```vue
<template>
  <div class="indicator-panel">
    <div class="indicator-header">
      <h4>技术指标</h4>
      <a-button size="small" @click="showAddModal = true">添加指标</a-button>
    </div>
    
    <!-- 主图指标 -->
    <div class="indicator-section">
      <h5>主图指标</h5>
      <div class="indicator-list">
        <a-tag
          v-for="indicator in mainIndicators"
          :key="indicator.name"
          closable
          @close="removeMainIndicator(indicator.name)"
        >
          {{ indicator.name }}
        </a-tag>
      </div>
    </div>
    
    <!-- 副图指标 -->
    <div class="indicator-section">
      <h5>副图指标</h5>
      <div class="indicator-list">
        <a-tag
          v-for="indicator in subIndicators"
          :key="indicator.name"
          closable
          @close="removeSubIndicator(indicator.name)"
        >
          {{ indicator.name }}
        </a-tag>
      </div>
    </div>
    
    <!-- 添加指标弹窗 -->
    <a-modal
      v-model:visible="showAddModal"
      title="添加技术指标"
      @ok="addSelectedIndicator"
    >
      <a-form :model="indicatorForm" layout="vertical">
        <a-form-item label="指标类型">
          <a-select v-model:value="indicatorForm.name" placeholder="选择指标">
            <a-select-option v-for="ind in availableIndicators" :key="ind.name" :value="ind.name">
              {{ ind.name }} - {{ ind.description }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="显示位置">
          <a-radio-group v-model:value="indicatorForm.position">
            <a-radio value="main">主图</a-radio>
            <a-radio value="sub">副图</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="参数设置" v-if="selectedIndicatorInfo">
          <a-input
            v-model:value="indicatorForm.params"
            :placeholder="`默认参数: [${selectedIndicatorInfo.defaultParams.join(', ')}]`"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, inject } from 'vue'
import { useIndicators } from '@/composables/useIndicators'

// 注入图表实例
const chartInstances = inject('chartInstances')

// 响应式数据
const showAddModal = ref(false)
const indicatorForm = ref({
  name: '',
  position: 'sub',
  params: ''
})

// 组合式函数
const {
  mainIndicators,
  subIndicators,
  availableIndicators,
  addIndicator,
  removeIndicator
} = useIndicators(chartInstances)

// 计算属性
const selectedIndicatorInfo = computed(() => {
  return availableIndicators.find(ind => ind.name === indicatorForm.value.name)
})

// 添加选中的指标
const addSelectedIndicator = () => {
  const { name, position, params } = indicatorForm.value
  
  if (!name) return
  
  const calcParams = params ? params.split(',').map(p => Number(p.trim())) : undefined
  
  addIndicator(name, position === 'main', calcParams)
  
  // 重置表单
  indicatorForm.value = {
    name: '',
    position: 'sub',
    params: ''
  }
  showAddModal.value = false
}

// 移除主图指标
const removeMainIndicator = (name) => {
  removeIndicator(name, 'candle_pane')
}

// 移除副图指标
const removeSubIndicator = (name) => {
  removeIndicator(name)
}
</script>

<style scoped>
.indicator-panel {
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.indicator-section {
  margin-bottom: 16px;
}

.indicator-section h5 {
  margin-bottom: 8px;
  color: #666;
}

.indicator-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
```

## 🔧 组合式函数详解

### 1. useKLineCharts - 图表管理

```javascript
/**
 * K线图表管理组合式函数
 * 提供图表实例管理、初始化、更新等功能
 */
import { ref, reactive, onUnmounted } from 'vue'
import * as klinecharts from 'klinecharts'
import { chartConfig } from '@/config/chartConfig'

export function useKLineCharts() {
  // 图表实例集合
  const chartInstances = ref({})
  
  // 当前配置
  const currentConfig = reactive({
    symbol: '',
    timeframe: chartConfig.defaultTimeframe,
    theme: chartConfig.theme
  })
  
  /**
   * 初始化图表
   * @param {string} id - 图表ID
   * @param {HTMLElement} container - 容器元素
   * @param {Object} options - 配置选项
   */
  const initializeChart = async (id, container, options = {}) => {
    try {
      // 销毁已存在的图表
      if (chartInstances.value[id]) {
        chartInstances.value[id].dispose()
      }
      
      // 创建新图表实例
      const chart = klinecharts.init(container, {
        styles: getChartStyles(options.theme || currentConfig.theme)
      })
      
      // 保存实例
      chartInstances.value[id] = chart
      
      // 更新配置
      Object.assign(currentConfig, options)
      
      // 添加默认指标
      if (options.defaultIndicators !== false) {
        addDefaultIndicators(chart)
      }
      
      console.log(`图表 ${id} 初始化成功`)
      return chart
      
    } catch (error) {
      console.error(`初始化图表 ${id} 失败:`, error)
      throw error
    }
  }
  
  /**
   * 更新图表数据
   * @param {string} id - 图表ID
   * @param {Array} data - K线数据
   */
  const updateChart = (id, data) => {
    const chart = chartInstances.value[id]
    if (!chart || !Array.isArray(data) || data.length === 0) {
      return
    }
    
    try {
      // 验证数据格式
      const validData = validateKlineData(data)
      if (validData.length === 0) {
        console.warn('没有有效的K线数据')
        return
      }
      
      // 应用数据到图表
      chart.applyNewData(validData)
      console.log(`图表 ${id} 数据更新成功，共 ${validData.length} 条`)
      
    } catch (error) {
      console.error(`更新图表 ${id} 数据失败:`, error)
    }
  }
  
  /**
   * 实时更新最后一条数据
   * @param {string} id - 图表ID
   * @param {Object} data - 实时数据
   */
  const updateRealTimeData = (id, data) => {
    const chart = chartInstances.value[id]
    if (!chart || !data) return
    
    try {
      chart.updateData({
        timestamp: data.timestamp || Date.now(),
        open: parseFloat(data.open),
        high: parseFloat(data.high),
        low: parseFloat(data.low),
        close: parseFloat(data.close),
        volume: parseFloat(data.volume)
      })
    } catch (error) {
      console.error(`实时更新图表 ${id} 失败:`, error)
    }
  }
  
  /**
   * 设置交易对和精度
   * @param {string} symbol - 交易对
   * @param {Object} precision - 精度配置
   */
  const setSymbol = (symbol, precision = {}) => {
    currentConfig.symbol = symbol
    
    // 更新所有图表的精度设置
    Object.values(chartInstances.value).forEach(chart => {
      if (chart && precision.pxPrecision !== undefined) {
        updateChartPrecision(chart, precision)
      }
    })
  }
  
  /**
   * 添加技术指标
   * @param {string} id - 图表ID
   * @param {string} name - 指标名称
   * @param {boolean} isStack - 是否叠加到主图
   * @param {Array} calcParams - 计算参数
   */
  const addIndicator = (id, name, isStack = false, calcParams = []) => {
    const chart = chartInstances.value[id]
    if (!chart) return
    
    try {
      const indicatorConfig = {
        name,
        calcParams: calcParams.length > 0 ? calcParams : undefined
      }
      
      chart.createIndicator(indicatorConfig, isStack)
      console.log(`添加指标 ${name} 成功`)
      
    } catch (error) {
      console.error(`添加指标 ${name} 失败:`, error)
    }
  }
  
  /**
   * 移除技术指标
   * @param {string} id - 图表ID
   * @param {string} name - 指标名称
   * @param {string} paneId - 面板ID
   */
  const removeIndicator = (id, name, paneId) => {
    const chart = chartInstances.value[id]
    if (!chart) return
    
    try {
      chart.removeIndicator(paneId, name)
      console.log(`移除指标 ${name} 成功`)
      
    } catch (error) {
      console.error(`移除指标 ${name} 失败:`, error)
    }
  }
  
  /**
   * 设置主题
   * @param {string} theme - 主题名称
   */
  const setTheme = (theme) => {
    currentConfig.theme = theme
    const styles = getChartStyles(theme)
    
    Object.values(chartInstances.value).forEach(chart => {
      if (chart) {
        chart.setStyles(styles)
      }
    })
  }
  
  /**
   * 获取图表样式配置
   * @param {string} theme - 主题
   * @returns {Object} 样式配置
   */
  const getChartStyles = (theme) => {
    const isDark = theme === 'dark'
    
    return {
      grid: {
        horizontal: {
          color: isDark ? '#333' : '#EDEDED'
        },
        vertical: {
          color: isDark ? '#333' : '#EDEDED'
        }
      },
      candle: {
        bar: {
          upColor: isDark ? '#00b894' : '#2DC08E',
          downColor: isDark ? '#d63031' : '#F92855',
          upBorderColor: isDark ? '#00b894' : '#2DC08E',
          downBorderColor: isDark ? '#d63031' : '#F92855'
        }
      },
      xAxis: {
        axisLine: {
          color: isDark ? '#666' : '#DDDDDD'
        },
        tickText: {
          color: isDark ? '#999' : '#666'
        }
      },
      yAxis: {
        axisLine: {
          color: isDark ? '#666' : '#DDDDDD'
        },
        tickText: {
          color: isDark ? '#999' : '#666'
        }
      }
    }
  }
  
  /**
   * 验证K线数据
   * @param {Array} data - 原始数据
   * @returns {Array} 验证后的数据
   */
  const validateKlineData = (data) => {
    if (!Array.isArray(data)) return []
    
    return data.filter(item => {
      return item &&
             typeof item.timestamp === 'number' &&
             typeof item.open === 'number' &&
             typeof item.high === 'number' &&
             typeof item.low === 'number' &&
             typeof item.close === 'number' &&
             typeof item.volume === 'number' &&
             item.high >= item.low &&
             item.high >= Math.max(item.open, item.close) &&
             item.low <= Math.min(item.open, item.close)
    })
  }
  
  /**
   * 添加默认指标
   * @param {Object} chart - 图表实例
   */
  const addDefaultIndicators = (chart) => {
    const { defaultIndicators } = chartConfig
    
    // 添加主图指标
    defaultIndicators.main.forEach(name => {
      chart.createIndicator(name, true, { id: 'candle_pane' })
    })
    
    // 添加副图指标
    defaultIndicators.sub.forEach(name => {
      chart.createIndicator(name, false)
    })
  }
  
  /**
   * 更新图表精度
   * @param {Object} chart - 图表实例
   * @param {Object} precision - 精度配置
   */
  const updateChartPrecision = (chart, precision) => {
    chart.setStyles({
      candle: {
        tooltip: {
          custom: (data) => {
            return [
              { title: '时间', value: formatTime(data.timestamp) },
              { title: '开盘', value: formatPrice(data.open, precision.pxPrecision) },
              { title: '最高', value: formatPrice(data.high, precision.pxPrecision) },
              { title: '最低', value: formatPrice(data.low, precision.pxPrecision) },
              { title: '收盘', value: formatPrice(data.close, precision.pxPrecision) },
              { title: '成交量', value: formatVolume(data.volume, precision.szPrecision) }
            ]
          }
        }
      }
    })
  }
  
  /**
   * 清理所有图表实例
   */
  const cleanup = () => {
    Object.values(chartInstances.value).forEach(chart => {
      if (chart && typeof chart.dispose === 'function') {
        chart.dispose()
      }
    })
    chartInstances.value = {}
  }
  
  // 组件卸载时清理
  onUnmounted(() => {
    cleanup()
  })
  
  return {
    chartInstances,
    currentConfig,
    initializeChart,
    updateChart,
    updateRealTimeData,
    setSymbol,
    addIndicator,
    removeIndicator,
    setTheme,
    cleanup
  }
}

// 格式化工具函数
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}

const formatPrice = (price, precision = 2) => {
  return Number(price).toFixed(precision)
}

const formatVolume = (volume, precision = 8) => {
  return Number(volume).toFixed(precision)
}
```

### 2. useDataFetching - 数据获取

```javascript
/**
 * 数据获取组合式函数
 * 统一管理各种数据的获取逻辑
 */
import { ref, reactive } from 'vue'
import axios from 'axios'
import { UPDATE_INTERVALS } from '@/constants/updateConfig'

export function useDataFetching() {
  // 响应式数据
  const klineData = ref([])
  const tickerData = ref({})
  const depthData = ref({ asks: [], bids: [] })
  const tradesData = ref([])
  const loading = reactive({
    kline: false,
    ticker: false,
    depth: false,
    trades: false
  })
  const error = ref(null)
  
  /**
   * 获取K线数据
   * @param {string} symbol - 交易对
   * @param {string} timeframe - 时间周期
   * @param {number} limit - 数据条数
   */
  const fetchKlineData = async (symbol, timeframe = '1m', limit = 300) => {
    loading.kline = true
    error.value = null
    
    try {
      const response = await axios.get('/api/okx/market/candles', {
        params: {
          instId: symbol,
          bar: timeframe,
          limit: limit.toString()
        }
      })
      
      if (response.data.code === '0') {
        const rawData = response.data.data
        klineData.value = formatKlineData(rawData)
        console.log(`获取K线数据成功: ${symbol} ${timeframe}, ${klineData.value.length}条`)
      } else {
        throw new Error(response.data.msg || '获取K线数据失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('获取K线数据失败:', err)
    } finally {
      loading.kline = false
    }
  }
  
  /**
   * 获取实时行情
   * @param {string} symbol - 交易对
   */
  const fetchTickerData = async (symbol) => {
    loading.ticker = true
    
    try {
      const response = await axios.get('/api/okx/market/ticker', {
        params: { instId: symbol }
      })
      
      if (response.data.code === '0' && response.data.data.length > 0) {
        tickerData.value = response.data.data[0]
      }
    } catch (err) {
      console.error('获取行情数据失败:', err)
    } finally {
      loading.ticker = false
    }
  }
  
  /**
   * 获取市场深度
   * @param {string} symbol - 交易对
   * @param {number} size - 深度档位
   */
  const fetchDepthData = async (symbol, size = 20) => {
    loading.depth = true
    
    try {
      const response = await axios.get('/api/okx/market/books', {
        params: {
          instId: symbol,
          sz: size.toString()
        }
      })
      
      if (response.data.code === '0' && response.data.data.length > 0) {
        const data = response.data.data[0]
        depthData.value = {
          asks: data.asks.map(item => ({
            price: parseFloat(item[0]),
            size: parseFloat(item[1])
          })),
          bids: data.bids.map(item => ({
            price: parseFloat(item[0]),
            size: parseFloat(item[1])
          }))
        }
      }
    } catch (err) {
      console.error('获取深度数据失败:', err)
    } finally {
      loading.depth = false
    }
  }
  
  /**
   * 获取成交记录
   * @param {string} symbol - 交易对
   * @param {number} limit - 记录条数
   */
  const fetchTradesData = async (symbol, limit = 50) => {
    loading.trades = true
    
    try {
      const response = await axios.get('/api/okx/market/trades', {
        params: {
          instId: symbol,
          limit: limit.toString()
        }
      })
      
      if (response.data.code === '0') {
        tradesData.value = response.data.data.map(item => ({
          id: item.tradeId,
          price: parseFloat(item.px),
          size: parseFloat(item.sz),
          side: item.side,
          timestamp: parseInt(item.ts)
        }))
      }
    } catch (err) {
      console.error('获取成交记录失败:', err)
    } finally {
      loading.trades = false
    }
  }
  
  /**
   * 格式化K线数据
   * @param {Array} rawData - 原始数据
   * @returns {Array} 格式化后的数据
   */
  const formatKlineData = (rawData) => {
    if (!Array.isArray(rawData)) return []
    
    return rawData.map(item => ({
      timestamp: parseInt(item[0]),
      open: parseFloat(item[1]),
      high: parseFloat(item[2]),
      low: parseFloat(item[3]),
      close: parseFloat(item[4]),
      volume: parseFloat(item[5]),
      turnover: parseFloat(item[6] || 0)
    })).reverse() // 按时间正序排列
  }
  
  /**
   * 批量获取多个交易对的行情
   * @param {Array} symbols - 交易对列表
   */
  const fetchMultipleTickerData = async (symbols) => {
    try {
      const requests = symbols.map(symbol => 
        axios.get('/api/okx/market/ticker', {
          params: { instId: symbol }
        })
      )
      
      const responses = await Promise.all(requests)
      const result = {}
      
      responses.forEach((response, index) => {
        if (response.data.code === '0' && response.data.data.length > 0) {
          result[symbols[index]] = response.data.data[0]
        }
      })
      
      return result
    } catch (err) {
      console.error('批量获取行情数据失败:', err)
      return {}
    }
  }
  
  return {
    // 数据
    klineData,
    tickerData,
    depthData,
    tradesData,
    loading,
    error,
    
    // 方法
    fetchKlineData,
    fetchTickerData,
    fetchDepthData,
    fetchTradesData,
    fetchMultipleTickerData
  }
}
```

## 🎛️ 状态管理

### 图表数据状态 (chartDataStore.js)

```javascript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useChartDataStore = defineStore('chartData', () => {
  // 状态
  const currentSymbol = ref('BTC-USDT')
  const currentTimeframe = ref('1m')
  const klineData = ref([])
  const realtimeData = ref({})
  const instruments = ref([])
  
  // 计算属性
  const currentInstrument = computed(() => {
    return instruments.value.find(item => item.instId === currentSymbol.value)
  })
  
  const pricePrecision = computed(() => {
    return currentInstrument.value?.pxPrecision || 2
  })
  
  const sizePrecision = computed(() => {
    return currentInstrument.value?.szPrecision || 8
  })
  
  // 操作
  const setSymbol = (symbol) => {
    currentSymbol.value = symbol
  }
  
  const setTimeframe = (timeframe) => {
    currentTimeframe.value = timeframe
  }
  
  const updateKlineData = (data) => {
    klineData.value = data
  }
  
  const updateRealtimeData = (data) => {
    realtimeData.value = { ...realtimeData.value, ...data }
  }
  
  const setInstruments = (data) => {
    instruments.value = data
  }
  
  return {
    // 状态
    currentSymbol,
    currentTimeframe,
    klineData,
    realtimeData,
    instruments,
    
    // 计算属性
    currentInstrument,
    pricePrecision,
    sizePrecision,
    
    // 操作
    setSymbol,
    setTimeframe,
    updateKlineData,
    updateRealtimeData,
    setInstruments
  }
})
```

### 图表设置状态 (chartSettingsStore.js)

```javascript
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useChartSettingsStore = defineStore('chartSettings', () => {
  // 状态
  const theme = ref('light')
  const mainIndicators = ref(['MA', 'BOLL'])
  const subIndicators = ref(['MACD', 'RSI'])
  const chartLayout = ref({
    showVolume: true,
    showDepth: true,
    showTrades: true
  })
  const updateIntervals = ref({
    kline: 30000,
    ticker: 3000,
    depth: 2000
  })
  
  // 操作
  const setTheme = (newTheme) => {
    theme.value = newTheme
    localStorage.setItem('chart-theme', newTheme)
  }
  
  const addMainIndicator = (indicator) => {
    if (!mainIndicators.value.includes(indicator)) {
      mainIndicators.value.push(indicator)
      saveSettings()
    }
  }
  
  const removeMainIndicator = (indicator) => {
    const index = mainIndicators.value.indexOf(indicator)
    if (index > -1) {
      mainIndicators.value.splice(index, 1)
      saveSettings()
    }
  }
  
  const addSubIndicator = (indicator) => {
    if (!subIndicators.value.includes(indicator)) {
      subIndicators.value.push(indicator)
      saveSettings()
    }
  }
  
  const removeSubIndicator = (indicator) => {
    const index = subIndicators.value.indexOf(indicator)
    if (index > -1) {
      subIndicators.value.splice(index, 1)
      saveSettings()
    }
  }
  
  const updateChartLayout = (layout) => {
    chartLayout.value = { ...chartLayout.value, ...layout }
    saveSettings()
  }
  
  const updateUpdateIntervals = (intervals) => {
    updateIntervals.value = { ...updateIntervals.value, ...intervals }
    saveSettings()
  }
  
  const saveSettings = () => {
    const settings = {
      theme: theme.value,
      mainIndicators: mainIndicators.value,
      subIndicators: subIndicators.value,
      chartLayout: chartLayout.value,
      updateIntervals: updateIntervals.value
    }
    localStorage.setItem('chart-settings', JSON.stringify(settings))
  }
  
  const loadSettings = () => {
    try {
      const saved = localStorage.getItem('chart-settings')
      if (saved) {
        const settings = JSON.parse(saved)
        theme.value = settings.theme || 'light'
        mainIndicators.value = settings.mainIndicators || ['MA', 'BOLL']
        subIndicators.value = settings.subIndicators || ['MACD', 'RSI']
        chartLayout.value = settings.chartLayout || chartLayout.value
        updateIntervals.value = settings.updateIntervals || updateIntervals.value
      }
    } catch (err) {
      console.error('加载设置失败:', err)
    }
  }
  
  // 初始化时加载设置
  loadSettings()
  
  return {
    // 状态
    theme,
    mainIndicators,
    subIndicators,
    chartLayout,
    updateIntervals,
    
    // 操作
    setTheme,
    addMainIndicator,
    removeMainIndicator,
    addSubIndicator,
    removeSubIndicator,
    updateChartLayout,
    updateUpdateIntervals,
    saveSettings,
    loadSettings
  }
})
```

## 🚀 部署和优化

### 1. 构建配置

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'klinecharts': ['klinecharts'],
          'vendor': ['vue', 'pinia', 'axios'],
          'antd': ['ant-design-vue']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      },
      '/ws': {
        target: 'ws://localhost:8000',
        ws: true
      }
    }
  }
})
```

### 2. 性能优化

```javascript
// 懒加载图表组件
const KLineChart = defineAsyncComponent(() => 
  import('@/components/charts/KLineChart.vue')
)

// 数据缓存
const dataCache = new Map()
const getCachedData = (key, fetcher, ttl = 60000) => {
  const cached = dataCache.get(key)
  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data
  }
  
  const data = fetcher()
  dataCache.set(key, {
    data,
    timestamp: Date.now()
  })
  
  return data
}

// 防抖更新
const debouncedUpdate = debounce((chart, data) => {
  chart.updateData(data)
}, 100)

// 内存清理
const cleanupInterval = setInterval(() => {
  const now = Date.now()
  for (const [key, value] of dataCache.entries()) {
    if (now - value.timestamp > 300000) { // 5分钟
      dataCache.delete(key)
    }
  }
}, 60000) // 每分钟清理一次
```

## 📝 最佳实践

### 1. 错误处理

```javascript
// 全局错误处理
const handleChartError = (error, context) => {
  console.error(`图表错误 [${context}]:`, error)
  
  // 发送错误报告
  if (process.env.NODE_ENV === 'production') {
    sendErrorReport(error, context)
  }
  
  // 用户友好的错误提示
  message.error('图表加载失败，请刷新页面重试')
}

// 数据验证
const validateChartData = (data) => {
  if (!Array.isArray(data) || data.length === 0) {
    throw new Error('图表数据为空或格式错误')
  }
  
  const requiredFields = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
  const invalidItems = data.filter(item => {
    return !requiredFields.every(field => 
      item.hasOwnProperty(field) && typeof item[field] === 'number'
    )
  })
  
  if (invalidItems.length > data.length * 0.1) {
    throw new Error('图表数据质量不符合要求')
  }
  
  return data.filter(item => 
    requiredFields.every(field => 
      item.hasOwnProperty(field) && typeof item[field] === 'number'
    )
  )
}
```

### 2. 内存管理

```javascript
// 组件卸载时清理资源
onUnmounted(() => {
  // 清理图表实例
  Object.values(chartInstances.value).forEach(chart => {
    if (chart && typeof chart.dispose === 'function') {
      chart.dispose()
    }
  })
  
  // 清理定时器
  if (updateTimer.value) {
    clearInterval(updateTimer.value)
  }
  
  // 断开WebSocket连接
  if (ws.value) {
    ws.value.close()
  }
  
  // 清理事件监听
  window.removeEventListener('resize', handleResize)
})
```

### 3. 用户体验优化

```javascript
// 加载状态管理
const loading = ref({
  chart: false,
  data: false,
  indicators: false
})

// 骨架屏组件
const ChartSkeleton = {
  template: `
    <div class="chart-skeleton">
      <div class="skeleton-header"></div>
      <div class="skeleton-body"></div>
      <div class="skeleton-footer"></div>
    </div>
  `
}

// 响应式设计
const handleResize = debounce(() => {
  Object.values(chartInstances.value).forEach(chart => {
    if (chart && typeof chart.resize === 'function') {
      chart.resize()
    }
  })
}, 300)

window.addEventListener('resize', handleResize)
```

---

*本指南涵盖了 KLineChart 在项目中的完整集成方案，包括组件使用、状态管理、性能优化等方面。如有问题请参考相关文档或联系开发团队。*