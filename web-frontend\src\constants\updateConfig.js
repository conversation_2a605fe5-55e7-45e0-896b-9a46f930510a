/**
 * 数据更新频率配置
 * 统一管理各种数据的更新间隔，便于调整和维护
 * 现在使用配置管理系统，支持动态调整
 */

import chartConfig, { getUpdateConfig } from '@/config/chartConfig'

// 默认更新频率配置（单位：毫秒）- 作为后备配置
const DEFAULT_UPDATE_INTERVALS = {
  // 实时价格数据（Ticker）- 优化频率减少闪烁
  TICKER: 3000, // 3秒
  
  // K线数据 - 中频更新（优化频率减少闪烁）
  CANDLESTICK: 30000, // 30秒
  
  // 账户余额 - 低频更新
  BALANCE: 30000, // 30秒
  
  // 持仓信息 - 低频更新
  POSITIONS: 15000, // 15秒
  
  // 订单状态 - 中频更新
  ORDERS: 5000, // 5秒
  
  // 市场深度 - 高频更新
  DEPTH: 2000, // 2秒
  
  // 成交记录 - 中频更新
  TRADES: 5000, // 5秒
  
  // 技术指标 - 跟随K线数据
  INDICATORS: 15000, // 15秒
  
  // 实时成交量 - 跟随K线数据
  VOLUME: 10000, // 10秒
  
  // WebSocket心跳
  HEARTBEAT: 10000, // 10秒
  
  // 重连间隔
  RECONNECT: 5000 // 5秒
};

// 动态获取更新频率配置
export const UPDATE_INTERVALS = new Proxy(DEFAULT_UPDATE_INTERVALS, {
  get(target, prop) {
    const updateConfig = getUpdateConfig()
    const configKey = prop.toLowerCase() + 'Interval'
    return updateConfig[configKey] || target[prop] || 5000
  }
})

// 根据市场活跃度动态调整的配置
export const DYNAMIC_INTERVALS = {
  // 高活跃度（交易量大）
  HIGH_ACTIVITY: {
    TICKER: 1000,
    CANDLESTICK: 5000,
    DEPTH: 1000
  },
  
  // 中等活跃度
  MEDIUM_ACTIVITY: {
    TICKER: 3000,
    CANDLESTICK: 10000,
    DEPTH: 2000
  },
  
  // 低活跃度（交易量小）
  LOW_ACTIVITY: {
    TICKER: 5000,
    CANDLESTICK: 15000,
    DEPTH: 5000
  }
};

// 获取当前使用的更新间隔
export function getUpdateInterval(type, activityLevel = 'MEDIUM_ACTIVITY') {
  if (DYNAMIC_INTERVALS[activityLevel] && DYNAMIC_INTERVALS[activityLevel][type]) {
    return DYNAMIC_INTERVALS[activityLevel][type];
  }
  return UPDATE_INTERVALS[type] || 5000;
}

// 判断市场活跃度的函数
export function getMarketActivity(volume24h) {
  if (!volume24h) return 'MEDIUM_ACTIVITY';
  
  const vol = parseFloat(volume24h);
  if (vol > 1000000) return 'HIGH_ACTIVITY';
  if (vol > 100000) return 'MEDIUM_ACTIVITY';
  return 'LOW_ACTIVITY';
}

// 更新频率的说明
export const UPDATE_DESCRIPTIONS = {
  TICKER: '实时价格数据，影响价格显示的实时性',
  CANDLESTICK: 'K线数据，影响图表和技术分析的准确性',
  BALANCE: '账户余额，影响资金显示的准确性',
  POSITIONS: '持仓信息，影响仓位管理的实时性',
  ORDERS: '订单状态，影响交易执行的监控',
  DEPTH: '市场深度，影响买卖盘显示的实时性',
  TRADES: '成交记录，影响市场活动的监控',
  INDICATORS: '技术指标，影响分析工具的准确性',
  VOLUME: '实时成交量，影响交易量分析的准确性'
};