import { ref, computed, nextTick, onUnmounted } from 'vue'
import * as klinecharts from 'klinecharts'
import { useChartSettingsStore } from '@/stores/chartSettingsStore'
import performanceMonitor, { withPerformanceMonitoring } from '@/utils/performanceMonitor'
import memoryManager, { createManagedInterval } from '@/utils/memoryManager'
import { getUpdateConfig } from '@/config/chartConfig'

export function useKLineCharts(refs = {}) {
  // 图表实例
  const chartInstances = ref({})
  const isInitialized = ref(false)
  const currentTheme = ref('light')
  
  // 主面板指标列表
  const mainPaneIndicators = ['MA', 'EMA', 'BOLL', 'SuperTrend', 'SAR']
  
  // 倒计时相关
  const countdownTimer = ref(null) // 倒计时定时器
  
  // 获取图表设置store
  const chartSettingsStore = useChartSettingsStore()
  
  /**
   * 注册图表实例
   * @param {string} key - 图表实例的唯一标识
   * @param {Object} chartInstance - 图表实例
   */
  const registerChartInstance = (key, chartInstance) => {
    chartInstances.value[key] = chartInstance
  }
  
  /**
   * 注销图表实例
   * @param {string} key - 图表实例的唯一标识
   */
  const unregisterChartInstance = (key) => {
    delete chartInstances.value[key]
  }

  /**
   * 更新图表样式
   * @param {boolean} isDark - 是否为暗色主题
   * @param {Object} customStyles - 自定义样式配置
   */
  const updateChartStyles = (isDark = false, customStyles = {}) => {
    const newStyles = getKLineThemeOptions(isDark, customStyles)
    
    Object.values(chartInstances.value).forEach(chart => {
      if (chart && typeof chart.setStyles === 'function') {
        chart.setStyles(newStyles)
      }
    })
  }

  /**
   * 获取当前时间周期
   * @returns {string} 时间周期
   */
  const getCurrentTimeframe = () => {
    return chartSettingsStore.selectedTimeframe
  }

  /**
   * 计算下一个时间周期的时间戳
   * @param {number} timestamp - 当前时间戳
   * @param {string} timeframe - 时间周期
   * @returns {number} 下一个周期的时间戳
   */
  const getNextPeriodTime = (timestamp, timeframe) => {
    const date = new Date(timestamp)
    const timeframeMap = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1H': 60 * 60 * 1000,
      '2H': 2 * 60 * 60 * 1000,
      '4H': 4 * 60 * 60 * 1000,
      '6H': 6 * 60 * 60 * 1000,
      '12H': 12 * 60 * 60 * 1000,
      '1D': 24 * 60 * 60 * 1000,
      '3D': 3 * 24 * 60 * 60 * 1000,
      '1W': 7 * 24 * 60 * 60 * 1000,
      '1M': 30 * 24 * 60 * 60 * 1000
    }
    
    const interval = timeframeMap[timeframe] || 60 * 1000
    return Math.ceil(date.getTime() / interval) * interval
  }

  /**
   * 启动倒计时更新
   */
  const startCountdownUpdate = () => {
    const startTime = performance.now()
    
    try {
      if (countdownTimer.value) {
        memoryManager.unregisterTimer(countdownTimer.value)
      }
      
      const updateConfig = getUpdateConfig()
      const interval = updateConfig.countdownInterval || 5000
      
      countdownTimer.value = createManagedInterval(() => {
         // 触发图表更新以显示倒计时效果
         Object.values(chartInstances.value).forEach(chart => {
           if (chart && typeof chart.invalidate === 'function') {
             chart.invalidate() // 触发图表重绘
           }
         })
         
         const tickTime = performance.now()
         performanceMonitor.recordChartRenderTime('countdown_tick', 1) // 记录倒计时tick事件
       }, interval)
      
      const endTime = performance.now()
      performanceMonitor.recordChartRenderTime('countdown_update', endTime - startTime)
    } catch (error) {
      performanceMonitor.recordError(error, 'countdown_update')
      throw error
    }
  }

  /**
   * 停止倒计时更新
   */
  const stopCountdownUpdate = () => {
    if (countdownTimer.value) {
      memoryManager.unregisterTimer(countdownTimer.value)
      countdownTimer.value = null
    }
  }

  /**
   * 获取KLineChart主题配置
   * @param {boolean} isDark - 是否为暗色主题
   * @param {Object} customStyles - 自定义样式配置
   * @returns {Object} 主题配置
   */
  const getKLineThemeOptions = (isDark = false, customStyles = {}) => {
    const defaultStyles = {
      upColor: isDark ? '#26a69a' : '#26a69a',
      downColor: isDark ? '#ef5350' : '#ef5350',
      noChangeColor: isDark ? '#888888' : '#888888',
      gridColor: isDark ? '#333333' : '#e8e8e8',
      axisColor: isDark ? '#666666' : '#DDDDDD',
      textColor: isDark ? '#D9D9D9' : '#76808F',
      animationDuration: 1000
    }
    
    const styles = { ...defaultStyles, ...customStyles }
    return {
      // 动画配置
      animationDuration: styles.animationDuration,
      
      grid: {
        show: true,
        horizontal: {
          show: true,
          size: 1,
          color: styles.gridColor,
          style: 'solid'
        },
        vertical: {
          show: true,
          size: 1,
          color: styles.gridColor,
          style: 'solid'
        }
      },
      candle: {
        type: 'candle_solid',
        bar: {
          upColor: styles.upColor,
          downColor: styles.downColor,
          noChangeColor: styles.noChangeColor
        },
        area: {
          point: {
            animation: true,
            animationDuration: styles.animationDuration
          }
        },
        tooltip: {
          showRule: 'always',
          showType: 'standard',
          labels: ['时间', '开盘', '收盘', '最高', '最低', '成交量']
        }
      },
      technicalIndicator: {
        margin: {
          top: 0.2,
          bottom: 0.1
        },
        bar: {
          upColor: styles.upColor,
          downColor: styles.downColor,
          noChangeColor: styles.noChangeColor
        },
        line: {
          size: 1,
          colors: [
            isDark ? '#FF6B6B' : '#FF6B6B',
            isDark ? '#4ECDC4' : '#4ECDC4', 
            isDark ? '#45B7D1' : '#45B7D1',
            isDark ? '#96CEB4' : '#96CEB4',
            isDark ? '#FFEAA7' : '#FFEAA7'
          ]
        },
        circle: {
          upColor: styles.upColor,
          downColor: styles.downColor,
          noChangeColor: styles.noChangeColor
        }
      },
      xAxis: {
        show: true,
        height: null,
        axisLine: {
          show: true,
          color: styles.axisColor,
          size: 1
        },
        tickText: {
          show: true,
          color: styles.textColor,
          size: 12,
          family: 'Helvetica Neue',
          weight: 'normal',
          paddingTop: 3,
          paddingBottom: 6,
          // 自定义格式化函数，支持倒计时显示
          formatter: (timestamp, { type }) => {
            const date = new Date(timestamp)
            const now = new Date()
            
            // 检查是否启用倒计时显示
            const showCountdown = chartSettingsStore.displaySettings?.showCountdown !== false
            
            // 计算到下一个时间周期的倒计时
            const timeframe = getCurrentTimeframe()
            const nextPeriod = getNextPeriodTime(timestamp, timeframe)
            const countdown = Math.max(0, Math.floor((nextPeriod - now.getTime()) / 1000))
            
            if (type === 'crosshair' && showCountdown && countdown > 0) {
              const minutes = Math.floor(countdown / 60)
              const seconds = countdown % 60
              return `${date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })} (${minutes}:${seconds.toString().padStart(2, '0')})`
            }
            
            return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
          }
        },
        tickLine: {
          show: true,
          size: 1,
          length: 3,
          color: styles.axisColor
        }
      },
      yAxis: {
        show: true,
        width: null,
        position: 'right',
        type: 'normal',
        inside: false,
        reverse: false,
        axisLine: {
          show: true,
          color: styles.axisColor,
          size: 1
        },
        tickText: {
          show: true,
          color: styles.textColor,
          size: 12,
          family: 'Helvetica Neue',
          weight: 'normal',
          paddingLeft: 3,
          paddingRight: 6
        },
        tickLine: {
          show: true,
          size: 1,
          length: 3,
          color: styles.axisColor
        }
      },
      separator: {
        size: 1,
        color: isDark ? '#333333' : '#e8e8e8',
        fill: true,
        activeBackgroundColor: isDark ? 'rgba(230, 230, 230, .15)' : 'rgba(230, 230, 230, .15)'
      },
      crosshair: {
        show: true,
        horizontal: {
          show: true,
          line: {
            show: true,
            style: 'dash',
            dashValue: [4, 2],
            size: 1,
            color: styles.axisColor
          },
          text: {
            show: true,
            color: isDark ? '#D9D9D9' : '#FFFFFF',
            size: 12,
            family: 'Helvetica Neue',
            weight: 'normal',
            borderStyle: 'solid',
            borderSize: 1,
            borderColor: isDark ? '#666666' : '#686D76',
            borderRadius: 2,
            paddingLeft: 4,
            paddingRight: 4,
            paddingTop: 2,
            paddingBottom: 2,
            backgroundColor: isDark ? '#686D76' : '#686D76'
          }
        },
        vertical: {
          show: true,
          line: {
            show: true,
            style: 'dash',
            dashValue: [4, 2],
            size: 1,
            color: styles.axisColor
          },
          text: {
            show: true,
            color: isDark ? '#D9D9D9' : '#FFFFFF',
            size: 12,
            family: 'Helvetica Neue',
            weight: 'normal',
            borderStyle: 'solid',
            borderSize: 1,
            borderColor: isDark ? '#666666' : '#686D76',
            borderRadius: 2,
            paddingLeft: 4,
            paddingRight: 4,
            paddingTop: 2,
            paddingBottom: 2,
            backgroundColor: isDark ? '#686D76' : '#686D76'
          }
        }
      }
    }
  }

  /**
   * 初始化主K线图表
   * @param {HTMLElement} container - 图表容器
   * @param {boolean} isDark - 是否为暗色主题
   * @param {Object} customStyles - 自定义样式配置
   * @returns {Object} 图表实例
   */
  const initializeMainChart = async (container, isDark = false, customStyles = {}) => {
    try {
      if (!container) {
        throw new Error('图表容器不存在')
      }

      // 创建主图表实例
      const chart = klinecharts.init(container, {
        theme: isDark ? 'dark' : 'light',
        styles: getKLineThemeOptions(isDark, customStyles)
      })

      if (!chart) {
        throw new Error('KLineChart实例创建失败')
      }

      // 注意：VOL指标现在由UnifiedIndicatorManager统一管理
      // 不在这里直接创建指标，避免重复
      
      // 存储图表实例
      chartInstances.value.main = chart
      currentTheme.value = isDark ? 'dark' : 'light'

      // 注册到全局图表实例管理器（开发环境）
      if (process.env.NODE_ENV === 'development' && window.registerChartInstance) {
        window.registerChartInstance('kline_main', chart)
      }

      console.log('主K线图表初始化成功，等待指标管理器添加指标')
      return chart
    } catch (error) {
      console.error('初始化主K线图表失败:', error)
      throw error
    }
  }

  /**
   * 初始化成交量图表
   * @param {HTMLElement} container - 图表容器
   * @param {boolean} isDark - 是否为暗色主题
   * @param {Object} customStyles - 自定义样式配置
   * @returns {Object} 图表实例
   */
  const initializeVolumeChart = async (container, isDark = false, customStyles = {}) => {
    try {
      if (!container) {
        throw new Error('成交量图表容器不存在')
      }

      // 创建成交量图表实例
      const chart = klinecharts.init(container, {
        theme: isDark ? 'dark' : 'light',
        styles: getKLineThemeOptions(isDark, customStyles)
      })

      if (!chart) {
        throw new Error('成交量图表实例创建失败')
      }

      // 注意：VOL指标现在由UnifiedIndicatorManager统一管理
      // 不在这里直接创建指标，避免重复
      
      // 存储图表实例
      chartInstances.value.volume = chart

      console.log('成交量图表初始化成功')
      return chart
    } catch (error) {
      console.error('初始化成交量图表失败:', error)
      throw error
    }
  }

  /**
   * 更新图表数据
   * @param {Array} klineData - K线数据
   * @param {Object} options - 更新选项
   */
  const updateChartData = async (klineData, options = {}) => {
    try {
      if (!klineData || !Array.isArray(klineData) || klineData.length === 0) {
        console.warn('K线数据为空或格式不正确')
        return
      }

      // 转换数据格式为KLineChart所需格式
      const formattedData = klineData.map(item => ({
        timestamp: item.timestamp || item.time,
        open: parseFloat(item.open),
        high: parseFloat(item.high),
        low: parseFloat(item.low),
        close: parseFloat(item.close),
        volume: parseFloat(item.volume || 0)
      }))

      // 更新主图表数据
      if (chartInstances.value.main) {
        if (options.append) {
          // 追加数据
          formattedData.forEach(dataPoint => {
            chartInstances.value.main.updateData(dataPoint)
          })
        } else {
          // 替换所有数据
          chartInstances.value.main.applyNewData(formattedData)
        }
      }

      // 更新成交量图表数据
      if (chartInstances.value.volume) {
        if (options.append) {
          formattedData.forEach(dataPoint => {
            chartInstances.value.volume.updateData(dataPoint)
          })
        } else {
          chartInstances.value.volume.applyNewData(formattedData)
        }
      }

      console.log('图表数据更新成功，数据点数量:', formattedData.length)
    } catch (error) {
      console.error('更新图表数据失败:', error)
      throw error
    }
  }

  /**
   * 更新图表主题
   * @param {boolean} isDark - 是否为暗色主题
   */
  const updateTheme = async (isDark) => {
    try {
      const newTheme = isDark ? 'dark' : 'light'
      const themeOptions = getKLineThemeOptions(isDark)

      // 更新所有图表实例的主题
      Object.values(chartInstances.value).forEach(chart => {
        if (chart && typeof chart.setStyles === 'function') {
          chart.setStyles(themeOptions)
        }
      })

      currentTheme.value = newTheme
      console.log('图表主题更新成功:', newTheme)
    } catch (error) {
      console.error('更新图表主题失败:', error)
    }
  }

  /**
   * 添加技术指标
   * @param {string} indicatorName - 指标名称
   * @param {Object} params - 指标参数
   * @param {string} paneId - 面板ID
   * @param {boolean} forceMainPane - 强制添加到主面板
   */
  const addIndicator = (indicatorName, params = {}, paneId = null, forceMainPane = false) => {
    try {
      const mainChart = chartInstances.value.main
      if (!mainChart) {
        console.warn('主图表实例不存在，无法添加指标')
        return
      }

      // 定义应该显示在主图上的指标
      const mainPaneIndicators = ['MA', 'EMA', 'BOLL', 'SuperTrend', 'SAR']
      const shouldShowOnMain = forceMainPane || mainPaneIndicators.includes(indicatorName)

      let indicatorId
      if (shouldShowOnMain) {
        // 添加到主面板（K线图上）
        indicatorId = mainChart.createIndicator(indicatorName, false, {
          id: 'candle_pane', // 主面板ID
          ...params
        })
        console.log(`技术指标 ${indicatorName} 已添加到主图，ID: ${indicatorId}`)
      } else {
        // 添加到独立面板
        indicatorId = mainChart.createIndicator(indicatorName, true, {
          id: paneId || `${indicatorName.toLowerCase()}_pane`,
          height: 120,
          minHeight: 80,
          dragEnabled: true,
          gap: { top: 0.2, bottom: 0.1 },
          ...params
        })
        console.log(`技术指标 ${indicatorName} 已添加到独立面板，ID: ${indicatorId}`)
      }

      return indicatorId
    } catch (error) {
      console.error(`添加技术指标 ${indicatorName} 失败:`, error)
    }
  }

  /**
   * 添加均线到主图
   * @param {Array} periods - 均线周期数组，如 [5, 10, 20, 60]
   * @param {string} type - 均线类型，'MA' 或 'EMA'
   */
  const addMovingAverages = (periods = [5, 10, 20, 60], type = 'MA') => {
    try {
      const mainChart = chartInstances.value.main
      if (!mainChart) {
        console.warn('主图表实例不存在，无法添加均线')
        return []
      }

      const indicatorIds = []
      periods.forEach(period => {
        const indicatorId = mainChart.createIndicator(type, false, {
          id: 'candle_pane',
          calcParams: [period],
          styles: {
            lines: [{
              color: getMAColor(period)
            }]
          }
        })
        if (indicatorId) {
          indicatorIds.push(indicatorId)
          console.log(`${type}${period} 已添加到主图，ID: ${indicatorId}`)
        }
      })

      return indicatorIds
    } catch (error) {
      console.error(`添加${type}均线失败:`, error)
      return []
    }
  }

  /**
   * 添加布林带到主图
   * @param {number} period - 周期，默认20
   * @param {number} stdDev - 标准差倍数，默认2
   */
  const addBollingerBands = (period = 20, stdDev = 2) => {
    try {
      const mainChart = chartInstances.value.main
      if (!mainChart) {
        console.warn('主图表实例不存在，无法添加布林带')
        return
      }

      const indicatorId = mainChart.createIndicator('BOLL', false, {
        id: 'candle_pane',
        calcParams: [period, stdDev],
        styles: {
          lines: [
            { color: '#FF6B6B' }, // 上轨
            { color: '#4ECDC4' }, // 中轨
            { color: '#45B7D1' }  // 下轨
          ]
        }
      })

      if (indicatorId) {
        console.log(`布林带已添加到主图，ID: ${indicatorId}`)
      }
      return indicatorId
    } catch (error) {
      console.error('添加布林带失败:', error)
    }
  }

  /**
   * 添加支撑阻力位到主图
   * @param {number} lookbackPeriod - 回看周期，默认50
   * @param {number} minTouchCount - 最小触碰次数，默认2
   */
  const addSupportResistance = (lookbackPeriod = 50, minTouchCount = 2) => {
    try {
      const mainChart = chartInstances.value.main
      if (!mainChart) {
        console.warn('主图表实例不存在，无法添加支撑阻力位')
        return
      }

      const indicatorId = mainChart.createIndicator('SupportResistanceTrend', false, {
        id: 'candle_pane',
        calcParams: [lookbackPeriod, minTouchCount]
      })

      if (indicatorId) {
        console.log(`支撑阻力位已添加到主图，ID: ${indicatorId}`)
      }
      return indicatorId
    } catch (error) {
      console.error('添加支撑阻力位失败:', error)
    }
  }

  /**
   * 获取均线颜色
   * @param {number} period - 周期
   * @returns {string} 颜色值
   */
  const getMAColor = (period) => {
    const colorMap = {
      5: '#FF6B6B',
      10: '#4ECDC4',
      20: '#45B7D1',
      30: '#96CEB4',
      60: '#FFEAA7',
      120: '#DDA0DD',
      250: '#98D8C8'
    }
    return colorMap[period] || '#888888'
  }

  /**
   * 移除技术指标
   * @param {string} indicatorId - 指标ID
   */
  const removeIndicator = (indicatorId) => {
    try {
      const mainChart = chartInstances.value.main
      if (!mainChart) {
        console.warn('主图表实例不存在，无法移除指标')
        return
      }

      mainChart.removeIndicator(indicatorId)
      console.log(`技术指标 ${indicatorId} 移除成功`)
    } catch (error) {
      console.error(`移除技术指标 ${indicatorId} 失败:`, error)
    }
  }

  /**
   * 初始化所有图表
   * @param {boolean} isDark - 是否为暗色主题
   */
  const initializeCharts = async (isDark = false) => {
    try {
      console.log('开始初始化KLineChart图表...')

      // 等待DOM更新
      await nextTick()

      const promises = []

      // 初始化主K线图表
      if (refs.klineChartRef?.value) {
        const container = refs.klineChartRef.value
        promises.push(initializeMainChart(container, isDark))
      }

      // 注意：成交量图表现在由UnifiedIndicatorManager统一管理
      // 不再在这里单独初始化成交量图表，避免重复创建

      await Promise.all(promises)
      isInitialized.value = true
      
      // 启动倒计时更新
      startCountdownUpdate()
      
      console.log('所有KLineChart图表初始化完成，倒计时已启动')
    } catch (error) {
      console.error('初始化KLineChart图表失败:', error)
      throw error
    }
  }

  /**
   * 销毁所有图表
   */
  const destroyCharts = () => {
    try {
      // 停止倒计时更新
      stopCountdownUpdate()
      
      Object.values(chartInstances.value).forEach(chart => {
        if (chart && typeof chart.dispose === 'function') {
          chart.dispose()
        }
      })
      
      chartInstances.value = {}
      isInitialized.value = false
      console.log('所有KLineChart图表已销毁，倒计时已停止')
    } catch (error) {
      console.error('销毁KLineChart图表失败:', error)
    }
  }

  /**
   * 调整图表大小
   */
  const resizeCharts = () => {
    try {
      Object.values(chartInstances.value).forEach(chart => {
        if (chart && typeof chart.resize === 'function') {
          chart.resize()
        }
      })
      console.log('图表大小调整完成')
    } catch (error) {
      console.error('调整图表大小失败:', error)
    }
  }

  return {
    // 状态
    chartInstances,
    isInitialized,
    currentTheme,
    
    // 图表实例别名（为了兼容性）
    get klineChartInstance() {
      return chartInstances.value.main
    },
    
    // 方法
    initializeCharts,
    updateChartData,
    updateTheme,
    updateChartStyles,
    addIndicator,
    removeIndicator,
    destroyCharts,
    resizeCharts,
    registerChartInstance,
    unregisterChartInstance,
    
    // 指标添加方法
    addMovingAverages,
    addBollingerBands,
    addSupportResistance,
    
    // 倒计时相关
    startCountdownUpdate,
    stopCountdownUpdate,
    
    // 单独的初始化方法
    initializeMainChart,
    initializeVolumeChart
  }
}