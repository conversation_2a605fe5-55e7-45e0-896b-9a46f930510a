<template>
  <div class="trading-signals-panel">
    <a-card title="交易信号" style="margin-bottom: 16px;">
      <template #extra>
        <a-space>
          <a-badge :count="unreadSignalsCount" :offset="[10, 0]">
            <a-button size="small" @click="markAllAsRead">
              <template #icon>
                <CheckOutlined />
              </template>
              全部已读
            </a-button>
          </a-badge>
          <a-button size="small" @click="clearAllSignals">
            <template #icon>
              <ClearOutlined />
            </template>
            清空
          </a-button>
        </a-space>
      </template>

      <div class="signals-container">
        <a-empty v-if="tradingSignals.length === 0" description="暂无交易信号" />
        
        <div v-else class="signals-list">
          <div
            v-for="signal in displayedSignals"
            :key="signal.id"
            :class="[
              'signal-item',
              signal.type,
              { 'unread': !signal.isRead }
            ]"
            @click="handleSignalClick(signal)"
          >
            <div class="signal-header">
              <div class="signal-type">
                <a-tag
                  :color="getSignalColor(signal.type)"
                  class="signal-tag"
                >
                  <template #icon>
                    <ArrowUpOutlined v-if="signal.type === 'buy'" />
                    <ArrowDownOutlined v-if="signal.type === 'sell'" />
                    <ExclamationCircleOutlined v-if="signal.type === 'warning'" />
                  </template>
                  {{ getSignalTypeText(signal.type) }}
                </a-tag>
              </div>
              <div class="signal-time">
                {{ formatTime(signal.timestamp) }}
              </div>
            </div>
            
            <div class="signal-content">
              <div class="signal-symbol">{{ signal.symbol }}</div>
              <div class="signal-price">价格: {{ signal.price }}</div>
              <div class="signal-indicator">{{ signal.indicator }}</div>
            </div>
            
            <div class="signal-description">
              {{ signal.description }}
            </div>
            
            <div v-if="signal.confidence" class="signal-confidence">
              <span class="confidence-label">信号强度:</span>
              <a-progress
                :percent="signal.confidence"
                :stroke-color="getConfidenceColor(signal.confidence)"
                size="small"
                :show-info="false"
                style="width: 60px;"
              />
              <span class="confidence-value">{{ signal.confidence }}%</span>
            </div>
            
            <div class="signal-actions">
              <a-button
                v-if="signal.type === 'buy' || signal.type === 'sell'"
                type="primary"
                size="small"
                @click.stop="handleQuickTrade(signal)"
              >
                快速{{ signal.type === 'buy' ? '买入' : '卖出' }}
              </a-button>
              <a-button
                size="small"
                @click.stop="handleIgnoreSignal(signal)"
              >
                忽略
              </a-button>
            </div>
          </div>
        </div>
        
        <div v-if="tradingSignals.length > displayLimit" class="signals-pagination">
          <a-pagination
            v-model:current="currentPage"
            :total="tradingSignals.length"
            :page-size="displayLimit"
            size="small"
            :show-size-changer="false"
            :show-quick-jumper="false"
          />
        </div>
      </div>
    </a-card>

    <!-- 快速交易表单 -->
    <a-card title="快速交易" size="small">
      <a-form
        :model="quickTradeForm"
        layout="vertical"
        @finish="handleQuickTradeSubmit"
      >
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="交易类型" name="type">
              <a-select v-model:value="quickTradeForm.type" size="small">
                <a-select-option value="buy">买入</a-select-option>
                <a-select-option value="sell">卖出</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="订单类型" name="orderType">
              <a-select v-model:value="quickTradeForm.orderType" size="small">
                <a-select-option value="market">市价单</a-select-option>
                <a-select-option value="limit">限价单</a-select-option>
                <a-select-option value="stop">止损单</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item label="数量" name="amount">
              <a-input-number
                v-model:value="quickTradeForm.amount"
                :min="0"
                :step="0.001"
                size="small"
                style="width: 100%"
                placeholder="输入数量"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              v-if="quickTradeForm.orderType === 'limit'"
              label="价格"
              name="price"
            >
              <a-input-number
                v-model:value="quickTradeForm.price"
                :min="0"
                :step="0.01"
                size="small"
                style="width: 100%"
                placeholder="输入价格"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item>
          <a-space style="width: 100%; justify-content: space-between;">
            <a-space>
              <span class="quick-amount-btn" @click="setQuickAmount(25)">25%</span>
              <span class="quick-amount-btn" @click="setQuickAmount(50)">50%</span>
              <span class="quick-amount-btn" @click="setQuickAmount(75)">75%</span>
              <span class="quick-amount-btn" @click="setQuickAmount(100)">100%</span>
            </a-space>
            <a-button
              type="primary"
              html-type="submit"
              size="small"
              :loading="isTrading"
            >
              {{ quickTradeForm.type === 'buy' ? '买入' : '卖出' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 信号详情模态框 -->
    <a-modal
      v-model:open="showSignalDetail"
      title="信号详情"
      :footer="null"
      width="500px"
    >
      <div v-if="selectedSignal" class="signal-detail">
        <a-descriptions :column="1" bordered size="small">
          <a-descriptions-item label="信号类型">
            <a-tag :color="getSignalColor(selectedSignal.type)">
              {{ getSignalTypeText(selectedSignal.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="交易对">{{ selectedSignal.symbol }}</a-descriptions-item>
          <a-descriptions-item label="价格">{{ selectedSignal.price }}</a-descriptions-item>
          <a-descriptions-item label="指标">{{ selectedSignal.indicator }}</a-descriptions-item>
          <a-descriptions-item label="时间">{{ formatFullTime(selectedSignal.timestamp) }}</a-descriptions-item>
          <a-descriptions-item v-if="selectedSignal.confidence" label="信号强度">
            {{ selectedSignal.confidence }}%
          </a-descriptions-item>
          <a-descriptions-item label="描述">
            {{ selectedSignal.description }}
          </a-descriptions-item>
        </a-descriptions>
        
        <div v-if="selectedSignal.additionalData" class="additional-data">
          <h4>技术指标数据</h4>
          <a-descriptions :column="2" size="small">
            <a-descriptions-item
              v-for="(value, key) in selectedSignal.additionalData"
              :key="key"
              :label="key"
            >
              {{ value }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  CheckOutlined,
  ClearOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

// 定义 props
const props = defineProps({
  tradingSignals: {
    type: Array,
    default: () => []
  }
})

// 定义事件
const emit = defineEmits(['quick-trade', 'signal-click', 'signal-ignore'])

// 本地状态
const currentPage = ref(1)
const displayLimit = ref(10)
const showSignalDetail = ref(false)
const selectedSignal = ref(null)
const isTrading = ref(false)

// 快速交易表单
const quickTradeForm = ref({
  type: 'buy',
  orderType: 'market',
  amount: null,
  price: null
})

// 计算属性
const unreadSignalsCount = computed(() => {
  return props.tradingSignals.filter(signal => !signal.isRead).length
})

const displayedSignals = computed(() => {
  const start = (currentPage.value - 1) * displayLimit.value
  const end = start + displayLimit.value
  return props.tradingSignals.slice(start, end)
})

// 工具函数
const getSignalColor = (type) => {
  const colors = {
    buy: 'green',
    sell: 'red',
    warning: 'orange',
    info: 'blue'
  }
  return colors[type] || 'default'
}

const getSignalTypeText = (type) => {
  const texts = {
    buy: '买入信号',
    sell: '卖出信号',
    warning: '警告',
    info: '信息'
  }
  return texts[type] || type
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 80) return '#52c41a'
  if (confidence >= 60) return '#faad14'
  return '#ff4d4f'
}

const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatFullTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN')
}

// 事件处理函数
const handleSignalClick = (signal) => {
  selectedSignal.value = signal
  showSignalDetail.value = true
  
  // 标记为已读
  if (!signal.isRead) {
    signal.isRead = true
  }
  
  emit('signal-click', signal)
}

const handleQuickTrade = (signal) => {
  // 根据信号预填表单
  quickTradeForm.value.type = signal.type
  quickTradeForm.value.price = signal.price
  
  // 发送快速交易事件
  emit('quick-trade', {
    signal,
    tradeData: { ...quickTradeForm.value }
  })
}

const handleIgnoreSignal = (signal) => {
  emit('signal-ignore', signal)
}

const handleQuickTradeSubmit = async () => {
  if (!quickTradeForm.value.amount) {
    message.error('请输入交易数量')
    return
  }
  
  if (quickTradeForm.value.orderType === 'limit' && !quickTradeForm.value.price) {
    message.error('限价单请输入价格')
    return
  }
  
  isTrading.value = true
  
  try {
    emit('quick-trade', {
      tradeData: { ...quickTradeForm.value }
    })
    
    message.success('交易订单已提交')
    
    // 重置表单
    quickTradeForm.value.amount = null
    quickTradeForm.value.price = null
  } catch (error) {
    message.error('交易失败: ' + error.message)
  } finally {
    isTrading.value = false
  }
}

const setQuickAmount = (percentage) => {
  // 这里应该根据实际的账户余额计算
  // 现在使用模拟值
  const mockBalance = 1000
  quickTradeForm.value.amount = (mockBalance * percentage / 100).toFixed(3)
}

const markAllAsRead = () => {
  props.tradingSignals.forEach(signal => {
    signal.isRead = true
  })
  message.success('所有信号已标记为已读')
}

const clearAllSignals = () => {
  // 这里应该调用父组件的清空方法
  message.success('所有信号已清空')
}

// 监听信号变化，自动滚动到最新信号
watch(
  () => props.tradingSignals.length,
  (newLength, oldLength) => {
    if (newLength > oldLength) {
      // 有新信号时，跳转到第一页
      currentPage.value = 1
    }
  }
)
</script>

<style scoped>
.trading-signals-panel {
  height: 100%;
}

.signals-container {
  max-height: 400px;
  overflow-y: auto;
}

.signals-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.signal-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.signal-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.signal-item.unread {
  border-left: 4px solid #1890ff;
  background: #f6ffed;
}

.signal-item.buy {
  border-left-color: #52c41a;
}

.signal-item.sell {
  border-left-color: #ff4d4f;
}

.signal-item.warning {
  border-left-color: #faad14;
}

.signal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.signal-tag {
  margin: 0;
}

.signal-time {
  font-size: 12px;
  color: #8c8c8c;
}

.signal-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.signal-symbol {
  font-weight: bold;
  color: #1890ff;
}

.signal-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.signal-confidence {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
}

.confidence-label {
  color: #8c8c8c;
}

.confidence-value {
  color: #666;
  font-weight: 500;
}

.signal-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.signals-pagination {
  margin-top: 16px;
  text-align: center;
}

.quick-amount-btn {
  padding: 2px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  transition: all 0.3s ease;
}

.quick-amount-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.signal-detail .additional-data {
  margin-top: 16px;
}

.signal-detail .additional-data h4 {
  margin-bottom: 8px;
  color: #1890ff;
}

/* 深色主题样式 */
.trading-signals-panel.dark-theme .signal-item {
  background: #2a2e39;
  border-color: #434651;
  color: #ffffff;
}

.trading-signals-panel.dark-theme .signal-item:hover {
  border-color: #2962ff;
  box-shadow: 0 2px 8px rgba(41, 98, 255, 0.1);
}

.trading-signals-panel.dark-theme .signal-item.unread {
  background: #1f2937;
}

.trading-signals-panel.dark-theme .signal-time {
  color: #9ca3af;
}

.trading-signals-panel.dark-theme .signal-description {
  color: #d1d5db;
}

.trading-signals-panel.dark-theme .quick-amount-btn {
  border-color: #434651;
  color: #d1d5db;
  background: #2a2e39;
}

.trading-signals-panel.dark-theme .quick-amount-btn:hover {
  border-color: #2962ff;
  color: #2962ff;
}
</style>