#!/usr/bin/env python3
"""
项目环境设置脚本
自动化设置开发环境，包括依赖安装、配置文件创建等
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, cwd=None):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True, 
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    print(f"✅ Python {sys.version.split()[0]}")
    return True

def check_node_version():
    """检查Node.js版本"""
    print("🟢 检查Node.js版本...")
    success, output = run_command("node --version")
    if not success:
        print("❌ 未找到Node.js，请先安装Node.js")
        return False
    
    version = output.strip().replace('v', '')
    major_version = int(version.split('.')[0])
    if major_version < 16:
        print(f"❌ 需要Node.js 16或更高版本，当前版本: {version}")
        return False
    
    print(f"✅ Node.js {version}")
    return True

def setup_python_env():
    """设置Python环境"""
    print("\n🔧 设置Python环境...")
    
    # 检查虚拟环境
    venv_path = Path("venv")
    if not venv_path.exists():
        print("📦 创建虚拟环境...")
        success, output = run_command(f"{sys.executable} -m venv venv")
        if not success:
            print(f"❌ 创建虚拟环境失败: {output}")
            return False
        print("✅ 虚拟环境创建成功")
    else:
        print("✅ 虚拟环境已存在")
    
    # 激活虚拟环境并安装依赖
    if os.name == 'nt':  # Windows
        pip_path = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        pip_path = "venv/bin/pip"
    
    print("📦 安装Python依赖...")
    success, output = run_command(f"{pip_path} install -r requirements.txt")
    if not success:
        print(f"❌ 安装依赖失败: {output}")
        return False
    
    print("✅ Python依赖安装完成")
    return True

def setup_frontend_env():
    """设置前端环境"""
    print("\n🎨 设置前端环境...")
    
    frontend_path = Path("web-frontend")
    if not frontend_path.exists():
        print("❌ 未找到web-frontend目录")
        return False
    
    print("📦 安装前端依赖...")
    success, output = run_command("npm install", cwd="web-frontend")
    if not success:
        print(f"❌ 安装前端依赖失败: {output}")
        return False
    
    print("✅ 前端依赖安装完成")
    return True

def setup_config_files():
    """设置配置文件"""
    print("\n⚙️ 设置配置文件...")
    
    # 检查并创建config.env
    config_example = Path("config.env.example")
    config_file = Path("config.env")
    
    if config_example.exists() and not config_file.exists():
        shutil.copy(config_example, config_file)
        print("✅ 创建config.env文件")
        print("⚠️ 请编辑config.env文件，填入你的API配置")
    elif config_file.exists():
        print("✅ config.env文件已存在")
    else:
        print("⚠️ 未找到config.env.example文件")
    
    return True

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建项目目录...")
    
    directories = [
        "logs",
        "data",
        "backups"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"✅ 目录已存在: {directory}")
    
    return True

def main():
    """主函数"""
    print("🚀 OKX量化交易系统 - 环境设置")
    print("=" * 50)
    
    # 检查系统要求
    if not check_python_version():
        sys.exit(1)
    
    if not check_node_version():
        sys.exit(1)
    
    # 设置环境
    steps = [
        ("Python环境", setup_python_env),
        ("前端环境", setup_frontend_env),
        ("配置文件", setup_config_files),
        ("项目目录", create_directories)
    ]
    
    for step_name, step_func in steps:
        if not step_func():
            print(f"\n❌ {step_name}设置失败")
            sys.exit(1)
    
    print("\n🎉 环境设置完成！")
    print("\n📋 下一步:")
    print("1. 编辑 config.env 文件，填入你的OKX API配置")
    print("2. 启动后端: python run_backend.py")
    print("3. 启动前端: cd web-frontend && npm run dev")
    print("\n📚 更多信息请查看: docs/README.md")

if __name__ == "__main__":
    main()