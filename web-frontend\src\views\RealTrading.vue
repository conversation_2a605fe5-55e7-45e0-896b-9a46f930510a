<template>
  <div class="real-trading">
    <a-row :gutter="24">
      <!-- 左侧：交易面板 -->
      <a-col :span="16">
        <a-card title="实盘交易" style="margin-bottom: 24px;">
          <a-alert
            v-if="!hasApiConfig"
            message="请先配置API密钥"
            description="在开始交易前，请先在设置中配置您的OKX API密钥。"
            type="warning"
            show-icon
            style="margin-bottom: 24px;"
            action
          >
            <template #action>
              <a-button size="small" type="primary" @click="goToSettings">
                去设置
              </a-button>
            </template>
          </a-alert>
          
          <a-form
            :model="tradeForm"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
            @finish="submitOrder"
          >
            <a-form-item
              label="交易对"
              name="instId"
              :rules="[{ required: true, message: '请选择交易对' }]"
            >
              <a-select
                v-model:value="tradeForm.instId"
                placeholder="选择交易对"
                show-search
                :loading="loadingInstruments"
                @change="onInstrumentChange"
              >
                <a-select-option
                  v-for="instrument in instruments"
                  :key="instrument.instId"
                  :value="instrument.instId"
                >
                  {{ instrument.instId }}
                </a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item
              label="交易模式"
              name="tdMode"
            >
              <a-radio-group v-model:value="tradeForm.tdMode">
                <a-radio value="cash">现货</a-radio>
                <a-radio value="cross">全仓杠杆</a-radio>
                <a-radio value="isolated">逐仓杠杆</a-radio>
              </a-radio-group>
            </a-form-item>
            
            <a-form-item
              label="交易方向"
              name="side"
            >
              <a-radio-group v-model:value="tradeForm.side">
                <a-radio value="buy">买入</a-radio>
                <a-radio value="sell">卖出</a-radio>
              </a-radio-group>
            </a-form-item>
            
            <a-form-item
              label="订单类型"
              name="ordType"
            >
              <a-select v-model:value="tradeForm.ordType">
                <a-select-option value="market">市价单</a-select-option>
                <a-select-option value="limit">限价单</a-select-option>
                <a-select-option value="post_only">只做maker</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item
              v-if="tradeForm.ordType !== 'market'"
              label="价格"
              name="px"
              :rules="[{ required: tradeForm.ordType !== 'market', message: '请输入价格' }]"
            >
              <a-input-number
                v-model:value="tradeForm.px"
                placeholder="请输入价格"
                :precision="currentInstrument?.pxPrecision || 2"
                style="width: 100%"
              />
            </a-form-item>
            
            <a-form-item
              label="数量"
              name="sz"
              :rules="[{ required: true, message: '请输入数量' }]"
            >
              <a-input-number
                v-model:value="tradeForm.sz"
                placeholder="请输入数量"
                :precision="currentInstrument?.szPrecision || 4"
                style="width: 100%"
              />
            </a-form-item>
            
            <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
              <a-space>
                <a-button
                  type="primary"
                  html-type="submit"
                  :loading="submitting"
                  :disabled="!hasApiConfig"
                >
                  {{ tradeForm.side === 'buy' ? '买入' : '卖出' }}
                </a-button>
                <a-button @click="resetForm">重置</a-button>
                <a-button @click="getQuickQuote" :loading="loadingQuote">
                  获取报价
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>
        
        <!-- 当前持仓 -->
        <a-card title="当前持仓">
          <a-table
            :dataSource="positions"
            :columns="positionColumns"
            :loading="loadingPositions"
            size="small"
            :pagination="false"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'pnl'">
                <span :style="{ color: parseFloat(record.upl) >= 0 ? '#52c41a' : '#ff4d4f' }">
                  {{ record.upl }}
                </span>
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button size="small" @click="closePosition(record)">平仓</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
      
      <!-- 右侧：市场信息 -->
      <a-col :span="8">
        <!-- 实时价格 -->
        <a-card title="实时行情" style="margin-bottom: 24px;">
          <div v-if="currentTicker">
            <a-statistic
              :title="currentTicker.instId"
              :value="currentTicker.last"
              :precision="2"
              suffix="USDT"
            />
            <a-row :gutter="16" style="margin-top: 16px;">
              <a-col :span="12">
                <a-statistic
                  title="24h涨跌"
                  :value="currentTicker.sodUtc8"
                  suffix="%"
                  :value-style="{ color: parseFloat(currentTicker.sodUtc8) >= 0 ? '#52c41a' : '#ff4d4f' }"
                />
              </a-col>
              <a-col :span="12">
                <a-statistic
                  title="24h成交量"
                  :value="currentTicker.vol24h"
                  :precision="0"
                />
              </a-col>
            </a-row>
          </div>
          <a-skeleton v-else active />
        </a-card>
        
        <!-- 账户余额 -->
        <a-card title="账户余额">
          <a-list
            :dataSource="balances"
            :loading="loadingBalances"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>{{ item.ccy }}</template>
                  <template #description>
                    可用: {{ item.availEq }} | 总计: {{ item.eq }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

const router = useRouter()

// 响应式数据
const hasApiConfig = ref(false)
const loadingInstruments = ref(false)
const loadingPositions = ref(false)
const loadingBalances = ref(false)
const loadingQuote = ref(false)
const submitting = ref(false)

const instruments = ref([])
const positions = ref([])
const balances = ref([])
const currentTicker = ref(null)

// 交易表单
const tradeForm = ref({
  instId: 'BTC-USDT-SWAP',
  tdMode: 'cross',
  side: 'buy',
  ordType: 'limit',
  px: null,
  sz: null
})

// 当前选中的交易对信息
const currentInstrument = computed(() => {
  return instruments.value.find(inst => inst.instId === tradeForm.value.instId)
})

// 持仓表格列
const positionColumns = [
  { title: '交易对', dataIndex: 'instId', key: 'instId' },
  { title: '方向', dataIndex: 'posSide', key: 'posSide' },
  { title: '数量', dataIndex: 'pos', key: 'pos' },
  { title: '均价', dataIndex: 'avgPx', key: 'avgPx' },
  { title: '未实现盈亏', dataIndex: 'upl', key: 'pnl' },
  { title: '操作', key: 'actions' }
]

// 检查API配置
const checkApiConfig = async () => {
  try {
    const response = await axios.get('/api/user/api-config')
    hasApiConfig.value = response.data.code === 0 && response.data.data?.has_config
  } catch (error) {
    console.warn('检查API配置失败:', error.message)
    hasApiConfig.value = false
  }
}

// 获取交易对列表
const loadInstruments = async () => {
  loadingInstruments.value = true
  try {
    const response = await axios.get('/api/okx/public/instruments?instType=SWAP')
    if (response.data.code === 0) {
      instruments.value = response.data.data.slice(0, 20) // 只显示前20个
    }
  } catch (error) {
    message.error('获取交易对失败: ' + error.message)
  } finally {
    loadingInstruments.value = false
  }
}

// 获取持仓信息
const loadPositions = async () => {
  if (!hasApiConfig.value) return
  
  loadingPositions.value = true
  try {
    const response = await axios.get('/api/okx/account/positions')
    if (response.data.code === 0) {
      positions.value = response.data.data.filter(pos => parseFloat(pos.pos) !== 0)
    }
  } catch (error) {
    message.error('获取持仓失败: ' + error.message)
  } finally {
    loadingPositions.value = false
  }
}

// 获取账户余额
const loadBalances = async () => {
  if (!hasApiConfig.value) return
  
  loadingBalances.value = true
  try {
    const response = await axios.get('/api/okx/account/balance')
    if (response.data.code === 0) {
      balances.value = response.data.data[0]?.details?.filter(detail => 
        parseFloat(detail.eq) > 0
      ) || []
    }
  } catch (error) {
    message.error('获取余额失败: ' + error.message)
  } finally {
    loadingBalances.value = false
  }
}

// 获取实时行情
const loadTicker = async (instId) => {
  try {
    const response = await axios.get(`/api/okx/market/ticker?instId=${instId}`)
    if (response.data.code === 0) {
      currentTicker.value = response.data.data[0]
    }
  } catch (error) {
    console.warn('获取行情失败:', error.message)
  }
}

// 交易对变化时更新行情
const onInstrumentChange = (instId) => {
  loadTicker(instId)
}

// 获取快速报价
const getQuickQuote = async () => {
  if (!tradeForm.value.instId) {
    message.warning('请先选择交易对')
    return
  }
  
  loadingQuote.value = true
  try {
    await loadTicker(tradeForm.value.instId)
    if (currentTicker.value) {
      const price = parseFloat(currentTicker.value.last)
      tradeForm.value.px = tradeForm.value.side === 'buy' 
        ? price * 0.999  // 买入时稍微低一点
        : price * 1.001  // 卖出时稍微高一点
      message.success('已获取最新报价')
    }
  } catch (error) {
    message.error('获取报价失败: ' + error.message)
  } finally {
    loadingQuote.value = false
  }
}

// 提交订单
const submitOrder = async () => {
  submitting.value = true
  try {
    const orderData = {
      instId: tradeForm.value.instId,
      tdMode: tradeForm.value.tdMode,
      side: tradeForm.value.side,
      ordType: tradeForm.value.ordType,
      sz: tradeForm.value.sz.toString()
    }
    
    if (tradeForm.value.ordType !== 'market') {
      orderData.px = tradeForm.value.px.toString()
    }
    
    const response = await axios.post('/api/okx/trade/order', orderData)
    
    if (response.data.code === 0) {
      message.success('订单提交成功')
      resetForm()
      // 刷新持仓和余额
      loadPositions()
      loadBalances()
    } else {
      message.error(response.data.msg || '订单提交失败')
    }
  } catch (error) {
    message.error('订单提交失败: ' + error.message)
  } finally {
    submitting.value = false
  }
}

// 平仓
const closePosition = async (position) => {
  try {
    const orderData = {
      instId: position.instId,
      tdMode: position.mgnMode,
      side: position.posSide === 'long' ? 'sell' : 'buy',
      ordType: 'market',
      sz: Math.abs(parseFloat(position.pos)).toString(),
      reduceOnly: true
    }
    
    const response = await axios.post('/api/okx/trade/order', orderData)
    
    if (response.data.code === 0) {
      message.success('平仓订单提交成功')
      loadPositions()
      loadBalances()
    } else {
      message.error(response.data.msg || '平仓失败')
    }
  } catch (error) {
    message.error('平仓失败: ' + error.message)
  }
}

// 重置表单
const resetForm = () => {
  tradeForm.value = {
    instId: tradeForm.value.instId, // 保留交易对选择
    tdMode: 'cross',
    side: 'buy',
    ordType: 'limit',
    px: null,
    sz: null
  }
}

// 跳转到设置页面
const goToSettings = () => {
  router.push('/settings/api')
}

// 监听API配置变化
watch(hasApiConfig, (newValue) => {
  if (newValue) {
    loadPositions()
    loadBalances()
  }
})

// 页面加载时初始化
onMounted(async () => {
  await checkApiConfig()
  await loadInstruments()
  if (hasApiConfig.value) {
    loadPositions()
    loadBalances()
  }
  loadTicker(tradeForm.value.instId)
})
</script>

<style scoped>
.real-trading {
  padding: 24px;
}

.ant-statistic {
  text-align: center;
}
</style>
