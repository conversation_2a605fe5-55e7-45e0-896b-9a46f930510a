import { message, notification } from 'ant-design-vue';

/**
 * A more descriptive logger for development environment.
 * @param {Error} err - The error object.
 * @param {Object} instance - The component instance.
 * @param {string} info - Vue-specific error info.
 */
function logError(err, instance, info) {
  console.error('--- Global Error ---');
  console.error('Error:', err);
  console.error('Vue Info:', info);
  if (instance) {
    console.error('Component:', instance.$options.name || instance.$.type.name || 'Anonymous Component');
    console.error('Instance:', instance);
  }
  console.error('--------------------');
}

/**
 * Global error handler for the Vue application.
 * @param {Error} err - The error object.
 * @param {Object} instance - The component instance where the error occurred.
 * @param {string} info - A string containing Vue-specific error information.
 */
export function globalErrorHandler(err, instance, info) {
  // In development, log detailed error information to the console.
  if (import.meta.env.DEV) {
    logError(err, instance, info);
  }

  // In production, you might want to send this to a logging service like Sentry, LogRocket, etc.
  // For now, we'll show a user-friendly notification.

  notification.error({
    message: 'An Unexpected Error Occurred',
    description: 'We have been notified of the issue. Please try refreshing the page, or contact support if the problem persists.',
    duration: 0, // Keep it open until the user closes it
  });
}

/**
 * Handles unhandled promise rejections.
 * @param {PromiseRejectionEvent} event - The event object.
 */
export function unhandledRejectionHandler(event) {
    console.error('--- Unhandled Promise Rejection ---');
    console.error('Reason:', event.reason);
    console.error('Stack:', event.reason?.stack);
    console.error('---------------------------------');
    
    let errorMessage = 'An unexpected error occurred in an async operation.';
    if (event.reason instanceof Error) {
        errorMessage = event.reason.message;
    } else if (typeof event.reason === 'string') {
        errorMessage = event.reason;
    } else if (event.reason && typeof event.reason === 'object') {
        errorMessage = event.reason.message || event.reason.toString() || 'Unknown error object';
    }

    // 阻止默认的错误处理，避免在控制台显示未处理的Promise拒绝
    event.preventDefault();
    
    // 只在开发环境显示错误消息，生产环境记录但不显示给用户
    if (process.env.NODE_ENV === 'development') {
        message.error(`Unhandled Promise Rejection: ${errorMessage}`);
    } else {
        // 生产环境只记录错误，不显示给用户
        console.error('Production error logged:', errorMessage);
    }
}