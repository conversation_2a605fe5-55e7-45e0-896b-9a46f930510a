<template>
  <div class="indicator-test-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>增强技术指标面板测试页面</h1>
      <p>展示实时参数调整、动态添加/移除指标和优化显示效果</p>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <a-card title="测试控制" size="small">
        <a-space>
          <a-button type="primary" @click="initializeChart">初始化图表</a-button>
          <a-button @click="loadSampleData">加载示例数据</a-button>
          <a-button @click="clearAllIndicators">清除所有指标</a-button>
          <a-button @click="showPerformanceStats">性能统计</a-button>
        </a-space>
      </a-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 图表区域 -->
      <div class="chart-section">
        <a-card title="K线图表" :bodyStyle="{ padding: '12px' }">
          <div ref="chartContainer" class="chart-container"></div>
        </a-card>
      </div>

      <!-- 指标控制面板 -->
      <div class="indicator-section">
        <a-card title="统一指标管理器" :bodyStyle="{ padding: '12px' }">
          <UnifiedIndicatorManager
            :chart-instance="klineChart"
            @indicator-changed="onIndicatorChanged"
            @settings-updated="onSettingsUpdated"
          />
        </a-card>
      </div>
    </div>

    <!-- 状态信息 -->
    <div class="status-section">
      <a-row :gutter="16">
        <!-- 当前指标列表 -->
        <a-col :span="8">
          <a-card title="当前指标" size="small">
            <div v-if="activeIndicators.length === 0" class="empty-state">
              暂无活跃指标
            </div>
            <div v-else>
              <a-tag
                v-for="indicator in activeIndicators"
                :key="indicator.id"
                :color="getIndicatorColor(indicator.name)"
                closable
                @close="removeIndicator(indicator.id)"
              >
                {{ indicator.name }} ({{ indicator.params.join(',') }})
              </a-tag>
            </div>
          </a-card>
        </a-col>

        <!-- 性能监控 -->
        <a-col :span="8">
          <a-card title="性能监控" size="small">
            <div class="performance-stats">
              <div class="stat-item">
                <span class="label">更新频率:</span>
                <span class="value">{{ performanceStats.updateRate }}/s</span>
              </div>
              <div class="stat-item">
                <span class="label">平均延迟:</span>
                <span class="value">{{ performanceStats.averageDelay }}ms</span>
              </div>
              <div class="stat-item">
                <span class="label">内存使用:</span>
                <span class="value">{{ performanceStats.memoryUsage }}MB</span>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 操作日志 -->
        <a-col :span="8">
          <a-card title="操作日志" size="small">
            <div class="log-container">
              <div
                v-for="(log, index) in operationLogs"
                :key="index"
                class="log-item"
                :class="log.type"
              >
                <span class="time">{{ formatTime(log.time) }}</span>
                <span class="message">{{ log.message }}</span>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 示例配置展示 -->
    <div class="examples-section">
      <a-card title="示例配置">
        <a-tabs>
          <a-tab-pane key="presets" tab="预设配置">
            <div class="preset-examples">
              <a-row :gutter="16">
                <a-col :span="8" v-for="(preset, key) in presetExamples" :key="key">
                  <a-card size="small" :title="preset.name">
                    <p>{{ preset.description }}</p>
                    <a-button size="small" @click="applyPreset(key)">
                      应用配置
                    </a-button>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-tab-pane>

          <a-tab-pane key="styles" tab="样式主题">
            <div class="style-examples">
              <a-row :gutter="16">
                <a-col :span="8" v-for="(theme, key) in styleThemes" :key="key">
                  <a-card size="small" :title="theme.name">
                    <div class="theme-preview">
                      <div
                        v-for="(color, colorKey) in theme.colors"
                        :key="colorKey"
                        class="color-block"
                        :style="{ backgroundColor: color }"
                        :title="colorKey"
                      ></div>
                    </div>
                    <a-button size="small" @click="applyTheme(key)">
                      应用主题
                    </a-button>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-tab-pane>

          <a-tab-pane key="performance" tab="性能配置">
            <div class="performance-examples">
              <a-row :gutter="16">
                <a-col :span="8" v-for="(config, key) in performanceConfigs" :key="key">
                  <a-card size="small" :title="config.name">
                    <div class="config-details">
                      <div>更新延迟: {{ config.updateDelay }}ms</div>
                      <div>缓存: {{ config.enableCache ? '启用' : '禁用' }}</div>
                      <div>最大指标: {{ config.maxIndicators }}</div>
                    </div>
                    <a-button size="small" @click="applyPerformanceConfig(key)">
                      应用配置
                    </a-button>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { init as klinechartsInit } from 'klinecharts'
import UnifiedIndicatorManager from '@/components/UnifiedIndicatorManager.vue'
import { indicatorOptimizer } from '@/utils/indicatorOptimizer'
import {
  presetConfigurations,
  advancedStyleConfigs,
  performanceConfigs,
  IndicatorExamples
} from '@/examples/indicator-examples'

export default {
  name: 'IndicatorTestPage',
  components: {
    UnifiedIndicatorManager
  },
  setup() {
    // 响应式数据
    const chartContainer = ref(null)
    const klineChart = ref(null)
    const activeIndicators = ref([])
    const operationLogs = ref([])
    const indicatorExamples = ref(null)
    
    const performanceStats = reactive({
      updateRate: 0,
      averageDelay: 0,
      memoryUsage: 0
    })

    // 示例配置数据
    const presetExamples = ref(presetConfigurations)
    const styleThemes = ref({
      dark: {
        name: '深色主题',
        colors: {
          primary: '#9254de',
          success: '#95de64',
          warning: '#ffc53d',
          error: '#ff7875'
        }
      },
      light: {
        name: '浅色主题',
        colors: {
          primary: '#722ed1',
          success: '#52c41a',
          warning: '#faad14',
          error: '#f5222d'
        }
      },
      highContrast: {
        name: '高对比度',
        colors: {
          primary: '#000000',
          success: '#00ff00',
          warning: '#ffff00',
          error: '#ff0000'
        }
      }
    })

    // 生成示例K线数据
    const generateSampleData = () => {
      const data = []
      let basePrice = 50000
      const now = Date.now()
      
      for (let i = 0; i < 200; i++) {
        const timestamp = now - (200 - i) * 60000 // 每分钟一根K线
        const change = (Math.random() - 0.5) * 1000
        basePrice += change
        
        const open = basePrice
        const high = open + Math.random() * 500
        const low = open - Math.random() * 500
        const close = low + Math.random() * (high - low)
        const volume = Math.random() * 1000000
        
        data.push({
          timestamp,
          open: parseFloat(open.toFixed(2)),
          high: parseFloat(high.toFixed(2)),
          low: parseFloat(low.toFixed(2)),
          close: parseFloat(close.toFixed(2)),
          volume: parseInt(volume)
        })
      }
      
      return data
    }

    // 初始化图表
    const initializeChart = async () => {
      try {
        if (klineChart.value) {
          klineChart.value.dispose()
        }
        
        await nextTick()
        
        klineChart.value = klinechartsInit(chartContainer.value, {
          grid: {
            show: true,
            horizontal: {
              show: true,
              size: 1,
              color: '#E9E9E9',
              style: 'dashed'
            },
            vertical: {
              show: true,
              size: 1,
              color: '#E9E9E9',
              style: 'dashed'
            }
          },
          candle: {
            margin: {
              top: 0.2,
              bottom: 0.1
            },
            type: 'candle_solid',
            bar: {
              upColor: '#26A69A',
              downColor: '#EF5350',
              noChangeColor: '#888888'
            },
            tooltip: {
              showRule: 'always',
              showType: 'standard',
              labels: ['时间', '开', '收', '高', '低', '成交量']
            }
          },
          xAxis: {
            show: true,
            height: null,
            axisLine: {
              show: true,
              color: '#888888',
              size: 1
            },
            tickText: {
              show: true,
              color: '#888888',
              size: 12,
              family: 'Helvetica Neue',
              weight: 'normal',
              paddingTop: 3,
              paddingBottom: 6
            },
            tickLine: {
              show: true,
              size: 1,
              length: 3,
              color: '#888888'
            }
          },
          yAxis: {
            show: true,
            width: null,
            position: 'right',
            type: 'normal',
            inside: false,
            reverse: false,
            axisLine: {
              show: true,
              color: '#888888',
              size: 1
            },
            tickText: {
              show: true,
              color: '#888888',
              size: 12,
              family: 'Helvetica Neue',
              weight: 'normal',
              paddingLeft: 3,
              paddingRight: 6
            },
            tickLine: {
              show: true,
              size: 1,
              length: 3,
              color: '#888888'
            }
          }
        })
        
        // VOL指标已被移除，不再自动创建成交量面板
        // const volumePaneId = klineChart.value.createIndicator('VOL', false, {
        //   id: 'volume_pane',
        //   height: 80,
        //   minHeight: 50,
        //   dragEnabled: true,
        //   gap: { top: 0.2, bottom: 0.1 }
        // })
        
        // 创建示例实例
        indicatorExamples.value = new IndicatorExamples(klineChart.value)
        
        addLog('success', '图表初始化成功，成交量面板已禁用')
        message.success('图表初始化成功')
      } catch (error) {
        console.error('图表初始化失败:', error)
        addLog('error', `图表初始化失败: ${error.message}`)
        message.error('图表初始化失败')
      }
    }

    // 加载示例数据
    const loadSampleData = () => {
      if (!klineChart.value) {
        message.warning('请先初始化图表')
        return
      }
      
      try {
        const data = generateSampleData()
        klineChart.value.applyNewData(data)
        addLog('info', `加载了 ${data.length} 条示例数据`)
        message.success('示例数据加载成功')
      } catch (error) {
        console.error('加载示例数据失败:', error)
        addLog('error', `加载示例数据失败: ${error.message}`)
        message.error('加载示例数据失败')
      }
    }

    // 清除所有指标
    const clearAllIndicators = () => {
      if (!klineChart.value) {
        message.warning('请先初始化图表')
        return
      }
      
      try {
        // 获取所有指标面板ID并移除
        const paneIds = klineChart.value.getAllPanes().map(pane => pane.id)
        paneIds.forEach(paneId => {
          if (paneId !== 'candle_pane') { // 保留主图面板
            klineChart.value.removeIndicator(paneId)
          }
        })
        
        activeIndicators.value = []
        addLog('info', '已清除所有指标')
        message.success('所有指标已清除')
      } catch (error) {
        console.error('清除指标失败:', error)
        addLog('error', `清除指标失败: ${error.message}`)
        message.error('清除指标失败')
      }
    }

    // 显示性能统计
    const showPerformanceStats = () => {
      if (!indicatorOptimizer) {
        message.warning('性能监控未启用')
        return
      }
      
      try {
        const stats = indicatorOptimizer.getAllPerformanceStats()
        console.log('性能统计:', stats)
        
        // 更新性能统计显示
        const totalUpdates = Object.values(stats).reduce((sum, stat) => sum + stat.count, 0)
        const avgDelay = Object.values(stats).reduce((sum, stat) => sum + stat.average, 0) / Object.keys(stats).length || 0
        
        performanceStats.updateRate = Math.round(totalUpdates / 60) // 假设统计1分钟内的更新
        performanceStats.averageDelay = Math.round(avgDelay)
        performanceStats.memoryUsage = Math.round(performance.memory?.usedJSHeapSize / 1024 / 1024 || 0)
        
        addLog('info', `性能统计已更新 - 更新率: ${performanceStats.updateRate}/s, 延迟: ${performanceStats.averageDelay}ms`)
        message.success('性能统计已更新')
      } catch (error) {
        console.error('获取性能统计失败:', error)
        addLog('error', `获取性能统计失败: ${error.message}`)
        message.error('获取性能统计失败')
      }
    }

    // 应用预设配置
    const applyPreset = async (presetKey) => {
      if (!indicatorExamples.value) {
        message.warning('请先初始化图表')
        return
      }
      
      try {
        const results = await indicatorExamples.value.addPresetIndicators(presetKey)
        const successCount = results.filter(r => r.success).length
        const failCount = results.filter(r => !r.success).length
        
        // 更新活跃指标列表
        results.forEach(result => {
          if (result.success) {
            activeIndicators.value.push({
              id: result.paneId,
              name: result.name,
              params: presetConfigurations[presetKey].indicators[result.name].params || []
            })
          }
        })
        
        addLog('success', `预设配置 "${presetConfigurations[presetKey].name}" 应用完成 - 成功: ${successCount}, 失败: ${failCount}`)
        message.success(`预设配置应用完成 - 成功: ${successCount}, 失败: ${failCount}`)
      } catch (error) {
        console.error('应用预设配置失败:', error)
        addLog('error', `应用预设配置失败: ${error.message}`)
        message.error('应用预设配置失败')
      }
    }

    // 应用主题
    const applyTheme = (themeKey) => {
      try {
        const theme = styleThemes.value[themeKey]
        // 这里可以实现主题应用逻辑
        addLog('info', `主题 "${theme.name}" 已应用`)
        message.success(`主题 "${theme.name}" 已应用`)
      } catch (error) {
        console.error('应用主题失败:', error)
        addLog('error', `应用主题失败: ${error.message}`)
        message.error('应用主题失败')
      }
    }

    // 应用性能配置
    const applyPerformanceConfig = (configKey) => {
      try {
        const config = performanceConfigs[configKey]
        // 这里可以实现性能配置应用逻辑
        addLog('info', `性能配置 "${config.name}" 已应用`)
        message.success(`性能配置 "${config.name}" 已应用`)
      } catch (error) {
        console.error('应用性能配置失败:', error)
        addLog('error', `应用性能配置失败: ${error.message}`)
        message.error('应用性能配置失败')
      }
    }

    // 移除指标
    const removeIndicator = (indicatorId) => {
      if (!klineChart.value) return
      
      try {
        klineChart.value.removeIndicator(indicatorId)
        activeIndicators.value = activeIndicators.value.filter(ind => ind.id !== indicatorId)
        addLog('info', `指标 ${indicatorId} 已移除`)
      } catch (error) {
        console.error('移除指标失败:', error)
        addLog('error', `移除指标失败: ${error.message}`)
      }
    }

    // 获取指标颜色
    const getIndicatorColor = (indicatorName) => {
      const colors = {
        RSI: 'purple',
        MACD: 'blue',
        KDJ: 'orange',
        BOLL: 'green',
        MA: 'cyan',
        EMA: 'magenta'
      }
      return colors[indicatorName] || 'default'
    }

    // 添加操作日志
    const addLog = (type, message) => {
      operationLogs.value.unshift({
        type,
        message,
        time: new Date()
      })
      
      // 保持日志数量在合理范围内
      if (operationLogs.value.length > 50) {
        operationLogs.value = operationLogs.value.slice(0, 50)
      }
    }

    // 格式化时间
    const formatTime = (time) => {
      return time.toLocaleTimeString()
    }

    // 事件处理函数
    const onIndicatorChanged = (data) => {
      if (data.action === 'add') {
        activeIndicators.value.push({
          id: data.indicatorId || data.name,
          name: data.name,
          params: data.params || []
        })
        addLog('success', `指标 ${data.name} 已添加`)
      } else if (data.action === 'remove') {
        activeIndicators.value = activeIndicators.value.filter(ind => 
          ind.id !== (data.indicatorId || data.name) && ind.name !== data.name
        )
        addLog('info', `指标 ${data.name} 已移除`)
      }
    }

    const onSettingsUpdated = (data) => {
      const indicator = activeIndicators.value.find(ind => 
        ind.name === data.indicatorName || ind.id === data.indicatorId
      )
      if (indicator) {
        indicator.params = data.params || []
      }
      addLog('info', `指标 ${data.indicatorName} 参数已更新`)
    }

    // 生命周期
    onMounted(() => {
      addLog('info', '测试页面已加载')
    })

    onUnmounted(() => {
      if (klineChart.value) {
        klineChart.value.dispose()
      }
      if (indicatorExamples.value) {
        indicatorExamples.value.cleanup()
      }
    })

    return {
      // 模板引用
      chartContainer,
      klineChart,
      
      // 响应式数据
      activeIndicators,
      operationLogs,
      performanceStats,
      presetExamples,
      styleThemes,
      performanceConfigs,
      
      // 方法
      initializeChart,
      loadSampleData,
      clearAllIndicators,
      showPerformanceStats,
      applyPreset,
      applyTheme,
      applyPerformanceConfig,
      removeIndicator,
      getIndicatorColor,
      formatTime,
      
      // 事件处理
      onIndicatorChanged,
      onSettingsUpdated
    }
  }
}
</script>

<style scoped>
.indicator-test-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
}

.page-header h1 {
  color: #1890ff;
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  font-size: 14px;
}

.control-panel {
  margin-bottom: 20px;
}

.main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-container {
  width: 100%;
  height: 500px;
  background-color: #fff;
  border-radius: 6px;
}

.status-section {
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  color: #999;
  padding: 20px;
}

.performance-stats {
  .stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    
    .label {
      color: #666;
    }
    
    .value {
      font-weight: bold;
      color: #1890ff;
    }
  }
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  
  .log-item {
    display: flex;
    align-items: center;
    padding: 4px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
    
    .time {
      color: #999;
      margin-right: 8px;
      min-width: 80px;
    }
    
    .message {
      flex: 1;
    }
    
    &.success .message {
      color: #52c41a;
    }
    
    &.error .message {
      color: #f5222d;
    }
    
    &.info .message {
      color: #1890ff;
    }
  }
}

.preset-examples,
.style-examples,
.performance-examples {
  .ant-card {
    margin-bottom: 16px;
  }
}

.theme-preview {
  display: flex;
  gap: 4px;
  margin-bottom: 12px;
  
  .color-block {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
  }
}

.config-details {
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
  
  div {
    margin-bottom: 4px;
  }
}

.examples-section {
  .ant-tabs-content {
    padding-top: 16px;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .indicator-test-page {
    padding: 10px;
  }
  
  .chart-container {
    height: 300px;
  }
}
</style>