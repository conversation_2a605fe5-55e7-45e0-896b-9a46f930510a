<!--
  MarketSentiment.vue
  市场情绪组件

  该组件显示当前的市场情绪，以恐惧和贪婪指数的形式呈现。
  数据来源于 chartStore。
-->
<template>
  <div class="market-sentiment">
    <div class="sentiment-header">
      <span class="sentiment-title">市场情绪</span>
      <a-tooltip :title="getSentimentDescription(marketSentiment.score)">
        <InfoCircleOutlined class="sentiment-info" />
      </a-tooltip>
    </div>
    <div class="sentiment-meter">
      <div class="sentiment-bar">
        <div 
          class="sentiment-fill"
          :style="{ 
            width: marketSentiment.score + '%',
            backgroundColor: getSentimentColor(marketSentiment.score)
          }"
        ></div>
      </div>
      <div class="sentiment-labels">
        <span class="label-fear">恐惧</span>
        <span class="label-neutral">中性</span>
        <span class="label-greed">贪婪</span>
      </div>
    </div>
    <div class="sentiment-score">
      {{ getSentimentText(marketSentiment.score) }} ({{ marketSentiment.score }})
    </div>
  </div>
</template>

<script setup>
import { useInfoPanelStore } from '@/stores/infoPanelStore'
import { storeToRefs } from 'pinia'
import { InfoCircleOutlined } from '@ant-design/icons-vue'

const infoPanelStore = useInfoPanelStore()
const { marketSentiment } = storeToRefs(infoPanelStore)

const getSentimentColor = (score) => {
  if (score <= 25) return '#ff4d4f' // 极度恐惧
  if (score <= 45) return '#fa8c16' // 恐惧
  if (score <= 55) return '#fadb14' // 中性
  if (score <= 75) return '#a0d911' // 贪婪
  return '#52c41a' // 极度贪婪
}

const getSentimentText = (score) => {
  if (score <= 25) return '极度恐惧'
  if (score <= 45) return '恐惧'
  if (score <= 55) return '中性'
  if (score <= 75) return '贪婪'
  return '极度贪婪'
}

const getSentimentDescription = (score) => {
  const descriptions = {
    '极度恐惧': '市场情绪极度悲观，可能是买入机会',
    '恐惧': '市场情绪偏向悲观，谨慎观望',
    '中性': '市场情绪相对平衡',
    '贪婪': '市场情绪偏向乐观，注意风险',
    '极度贪婪': '市场情绪极度乐观，可能存在泡沫风险'
  }
  return descriptions[getSentimentText(score)]
}
</script>

<style scoped>
.market-sentiment {
  padding: 12px;
  background: #fafafa;
  border-radius: 4px;
}

.sentiment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.sentiment-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.sentiment-info {
  color: #8c8c8c;
  cursor: help;
}

.sentiment-meter {
  margin-bottom: 8px;
}

.sentiment-bar {
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 4px;
}

.sentiment-fill {
  height: 100%;
  transition: all 0.3s ease;
}

.sentiment-labels {
  display: flex;
  justify-content: space-between;
  font-size: 10px;
  color: #8c8c8c;
}

.sentiment-score {
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.dark-theme .market-sentiment {
  background: #2a2e39;
}

.dark-theme .sentiment-title {
  color: #ffffff;
}

.dark-theme .sentiment-bar {
  background: #434651;
}
</style>