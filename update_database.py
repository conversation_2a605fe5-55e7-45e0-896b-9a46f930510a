#!/usr/bin/env python3
"""
更新数据库结构的脚本
"""

import sqlite3

def update_database():
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    
    try:
        # 添加environment字段
        cursor.execute("ALTER TABLE user ADD COLUMN environment VARCHAR(20) DEFAULT 'demo'")
        print("添加environment字段成功")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("environment字段已存在")
        else:
            print(f"添加environment字段失败: {e}")
    
    # 由于SQLite不支持直接修改列类型，我们需要检查现有数据
    # 如果api_secret或passphrase字段的数据长度超过128，我们需要重建表
    cursor.execute("SELECT api_secret, passphrase FROM user WHERE api_secret IS NOT NULL OR passphrase IS NOT NULL")
    rows = cursor.fetchall()
    
    needs_rebuild = False
    for row in rows:
        if row[0] and len(row[0]) > 128:
            needs_rebuild = True
            break
        if row[1] and len(row[1]) > 128:
            needs_rebuild = True
            break
    
    if needs_rebuild:
        print("需要重建表以支持更长的加密字段...")
        
        # 备份现有数据
        cursor.execute("SELECT * FROM user")
        user_data = cursor.fetchall()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        
        # 删除旧表
        cursor.execute("DROP TABLE user")
        
        # 创建新表
        cursor.execute("""
            CREATE TABLE user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(128) NOT NULL,
                api_key VARCHAR(128),
                api_secret TEXT,
                passphrase TEXT,
                environment VARCHAR(20) DEFAULT 'demo',
                created_at DATETIME,
                is_active BOOLEAN DEFAULT 1
            )
        """)
        
        # 恢复数据
        for row in user_data:
            # 处理旧数据格式（可能没有environment字段）
            if len(row) == 8:  # 旧格式
                cursor.execute("""
                    INSERT INTO user (id, username, password, api_key, api_secret, passphrase, created_at, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, row)
            else:  # 新格式或其他
                cursor.execute("""
                    INSERT INTO user (id, username, password, api_key, api_secret, passphrase, environment, created_at, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, row)
        
        print("表重建完成")
    else:
        print("现有字段长度足够，无需重建表")
    
    conn.commit()
    conn.close()
    print("数据库更新完成！")

if __name__ == "__main__":
    update_database()
