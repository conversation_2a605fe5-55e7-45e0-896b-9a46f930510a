<template>
  <a-form @submit.prevent="onRegister" :model="form" class="register-form">
    <a-form-item label="用户名">
      <a-input v-model="form.username" />
    </a-form-item>
    <a-form-item label="密码">
      <a-input-password v-model="form.password" />
    </a-form-item>
    <a-form-item>
      <a-button type="primary" html-type="submit" :loading="loading">注册</a-button>
    </a-form-item>
    <div v-if="error" style="color:red">{{ error }}</div>
  </a-form>
</template>

<script setup>
import { ref } from 'vue'
import axios from 'axios'
import { message } from 'ant-design-vue'
import { useUserStore } from '../stores/user'

const userStore = useUserStore()
const form = ref({ username: '', password: '' })
const loading = ref(false)
const error = ref('')

const onRegister = async () => {
  loading.value = true
  error.value = ''
  try {
    const res = await axios.post('/api/user/register', form.value)
    if (res.data.code === 0) {
      userStore.clearUser()
      message.success('注册成功，请登录')
      setTimeout(() => {
        window.location.href = '/login'
      }, 500)
    } else {
      error.value = res.data.msg
      message.error(res.data.msg)
    }
  } catch (e) {
    error.value = e.response?.data?.detail || '注册失败'
    message.error(error.value)
  }
  loading.value = false
}
</script>

<style>
.register-form {
  max-width: 300px;
  margin: 100px auto;
}
</style> 