import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { INDICATOR_DEFAULTS, TIMEFRAMES } from '@/constants/chartConstants';

export const useChartSettingsStore = defineStore('chartSettings', () => {
  // State
  const selectedSymbol = ref('ETH-USDT-SWAP');
  const selectedTimeframe = ref('1m');
  const isDarkTheme = ref(false);

  const displaySettings = ref({
    showVolume: true,
    showRSI: true,
    showMACD: true,
    showKDJ: true,
    showBollingerBands: true,
    showSupertrend: false,
    showMA: true,
    showEMA: false
  });

  const indicatorSettings = ref({
    rsi: { ...INDICATOR_DEFAULTS.RSI },
    macd: { ...INDICATOR_DEFAULTS.MACD },
    kdj: { ...INDICATOR_DEFAULTS.KDJ },
    bollingerBands: { ...INDICATOR_DEFAULTS.BOLLINGER_BANDS },
    ma: { periods: [5, 10, 20, 50, 200] },
    ema: { periods: [8, 13, 21, 55] },
    supertrend: { ...INDICATOR_DEFAULTS.SUPERTREND },
    signalStrength: {
      rsi_oversold: 30,
      rsi_overbought: 70,
      rsi_strong_oversold: 20,
      rsi_strong_overbought: 80,
      trend_strength_threshold: 0.7,
      volume_surge_threshold: 1.5
    },
    multiTimeframe: {
      enabled: false,
      weights: [0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45]
    }
  });

  // Computed
  const availableSymbols = computed(() => [
    'BTC-USDT-SWAP',
    'ETH-USDT-SWAP',
    'SOL-USDT-SWAP',
    'ADA-USDT-SWAP',
    'DOT-USDT-SWAP'
  ]);

  const availableTimeframes = computed(() => Object.values(TIMEFRAMES));

  // Actions
  const setSelectedSymbol = (symbol) => {
    console.log('设置交易对:', symbol);
    selectedSymbol.value = symbol;
    saveSettings();
    console.log('交易对设置已保存');
  };

  const setSelectedTimeframe = (timeframe) => {
    console.log('设置周期:', timeframe);
    selectedTimeframe.value = timeframe;
    saveSettings();
    console.log('周期设置已保存');
  };

  const toggleTheme = () => {
    isDarkTheme.value = !isDarkTheme.value;
  };

  const setTheme = (dark) => {
    isDarkTheme.value = dark;
  };

  const updateDisplaySettings = (settings) => {
    displaySettings.value = { ...displaySettings.value, ...settings };
  };

  const updateIndicatorSettings = (indicator, settings) => {
    if (indicatorSettings.value[indicator]) {
      indicatorSettings.value[indicator] = { ...indicatorSettings.value[indicator], ...settings };
    }
  };

  const resetIndicatorSettings = () => {
    indicatorSettings.value = {
      rsi: { ...INDICATOR_DEFAULTS.RSI },
      macd: { ...INDICATOR_DEFAULTS.MACD },
      kdj: { ...INDICATOR_DEFAULTS.KDJ },
      bollingerBands: { ...INDICATOR_DEFAULTS.BOLLINGER_BANDS },
      ma: { periods: [5, 10, 20, 50, 200] },
      ema: { periods: [8, 13, 21, 55] },
      supertrend: { ...INDICATOR_DEFAULTS.SUPERTREND }
    };
  };

  const saveSettings = () => {
    try {
      const settings = {
        displaySettings: displaySettings.value,
        indicatorSettings: indicatorSettings.value,
        isDarkTheme: isDarkTheme.value,
        selectedSymbol: selectedSymbol.value,
        selectedTimeframe: selectedTimeframe.value
      };
      console.log('正在保存设置:', settings);
      localStorage.setItem('chartSettings', JSON.stringify(settings));
      console.log('设置已保存到localStorage');
    } catch (err) {
      console.error('保存设置失败:', err);
    }
  };

  const loadSettings = () => {
    try {
      const saved = localStorage.getItem('chartSettings');
      console.log('从localStorage加载设置:', saved);
      if (saved) {
        const settings = JSON.parse(saved);
        console.log('解析的设置:', settings);
        if (settings.displaySettings) {
          displaySettings.value = { ...displaySettings.value, ...settings.displaySettings };
        }
        if (settings.indicatorSettings) {
          indicatorSettings.value = { ...indicatorSettings.value, ...settings.indicatorSettings };
        }
        if (typeof settings.isDarkTheme === 'boolean') {
          isDarkTheme.value = settings.isDarkTheme;
        }
        if (settings.selectedSymbol) {
          console.log('恢复交易对:', settings.selectedSymbol);
          selectedSymbol.value = settings.selectedSymbol;
        }
        if (settings.selectedTimeframe) {
          console.log('恢复周期:', settings.selectedTimeframe);
          selectedTimeframe.value = settings.selectedTimeframe;
        }
        console.log('设置加载完成');
      } else {
        console.log('没有找到保存的设置，使用默认值');
      }
    } catch (err) {
      console.error('加载设置失败:', err);
    }
  };

  // Load settings on initialization
  loadSettings();

  return {
    selectedSymbol,
    selectedTimeframe,
    isDarkTheme,
    displaySettings,
    indicatorSettings,
    availableSymbols,
    availableTimeframes,
    setSelectedSymbol,
    setSelectedTimeframe,
    toggleTheme,
    setTheme,
    updateDisplaySettings,
    updateIndicatorSettings,
    resetIndicatorSettings,
    saveSettings,
    loadSettings
  };
});