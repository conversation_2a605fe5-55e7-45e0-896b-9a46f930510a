import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 模式识别算法组合式函数
 * 用于识别各种技术分析模式
 */
export function usePatternRecognition() {
  // 响应式数据
  const recognizedPatterns = ref([])
  const isAnalyzing = ref(false)
  const analysisProgress = ref(0)
  
  // 模式类型定义
  const PATTERN_TYPES = {
    // 反转模式
    HEAD_AND_SHOULDERS: 'head_and_shoulders',
    INVERSE_HEAD_AND_SHOULDERS: 'inverse_head_and_shoulders',
    DOUBLE_TOP: 'double_top',
    DOUBLE_BOTTOM: 'double_bottom',
    TRIPLE_TOP: 'triple_top',
    TRIPLE_BOTTOM: 'triple_bottom',
    
    // 持续模式
    ASCENDING_TRIANGLE: 'ascending_triangle',
    DESCENDING_TRIANGLE: 'descending_triangle',
    SYMMETRICAL_TRIANGLE: 'symmetrical_triangle',
    WEDGE_RISING: 'wedge_rising',
    WEDGE_FALLING: 'wedge_falling',
    FLAG: 'flag',
    PENNANT: 'pennant',
    
    // K线模式
    DOJI: 'doji',
    HAMMER: 'hammer',
    HANGING_MAN: 'hanging_man',
    SHOOTING_STAR: 'shooting_star',
    ENGULFING_BULLISH: 'engulfing_bullish',
    ENGULFING_BEARISH: 'engulfing_bearish',
    MORNING_STAR: 'morning_star',
    EVENING_STAR: 'evening_star'
  }
  
  // 模式信息映射
  const PATTERN_INFO = {
    [PATTERN_TYPES.HEAD_AND_SHOULDERS]: {
      name: '头肩顶',
      type: 'reversal',
      signal: 'bearish',
      description: '看跌反转模式，通常出现在上升趋势的顶部',
      reliability: 0.85
    },
    [PATTERN_TYPES.INVERSE_HEAD_AND_SHOULDERS]: {
      name: '头肩底',
      type: 'reversal',
      signal: 'bullish',
      description: '看涨反转模式，通常出现在下降趋势的底部',
      reliability: 0.85
    },
    [PATTERN_TYPES.DOUBLE_TOP]: {
      name: '双顶',
      type: 'reversal',
      signal: 'bearish',
      description: '看跌反转模式，价格两次触及相似高点后回落',
      reliability: 0.75
    },
    [PATTERN_TYPES.DOUBLE_BOTTOM]: {
      name: '双底',
      type: 'reversal',
      signal: 'bullish',
      description: '看涨反转模式，价格两次触及相似低点后反弹',
      reliability: 0.75
    },
    [PATTERN_TYPES.ASCENDING_TRIANGLE]: {
      name: '上升三角形',
      type: 'continuation',
      signal: 'bullish',
      description: '看涨持续模式，价格在水平阻力位和上升支撑线之间震荡',
      reliability: 0.70
    },
    [PATTERN_TYPES.DESCENDING_TRIANGLE]: {
      name: '下降三角形',
      type: 'continuation',
      signal: 'bearish',
      description: '看跌持续模式，价格在水平支撑位和下降阻力线之间震荡',
      reliability: 0.70
    },
    [PATTERN_TYPES.DOJI]: {
      name: '十字星',
      type: 'reversal',
      signal: 'neutral',
      description: '市场犹豫不决的信号，可能预示趋势反转',
      reliability: 0.60
    },
    [PATTERN_TYPES.HAMMER]: {
      name: '锤子线',
      type: 'reversal',
      signal: 'bullish',
      description: '看涨反转信号，通常出现在下降趋势底部',
      reliability: 0.65
    },
    [PATTERN_TYPES.ENGULFING_BULLISH]: {
      name: '看涨吞没',
      type: 'reversal',
      signal: 'bullish',
      description: '强烈的看涨反转信号，大阳线完全吞没前一根阴线',
      reliability: 0.80
    },
    [PATTERN_TYPES.ENGULFING_BEARISH]: {
      name: '看跌吞没',
      type: 'reversal',
      signal: 'bearish',
      description: '强烈的看跌反转信号，大阴线完全吞没前一根阳线',
      reliability: 0.80
    }
  }
  
  /**
   * 分析K线数据识别模式
   * @param {Array} klineData - K线数据数组
   * @param {Object} options - 分析选项
   * @returns {Promise<Array>} 识别到的模式数组
   */
  const analyzePatterns = async (klineData, options = {}) => {
    if (!klineData || klineData.length < 10) {
      message.warning('数据不足，无法进行模式识别')
      return []
    }
    
    isAnalyzing.value = true
    analysisProgress.value = 0
    recognizedPatterns.value = []
    
    try {
      const patterns = []
      const totalSteps = 8
      let currentStep = 0
      
      // 识别反转模式
      analysisProgress.value = (++currentStep / totalSteps) * 100
      patterns.push(...await recognizeReversalPatterns(klineData))
      
      // 识别持续模式
      analysisProgress.value = (++currentStep / totalSteps) * 100
      patterns.push(...await recognizeContinuationPatterns(klineData))
      
      // 识别单K线模式
      analysisProgress.value = (++currentStep / totalSteps) * 100
      patterns.push(...await recognizeCandlestickPatterns(klineData))
      
      // 识别多K线组合模式
      analysisProgress.value = (++currentStep / totalSteps) * 100
      patterns.push(...await recognizeMultiCandlestickPatterns(klineData))
      
      // 识别三角形模式
      analysisProgress.value = (++currentStep / totalSteps) * 100
      patterns.push(...await recognizeTrianglePatterns(klineData))
      
      // 识别楔形模式
      analysisProgress.value = (++currentStep / totalSteps) * 100
      patterns.push(...await recognizeWedgePatterns(klineData))
      
      // 识别旗形模式
      analysisProgress.value = (++currentStep / totalSteps) * 100
      patterns.push(...await recognizeFlagPatterns(klineData))
      
      // 过滤和排序
      analysisProgress.value = (++currentStep / totalSteps) * 100
      const filteredPatterns = filterAndSortPatterns(patterns, options)
      
      recognizedPatterns.value = filteredPatterns
      analysisProgress.value = 100
      
      message.success(`识别到 ${filteredPatterns.length} 个技术模式`)
      return filteredPatterns
      
    } catch (error) {
      console.error('模式识别失败:', error)
      message.error('模式识别失败')
      return []
    } finally {
      isAnalyzing.value = false
    }
  }
  
  /**
   * 识别反转模式
   * @param {Array} data - K线数据
   * @returns {Array} 识别到的反转模式
   */
  const recognizeReversalPatterns = async (data) => {
    const patterns = []
    const minPeriod = 20
    const maxPeriod = 60
    
    for (let i = maxPeriod; i < data.length - minPeriod; i++) {
      const segment = data.slice(i - maxPeriod, i + minPeriod)
      
      // 头肩顶模式
      const headAndShoulders = detectHeadAndShoulders(segment, i - maxPeriod)
      if (headAndShoulders) {
        patterns.push(headAndShoulders)
      }
      
      // 双顶模式
      const doubleTop = detectDoubleTop(segment, i - maxPeriod)
      if (doubleTop) {
        patterns.push(doubleTop)
      }
      
      // 双底模式
      const doubleBottom = detectDoubleBottom(segment, i - maxPeriod)
      if (doubleBottom) {
        patterns.push(doubleBottom)
      }
    }
    
    return patterns
  }
  
  /**
   * 识别持续模式
   * @param {Array} data - K线数据
   * @returns {Array} 识别到的持续模式
   */
  const recognizeContinuationPatterns = async (data) => {
    const patterns = []
    // 实现持续模式识别逻辑
    return patterns
  }
  
  /**
   * 识别单K线模式
   * @param {Array} data - K线数据
   * @returns {Array} 识别到的单K线模式
   */
  const recognizeCandlestickPatterns = async (data) => {
    const patterns = []
    
    for (let i = 1; i < data.length; i++) {
      const candle = data[i]
      const prevCandle = data[i - 1]
      
      // 十字星
      const doji = detectDoji(candle, i)
      if (doji) patterns.push(doji)
      
      // 锤子线
      const hammer = detectHammer(candle, i, data.slice(Math.max(0, i - 10), i))
      if (hammer) patterns.push(hammer)
      
      // 流星线
      const shootingStar = detectShootingStar(candle, i, data.slice(Math.max(0, i - 10), i))
      if (shootingStar) patterns.push(shootingStar)
    }
    
    return patterns
  }
  
  /**
   * 识别多K线组合模式
   * @param {Array} data - K线数据
   * @returns {Array} 识别到的多K线组合模式
   */
  const recognizeMultiCandlestickPatterns = async (data) => {
    const patterns = []
    
    for (let i = 1; i < data.length; i++) {
      const current = data[i]
      const previous = data[i - 1]
      
      // 吞没模式
      const engulfing = detectEngulfingPattern(previous, current, i)
      if (engulfing) patterns.push(engulfing)
      
      // 启明星/黄昏星（需要3根K线）
      if (i >= 2) {
        const star = detectStarPattern(data[i - 2], data[i - 1], data[i], i)
        if (star) patterns.push(star)
      }
    }
    
    return patterns
  }
  
  /**
   * 识别三角形模式
   * @param {Array} data - K线数据
   * @returns {Array} 识别到的三角形模式
   */
  const recognizeTrianglePatterns = async (data) => {
    const patterns = []
    const minPeriod = 15
    const maxPeriod = 50
    
    for (let i = maxPeriod; i < data.length - 5; i++) {
      const segment = data.slice(i - maxPeriod, i)
      
      // 上升三角形
      const ascendingTriangle = detectAscendingTriangle(segment, i - maxPeriod)
      if (ascendingTriangle) patterns.push(ascendingTriangle)
      
      // 下降三角形
      const descendingTriangle = detectDescendingTriangle(segment, i - maxPeriod)
      if (descendingTriangle) patterns.push(descendingTriangle)
      
      // 对称三角形
      const symmetricalTriangle = detectSymmetricalTriangle(segment, i - maxPeriod)
      if (symmetricalTriangle) patterns.push(symmetricalTriangle)
    }
    
    return patterns
  }
  
  /**
   * 识别楔形模式
   * @param {Array} data - K线数据
   * @returns {Array} 识别到的楔形模式
   */
  const recognizeWedgePatterns = async (data) => {
    const patterns = []
    // 实现楔形模式识别逻辑
    return patterns
  }
  
  /**
   * 识别旗形模式
   * @param {Array} data - K线数据
   * @returns {Array} 识别到的旗形模式
   */
  const recognizeFlagPatterns = async (data) => {
    const patterns = []
    // 实现旗形模式识别逻辑
    return patterns
  }
  
  /**
   * 检测头肩顶模式
   * @param {Array} segment - 数据片段
   * @param {number} startIndex - 起始索引
   * @returns {Object|null} 头肩顶模式或null
   */
  const detectHeadAndShoulders = (segment, startIndex) => {
    if (segment.length < 30) return null
    
    const highs = segment.map((item, index) => ({ value: item.high, index }))
    const peaks = findPeaks(highs, 5)
    
    if (peaks.length < 3) return null
    
    // 寻找三个主要峰值
    const sortedPeaks = peaks.sort((a, b) => b.value - a.value)
    const head = sortedPeaks[0]
    const leftShoulder = peaks.find(p => p.index < head.index && Math.abs(p.value - head.value) > head.value * 0.02)
    const rightShoulder = peaks.find(p => p.index > head.index && Math.abs(p.value - head.value) > head.value * 0.02)
    
    if (!leftShoulder || !rightShoulder) return null
    
    // 验证肩膀高度相似
    const shoulderDiff = Math.abs(leftShoulder.value - rightShoulder.value) / Math.max(leftShoulder.value, rightShoulder.value)
    if (shoulderDiff > 0.05) return null
    
    // 验证头部高于肩膀
    if (head.value <= Math.max(leftShoulder.value, rightShoulder.value)) return null
    
    return {
      type: PATTERN_TYPES.HEAD_AND_SHOULDERS,
      ...PATTERN_INFO[PATTERN_TYPES.HEAD_AND_SHOULDERS],
      startIndex: startIndex + leftShoulder.index,
      endIndex: startIndex + rightShoulder.index,
      keyPoints: {
        leftShoulder: { index: startIndex + leftShoulder.index, value: leftShoulder.value },
        head: { index: startIndex + head.index, value: head.value },
        rightShoulder: { index: startIndex + rightShoulder.index, value: rightShoulder.value }
      },
      confidence: calculatePatternConfidence('head_and_shoulders', {
        shoulderDiff,
        headHeight: (head.value - Math.max(leftShoulder.value, rightShoulder.value)) / head.value
      }),
      timestamp: Date.now()
    }
  }
  
  /**
   * 检测双顶模式
   * @param {Array} segment - 数据片段
   * @param {number} startIndex - 起始索引
   * @returns {Object|null} 双顶模式或null
   */
  const detectDoubleTop = (segment, startIndex) => {
    if (segment.length < 20) return null
    
    const highs = segment.map((item, index) => ({ value: item.high, index }))
    const peaks = findPeaks(highs, 3)
    
    if (peaks.length < 2) return null
    
    // 寻找两个主要峰值
    const sortedPeaks = peaks.sort((a, b) => b.value - a.value).slice(0, 2)
    const [peak1, peak2] = sortedPeaks.sort((a, b) => a.index - b.index)
    
    // 验证峰值高度相似
    const heightDiff = Math.abs(peak1.value - peak2.value) / Math.max(peak1.value, peak2.value)
    if (heightDiff > 0.03) return null
    
    // 验证峰值之间的距离
    const distance = peak2.index - peak1.index
    if (distance < 5 || distance > segment.length * 0.8) return null
    
    // 寻找中间的谷值
    const valleySegment = segment.slice(peak1.index, peak2.index + 1)
    const valley = valleySegment.reduce((min, current, index) => 
      current.low < min.value ? { value: current.low, index: peak1.index + index } : min,
      { value: Infinity, index: -1 }
    )
    
    // 验证谷值深度
    const valleyDepth = (Math.min(peak1.value, peak2.value) - valley.value) / Math.min(peak1.value, peak2.value)
    if (valleyDepth < 0.02) return null
    
    return {
      type: PATTERN_TYPES.DOUBLE_TOP,
      ...PATTERN_INFO[PATTERN_TYPES.DOUBLE_TOP],
      startIndex: startIndex + peak1.index,
      endIndex: startIndex + peak2.index,
      keyPoints: {
        peak1: { index: startIndex + peak1.index, value: peak1.value },
        valley: { index: startIndex + valley.index, value: valley.value },
        peak2: { index: startIndex + peak2.index, value: peak2.value }
      },
      confidence: calculatePatternConfidence('double_top', {
        heightDiff,
        valleyDepth,
        distance
      }),
      timestamp: Date.now()
    }
  }
  
  /**
   * 检测双底模式
   * @param {Array} segment - 数据片段
   * @param {number} startIndex - 起始索引
   * @returns {Object|null} 双底模式或null
   */
  const detectDoubleBottom = (segment, startIndex) => {
    if (segment.length < 20) return null
    
    const lows = segment.map((item, index) => ({ value: item.low, index }))
    const troughs = findTroughs(lows, 3)
    
    if (troughs.length < 2) return null
    
    // 寻找两个主要谷值
    const sortedTroughs = troughs.sort((a, b) => a.value - b.value).slice(0, 2)
    const [trough1, trough2] = sortedTroughs.sort((a, b) => a.index - b.index)
    
    // 验证谷值深度相似
    const depthDiff = Math.abs(trough1.value - trough2.value) / Math.min(trough1.value, trough2.value)
    if (depthDiff > 0.03) return null
    
    // 验证谷值之间的距离
    const distance = trough2.index - trough1.index
    if (distance < 5 || distance > segment.length * 0.8) return null
    
    // 寻找中间的峰值
    const peakSegment = segment.slice(trough1.index, trough2.index + 1)
    const peak = peakSegment.reduce((max, current, index) => 
      current.high > max.value ? { value: current.high, index: trough1.index + index } : max,
      { value: -Infinity, index: -1 }
    )
    
    // 验证峰值高度
    const peakHeight = (peak.value - Math.max(trough1.value, trough2.value)) / Math.max(trough1.value, trough2.value)
    if (peakHeight < 0.02) return null
    
    return {
      type: PATTERN_TYPES.DOUBLE_BOTTOM,
      ...PATTERN_INFO[PATTERN_TYPES.DOUBLE_BOTTOM],
      startIndex: startIndex + trough1.index,
      endIndex: startIndex + trough2.index,
      keyPoints: {
        trough1: { index: startIndex + trough1.index, value: trough1.value },
        peak: { index: startIndex + peak.index, value: peak.value },
        trough2: { index: startIndex + trough2.index, value: trough2.value }
      },
      confidence: calculatePatternConfidence('double_bottom', {
        depthDiff,
        peakHeight,
        distance
      }),
      timestamp: Date.now()
    }
  }
  
  /**
   * 检测十字星模式
   * @param {Object} candle - K线数据
   * @param {number} index - 索引
   * @returns {Object|null} 十字星模式或null
   */
  const detectDoji = (candle, index) => {
    const bodySize = Math.abs(candle.close - candle.open)
    const totalRange = candle.high - candle.low
    
    if (totalRange === 0) return null
    
    const bodyRatio = bodySize / totalRange
    
    // 十字星的实体应该很小
    if (bodyRatio > 0.1) return null
    
    return {
      type: PATTERN_TYPES.DOJI,
      ...PATTERN_INFO[PATTERN_TYPES.DOJI],
      startIndex: index,
      endIndex: index,
      keyPoints: {
        open: candle.open,
        close: candle.close,
        high: candle.high,
        low: candle.low
      },
      confidence: calculatePatternConfidence('doji', { bodyRatio }),
      timestamp: Date.now()
    }
  }
  
  /**
   * 检测锤子线模式
   * @param {Object} candle - K线数据
   * @param {number} index - 索引
   * @param {Array} context - 上下文数据
   * @returns {Object|null} 锤子线模式或null
   */
  const detectHammer = (candle, index, context) => {
    const bodySize = Math.abs(candle.close - candle.open)
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low
    const upperShadow = candle.high - Math.max(candle.open, candle.close)
    const totalRange = candle.high - candle.low
    
    if (totalRange === 0) return null
    
    // 锤子线特征：下影线长，上影线短，实体小
    const lowerShadowRatio = lowerShadow / totalRange
    const upperShadowRatio = upperShadow / totalRange
    const bodyRatio = bodySize / totalRange
    
    if (lowerShadowRatio < 0.6 || upperShadowRatio > 0.1 || bodyRatio > 0.3) return null
    
    // 检查是否在下降趋势中
    const isInDowntrend = context.length >= 3 && 
      context.slice(-3).every((item, i, arr) => i === 0 || item.close < arr[i - 1].close)
    
    if (!isInDowntrend) return null
    
    return {
      type: PATTERN_TYPES.HAMMER,
      ...PATTERN_INFO[PATTERN_TYPES.HAMMER],
      startIndex: index,
      endIndex: index,
      keyPoints: {
        open: candle.open,
        close: candle.close,
        high: candle.high,
        low: candle.low
      },
      confidence: calculatePatternConfidence('hammer', {
        lowerShadowRatio,
        upperShadowRatio,
        bodyRatio
      }),
      timestamp: Date.now()
    }
  }
  
  /**
   * 检测流星线模式
   * @param {Object} candle - K线数据
   * @param {number} index - 索引
   * @param {Array} context - 上下文数据
   * @returns {Object|null} 流星线模式或null
   */
  const detectShootingStar = (candle, index, context) => {
    const bodySize = Math.abs(candle.close - candle.open)
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low
    const upperShadow = candle.high - Math.max(candle.open, candle.close)
    const totalRange = candle.high - candle.low
    
    if (totalRange === 0) return null
    
    // 流星线特征：上影线长，下影线短，实体小
    const lowerShadowRatio = lowerShadow / totalRange
    const upperShadowRatio = upperShadow / totalRange
    const bodyRatio = bodySize / totalRange
    
    if (upperShadowRatio < 0.6 || lowerShadowRatio > 0.1 || bodyRatio > 0.3) return null
    
    // 检查是否在上升趋势中
    const isInUptrend = context.length >= 3 && 
      context.slice(-3).every((item, i, arr) => i === 0 || item.close > arr[i - 1].close)
    
    if (!isInUptrend) return null
    
    return {
      type: PATTERN_TYPES.SHOOTING_STAR,
      ...PATTERN_INFO[PATTERN_TYPES.SHOOTING_STAR],
      startIndex: index,
      endIndex: index,
      keyPoints: {
        open: candle.open,
        close: candle.close,
        high: candle.high,
        low: candle.low
      },
      confidence: calculatePatternConfidence('shooting_star', {
        lowerShadowRatio,
        upperShadowRatio,
        bodyRatio
      }),
      timestamp: Date.now()
    }
  }
  
  /**
   * 检测吞没模式
   * @param {Object} prev - 前一根K线
   * @param {Object} current - 当前K线
   * @param {number} index - 索引
   * @returns {Object|null} 吞没模式或null
   */
  const detectEngulfingPattern = (prev, current, index) => {
    const prevBody = Math.abs(prev.close - prev.open)
    const currentBody = Math.abs(current.close - current.open)
    
    // 当前K线实体必须完全吞没前一根K线实体
    const isEngulfing = current.open < Math.min(prev.open, prev.close) && 
                       current.close > Math.max(prev.open, prev.close) ||
                       current.open > Math.max(prev.open, prev.close) && 
                       current.close < Math.min(prev.open, prev.close)
    
    if (!isEngulfing) return null
    
    // 判断是看涨还是看跌吞没
    const isBullish = prev.close < prev.open && current.close > current.open
    const isBearish = prev.close > prev.open && current.close < current.open
    
    if (!isBullish && !isBearish) return null
    
    const type = isBullish ? PATTERN_TYPES.ENGULFING_BULLISH : PATTERN_TYPES.ENGULFING_BEARISH
    
    return {
      type,
      ...PATTERN_INFO[type],
      startIndex: index - 1,
      endIndex: index,
      keyPoints: {
        prev: { open: prev.open, close: prev.close, high: prev.high, low: prev.low },
        current: { open: current.open, close: current.close, high: current.high, low: current.low }
      },
      confidence: calculatePatternConfidence('engulfing', {
        bodyRatio: currentBody / prevBody,
        isBullish
      }),
      timestamp: Date.now()
    }
  }
  
  /**
   * 检测星形模式（启明星/黄昏星）
   * @param {Object} first - 第一根K线
   * @param {Object} star - 星线
   * @param {Object} third - 第三根K线
   * @param {number} index - 索引
   * @returns {Object|null} 星形模式或null
   */
  const detectStarPattern = (first, star, third, index) => {
    // 星线应该有缺口
    const hasGapUp = star.low > Math.max(first.open, first.close)
    const hasGapDown = star.high < Math.min(first.open, first.close)
    
    if (!hasGapUp && !hasGapDown) return null
    
    // 星线实体应该很小
    const starBody = Math.abs(star.close - star.open)
    const starRange = star.high - star.low
    if (starRange === 0 || starBody / starRange > 0.3) return null
    
    // 判断是启明星还是黄昏星
    const isMorningStar = first.close < first.open && // 第一根是阴线
                         hasGapDown && // 星线向下跳空
                         third.close > third.open && // 第三根是阳线
                         third.close > (first.open + first.close) / 2 // 第三根收盘价超过第一根实体中点
    
    const isEveningStar = first.close > first.open && // 第一根是阳线
                         hasGapUp && // 星线向上跳空
                         third.close < third.open && // 第三根是阴线
                         third.close < (first.open + first.close) / 2 // 第三根收盘价低于第一根实体中点
    
    if (!isMorningStar && !isEveningStar) return null
    
    const type = isMorningStar ? PATTERN_TYPES.MORNING_STAR : PATTERN_TYPES.EVENING_STAR
    
    return {
      type,
      ...PATTERN_INFO[type],
      startIndex: index - 2,
      endIndex: index,
      keyPoints: {
        first: { open: first.open, close: first.close, high: first.high, low: first.low },
        star: { open: star.open, close: star.close, high: star.high, low: star.low },
        third: { open: third.open, close: third.close, high: third.high, low: third.low }
      },
      confidence: calculatePatternConfidence('star', {
        starBodyRatio: starBody / starRange,
        isMorningStar
      }),
      timestamp: Date.now()
    }
  }
  
  /**
   * 检测上升三角形
   * @param {Array} segment - 数据片段
   * @param {number} startIndex - 起始索引
   * @returns {Object|null} 上升三角形模式或null
   */
  const detectAscendingTriangle = (segment, startIndex) => {
    if (segment.length < 15) return null
    
    const highs = segment.map((item, index) => ({ value: item.high, index }))
    const lows = segment.map((item, index) => ({ value: item.low, index }))
    
    // 寻找水平阻力位（高点相似）
    const peaks = findPeaks(highs, 3)
    if (peaks.length < 3) return null
    
    const resistanceLevel = peaks.reduce((sum, peak) => sum + peak.value, 0) / peaks.length
    const resistanceVariation = Math.max(...peaks.map(p => Math.abs(p.value - resistanceLevel))) / resistanceLevel
    
    if (resistanceVariation > 0.02) return null // 阻力位变化不能超过2%
    
    // 寻找上升支撑线（低点逐渐上升）
    const troughs = findTroughs(lows, 3)
    if (troughs.length < 2) return null
    
    const supportSlope = (troughs[troughs.length - 1].value - troughs[0].value) / (troughs[troughs.length - 1].index - troughs[0].index)
    
    if (supportSlope <= 0) return null // 支撑线必须上升
    
    return {
      type: PATTERN_TYPES.ASCENDING_TRIANGLE,
      ...PATTERN_INFO[PATTERN_TYPES.ASCENDING_TRIANGLE],
      startIndex: startIndex,
      endIndex: startIndex + segment.length - 1,
      keyPoints: {
        resistance: resistanceLevel,
        supportLine: {
          start: { index: startIndex + troughs[0].index, value: troughs[0].value },
          end: { index: startIndex + troughs[troughs.length - 1].index, value: troughs[troughs.length - 1].value }
        },
        peaks: peaks.map(p => ({ index: startIndex + p.index, value: p.value })),
        troughs: troughs.map(t => ({ index: startIndex + t.index, value: t.value }))
      },
      confidence: calculatePatternConfidence('ascending_triangle', {
        resistanceVariation,
        supportSlope,
        peakCount: peaks.length,
        troughCount: troughs.length
      }),
      timestamp: Date.now()
    }
  }
  
  /**
   * 检测下降三角形
   * @param {Array} segment - 数据片段
   * @param {number} startIndex - 起始索引
   * @returns {Object|null} 下降三角形模式或null
   */
  const detectDescendingTriangle = (segment, startIndex) => {
    if (segment.length < 15) return null
    
    const highs = segment.map((item, index) => ({ value: item.high, index }))
    const lows = segment.map((item, index) => ({ value: item.low, index }))
    
    // 寻找水平支撑位（低点相似）
    const troughs = findTroughs(lows, 3)
    if (troughs.length < 3) return null
    
    const supportLevel = troughs.reduce((sum, trough) => sum + trough.value, 0) / troughs.length
    const supportVariation = Math.max(...troughs.map(t => Math.abs(t.value - supportLevel))) / supportLevel
    
    if (supportVariation > 0.02) return null // 支撑位变化不能超过2%
    
    // 寻找下降阻力线（高点逐渐下降）
    const peaks = findPeaks(highs, 3)
    if (peaks.length < 2) return null
    
    const resistanceSlope = (peaks[peaks.length - 1].value - peaks[0].value) / (peaks[peaks.length - 1].index - peaks[0].index)
    
    if (resistanceSlope >= 0) return null // 阻力线必须下降
    
    return {
      type: PATTERN_TYPES.DESCENDING_TRIANGLE,
      ...PATTERN_INFO[PATTERN_TYPES.DESCENDING_TRIANGLE],
      startIndex: startIndex,
      endIndex: startIndex + segment.length - 1,
      keyPoints: {
        support: supportLevel,
        resistanceLine: {
          start: { index: startIndex + peaks[0].index, value: peaks[0].value },
          end: { index: startIndex + peaks[peaks.length - 1].index, value: peaks[peaks.length - 1].value }
        },
        peaks: peaks.map(p => ({ index: startIndex + p.index, value: p.value })),
        troughs: troughs.map(t => ({ index: startIndex + t.index, value: t.value }))
      },
      confidence: calculatePatternConfidence('descending_triangle', {
        supportVariation,
        resistanceSlope: Math.abs(resistanceSlope),
        peakCount: peaks.length,
        troughCount: troughs.length
      }),
      timestamp: Date.now()
    }
  }
  
  /**
   * 检测对称三角形
   * @param {Array} segment - 数据片段
   * @param {number} startIndex - 起始索引
   * @returns {Object|null} 对称三角形模式或null
   */
  const detectSymmetricalTriangle = (segment, startIndex) => {
    if (segment.length < 15) return null
    
    const highs = segment.map((item, index) => ({ value: item.high, index }))
    const lows = segment.map((item, index) => ({ value: item.low, index }))
    
    const peaks = findPeaks(highs, 3)
    const troughs = findTroughs(lows, 3)
    
    if (peaks.length < 2 || troughs.length < 2) return null
    
    // 计算阻力线斜率（应该下降）
    const resistanceSlope = (peaks[peaks.length - 1].value - peaks[0].value) / (peaks[peaks.length - 1].index - peaks[0].index)
    
    // 计算支撑线斜率（应该上升）
    const supportSlope = (troughs[troughs.length - 1].value - troughs[0].value) / (troughs[troughs.length - 1].index - troughs[0].index)
    
    // 对称三角形：阻力线下降，支撑线上升，且斜率相近
    if (resistanceSlope >= 0 || supportSlope <= 0) return null
    
    const slopeRatio = Math.abs(resistanceSlope / supportSlope)
    if (slopeRatio < 0.5 || slopeRatio > 2) return null // 斜率应该相近
    
    return {
      type: PATTERN_TYPES.SYMMETRICAL_TRIANGLE,
      ...PATTERN_INFO[PATTERN_TYPES.SYMMETRICAL_TRIANGLE],
      startIndex: startIndex,
      endIndex: startIndex + segment.length - 1,
      keyPoints: {
        resistanceLine: {
          start: { index: startIndex + peaks[0].index, value: peaks[0].value },
          end: { index: startIndex + peaks[peaks.length - 1].index, value: peaks[peaks.length - 1].value }
        },
        supportLine: {
          start: { index: startIndex + troughs[0].index, value: troughs[0].value },
          end: { index: startIndex + troughs[troughs.length - 1].index, value: troughs[troughs.length - 1].value }
        },
        peaks: peaks.map(p => ({ index: startIndex + p.index, value: p.value })),
        troughs: troughs.map(t => ({ index: startIndex + t.index, value: t.value }))
      },
      confidence: calculatePatternConfidence('symmetrical_triangle', {
        resistanceSlope: Math.abs(resistanceSlope),
        supportSlope,
        slopeRatio,
        peakCount: peaks.length,
        troughCount: troughs.length
      }),
      timestamp: Date.now()
    }
  }
  
  /**
   * 寻找峰值
   * @param {Array} data - 数据数组
   * @param {number} window - 窗口大小
   * @returns {Array} 峰值数组
   */
  const findPeaks = (data, window = 3) => {
    const peaks = []
    
    for (let i = window; i < data.length - window; i++) {
      let isPeak = true
      
      for (let j = i - window; j <= i + window; j++) {
        if (j !== i && data[j].value >= data[i].value) {
          isPeak = false
          break
        }
      }
      
      if (isPeak) {
        peaks.push(data[i])
      }
    }
    
    return peaks
  }
  
  /**
   * 寻找谷值
   * @param {Array} data - 数据数组
   * @param {number} window - 窗口大小
   * @returns {Array} 谷值数组
   */
  const findTroughs = (data, window = 3) => {
    const troughs = []
    
    for (let i = window; i < data.length - window; i++) {
      let isTrough = true
      
      for (let j = i - window; j <= i + window; j++) {
        if (j !== i && data[j].value <= data[i].value) {
          isTrough = false
          break
        }
      }
      
      if (isTrough) {
        troughs.push(data[i])
      }
    }
    
    return troughs
  }
  
  /**
   * 计算模式置信度
   * @param {string} patternType - 模式类型
   * @param {Object} metrics - 度量指标
   * @returns {number} 置信度 (0-1)
   */
  const calculatePatternConfidence = (patternType, metrics) => {
    let confidence = 0.5 // 基础置信度
    
    switch (patternType) {
      case 'head_and_shoulders':
        confidence = 0.9 - metrics.shoulderDiff * 10 + metrics.headHeight * 2
        break
      case 'double_top':
      case 'double_bottom':
        confidence = 0.8 - metrics.heightDiff * 15 + Math.min(metrics.valleyDepth || metrics.peakHeight, 0.1) * 5
        break
      case 'doji':
        confidence = 0.7 - metrics.bodyRatio * 5
        break
      case 'hammer':
      case 'shooting_star':
        confidence = 0.6 + metrics.lowerShadowRatio * 0.5 - metrics.upperShadowRatio * 2 - metrics.bodyRatio
        break
      case 'engulfing':
        confidence = 0.7 + Math.min(metrics.bodyRatio - 1, 1) * 0.2
        break
      case 'ascending_triangle':
      case 'descending_triangle':
        confidence = 0.7 - metrics.resistanceVariation * 20 + Math.min(metrics.peakCount, 5) * 0.05
        break
      case 'symmetrical_triangle':
        confidence = 0.6 + (1 - Math.abs(metrics.slopeRatio - 1)) * 0.3
        break
      default:
        confidence = 0.5
    }
    
    return Math.max(0, Math.min(1, confidence))
  }
  
  /**
   * 过滤和排序模式
   * @param {Array} patterns - 模式数组
   * @param {Object} options - 选项
   * @returns {Array} 过滤后的模式数组
   */
  const filterAndSortPatterns = (patterns, options = {}) => {
    const {
      minConfidence = 0.5,
      maxPatterns = 20,
      patternTypes = null,
      timeRange = null
    } = options
    
    let filtered = patterns.filter(pattern => pattern.confidence >= minConfidence)
    
    // 按模式类型过滤
    if (patternTypes && patternTypes.length > 0) {
      filtered = filtered.filter(pattern => patternTypes.includes(pattern.type))
    }
    
    // 按时间范围过滤
    if (timeRange) {
      const { start, end } = timeRange
      filtered = filtered.filter(pattern => 
        pattern.startIndex >= start && pattern.endIndex <= end
      )
    }
    
    // 去重（移除重叠的相似模式）
    filtered = removeDuplicatePatterns(filtered)
    
    // 按置信度排序
    filtered.sort((a, b) => b.confidence - a.confidence)
    
    // 限制数量
    return filtered.slice(0, maxPatterns)
  }
  
  /**
   * 移除重复模式
   * @param {Array} patterns - 模式数组
   * @returns {Array} 去重后的模式数组
   */
  const removeDuplicatePatterns = (patterns) => {
    const unique = []
    
    for (const pattern of patterns) {
      const isDuplicate = unique.some(existing => {
        // 检查是否为相同类型且位置重叠的模式
        if (existing.type !== pattern.type) return false
        
        const overlap = Math.max(0, 
          Math.min(existing.endIndex, pattern.endIndex) - 
          Math.max(existing.startIndex, pattern.startIndex)
        )
        const totalRange = Math.max(existing.endIndex, pattern.endIndex) - 
                          Math.min(existing.startIndex, pattern.startIndex)
        
        return overlap / totalRange > 0.5 // 重叠超过50%认为是重复
      })
      
      if (!isDuplicate) {
        unique.push(pattern)
      }
    }
    
    return unique
  }
  
  /**
   * 获取模式统计信息
   */
  const getPatternStatistics = computed(() => {
    const stats = {
      total: recognizedPatterns.value.length,
      byType: {},
      bySignal: { bullish: 0, bearish: 0, neutral: 0 },
      avgConfidence: 0,
      highConfidence: 0
    }
    
    recognizedPatterns.value.forEach(pattern => {
      // 按类型统计
      if (!stats.byType[pattern.type]) {
        stats.byType[pattern.type] = 0
      }
      stats.byType[pattern.type]++
      
      // 按信号统计
      stats.bySignal[pattern.signal]++
      
      // 置信度统计
      stats.avgConfidence += pattern.confidence
      if (pattern.confidence >= 0.8) {
        stats.highConfidence++
      }
    })
    
    if (stats.total > 0) {
      stats.avgConfidence /= stats.total
    }
    
    return stats
  })
  
  /**
   * 清除识别结果
   */
  const clearPatterns = () => {
    recognizedPatterns.value = []
    analysisProgress.value = 0
  }
  
  return {
    // 响应式数据
    recognizedPatterns,
    isAnalyzing,
    analysisProgress,
    
    // 计算属性
    getPatternStatistics,
    
    // 方法
    analyzePatterns,
    clearPatterns,
    
    // 常量
    PATTERN_TYPES,
    PATTERN_INFO
  }
}

export default usePatternRecognition