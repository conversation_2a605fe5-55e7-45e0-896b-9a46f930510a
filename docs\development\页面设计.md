# 页面设计文档

## 1. 整体架构

### 1.1 技术栈
- **前端框架**: Vue 3.4.21 + TypeScript
- **构建工具**: Vite 5.1.4
- **UI组件库**: Ant Design Vue 4.1.2
- **状态管理**: Pinia 2.1.7
- **路由管理**: Vue Router 4.3.0
- **图表库**: KLineCharts 9.8.6 + ECharts 5.5.0
- **HTTP客户端**: Axios 1.6.7
- **样式预处理**: Sass 1.71.1

### 1.2 项目结构
```
src/
├── components/          # 可复用组件
│   ├── charts/         # 图表组件
│   ├── trading/        # 交易相关组件
│   ├── panels/         # 面板组件
│   └── common/         # 通用组件
├── views/              # 页面组件
├── stores/             # Pinia状态管理
├── services/           # API服务
├── utils/              # 工具函数
├── constants/          # 常量定义
└── router/             # 路由配置
```

## 2. 核心页面设计

### 2.1 主交易页面 (TradingView)
**路径**: `/trading`
**组件**: `views/TradingView.vue`

**布局结构**:
```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏                              │
├─────────────────────────────────────────────────────────┤
│  K线图表区域 (70%)           │  交易面板 (30%)            │
│  - KLineChart组件            │  - TradingPanel组件        │
│  - 技术指标                   │  - 买入/卖出表单           │
│  - 绘图工具                   │  - 订单簿                  │
├─────────────────────────────────────────────────────────┤
│  订单管理区域 (100%)                                      │
│  - 当前订单 - 历史订单 - 成交记录                         │
└─────────────────────────────────────────────────────────┘
```

**核心组件**:
- `KLineChart.vue`: K线图表主组件
- `TradingPanel.vue`: 交易操作面板
- `OrderBook.vue`: 订单簿组件
- `OrderHistory.vue`: 订单历史组件

### 2.2 策略管理页面 (StrategyView)
**路径**: `/strategy`
**组件**: `views/StrategyView.vue`

**功能模块**:
- 策略列表展示
- 策略创建/编辑
- 策略回测
- 策略监控
- 性能分析

### 2.3 AI分析页面 (AIAnalysisView)
**路径**: `/ai-analysis`
**组件**: `views/AIAnalysisView.vue`

**分析模块**:
- 趋势线分析
- 支撑阻力位识别
- 交易信号生成
- 市场情绪分析
- 风险评估

### 2.4 账户管理页面 (AccountView)
**路径**: `/account`
**组件**: `views/AccountView.vue`

**管理功能**:
- 资产概览
- 持仓管理
- 交易历史
- API密钥配置
- 风险设置

### 2.5 数据分析页面 (AnalyticsView)
**路径**: `/analytics`
**组件**: `views/AnalyticsView.vue`

**分析维度**:
- 交易统计
- 收益分析
- 风险指标
- 策略表现
- 市场对比

## 3. 核心组件库

### 3.1 图表组件 (components/charts/)

#### KLineChart.vue
- **功能**: 专业K线图表显示
- **特性**: 
  - 支持多时间周期 (1m, 5m, 15m, 1h, 4h, 1d)
  - 集成20+技术指标 (MA, MACD, RSI, BOLL等)
  - 支持趋势线绘制
  - 实时数据更新
  - 缩放和平移操作

#### DepthChart.vue
- **功能**: 市场深度图表
- **特性**: 买卖盘深度可视化

#### IndicatorChart.vue
- **功能**: 技术指标图表
- **特性**: 独立指标显示区域

### 3.2 交易组件 (components/trading/)

#### TradingPanel.vue
- **功能**: 交易操作面板
- **特性**:
  - 买入/卖出切换
  - 订单类型选择 (市价/限价/止损)
  - 杠杆设置
  - 风险计算

#### OrderBook.vue
- **功能**: 实时订单簿
- **特性**: 
  - 买卖盘展示
  - 价格聚合
  - 深度可视化

#### PositionPanel.vue
- **功能**: 持仓管理面板
- **特性**:
  - 持仓列表
  - 盈亏统计
  - 平仓操作

### 3.3 面板组件 (components/panels/)

#### InfoPanel.vue
- **功能**: 信息展示面板
- **特性**: 统一的信息卡片样式

#### ControlPanel.vue
- **功能**: 控制操作面板
- **特性**: 统一的操作按钮布局

#### StatusPanel.vue
- **功能**: 状态显示面板
- **特性**: 实时状态指示器

## 4. 状态管理 (stores/)

### 4.1 用户状态 (userStore.js)
- 用户信息管理
- 登录状态维护
- 权限控制

### 4.2 交易状态 (tradingStore.js)
- 当前交易对
- 订单管理
- 持仓信息

### 4.3 市场数据状态 (marketStore.js)
- K线数据缓存
- 实时行情
- 深度数据

### 4.4 策略状态 (strategyStore.js)
- 策略列表
- 策略参数
- 回测结果

## 5. 服务层 (services/)

### 5.1 API服务 (api.js)
- 统一的HTTP请求封装
- 错误处理
- 请求拦截器

### 5.2 WebSocket服务 (websocket.js)
- 实时数据推送
- 连接管理
- 重连机制

### 5.3 图表服务 (chartService.js)
- 图表数据处理
- 指标计算
- 绘图工具

## 6. 响应式设计

### 6.1 断点设置
- **xs**: < 576px (手机)
- **sm**: ≥ 576px (大手机)
- **md**: ≥ 768px (平板)
- **lg**: ≥ 992px (桌面)
- **xl**: ≥ 1200px (大桌面)
- **xxl**: ≥ 1600px (超大桌面)

### 6.2 布局适配
- 桌面端: 多列布局，完整功能
- 平板端: 双列布局，简化操作
- 手机端: 单列布局，标签页切换

## 7. 主题系统

### 7.1 色彩规范
```scss
// 主色调
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;

// 涨跌色
$up-color: #00b578;    // 涨
$down-color: #f54336;  // 跌

// 背景色
$bg-color: #f0f2f5;
$card-bg: #ffffff;
$dark-bg: #141414;

// 文字色
$text-primary: #262626;
$text-secondary: #8c8c8c;
$text-disabled: #bfbfbf;
```

### 7.2 暗色主题支持
- 自动检测系统主题
- 手动切换功能
- 组件级主题适配

## 8. 性能优化

### 8.1 代码分割
- 路由级别懒加载
- 组件按需导入
- 第三方库分包

### 8.2 数据优化
- 虚拟滚动
- 数据缓存
- 防抖节流

### 8.3 渲染优化
- 组件缓存
- 计算属性
- 事件委托

## 9. 用户体验

### 9.1 加载状态
- 骨架屏
- 进度指示器
- 懒加载

### 9.2 错误处理
- 全局错误捕获
- 友好错误提示
- 错误边界

### 9.3 交互反馈
- 操作确认
- 成功提示
- 实时验证

## 10. 可访问性

### 10.1 键盘导航
- Tab键顺序
- 快捷键支持
- 焦点管理

### 10.2 屏幕阅读器
- ARIA标签
- 语义化HTML
- 替代文本

### 10.3 视觉辅助
- 高对比度模式
- 字体大小调节
- 色盲友好设计