# 高级图表设置功能测试指南

## 测试环境
- 前端服务地址: http://localhost:5174/
- 测试脚本: `test_advanced_chart_settings.js`

## 功能测试清单

### 1. 显示设置测试

#### 技术指标显示
- [ ] **移动平均线 (MA)**: 勾选后图表应显示MA线
- [ ] **指数移动平均线 (EMA)**: 勾选后图表应显示EMA线
- [ ] **布林带 (BB)**: 勾选后图表应显示上下轨道
- [ ] **超级趋势 (Supertrend)**: 勾选后图表应显示趋势线
- [ ] **相对强弱指数 (RSI)**: 勾选后应显示RSI子图
- [ ] **MACD**: 勾选后应显示MACD子图
- [ ] **KDJ**: 勾选后应显示KDJ子图
- [ ] **成交量**: 勾选后应显示成交量柱状图

#### 图表样式
- [ ] **图表主题**: 切换浅色/深色主题，界面应立即变化
- [ ] **K线样式**: 切换蜡烛图/美国线，K线显示方式应改变
- [ ] **显示网格**: 勾选后图表应显示网格线
- [ ] **显示十字线**: 勾选后鼠标悬停应显示十字线
- [ ] **显示图例**: 勾选后应显示指标图例
- [ ] **启用动画**: 勾选后图表更新应有动画效果

### 2. 指标参数测试

#### 移动平均线设置
- [ ] **MA5周期**: 修改数值，MA5线应相应调整
- [ ] **MA10周期**: 修改数值，MA10线应相应调整
- [ ] **MA20周期**: 修改数值，MA20线应相应调整
- [ ] **MA60周期**: 修改数值，MA60线应相应调整

#### RSI设置
- [ ] **RSI周期**: 修改周期值(2-50)，RSI计算应更新
- [ ] **超买线**: 修改数值(50-100)，RSI图中超买线位置应改变
- [ ] **超卖线**: 修改数值(0-50)，RSI图中超卖线位置应改变

#### MACD设置
- [ ] **快线周期**: 修改数值(5-50)，MACD快线应调整
- [ ] **慢线周期**: 修改数值(10-100)，MACD慢线应调整
- [ ] **信号线周期**: 修改数值(5-20)，MACD信号线应调整

#### 布林带设置
- [ ] **周期**: 修改数值(10-50)，布林带宽度应调整
- [ ] **标准差**: 修改数值(1-3)，布林带上下轨距离应改变

### 3. 交易设置测试

#### 交易功能
- [ ] **启用交易信号**: 勾选后应显示买卖信号
- [ ] **启用价格提醒**: 勾选后应启用价格提醒功能
- [ ] **自动刷新数据**: 勾选后数据应自动更新
- [ ] **显示订单簿**: 勾选后应显示订单簿信息

#### 风险管理
- [ ] **止损百分比**: 修改数值(0.1-10%)，应影响风险计算
- [ ] **止盈百分比**: 修改数值(0.1-20%)，应影响收益计算
- [ ] **最大仓位百分比**: 修改数值(1-100%)，应影响仓位限制
- [ ] **刷新间隔**: 修改数值(1-60秒)，数据刷新频率应改变

### 4. 数据设置测试

#### 数据源设置
- [ ] **默认数据源**: 切换实时/模拟数据，数据来源应改变
- [ ] **数据条数**: 修改数值(100-2000)，图表显示的K线数量应改变
- [ ] **缓存时间**: 修改数值(1-60分钟)，数据缓存策略应更新

### 5. 设置持久化测试

#### 保存功能
- [ ] **设置保存**: 点击"保存设置"按钮，应显示保存成功提示
- [ ] **页面刷新**: 刷新页面后，所有设置应保持不变
- [ ] **浏览器重启**: 关闭并重新打开浏览器，设置应仍然存在

#### 重置功能
- [ ] **重置默认**: 点击"重置默认"按钮，所有设置应恢复默认值

## 自动化测试

### 运行测试脚本
1. 打开浏览器开发者工具 (F12)
2. 切换到 Console 标签
3. 复制并粘贴 `test_advanced_chart_settings.js` 文件内容
4. 按 Enter 执行脚本
5. 查看测试结果

### 测试脚本功能
- `runAllTests()`: 运行所有自动化测试
- `checkCurrentSettings()`: 检查当前保存的设置
- `clearTestSettings()`: 清理测试数据

## 验证步骤

### 步骤1: 基础功能验证
1. 访问 http://localhost:5174/
2. 点击高级图表设置按钮
3. 逐一测试每个选项卡的功能
4. 确认每个设置项都能正常修改

### 步骤2: 设置生效验证
1. 修改任意设置项
2. 点击"保存设置"
3. 观察图表是否立即反映变化
4. 检查相关功能是否按预期工作

### 步骤3: 持久化验证
1. 修改多个设置项并保存
2. 刷新页面
3. 重新打开设置面板
4. 确认所有设置都已保持

### 步骤4: 边界值验证
1. 尝试输入超出范围的数值
2. 确认系统能正确处理并提示错误
3. 验证输入验证功能正常工作

## 常见问题排查

### 设置不生效
- 检查是否点击了"保存设置"按钮
- 确认浏览器控制台没有错误信息
- 验证localStorage中是否保存了设置数据

### 设置丢失
- 检查浏览器是否禁用了localStorage
- 确认没有清除浏览器数据
- 验证设置保存逻辑是否正常执行

### 图表不更新
- 检查数据连接是否正常
- 确认自动刷新功能是否启用
- 验证图表组件是否正确响应设置变化

## 测试报告模板

```
测试日期: ____
测试人员: ____

功能测试结果:
- 显示设置: ✓/✗
- 指标参数: ✓/✗
- 交易设置: ✓/✗
- 数据设置: ✓/✗
- 设置持久化: ✓/✗

发现问题:
1. ____
2. ____

建议改进:
1. ____
2. ____
```

## 性能测试

### 响应时间
- 设置面板打开时间: < 500ms
- 设置保存时间: < 200ms
- 图表更新时间: < 1000ms

### 内存使用
- 监控长时间使用后的内存占用
- 检查是否存在内存泄漏
- 验证设置数据的存储效率

---

**注意**: 请按照此指南逐项测试，确保每个功能都能正常工作。如发现问题，请记录详细的错误信息和重现步骤。