<template>
  <div class="ma-indicators-container">
    <a-card size="small" :title="`MA 指标`">
      <div v-if="chartDataStore.isLoading" class="indicator-skeleton">
        <a-skeleton active :paragraph="{ rows: 3 }" />
      </div>
      <div v-else class="indicators-grid">
        <div v-for="ma in maIndicators" :key="ma.period" class="indicator-item">
          <div class="indicator-label">MA ({{ ma.period }})</div>
          <div class="indicator-value">{{ formatPrice(ma.value) }}</div>
          <a-tag :color="ma.status.color">{{ ma.status.text }}</a-tag>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useInfoPanelStore } from '@/stores/infoPanelStore';
import { useChartDataStore } from '@/stores/chartDataStore';
import { formatPrice } from '@/utils/formatters';

const infoPanelStore = useInfoPanelStore();
const chartDataStore = useChartDataStore();

const maIndicators = computed(() => {
  if (!infoPanelStore.indicatorData || !infoPanelStore.indicatorData.ma || !chartDataStore.currentPrice) {
    return [];
  }
  return infoPanelStore.indicatorData.ma.map(ma => ({
    ...ma,
    status: getMAStatus(ma.value, chartDataStore.currentPrice.last)
  }));
});

const getMAStatus = (maValue, currentPrice) => {
  if (currentPrice > maValue) {
    return { text: '多头', color: 'green' };
  } else if (currentPrice < maValue) {
    return { text: '空头', color: 'red' };
  } else {
    return { text: '中性', color: 'blue' };
  }
};
</script>

<style scoped>
.ma-indicators-container {
  margin-bottom: 16px;
}

.indicators-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.indicator-item {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  align-items: center;
  text-align: center;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
}

.indicator-label {
  font-size: 12px;
  color: #8c8c8c;
  text-align: left;
}

.indicator-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.dark-theme .indicator-item {
  background: #2a2e39;
}

.dark-theme .indicator-value {
  color: #ffffff;
}

.dark-theme .indicator-label {
  color: #a0a0a0;
}
</style>