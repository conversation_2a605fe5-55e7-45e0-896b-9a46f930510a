import pytest
import asyncio
import websockets
from unittest.mock import MagicMock, patch
import json

# 测试配置
TEST_BASE_URL = "http://localhost:8000"
TEST_WS_URL = "ws://localhost:8000"


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_okx_response():
    """Mock OKX API响应"""
    def _mock_response(data=None, code="0", msg=""):
        return {
            "code": code,
            "msg": msg,
            "data": data or []
        }
    return _mock_response


@pytest.fixture
def test_user_data():
    """测试用户数据"""
    return {
        "id": 1,
        "username": "testuser",
        "api_key": "test_api_key_123",
        "api_secret": "test_api_secret_456",
        "passphrase": "test_passphrase_789",
        "role": "user"
    }


@pytest.fixture
def test_token():
    """测试token"""
    return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_token"


@pytest.fixture
def auth_headers(test_token):
    """认证请求头"""
    return {
        "Authorization": f"Bearer {test_token}",
        "Content-Type": "application/json"
    }


@pytest.fixture
def mock_okx_market_data():
    """Mock OKX行情数据"""
    return {
        "code": "0",
        "msg": "",
        "data": [{
            "instId": "BTC-USDT",
            "last": "50000",
            "lastSz": "0.1",
            "askPx": "50001",
            "askSz": "1.0",
            "bidPx": "49999",
            "bidSz": "1.0",
            "open24h": "49000",
            "high24h": "51000",
            "low24h": "48000",
            "volCcy24h": "1000000",
            "vol24h": "20",
            "ts": "*************"
        }]
    }


@pytest.fixture
def mock_okx_account_data():
    """Mock OKX账户数据"""
    return {
        "code": "0",
        "msg": "",
        "data": [{
            "ccy": "USDT",
            "cashBal": "1000.0",
            "upl": "0",
            "availBal": "1000.0",
            "availEq": "1000.0",
            "ordFrozen": "0",
            "uplLiab": "0",
            "crossLiab": "0",
            "isoLiab": "0",
            "mgnRatio": "",
            "interest": "0",
            "twap": "0",
            "maxLoan": "",
            "eqUsd": "1000.0",
            "notionalLever": "",
            "stgyEq": "0",
            "isoUpl": ""
        }]
    }


@pytest.fixture
def mock_okx_positions_data():
    """Mock OKX持仓数据"""
    return {
        "code": "0",
        "msg": "",
        "data": [{
            "instId": "BTC-USDT-SWAP",
            "pos": "1",
            "posSide": "net",
            "avgPx": "50000",
            "lever": "3",
            "upl": "100",
            "uTime": "*************"
        }]
    }


@pytest.fixture
def mock_okx_orders_data():
    """Mock OKX订单数据"""
    return {
        "code": "0",
        "msg": "",
        "data": [{
            "ordId": "123456789",
            "clOrdId": "",
            "instId": "BTC-USDT-SWAP",
            "tdMode": "cross",
            "side": "buy",
            "ordType": "limit",
            "px": "50000",
            "sz": "1",
            "state": "live",
            "cTime": "*************"
        }]
    }


@pytest.fixture
def mock_ws_connection():
    """Mock WebSocket连接"""
    mock_ws = MagicMock()
    mock_ws.send = MagicMock()
    mock_ws.recv = MagicMock()
    mock_ws.close = MagicMock()
    return mock_ws


@pytest.fixture
def mock_okx_ws_message():
    """Mock OKX WebSocket消息"""
    return {
        "event": "subscribe",
        "arg": {
            "channel": "tickers",
            "instId": "BTC-USDT"
        }
    }


@pytest.fixture
def mock_okx_ws_ticker():
    """Mock OKX WebSocket ticker数据"""
    return {
        "arg": {
            "channel": "tickers",
            "instId": "BTC-USDT"
        },
        "data": [{
            "instId": "BTC-USDT",
            "last": "50000",
            "lastSz": "0.1",
            "askPx": "50001",
            "askSz": "1.0",
            "bidPx": "49999",
            "bidSz": "1.0",
            "open24h": "49000",
            "high24h": "51000",
            "low24h": "48000",
            "volCcy24h": "1000000",
            "vol24h": "20",
            "ts": "*************"
        }]
    }


@pytest.fixture
def mock_okx_ws_positions():
    """Mock OKX WebSocket持仓数据"""
    return {
        "arg": {
            "channel": "positions",
            "instType": "SWAP"
        },
        "data": [{
            "instId": "BTC-USDT-SWAP",
            "pos": "1",
            "posSide": "net",
            "avgPx": "50000",
            "lever": "3",
            "upl": "100",
            "uTime": "*************"
        }]
    }


@pytest.fixture
def mock_okx_ws_account():
    """Mock OKX WebSocket账户数据"""
    return {
        "arg": {
            "channel": "account"
        },
        "data": [{
            "adjEq": "1000.0",
            "details": [{
                "ccy": "USDT",
                "eq": "1000.0",
                "availBal": "1000.0",
                "frozenBal": "0"
            }],
            "uTime": "*************"
        }]
    }


@pytest.fixture
def test_order_data():
    """测试订单数据"""
    return {
        "instId": "BTC-USDT-SWAP",
        "tdMode": "cross",
        "side": "buy",
        "ordType": "limit",
        "px": "50000",
        "sz": "1"
    }


@pytest.fixture
def test_leverage_data():
    """测试杠杆数据"""
    return {
        "lever": "5",
        "mgnMode": "cross",
        "instId": "BTC-USDT-SWAP"
    }


@pytest.fixture
def test_transfer_data():
    """测试划转数据"""
    return {
        "ccy": "USDT",
        "amt": "100",
        "from": "6",
        "to": "18",
        "type": "0"
    }


@pytest.fixture
def mock_database():
    """Mock数据库连接"""
    mock_db = MagicMock()
    mock_db.query = MagicMock()
    mock_db.add = MagicMock()
    mock_db.commit = MagicMock()
    mock_db.refresh = MagicMock()
    return mock_db


@pytest.fixture
def mock_user_query(mock_database, test_user_data):
    """Mock用户查询"""
    mock_query = MagicMock()
    mock_query.filter = MagicMock()
    mock_query.first = MagicMock(return_value=test_user_data)
    mock_database.query.return_value = mock_query
    return mock_query


# 测试标记
pytest_plugins = []


def pytest_configure(config):
    """pytest配置"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试项"""
    for item in items:
        if "test_okx_api" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        elif "test_user_api" in item.nodeid:
            item.add_marker(pytest.mark.unit)
