<template>
  <div class="okx-transfer">
    <h2>OKX资金划转</h2>
    <a-form :model="form" label-col="{span: 4}" wrapper-col="{span: 8}" @submit.prevent>
      <a-form-item label="币种">
        <a-input v-model="form.ccy" placeholder="如 USDT" />
      </a-form-item>
      <a-form-item label="划转数量">
        <a-input-number v-model="form.amt" :min="0.0001" style="width: 200px" />
      </a-form-item>
      <a-form-item label="从账户">
        <a-select v-model="form.from">
          <a-select-option value="1">资金账户</a-select-option>
          <a-select-option value="6">交易账户</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="到账户">
        <a-select v-model="form.to">
          <a-select-option value="1">资金账户</a-select-option>
          <a-select-option value="6">交易账户</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="transfer" :loading="loading">划转</a-button>
      </a-form-item>
    </a-form>
    <div v-if="error" style="color:red; margin-top:16px;">{{ error }}</div>
    <div v-if="success" style="color:green; margin-top:16px;">划转成功！</div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const form = ref({ ccy: '', amt: null, from: '6', to: '1' })
const loading = ref(false)
const error = ref('')
const success = ref(false)

function transfer() {
  loading.value = true
  error.value = ''
  success.value = false
  fetch('/api/okx/transfer', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + localStorage.getItem('token')
    },
    body: JSON.stringify(form.value)
  })
    .then(res => res.json())
    .then(res => {
      if (res.code === '0') {
        success.value = true
        error.value = ''
      } else {
        error.value = res.msg || '划转失败'
      }
    })
    .catch(() => { error.value = '网络异常' })
    .finally(() => { loading.value = false })
}
</script>

<style scoped>
.okx-transfer {
  max-width: 600px;
  margin: 0 auto;
  padding: 24px;
}
</style>