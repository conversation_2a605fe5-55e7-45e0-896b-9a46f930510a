<template>
  <div class="chart-test">
    <a-card title="ECharts测试页面">
      <a-button @click="initTestChart" type="primary">初始化测试图表</a-button>
      <div id="test-chart" style="width: 100%; height: 400px; margin-top: 20px;"></div>
    </a-card>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import * as echarts from 'echarts'

let testChart = null

const initTestChart = () => {
  const chartContainer = document.getElementById('test-chart')
  if (chartContainer) {
    if (testChart) {
      testChart.dispose()
    }
    
    testChart = echarts.init(chartContainer)
    
    const option = {
      title: {
        text: 'ECharts测试图表'
      },
      tooltip: {},
      legend: {
        data: ['销量']
      },
      xAxis: {
        data: ['衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', '袜子']
      },
      yAxis: {},
      series: [
        {
          name: '销量',
          type: 'bar',
          data: [5, 20, 36, 10, 10, 20]
        }
      ]
    }
    
    testChart.setOption(option)
    console.log('测试图表初始化成功')
  }
}

onMounted(() => {
  setTimeout(initTestChart, 1000)
})
</script>

<style scoped>
.chart-test {
  padding: 24px;
}
</style>
