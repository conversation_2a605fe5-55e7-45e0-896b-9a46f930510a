import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 风险管理组合式函数
 * 提供交易风险控制和管理功能
 */
export function useRiskManagement() {
  // 风险指标
  const riskMetrics = ref({
    totalRisk: 0,
    riskLevel: 'low', // low, medium, high, extreme
    portfolioValue: 0,
    dailyPnL: 0,
    maxDrawdown: 0,
    sharpeRatio: 0,
    volatility: 0,
    var95: 0, // 95% Value at Risk
    expectedShortfall: 0, // 期望损失
    betaCoefficient: 1, // Beta系数
    correlationRisk: 0 // 相关性风险
  })

  // 风险设置
  const riskSettings = ref({
    maxPositionSize: 0.1, // 最大单仓位大小（占总资金比例）
    maxDailyLoss: 0.02, // 最大日损失（占总资金比例）
    stopLossPercentage: 0.05, // 默认止损百分比
    takeProfitPercentage: 0.1, // 默认止盈百分比
    maxOpenPositions: 5, // 最大开仓数量
    riskPerTrade: 0.02, // 每笔交易风险（占总资金比例）
    maxLeverage: 10, // 最大杠杆倍数
    correlationLimit: 0.7, // 相关性限制
    concentrationLimit: 0.3, // 集中度限制
    autoStopLoss: true, // 自动止损
    autoTakeProfit: false, // 自动止盈
    riskAlertThreshold: 0.8 // 风险警告阈值
  })

  // 风险报告
  const riskReport = ref(null)
  const riskAlerts = ref([])
  const isCalculating = ref(false)
  const isGeneratingReport = ref(false)

  // 风险等级定义
  const riskLevels = {
    low: {
      label: '低风险',
      color: '#52c41a',
      icon: '🟢',
      threshold: 0.2
    },
    medium: {
      label: '中等风险',
      color: '#faad14',
      icon: '🟡',
      threshold: 0.5
    },
    high: {
      label: '高风险',
      color: '#ff7875',
      icon: '🟠',
      threshold: 0.8
    },
    extreme: {
      label: '极高风险',
      color: '#ff4d4f',
      icon: '🔴',
      threshold: 1.0
    }
  }

  // 计算风险等级
  const currentRiskLevel = computed(() => {
    const risk = riskMetrics.value.totalRisk
    if (risk <= riskLevels.low.threshold) return riskLevels.low
    if (risk <= riskLevels.medium.threshold) return riskLevels.medium
    if (risk <= riskLevels.high.threshold) return riskLevels.high
    return riskLevels.extreme
  })

  // 风险警告状态
  const isRiskHigh = computed(() => {
    return riskMetrics.value.totalRisk >= riskSettings.value.riskAlertThreshold
  })

  /**
   * 计算投资组合风险
   * @param {Array} positions - 仓位列表
   * @param {Array} marketData - 市场数据
   * @returns {Promise<Object>} 风险计算结果
   */
  async function calculateRisk(positions = [], marketData = []) {
    try {
      isCalculating.value = true

      if (!positions.length) {
        riskMetrics.value = {
          ...riskMetrics.value,
          totalRisk: 0,
          riskLevel: 'low'
        }
        return riskMetrics.value
      }

      // 计算各项风险指标
      const portfolioValue = calculatePortfolioValue(positions)
      const dailyPnL = calculateDailyPnL(positions)
      const volatility = calculateVolatility(marketData)
      const var95 = calculateVaR(positions, marketData, 0.95)
      const expectedShortfall = calculateExpectedShortfall(positions, marketData, 0.95)
      const maxDrawdown = calculateMaxDrawdown(positions)
      const sharpeRatio = calculateSharpeRatio(positions, marketData)
      const correlationRisk = calculateCorrelationRisk(positions)
      const concentrationRisk = calculateConcentrationRisk(positions)
      const leverageRisk = calculateLeverageRisk(positions)

      // 综合风险评分
      const totalRisk = calculateTotalRisk({
        volatility,
        correlationRisk,
        concentrationRisk,
        leverageRisk,
        drawdownRisk: maxDrawdown
      })

      // 更新风险指标
      riskMetrics.value = {
        totalRisk,
        riskLevel: getRiskLevel(totalRisk),
        portfolioValue,
        dailyPnL,
        maxDrawdown,
        sharpeRatio,
        volatility,
        var95,
        expectedShortfall,
        correlationRisk
      }

      // 检查风险警告
      checkRiskAlerts()

      return riskMetrics.value
    } catch (error) {
      console.error('风险计算失败:', error)
      message.error('风险计算失败')
      throw error
    } finally {
      isCalculating.value = false
    }
  }

  /**
   * 计算投资组合价值
   */
  function calculatePortfolioValue(positions) {
    return positions.reduce((total, position) => {
      const value = position.size * position.currentPrice * (position.leverage || 1)
      return total + Math.abs(value)
    }, 0)
  }

  /**
   * 计算日盈亏
   */
  function calculateDailyPnL(positions) {
    return positions.reduce((total, position) => {
      return total + (position.unrealizedPnL || 0) + (position.realizedPnL || 0)
    }, 0)
  }

  /**
   * 计算波动率
   */
  function calculateVolatility(marketData) {
    if (!marketData.length || marketData.length < 2) return 0

    const returns = []
    for (let i = 1; i < marketData.length; i++) {
      const currentPrice = marketData[i].close
      const previousPrice = marketData[i - 1].close
      const returnRate = (currentPrice - previousPrice) / previousPrice
      returns.push(returnRate)
    }

    if (returns.length === 0) return 0

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length
    return Math.sqrt(variance) * Math.sqrt(252) // 年化波动率
  }

  /**
   * 计算VaR (Value at Risk)
   */
  function calculateVaR(positions, marketData, confidence = 0.95) {
    if (!marketData.length) return 0

    const portfolioValue = calculatePortfolioValue(positions)
    const volatility = calculateVolatility(marketData)
    
    // 使用正态分布假设计算VaR
    const zScore = confidence === 0.95 ? 1.645 : confidence === 0.99 ? 2.326 : 1.282
    return portfolioValue * volatility * zScore / Math.sqrt(252)
  }

  /**
   * 计算期望损失 (Expected Shortfall)
   */
  function calculateExpectedShortfall(positions, marketData, confidence = 0.95) {
    const var95 = calculateVaR(positions, marketData, confidence)
    // 简化计算，实际应该基于历史模拟或蒙特卡洛方法
    return var95 * 1.3
  }

  /**
   * 计算最大回撤
   */
  function calculateMaxDrawdown(positions) {
    // 简化计算，实际应该基于历史净值曲线
    const totalPnL = calculateDailyPnL(positions)
    const portfolioValue = calculatePortfolioValue(positions)
    
    if (portfolioValue === 0) return 0
    return Math.max(0, -totalPnL / portfolioValue)
  }

  /**
   * 计算夏普比率
   */
  function calculateSharpeRatio(positions, marketData) {
    const dailyPnL = calculateDailyPnL(positions)
    const portfolioValue = calculatePortfolioValue(positions)
    const volatility = calculateVolatility(marketData)
    
    if (volatility === 0 || portfolioValue === 0) return 0
    
    const returnRate = dailyPnL / portfolioValue
    const riskFreeRate = 0.02 // 假设无风险利率2%
    
    return (returnRate - riskFreeRate) / volatility
  }

  /**
   * 计算相关性风险
   */
  function calculateCorrelationRisk(positions) {
    if (positions.length < 2) return 0

    // 简化计算：基于同类资产的集中度
    const symbolGroups = {}
    positions.forEach(position => {
      const baseSymbol = position.symbol.split('-')[0]
      if (!symbolGroups[baseSymbol]) {
        symbolGroups[baseSymbol] = 0
      }
      symbolGroups[baseSymbol] += Math.abs(position.size * position.currentPrice)
    })

    const totalValue = Object.values(symbolGroups).reduce((sum, value) => sum + value, 0)
    if (totalValue === 0) return 0

    // 计算赫芬达尔指数
    const herfindahlIndex = Object.values(symbolGroups)
      .reduce((sum, value) => sum + Math.pow(value / totalValue, 2), 0)

    return herfindahlIndex
  }

  /**
   * 计算集中度风险
   */
  function calculateConcentrationRisk(positions) {
    if (!positions.length) return 0

    const totalValue = calculatePortfolioValue(positions)
    if (totalValue === 0) return 0

    // 计算最大单仓位占比
    const maxPositionRatio = Math.max(...positions.map(position => {
      const positionValue = Math.abs(position.size * position.currentPrice)
      return positionValue / totalValue
    }))

    return maxPositionRatio
  }

  /**
   * 计算杠杆风险
   */
  function calculateLeverageRisk(positions) {
    if (!positions.length) return 0

    const avgLeverage = positions.reduce((sum, position) => {
      return sum + (position.leverage || 1)
    }, 0) / positions.length

    // 杠杆风险评分：杠杆越高风险越大
    return Math.min(avgLeverage / riskSettings.value.maxLeverage, 1)
  }

  /**
   * 计算综合风险评分
   */
  function calculateTotalRisk(riskComponents) {
    const weights = {
      volatility: 0.3,
      correlationRisk: 0.2,
      concentrationRisk: 0.2,
      leverageRisk: 0.15,
      drawdownRisk: 0.15
    }

    return Object.entries(weights).reduce((totalRisk, [component, weight]) => {
      const componentRisk = riskComponents[component] || 0
      return totalRisk + (componentRisk * weight)
    }, 0)
  }

  /**
   * 获取风险等级
   */
  function getRiskLevel(totalRisk) {
    if (totalRisk <= riskLevels.low.threshold) return 'low'
    if (totalRisk <= riskLevels.medium.threshold) return 'medium'
    if (totalRisk <= riskLevels.high.threshold) return 'high'
    return 'extreme'
  }

  /**
   * 检查风险警告
   */
  function checkRiskAlerts() {
    const alerts = []
    const metrics = riskMetrics.value
    const settings = riskSettings.value

    // 总风险警告
    if (metrics.totalRisk >= settings.riskAlertThreshold) {
      alerts.push({
        id: `risk-${Date.now()}`,
        type: 'high-risk',
        level: 'error',
        message: `总风险过高: ${(metrics.totalRisk * 100).toFixed(1)}%`,
        timestamp: Date.now(),
        acknowledged: false
      })
    }

    // 集中度风险警告
    if (metrics.correlationRisk >= settings.concentrationLimit) {
      alerts.push({
        id: `concentration-${Date.now()}`,
        type: 'concentration-risk',
        level: 'warning',
        message: `仓位过于集中，建议分散投资`,
        timestamp: Date.now(),
        acknowledged: false
      })
    }

    // 日损失警告
    const dailyLossRatio = Math.abs(metrics.dailyPnL) / metrics.portfolioValue
    if (metrics.dailyPnL < 0 && dailyLossRatio >= settings.maxDailyLoss) {
      alerts.push({
        id: `daily-loss-${Date.now()}`,
        type: 'daily-loss',
        level: 'error',
        message: `日损失超限: ${(dailyLossRatio * 100).toFixed(1)}%`,
        timestamp: Date.now(),
        acknowledged: false
      })
    }

    // 最大回撤警告
    if (metrics.maxDrawdown >= 0.1) {
      alerts.push({
        id: `drawdown-${Date.now()}`,
        type: 'max-drawdown',
        level: 'warning',
        message: `最大回撤过大: ${(metrics.maxDrawdown * 100).toFixed(1)}%`,
        timestamp: Date.now(),
        acknowledged: false
      })
    }

    riskAlerts.value = alerts
  }

  /**
   * 更新风险设置
   */
  async function updateRiskSettings(newSettings) {
    try {
      // 验证设置值
      const validatedSettings = validateRiskSettings(newSettings)
      
      riskSettings.value = {
        ...riskSettings.value,
        ...validatedSettings
      }

      message.success('风险设置已更新')
      return { success: true }
    } catch (error) {
      console.error('更新风险设置失败:', error)
      message.error('更新风险设置失败')
      return { success: false, error: error.message }
    }
  }

  /**
   * 验证风险设置
   */
  function validateRiskSettings(settings) {
    const validated = { ...settings }

    // 验证百分比值
    const percentageFields = [
      'maxPositionSize', 'maxDailyLoss', 'stopLossPercentage', 
      'takeProfitPercentage', 'riskPerTrade', 'concentrationLimit', 'riskAlertThreshold'
    ]

    percentageFields.forEach(field => {
      if (validated[field] !== undefined) {
        if (validated[field] < 0 || validated[field] > 1) {
          throw new Error(`${field} 必须在 0-1 之间`)
        }
      }
    })

    // 验证整数值
    if (validated.maxOpenPositions !== undefined) {
      if (!Number.isInteger(validated.maxOpenPositions) || validated.maxOpenPositions < 1) {
        throw new Error('最大开仓数量必须是正整数')
      }
    }

    if (validated.maxLeverage !== undefined) {
      if (validated.maxLeverage < 1 || validated.maxLeverage > 100) {
        throw new Error('最大杠杆倍数必须在 1-100 之间')
      }
    }

    return validated
  }

  /**
   * 生成风险报告
   */
  async function generateRiskReport(positions = [], marketData = []) {
    try {
      isGeneratingReport.value = true

      // 计算最新风险指标
      await calculateRisk(positions, marketData)

      const report = {
        timestamp: new Date(),
        summary: generateRiskSummary(),
        recommendations: generateRecommendations(),
        riskScore: riskMetrics.value.totalRisk,
        riskLevel: riskMetrics.value.riskLevel,
        metrics: { ...riskMetrics.value },
        alerts: [...riskAlerts.value],
        portfolioAnalysis: {
          diversificationScore: calculateDiversificationScore(positions),
          riskAdjustedReturn: calculateRiskAdjustedReturn(positions),
          optimalPositionSizes: calculateOptimalPositionSizes(positions)
        }
      }

      riskReport.value = report
      return report
    } catch (error) {
      console.error('生成风险报告失败:', error)
      message.error('生成风险报告失败')
      throw error
    } finally {
      isGeneratingReport.value = false
    }
  }

  /**
   * 生成风险摘要
   */
  function generateRiskSummary() {
    const level = currentRiskLevel.value
    const risk = riskMetrics.value.totalRisk

    if (level.label === '低风险') {
      return '投资组合风险控制良好，可以考虑适当增加仓位。'
    } else if (level.label === '中等风险') {
      return '投资组合风险适中，建议密切关注市场变化。'
    } else if (level.label === '高风险') {
      return '投资组合风险较高，建议减少仓位或加强风险控制。'
    } else {
      return '投资组合风险极高，建议立即采取风险控制措施。'
    }
  }

  /**
   * 生成风险建议
   */
  function generateRecommendations() {
    const recommendations = []
    const metrics = riskMetrics.value
    const settings = riskSettings.value

    // 基于风险等级的建议
    if (metrics.totalRisk > 0.6) {
      recommendations.push('建议减少整体仓位规模')
      recommendations.push('考虑设置更严格的止损')
    }

    // 基于集中度的建议
    if (metrics.correlationRisk > settings.concentrationLimit) {
      recommendations.push('投资组合过于集中，建议分散到不同资产')
    }

    // 基于波动率的建议
    if (metrics.volatility > 0.5) {
      recommendations.push('市场波动较大，建议降低杠杆倍数')
    }

    // 基于回撤的建议
    if (metrics.maxDrawdown > 0.1) {
      recommendations.push('最大回撤过大，建议优化止损策略')
    }

    // 基于夏普比率的建议
    if (metrics.sharpeRatio < 0.5) {
      recommendations.push('风险调整后收益较低，建议重新评估投资策略')
    }

    return recommendations.length > 0 ? recommendations : ['当前风险控制良好，继续保持']
  }

  /**
   * 计算分散化评分
   */
  function calculateDiversificationScore(positions) {
    if (positions.length <= 1) return 0

    // 基于资产数量和相关性的分散化评分
    const assetCount = positions.length
    const correlationPenalty = riskMetrics.value.correlationRisk
    
    const baseScore = Math.min(assetCount / 10, 1) // 最多10个资产得满分
    const diversificationScore = baseScore * (1 - correlationPenalty)
    
    return Math.max(0, Math.min(1, diversificationScore))
  }

  /**
   * 计算风险调整后收益
   */
  function calculateRiskAdjustedReturn(positions) {
    const totalReturn = calculateDailyPnL(positions)
    const portfolioValue = calculatePortfolioValue(positions)
    const totalRisk = riskMetrics.value.totalRisk
    
    if (portfolioValue === 0 || totalRisk === 0) return 0
    
    const returnRate = totalReturn / portfolioValue
    return returnRate / totalRisk
  }

  /**
   * 计算最优仓位大小
   */
  function calculateOptimalPositionSizes(positions) {
    // 使用凯利公式的简化版本
    return positions.map(position => {
      const winRate = 0.6 // 假设胜率，实际应该基于历史数据
      const avgWin = 0.05 // 平均盈利
      const avgLoss = 0.03 // 平均亏损
      
      const kellyPercentage = (winRate * avgWin - (1 - winRate) * avgLoss) / avgWin
      const optimalSize = Math.max(0, Math.min(kellyPercentage, riskSettings.value.maxPositionSize))
      
      return {
        symbol: position.symbol,
        currentSize: position.size,
        optimalSize,
        recommendation: optimalSize > position.size ? 'increase' : 
                       optimalSize < position.size ? 'decrease' : 'maintain'
      }
    })
  }

  /**
   * 确认风险警告
   */
  function acknowledgeAlert(alertId) {
    const alert = riskAlerts.value.find(a => a.id === alertId)
    if (alert) {
      alert.acknowledged = true
      alert.acknowledgedAt = Date.now()
    }
  }

  /**
   * 清除已确认的警告
   */
  function clearAcknowledgedAlerts() {
    riskAlerts.value = riskAlerts.value.filter(alert => !alert.acknowledged)
  }

  /**
   * 导出风险数据
   */
  function exportRiskData() {
    const data = {
      timestamp: new Date().toISOString(),
      riskMetrics: riskMetrics.value,
      riskSettings: riskSettings.value,
      riskReport: riskReport.value,
      riskAlerts: riskAlerts.value
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `risk-data-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    
    URL.revokeObjectURL(url)
    message.success('风险数据已导出')
  }

  // 监听风险设置变化，自动重新计算风险
  watch(
    () => riskSettings.value,
    () => {
      // 延迟重新计算，避免频繁计算
      setTimeout(() => {
        if (riskMetrics.value.portfolioValue > 0) {
          calculateRisk()
        }
      }, 1000)
    },
    { deep: true }
  )

  return {
    // 状态
    riskMetrics,
    riskSettings,
    riskReport,
    riskAlerts,
    isCalculating,
    isGeneratingReport,
    
    // 计算属性
    currentRiskLevel,
    isRiskHigh,
    
    // 方法
    calculateRisk,
    updateRiskSettings,
    generateRiskReport,
    acknowledgeAlert,
    clearAcknowledgedAlerts,
    exportRiskData,
    
    // 工具方法
    calculatePortfolioValue,
    calculateDailyPnL,
    calculateVolatility,
    calculateCorrelationRisk,
    calculateConcentrationRisk,
    validateRiskSettings
  }
}