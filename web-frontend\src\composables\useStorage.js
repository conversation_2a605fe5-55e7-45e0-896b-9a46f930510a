import { ref, watch, nextTick } from 'vue'
import { useUserStore } from '@/stores/user'
import axios from 'axios'

/**
 * @description 本地存储管理的组合式函数
 * @returns {Object} 包含本地存储操作的对象
 */
export function useLocalStorage() {
  /**
   * @description 保存数据到本地存储
   * @param {string} key - 存储键名
   * @param {any} value - 存储值
   * @param {boolean} encrypt - 是否加密存储
   */
  const setItem = (key, value, encrypt = false) => {
    try {
      let dataToStore = value
      
      if (typeof value === 'object') {
        dataToStore = JSON.stringify(value)
      }
      
      if (encrypt) {
        // 简单的Base64编码（实际项目中应使用更安全的加密方式）
        dataToStore = btoa(dataToStore)
      }
      
      localStorage.setItem(key, dataToStore)
    } catch (error) {
      console.error('保存到本地存储失败:', error)
    }
  }
  
  /**
   * @description 从本地存储获取数据
   * @param {string} key - 存储键名
   * @param {any} defaultValue - 默认值
   * @param {boolean} decrypt - 是否解密
   * @returns {any} 存储的值
   */
  const getItem = (key, defaultValue = null, decrypt = false) => {
    try {
      let data = localStorage.getItem(key)
      
      if (data === null) {
        return defaultValue
      }
      
      if (decrypt) {
        data = atob(data)
      }
      
      // 尝试解析JSON
      try {
        return JSON.parse(data)
      } catch {
        return data
      }
    } catch (error) {
      console.error('从本地存储读取失败:', error)
      return defaultValue
    }
  }
  
  /**
   * @description 删除本地存储项
   * @param {string} key - 存储键名
   */
  const removeItem = (key) => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('删除本地存储项失败:', error)
    }
  }
  
  /**
   * @description 清空本地存储
   */
  const clear = () => {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('清空本地存储失败:', error)
    }
  }
  
  /**
   * @description 获取存储大小
   * @returns {number} 存储大小（字节）
   */
  const getStorageSize = () => {
    let total = 0
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        total += localStorage[key].length + key.length
      }
    }
    return total
  }
  
  return {
    setItem,
    getItem,
    removeItem,
    clear,
    getStorageSize
  }
}

/**
 * @description 用户设置持久化的组合式函数
 * @returns {Object} 包含用户设置管理的对象
 */
export function useUserSettings() {
  const userStore = useUserStore()
  const { setItem, getItem, removeItem } = useLocalStorage()
  
  // 设置状态
  const loading = ref(false)
  const error = ref(null)
  
  // 默认设置
  const defaultSettings = {
    theme: 'dark',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    notifications: {
      email: true,
      push: true,
      sound: true,
      priceAlerts: true,
      tradeAlerts: true
    },
    trading: {
      defaultLeverage: 1,
      riskLevel: 'medium',
      autoStopLoss: false,
      autoTakeProfit: false,
      slippage: 0.1
    },
    charts: {
      defaultTimeframe: '1h',
      indicators: ['MA', 'MACD', 'RSI'],
      chartType: 'candlestick',
      volume: true,
      grid: true
    },
    display: {
      currency: 'USDT',
      precision: 4,
      compactNumbers: true,
      showBalance: true
    }
  }
  
  /**
   * @description 获取用户设置
   * @returns {Object} 用户设置
   */
  const getSettings = () => {
    const localSettings = getItem('user_settings', {})
    return { ...defaultSettings, ...localSettings }
  }
  
  /**
   * @description 保存用户设置到本地
   * @param {Object} settings - 用户设置
   */
  const saveSettingsLocal = (settings) => {
    setItem('user_settings', settings)
  }
  
  /**
   * @description 保存用户设置到服务器
   * @param {Object} settings - 用户设置
   * @returns {Promise<boolean>} 保存是否成功
   */
  const saveSettingsRemote = async (settings) => {
    if (!userStore.isAuthenticated) {
      return false
    }
    
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.put('/api/user/settings', {
        settings: settings
      })
      
      if (response.data.success) {
        console.log('用户设置已同步到服务器')
        return true
      } else {
        error.value = response.data.message || '保存设置失败'
        return false
      }
    } catch (err) {
      console.error('保存用户设置错误:', err)
      error.value = err.response?.data?.message || '网络错误，请稍后重试'
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * @description 从服务器加载用户设置
   * @returns {Promise<Object|null>} 用户设置
   */
  const loadSettingsRemote = async () => {
    if (!userStore.isAuthenticated) {
      return null
    }
    
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.get('/api/user/settings')
      
      if (response.data.success) {
        const settings = response.data.data.settings || {}
        const mergedSettings = { ...defaultSettings, ...settings }
        
        // 保存到本地
        saveSettingsLocal(mergedSettings)
        
        console.log('用户设置已从服务器加载')
        return mergedSettings
      }
    } catch (err) {
      console.error('加载用户设置错误:', err)
      error.value = err.response?.data?.message || '网络错误，请稍后重试'
    } finally {
      loading.value = false
    }
    
    return null
  }
  
  /**
   * @description 更新设置项
   * @param {string} path - 设置路径（如 'theme' 或 'trading.defaultLeverage'）
   * @param {any} value - 设置值
   * @param {boolean} sync - 是否同步到服务器
   */
  const updateSetting = async (path, value, sync = true) => {
    const settings = getSettings()
    
    // 使用路径设置嵌套属性
    const keys = path.split('.')
    let current = settings
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {}
      }
      current = current[keys[i]]
    }
    
    current[keys[keys.length - 1]] = value
    
    // 保存到本地
    saveSettingsLocal(settings)
    
    // 同步到服务器
    if (sync && userStore.isAuthenticated) {
      await saveSettingsRemote(settings)
    }
    
    return settings
  }
  
  /**
   * @description 重置设置为默认值
   * @param {boolean} sync - 是否同步到服务器
   */
  const resetSettings = async (sync = true) => {
    saveSettingsLocal(defaultSettings)
    
    if (sync && userStore.isAuthenticated) {
      await saveSettingsRemote(defaultSettings)
    }
    
    return defaultSettings
  }
  
  return {
    loading,
    error,
    defaultSettings,
    getSettings,
    saveSettingsLocal,
    saveSettingsRemote,
    loadSettingsRemote,
    updateSetting,
    resetSettings
  }
}

/**
 * @description 历史数据持久化的组合式函数
 * @returns {Object} 包含历史数据管理的对象
 */
export function useHistoryData() {
  const userStore = useUserStore()
  const { setItem, getItem, removeItem } = useLocalStorage()
  
  const loading = ref(false)
  const error = ref(null)
  
  /**
   * @description 保存交易历史到本地
   * @param {Array} trades - 交易记录
   */
  const saveTradeHistoryLocal = (trades) => {
    const key = `trade_history_${userStore.user?.id || 'guest'}`
    setItem(key, trades)
  }
  
  /**
   * @description 获取本地交易历史
   * @returns {Array} 交易记录
   */
  const getTradeHistoryLocal = () => {
    const key = `trade_history_${userStore.user?.id || 'guest'}`
    return getItem(key, [])
  }
  
  /**
   * @description 保存图表配置到本地
   * @param {string} symbol - 交易对
   * @param {Object} config - 图表配置
   */
  const saveChartConfigLocal = (symbol, config) => {
    const key = `chart_config_${symbol}_${userStore.user?.id || 'guest'}`
    setItem(key, config)
  }
  
  /**
   * @description 获取本地图表配置
   * @param {string} symbol - 交易对
   * @returns {Object} 图表配置
   */
  const getChartConfigLocal = (symbol) => {
    const key = `chart_config_${symbol}_${userStore.user?.id || 'guest'}`
    return getItem(key, null)
  }
  
  /**
   * @description 保存观察列表到本地
   * @param {Array} watchlist - 观察列表
   */
  const saveWatchlistLocal = (watchlist) => {
    const key = `watchlist_${userStore.user?.id || 'guest'}`
    setItem(key, watchlist)
  }
  
  /**
   * @description 获取本地观察列表
   * @returns {Array} 观察列表
   */
  const getWatchlistLocal = () => {
    const key = `watchlist_${userStore.user?.id || 'guest'}`
    return getItem(key, [])
  }
  
  /**
   * @description 从服务器同步历史数据
   * @returns {Promise<Object|null>} 历史数据
   */
  const syncHistoryFromServer = async () => {
    if (!userStore.isAuthenticated) {
      return null
    }
    
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.get('/api/user/history')
      
      if (response.data.success) {
        const historyData = response.data.data
        
        // 保存各类历史数据到本地
        if (historyData.trades) {
          saveTradeHistoryLocal(historyData.trades)
        }
        
        if (historyData.watchlist) {
          saveWatchlistLocal(historyData.watchlist)
        }
        
        if (historyData.chartConfigs) {
          Object.entries(historyData.chartConfigs).forEach(([symbol, config]) => {
            saveChartConfigLocal(symbol, config)
          })
        }
        
        console.log('历史数据已从服务器同步')
        return historyData
      }
    } catch (err) {
      console.error('同步历史数据错误:', err)
      error.value = err.response?.data?.message || '网络错误，请稍后重试'
    } finally {
      loading.value = false
    }
    
    return null
  }
  
  /**
   * @description 上传历史数据到服务器
   * @returns {Promise<boolean>} 上传是否成功
   */
  const uploadHistoryToServer = async () => {
    if (!userStore.isAuthenticated) {
      return false
    }
    
    loading.value = true
    error.value = null
    
    try {
      const historyData = {
        trades: getTradeHistoryLocal(),
        watchlist: getWatchlistLocal(),
        chartConfigs: {}
      }
      
      // 收集所有图表配置
      const symbols = ['BTC-USDT', 'ETH-USDT', 'BNB-USDT'] // 可以从其他地方获取
      symbols.forEach(symbol => {
        const config = getChartConfigLocal(symbol)
        if (config) {
          historyData.chartConfigs[symbol] = config
        }
      })
      
      const response = await axios.put('/api/user/history', historyData)
      
      if (response.data.success) {
        console.log('历史数据已上传到服务器')
        return true
      } else {
        error.value = response.data.message || '上传历史数据失败'
        return false
      }
    } catch (err) {
      console.error('上传历史数据错误:', err)
      error.value = err.response?.data?.message || '网络错误，请稍后重试'
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * @description 添加交易记录
   * @param {Object} trade - 交易记录
   * @param {boolean} sync - 是否同步到服务器
   */
  const addTradeRecord = async (trade, sync = true) => {
    const trades = getTradeHistoryLocal()
    trades.unshift({
      ...trade,
      id: Date.now().toString(),
      timestamp: new Date().toISOString()
    })
    
    // 限制本地存储的记录数量
    if (trades.length > 1000) {
      trades.splice(1000)
    }
    
    saveTradeHistoryLocal(trades)
    
    if (sync && userStore.isAuthenticated) {
      await uploadHistoryToServer()
    }
  }
  
  /**
   * @description 更新观察列表
   * @param {Array} watchlist - 新的观察列表
   * @param {boolean} sync - 是否同步到服务器
   */
  const updateWatchlist = async (watchlist, sync = true) => {
    saveWatchlistLocal(watchlist)
    
    if (sync && userStore.isAuthenticated) {
      await uploadHistoryToServer()
    }
  }
  
  /**
   * @description 更新图表配置
   * @param {string} symbol - 交易对
   * @param {Object} config - 图表配置
   * @param {boolean} sync - 是否同步到服务器
   */
  const updateChartConfig = async (symbol, config, sync = true) => {
    saveChartConfigLocal(symbol, config)
    
    if (sync && userStore.isAuthenticated) {
      await uploadHistoryToServer()
    }
  }
  
  /**
   * @description 清除历史数据
   * @param {string} type - 数据类型（'trades', 'watchlist', 'charts', 'all'）
   */
  const clearHistoryData = (type = 'all') => {
    const userId = userStore.user?.id || 'guest'
    
    switch (type) {
      case 'trades':
        removeItem(`trade_history_${userId}`)
        break
      case 'watchlist':
        removeItem(`watchlist_${userId}`)
        break
      case 'charts':
        // 清除所有图表配置（这里需要遍历所有可能的symbol）
        const symbols = ['BTC-USDT', 'ETH-USDT', 'BNB-USDT']
        symbols.forEach(symbol => {
          removeItem(`chart_config_${symbol}_${userId}`)
        })
        break
      case 'all':
        clearHistoryData('trades')
        clearHistoryData('watchlist')
        clearHistoryData('charts')
        break
    }
  }
  
  return {
    loading,
    error,
    
    // 本地存储方法
    saveTradeHistoryLocal,
    getTradeHistoryLocal,
    saveChartConfigLocal,
    getChartConfigLocal,
    saveWatchlistLocal,
    getWatchlistLocal,
    
    // 服务器同步方法
    syncHistoryFromServer,
    uploadHistoryToServer,
    
    // 数据操作方法
    addTradeRecord,
    updateWatchlist,
    updateChartConfig,
    clearHistoryData
  }
}

/**
 * @description 缓存管理的组合式函数
 * @returns {Object} 包含缓存管理的对象
 */
export function useCache() {
  const { setItem, getItem, removeItem } = useLocalStorage()
  
  /**
   * @description 设置缓存
   * @param {string} key - 缓存键
   * @param {any} data - 缓存数据
   * @param {number} ttl - 过期时间（毫秒）
   */
  const setCache = (key, data, ttl = 5 * 60 * 1000) => { // 默认5分钟
    const cacheData = {
      data,
      timestamp: Date.now(),
      ttl
    }
    setItem(`cache_${key}`, cacheData)
  }
  
  /**
   * @description 获取缓存
   * @param {string} key - 缓存键
   * @returns {any|null} 缓存数据或null
   */
  const getCache = (key) => {
    const cacheData = getItem(`cache_${key}`)
    
    if (!cacheData) {
      return null
    }
    
    const { data, timestamp, ttl } = cacheData
    
    // 检查是否过期
    if (Date.now() - timestamp > ttl) {
      removeItem(`cache_${key}`)
      return null
    }
    
    return data
  }
  
  /**
   * @description 删除缓存
   * @param {string} key - 缓存键
   */
  const removeCache = (key) => {
    removeItem(`cache_${key}`)
  }
  
  /**
   * @description 清除所有过期缓存
   */
  const clearExpiredCache = () => {
    const keys = Object.keys(localStorage)
    const cacheKeys = keys.filter(key => key.startsWith('cache_'))
    
    cacheKeys.forEach(key => {
      const cacheData = getItem(key)
      if (cacheData && cacheData.timestamp && cacheData.ttl) {
        if (Date.now() - cacheData.timestamp > cacheData.ttl) {
          removeItem(key)
        }
      }
    })
  }
  
  return {
    setCache,
    getCache,
    removeCache,
    clearExpiredCache
  }
}