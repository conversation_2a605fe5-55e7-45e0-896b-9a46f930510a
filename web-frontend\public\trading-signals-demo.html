<!DOCTYPE html>
<html>
<head>
    <title>交易信号统计演示</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #1890ff;
            color: white;
            padding: 16px;
            text-align: center;
            font-weight: bold;
        }
        .signal-row {
            display: grid;
            grid-template-columns: 1fr auto auto;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        .signal-row:hover {
            background: #fafafa;
        }
        .signal-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }
        .signal-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            min-width: 60px;
            text-align: center;
        }
        .status-green { background: #52c41a; color: white; }
        .status-red { background: #ff4d4f; color: white; }
        .status-orange { background: #faad14; color: white; }
        .status-blue { background: #1890ff; color: white; }
        .status-gray { background: #d9d9d9; color: #666; }
        
        .signal-value {
            font-size: 12px;
            color: #999;
            text-align: right;
            min-width: 80px;
        }
        .summary-row {
            background: #e6f7ff;
            border-left: 3px solid #1890ff;
        }
        .recommendation {
            padding: 16px;
            background: #f9f9f9;
            border-top: 2px solid #1890ff;
        }
        .recommendation h4 {
            margin: 0 0 8px 0;
            color: #333;
        }
        .recommendation-action {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .action-buy { background: #52c41a; color: white; }
        .action-sell { background: #ff4d4f; color: white; }
        .action-hold { background: #1890ff; color: white; }
        
        .recommendation-text {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
            margin-bottom: 12px;
        }
        .stats {
            display: flex;
            gap: 8px;
            font-size: 11px;
        }
        .stat-item {
            padding: 4px 6px;
            background: #f0f0f0;
            border-radius: 3px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            交易信号统计
        </div>
        
        <!-- EMA 信号 -->
        <div class="signal-row">
            <span class="signal-label">EMA 交叉</span>
            <span class="signal-status status-green">金叉</span>
            <span class="signal-value">多头</span>
        </div>

        <!-- EMA 状态 -->
        <div class="signal-row">
            <span class="signal-label">EMA 慢线状态</span>
            <span class="signal-status status-green">多头</span>
            <span class="signal-value">2399.12</span>
        </div>

        <div class="signal-row">
            <span class="signal-label">EMA 中性线状态</span>
            <span class="signal-status status-green">多头</span>
            <span class="signal-value">2410.26</span>
        </div>

        <!-- MA 状态 -->
        <div class="signal-row">
            <span class="signal-label">MA 快线状态</span>
            <span class="signal-status status-green">多头</span>
            <span class="signal-value">2431.91</span>
        </div>

        <div class="signal-row">
            <span class="signal-label">MA 中性线状态</span>
            <span class="signal-status status-green">多头</span>
            <span class="signal-value">2415.27</span>
        </div>

        <div class="signal-row">
            <span class="signal-label">MA 慢线状态</span>
            <span class="signal-status status-green">多头</span>
            <span class="signal-value">2410.78</span>
        </div>

        <!-- RSI 状态 -->
        <div class="signal-row">
            <span class="signal-label">RSI 超买超卖</span>
            <span class="signal-status status-orange">RSI 中立</span>
            <span class="signal-value">55.47</span>
        </div>

        <div class="signal-row">
            <span class="signal-label">RSI 状态</span>
            <span class="signal-status status-blue">中立</span>
            <span class="signal-value">观望</span>
        </div>

        <!-- MACD 信号 -->
        <div class="signal-row">
            <span class="signal-label">MACD 信号</span>
            <span class="signal-status status-blue">中立</span>
            <span class="signal-value">3.40</span>
        </div>

        <!-- 熊猫指标 -->
        <div class="signal-row">
            <span class="signal-label">熊猫多空指标</span>
            <span class="signal-status status-green">多头</span>
            <span class="signal-value">考虑买入</span>
        </div>

        <div class="signal-row">
            <span class="signal-label">熊猫能量指标</span>
            <span class="signal-status status-green">买入</span>
            <span class="signal-value">考虑买入</span>
        </div>

        <!-- 超级趋势 -->
        <div class="signal-row">
            <span class="signal-label">超级趋势指标</span>
            <span class="signal-status status-orange">上升超势</span>
            <span class="signal-value">2415.33</span>
        </div>

        <!-- 统计汇总 -->
        <div class="signal-row summary-row">
            <span class="signal-label">多头空头统计</span>
            <span class="signal-status status-green">8/0</span>
            <span class="signal-value">多头</span>
        </div>

        <!-- 综合建议 -->
        <div class="recommendation">
            <h4>综合建议</h4>
            <span class="recommendation-action action-buy">强烈买入</span>
            <div class="recommendation-text">
                多头信号占绝对优势，8个指标显示买入信号，0个显示卖出信号。技术面非常强劲，建议积极买入。
            </div>
            <div class="stats">
                <span class="stat-item">多头信号: 8</span>
                <span class="stat-item">空头信号: 0</span>
                <span class="stat-item">中性信号: 4</span>
                <span class="stat-item">信号强度: 89%</span>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 20px; color: #666;">
        <p>这是一个静态演示页面，展示交易信号统计的界面设计</p>
        <p>实际使用时会根据真实的技术指标数据动态更新</p>
        <p>访问 <a href="/trading/signals" style="color: #1890ff;">/trading/signals</a> 查看完整功能</p>
    </div>

    <script>
        // 模拟数据更新效果
        setInterval(() => {
            const rsiValue = document.querySelector('.signal-row:nth-child(7) .signal-value');
            const newRsi = (Math.random() * 40 + 30).toFixed(2);
            rsiValue.textContent = newRsi;
            
            const macdValue = document.querySelector('.signal-row:nth-child(9) .signal-value');
            const newMacd = (Math.random() * 10 - 5).toFixed(2);
            macdValue.textContent = newMacd;
        }, 3000);
    </script>
</body>
</html>
