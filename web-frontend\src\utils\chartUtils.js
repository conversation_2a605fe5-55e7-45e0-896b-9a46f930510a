/**
 * 格式化图表显示的时间
 * @param {number|string} timestamp - 时间戳
 * @param {string} timeframe - 时间周期
 * @returns {string} 格式化后的时间字符串
 */
export function formatChartTime(timestamp, timeframe = '1m') {
  const date = new Date(parseInt(timestamp));
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  if (timeframe.includes('m') || timeframe.includes('H')) {
    return `${month}-${day} ${hours}:${minutes}`;
  }
  return `${year}-${month}-${day}`;
}

/**
 * 格式化数字单位 (<PERSON>, <PERSON>, <PERSON>)
 * @param {number} num - 需要格式化的数字
 * @param {number} digits - 保留的小数位数
 * @returns {string} 格式化后的字符串
 */
export function formatUnit(num, digits = 2) {
    const si = [
        { value: 1, symbol: "" },
        { value: 1E3, symbol: "K" },
        { value: 1E6, symbol: "M" },
        { value: 1E9, symbol: "B" },
        { value: 1E12, symbol: "T" }
      ];
      const rx = /\.0+$|(\.d*?[1-9])0+$/;
      let i;
      for (i = si.length - 1; i > 0; i--) {
        if (num >= si[i].value) {
          break;
        }
      }
      return (num / si[i].value).toFixed(digits).replace(rx, "$1") + si[i].symbol;
}