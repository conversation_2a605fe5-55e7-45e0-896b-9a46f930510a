# API 文档

本文档描述了 OKX 交易前端系统与后端服务的 API 接口规范，包括 REST API 和 WebSocket 连接。

## 📋 目录

- [基础信息](#基础信息)
- [认证机制](#认证机制)
- [REST API](#rest-api)
- [WebSocket API](#websocket-api)
- [数据模型](#数据模型)
- [错误处理](#错误处理)
- [限流规则](#限流规则)
- [示例代码](#示例代码)

## 🔧 基础信息

### 服务端点

```
开发环境: http://localhost:8000
测试环境: https://api-test.yourdomain.com
生产环境: https://api.yourdomain.com
```

### 版本控制

```
API 版本: v1
基础路径: /api/v1
```

### 请求格式

- **Content-Type**: `application/json`
- **字符编码**: UTF-8
- **时间格式**: ISO 8601 (YYYY-MM-DDTHH:mm:ss.sssZ)

## 🔐 认证机制

### JWT Token 认证

```javascript
// 请求头格式
Headers: {
  'Authorization': 'Bearer <jwt_token>',
  'Content-Type': 'application/json'
}
```

### OKX API 签名认证

```javascript
// OKX API 请求头
Headers: {
  'OK-ACCESS-KEY': 'your_api_key',
  'OK-ACCESS-SIGN': 'signature',
  'OK-ACCESS-TIMESTAMP': 'timestamp',
  'OK-ACCESS-PASSPHRASE': 'passphrase'
}
```

## 🌐 REST API

### 用户认证

#### 登录
```http
POST /api/v1/auth/login
```

**请求体**:
```json
{
  "username": "string",
  "password": "string"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "token": "jwt_token_string",
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "role": "string"
    }
  }
}
```

#### 刷新Token
```http
POST /api/v1/auth/refresh
```

**请求头**:
```
Authorization: Bearer <refresh_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "token": "new_jwt_token_string"
  }
}
```

### 市场数据

#### 获取K线数据
```http
GET /api/v1/market/kline
```

**查询参数**:
```
symbol: string (必需) - 交易对，如 "BTC-USDT"
interval: string (必需) - 时间间隔 (1m, 5m, 15m, 1h, 4h, 1d)
start: string (可选) - 开始时间 (ISO 8601)
end: string (可选) - 结束时间 (ISO 8601)
limit: number (可选) - 数据条数，默认100，最大1000
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "timestamp": "2024-01-01T00:00:00.000Z",
      "open": "43000.00",
      "high": "43500.00",
      "low": "42800.00",
      "close": "43200.00",
      "volume": "1234.56",
      "turnover": "53088000.00"
    }
  ],
  "meta": {
    "symbol": "BTC-USDT",
    "interval": "1h",
    "count": 100
  }
}
```

#### 获取实时价格
```http
GET /api/v1/market/ticker
```

**查询参数**:
```
symbol: string (可选) - 交易对，不传则返回所有
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "symbol": "BTC-USDT",
      "last": "43200.00",
      "bid": "43190.00",
      "ask": "43210.00",
      "high24h": "44000.00",
      "low24h": "42000.00",
      "volume24h": "12345.67",
      "change24h": "2.5",
      "changePercent24h": "5.8"
    }
  ]
}
```

#### 获取深度数据
```http
GET /api/v1/market/depth
```

**查询参数**:
```
symbol: string (必需) - 交易对
depth: number (可选) - 深度档位，默认20
```

**响应**:
```json
{
  "success": true,
  "data": {
    "symbol": "BTC-USDT",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "bids": [
      ["43190.00", "1.234"],
      ["43180.00", "2.567"]
    ],
    "asks": [
      ["43210.00", "1.456"],
      ["43220.00", "2.789"]
    ]
  }
}
```

### 交易接口

#### 下单
```http
POST /api/v1/trade/order
```

**请求体**:
```json
{
  "symbol": "BTC-USDT",
  "side": "buy",
  "type": "limit",
  "amount": "0.1",
  "price": "43000.00",
  "timeInForce": "GTC",
  "clientOrderId": "my_order_123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "orderId": "order_123456",
    "clientOrderId": "my_order_123",
    "symbol": "BTC-USDT",
    "side": "buy",
    "type": "limit",
    "amount": "0.1",
    "price": "43000.00",
    "status": "pending",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

#### 取消订单
```http
DELETE /api/v1/trade/order/{orderId}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "orderId": "order_123456",
    "status": "cancelled",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

#### 查询订单
```http
GET /api/v1/trade/order/{orderId}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "orderId": "order_123456",
    "clientOrderId": "my_order_123",
    "symbol": "BTC-USDT",
    "side": "buy",
    "type": "limit",
    "amount": "0.1",
    "price": "43000.00",
    "filled": "0.05",
    "remaining": "0.05",
    "status": "partially_filled",
    "avgPrice": "42950.00",
    "fee": "2.1475",
    "feeCurrency": "USDT",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:05:00.000Z"
  }
}
```

#### 查询订单历史
```http
GET /api/v1/trade/orders
```

**查询参数**:
```
symbol: string (可选) - 交易对
status: string (可选) - 订单状态
start: string (可选) - 开始时间
end: string (可选) - 结束时间
limit: number (可选) - 数据条数，默认50，最大500
offset: number (可选) - 偏移量，默认0
```

### 账户信息

#### 获取账户余额
```http
GET /api/v1/account/balance
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "currency": "BTC",
      "available": "1.********",
      "frozen": "0.1",
      "total": "1.********"
    },
    {
      "currency": "USDT",
      "available": "10000.00",
      "frozen": "500.00",
      "total": "10500.00"
    }
  ]
}
```

#### 获取交易历史
```http
GET /api/v1/account/trades
```

**查询参数**:
```
symbol: string (可选) - 交易对
start: string (可选) - 开始时间
end: string (可选) - 结束时间
limit: number (可选) - 数据条数
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "tradeId": "trade_123456",
      "orderId": "order_123456",
      "symbol": "BTC-USDT",
      "side": "buy",
      "amount": "0.05",
      "price": "42950.00",
      "fee": "1.07375",
      "feeCurrency": "USDT",
      "timestamp": "2024-01-01T00:05:00.000Z"
    }
  ]
}
```

### 智能分析

#### 模式识别
```http
POST /api/v1/analysis/pattern-recognition
```

**请求体**:
```json
{
  "symbol": "BTC-USDT",
  "interval": "1h",
  "patterns": ["head_and_shoulders", "double_top", "triangle"]
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "symbol": "BTC-USDT",
    "patterns": [
      {
        "type": "head_and_shoulders",
        "confidence": 0.85,
        "direction": "bearish",
        "startTime": "2024-01-01T00:00:00.000Z",
        "endTime": "2024-01-01T12:00:00.000Z",
        "targetPrice": "41000.00",
        "stopLoss": "44000.00"
      }
    ]
  }
}
```

#### 市场情绪分析
```http
GET /api/v1/analysis/sentiment
```

**查询参数**:
```
symbol: string (可选) - 交易对
```

**响应**:
```json
{
  "success": true,
  "data": {
    "overall": {
      "score": 0.65,
      "label": "bullish",
      "confidence": 0.8
    },
    "indicators": {
      "fearGreedIndex": 72,
      "socialSentiment": 0.6,
      "technicalSentiment": 0.7,
      "volumeAnalysis": 0.65
    },
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

#### 智能预警
```http
POST /api/v1/analysis/alerts
```

**请求体**:
```json
{
  "symbol": "BTC-USDT",
  "type": "price",
  "condition": "greater_than",
  "value": "45000.00",
  "message": "BTC突破45000美元"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "alertId": "alert_123456",
    "symbol": "BTC-USDT",
    "type": "price",
    "condition": "greater_than",
    "value": "45000.00",
    "status": "active",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

## 🔌 WebSocket API

### 连接信息

```
开发环境: ws://localhost:8000/ws
生产环境: wss://ws.yourdomain.com/ws
```

### 连接认证

```javascript
// 连接时发送认证消息
{
  "type": "auth",
  "token": "jwt_token_string"
}
```

### 订阅频道

#### 订阅K线数据
```javascript
{
  "type": "subscribe",
  "channel": "kline",
  "params": {
    "symbol": "BTC-USDT",
    "interval": "1m"
  }
}
```

**推送数据**:
```javascript
{
  "type": "kline",
  "channel": "kline.BTC-USDT.1m",
  "data": {
    "symbol": "BTC-USDT",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "open": "43000.00",
    "high": "43100.00",
    "low": "42900.00",
    "close": "43050.00",
    "volume": "123.45"
  }
}
```

#### 订阅深度数据
```javascript
{
  "type": "subscribe",
  "channel": "depth",
  "params": {
    "symbol": "BTC-USDT",
    "depth": 20
  }
}
```

**推送数据**:
```javascript
{
  "type": "depth",
  "channel": "depth.BTC-USDT",
  "data": {
    "symbol": "BTC-USDT",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "bids": [["43000.00", "1.234"]],
    "asks": [["43100.00", "2.567"]]
  }
}
```

#### 订阅交易数据
```javascript
{
  "type": "subscribe",
  "channel": "trades",
  "params": {
    "symbol": "BTC-USDT"
  }
}
```

**推送数据**:
```javascript
{
  "type": "trades",
  "channel": "trades.BTC-USDT",
  "data": {
    "tradeId": "trade_123456",
    "symbol": "BTC-USDT",
    "price": "43050.00",
    "amount": "0.1",
    "side": "buy",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

#### 订阅订单更新
```javascript
{
  "type": "subscribe",
  "channel": "orders"
}
```

**推送数据**:
```javascript
{
  "type": "order",
  "channel": "orders",
  "data": {
    "orderId": "order_123456",
    "symbol": "BTC-USDT",
    "side": "buy",
    "status": "filled",
    "filled": "0.1",
    "avgPrice": "43025.00",
    "timestamp": "2024-01-01T00:05:00.000Z"
  }
}
```

### 取消订阅

```javascript
{
  "type": "unsubscribe",
  "channel": "kline",
  "params": {
    "symbol": "BTC-USDT",
    "interval": "1m"
  }
}
```

## 📊 数据模型

### 订单状态

```typescript
type OrderStatus = 
  | 'pending'          // 待成交
  | 'partially_filled' // 部分成交
  | 'filled'          // 完全成交
  | 'cancelled'       // 已取消
  | 'rejected'        // 已拒绝
  | 'expired'         // 已过期
```

### 订单类型

```typescript
type OrderType = 
  | 'market'    // 市价单
  | 'limit'     // 限价单
  | 'stop'      // 止损单
  | 'stop_limit' // 止损限价单
```

### 交易方向

```typescript
type OrderSide = 
  | 'buy'   // 买入
  | 'sell'  // 卖出
```

### 时间有效性

```typescript
type TimeInForce = 
  | 'GTC'  // Good Till Cancelled
  | 'IOC'  // Immediate Or Cancel
  | 'FOK'  // Fill Or Kill
  | 'GTD'  // Good Till Date
```

## ❌ 错误处理

### 标准响应格式

所有API接口都遵循统一的响应格式：

**成功响应**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    // 具体数据内容
  }
}
```

**错误响应**:
```json
{
  "code": 1,
  "msg": "错误描述信息",
  "data": null
}
```

**重要说明**:
- `code` 字段统一使用数字类型：`0` 表示成功，`1` 表示失败
- 前端应检查 `code === 0` 来判断请求是否成功
- `msg` 字段包含人类可读的消息描述
- `data` 字段包含具体的响应数据，失败时为 `null`

### 错误响应格式（旧版本兼容）

```json
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "Invalid symbol parameter",
    "details": {
      "field": "symbol",
      "value": "INVALID-PAIR",
      "expected": "Valid trading pair like BTC-USDT"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 错误代码

| 代码 | HTTP状态 | 描述 |
|------|----------|------|
| `INVALID_PARAMETER` | 400 | 参数无效 |
| `UNAUTHORIZED` | 401 | 未授权 |
| `FORBIDDEN` | 403 | 禁止访问 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |
| `SERVICE_UNAVAILABLE` | 503 | 服务不可用 |
| `INSUFFICIENT_BALANCE` | 400 | 余额不足 |
| `INVALID_ORDER` | 400 | 订单无效 |
| `ORDER_NOT_FOUND` | 404 | 订单不存在 |
| `MARKET_CLOSED` | 400 | 市场关闭 |

## 🚦 限流规则

### REST API 限流

| 接口类型 | 限制 | 时间窗口 |
|----------|------|----------|
| 公共接口 | 100次/分钟 | 1分钟 |
| 私有接口 | 60次/分钟 | 1分钟 |
| 交易接口 | 20次/分钟 | 1分钟 |
| 查询接口 | 100次/分钟 | 1分钟 |

### WebSocket 限流

| 操作类型 | 限制 | 时间窗口 |
|----------|------|----------|
| 连接数 | 5个/IP | - |
| 订阅频道 | 100个/连接 | - |
| 消息发送 | 10次/秒 | 1秒 |

### 限流响应

```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded",
    "details": {
      "limit": 60,
      "window": "1m",
      "retry_after": 30
    }
  }
}
```

## 💻 示例代码

### JavaScript/TypeScript

#### REST API 调用

```javascript
class TradingAPI {
  constructor(baseURL, token) {
    this.baseURL = baseURL
    this.token = token
  }

  async request(method, endpoint, data = null) {
    const url = `${this.baseURL}${endpoint}`
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`
      }
    }

    if (data) {
      options.body = JSON.stringify(data)
    }

    const response = await fetch(url, options)
    const result = await response.json()

    if (!result.success) {
      throw new Error(result.error.message)
    }

    return result.data
  }

  // 获取K线数据
  async getKlineData(symbol, interval, limit = 100) {
    return this.request('GET', `/api/v1/market/kline?symbol=${symbol}&interval=${interval}&limit=${limit}`)
  }

  // 下单
  async placeOrder(orderData) {
    return this.request('POST', '/api/v1/trade/order', orderData)
  }

  // 获取账户余额
  async getBalance() {
    return this.request('GET', '/api/v1/account/balance')
  }
}

// 使用示例
const api = new TradingAPI('http://localhost:8000', 'your_jwt_token')

try {
  const klineData = await api.getKlineData('BTC-USDT', '1h', 100)
  console.log('K线数据:', klineData)
} catch (error) {
  console.error('API调用失败:', error.message)
}
```

#### WebSocket 连接

```javascript
class TradingWebSocket {
  constructor(url, token) {
    this.url = url
    this.token = token
    this.ws = null
    this.subscriptions = new Map()
  }

  connect() {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(this.url)

      this.ws.onopen = () => {
        // 发送认证消息
        this.send({
          type: 'auth',
          token: this.token
        })
        resolve()
      }

      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data)
        this.handleMessage(data)
      }

      this.ws.onerror = (error) => {
        reject(error)
      }

      this.ws.onclose = () => {
        console.log('WebSocket连接已关闭')
        // 重连逻辑
        setTimeout(() => this.connect(), 5000)
      }
    })
  }

  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    }
  }

  subscribe(channel, params, callback) {
    const key = `${channel}.${JSON.stringify(params)}`
    this.subscriptions.set(key, callback)

    this.send({
      type: 'subscribe',
      channel,
      params
    })
  }

  unsubscribe(channel, params) {
    const key = `${channel}.${JSON.stringify(params)}`
    this.subscriptions.delete(key)

    this.send({
      type: 'unsubscribe',
      channel,
      params
    })
  }

  handleMessage(data) {
    const { type, channel, data: messageData } = data

    // 查找对应的回调函数
    for (const [key, callback] of this.subscriptions) {
      if (key.startsWith(type) || key.startsWith(channel)) {
        callback(messageData)
      }
    }
  }
}

// 使用示例
const ws = new TradingWebSocket('ws://localhost:8000/ws', 'your_jwt_token')

ws.connect().then(() => {
  console.log('WebSocket连接成功')

  // 订阅K线数据
  ws.subscribe('kline', { symbol: 'BTC-USDT', interval: '1m' }, (data) => {
    console.log('K线数据更新:', data)
  })

  // 订阅深度数据
  ws.subscribe('depth', { symbol: 'BTC-USDT', depth: 20 }, (data) => {
    console.log('深度数据更新:', data)
  })
})
```

### Vue 3 Composable

```javascript
// composables/useAPI.js
import { ref, reactive } from 'vue'

export function useAPI() {
  const loading = ref(false)
  const error = ref(null)
  const data = ref(null)

  const request = async (method, url, payload = null) => {
    loading.value = true
    error.value = null

    try {
      const options = {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        }
      }

      if (payload) {
        options.body = JSON.stringify(payload)
      }

      const response = await fetch(url, options)
      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error.message)
      }

      data.value = result.data
      return result.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    data,
    request
  }
}

// composables/useWebSocket.js
import { ref, onMounted, onUnmounted } from 'vue'

export function useWebSocket(url) {
  const ws = ref(null)
  const connected = ref(false)
  const subscriptions = reactive(new Map())

  const connect = () => {
    ws.value = new WebSocket(url)

    ws.value.onopen = () => {
      connected.value = true
      console.log('WebSocket连接成功')
    }

    ws.value.onmessage = (event) => {
      const data = JSON.parse(event.data)
      handleMessage(data)
    }

    ws.value.onclose = () => {
      connected.value = false
      console.log('WebSocket连接关闭')
    }
  }

  const send = (message) => {
    if (ws.value && ws.value.readyState === WebSocket.OPEN) {
      ws.value.send(JSON.stringify(message))
    }
  }

  const subscribe = (channel, params, callback) => {
    const key = `${channel}.${JSON.stringify(params)}`
    subscriptions.set(key, callback)

    send({
      type: 'subscribe',
      channel,
      params
    })
  }

  const handleMessage = (data) => {
    // 处理消息逻辑
  }

  onMounted(() => {
    connect()
  })

  onUnmounted(() => {
    if (ws.value) {
      ws.value.close()
    }
  })

  return {
    connected,
    send,
    subscribe
  }
}
```

## 📝 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持基础交易功能
- 支持市场数据获取
- 支持WebSocket实时数据

### v1.1.0 (计划中)
- 增加高级订单类型
- 支持算法交易
- 增加更多技术指标
- 优化性能和稳定性

---

**最后更新**: 2024年1月  
**版本**: 1.0.0  
**维护**: 开发团队