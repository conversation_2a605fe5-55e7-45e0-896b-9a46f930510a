/**
 * @file useVolumeIndicators.js
 * @description 成交量技术指标计算工具
 * 提供专业的成交量分析功能，包括OBV、成交量移动平均、VWAP等指标
 */

import { CHART_DATA_INDEX } from '@/constants/chartConstants'

/**
 * 成交量指标计算工具
 */
export const useVolumeIndicators = () => {
  /**
   * 计算OBV (On-Balance Volume) 能量潮指标
   * @param {Array} data - K线数据
   * @returns {Array} OBV值数组
   */
  const calculateOBV = (data) => {
    if (!data || data.length === 0) return []
    
    const result = []
    let obv = 0
    
    for (let i = 0; i < data.length; i++) {
      const volume = parseFloat(data[i][CHART_DATA_INDEX.VOLUME])
      
      if (i === 0) {
        obv = volume
      } else {
        const prevClose = parseFloat(data[i - 1][CHART_DATA_INDEX.CLOSE])
        const currentClose = parseFloat(data[i][CHART_DATA_INDEX.CLOSE])
        
        if (currentClose > prevClose) {
          obv += volume
        } else if (currentClose < prevClose) {
          obv -= volume
        }
        // 如果价格相等，OBV保持不变
      }
      
      result.push(obv)
    }
    
    return result
  }

  /**
   * 计算成交量移动平均 (Volume Moving Average)
   * @param {Array} data - K线数据
   * @param {number} period - 周期
   * @returns {Array} VMA值数组
   */
  const calculateVMA = (data, period = 20) => {
    if (!data || data.length === 0) return []
    
    const result = []
    
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.push(null)
      } else {
        let sum = 0
        for (let j = 0; j < period; j++) {
          sum += parseFloat(data[i - j][CHART_DATA_INDEX.VOLUME])
        }
        result.push(sum / period)
      }
    }
    
    return result
  }

  /**
   * 计算VWAP (Volume Weighted Average Price) 成交量加权平均价格
   * @param {Array} data - K线数据
   * @param {number} period - 周期，0表示累积VWAP
   * @returns {Array} VWAP值数组
   */
  const calculateVWAP = (data, period = 0) => {
    if (!data || data.length === 0) return []
    
    const result = []
    
    if (period === 0) {
      // 累积VWAP
      let cumulativeVolumePrice = 0
      let cumulativeVolume = 0
      
      for (let i = 0; i < data.length; i++) {
        const high = parseFloat(data[i][CHART_DATA_INDEX.HIGH])
        const low = parseFloat(data[i][CHART_DATA_INDEX.LOW])
        const close = parseFloat(data[i][CHART_DATA_INDEX.CLOSE])
        const volume = parseFloat(data[i][CHART_DATA_INDEX.VOLUME])
        
        const typicalPrice = (high + low + close) / 3
        cumulativeVolumePrice += typicalPrice * volume
        cumulativeVolume += volume
        
        result.push(cumulativeVolume > 0 ? cumulativeVolumePrice / cumulativeVolume : typicalPrice)
      }
    } else {
      // 周期VWAP
      for (let i = 0; i < data.length; i++) {
        if (i < period - 1) {
          result.push(null)
        } else {
          let volumePrice = 0
          let totalVolume = 0
          
          for (let j = 0; j < period; j++) {
            const idx = i - j
            const high = parseFloat(data[idx][CHART_DATA_INDEX.HIGH])
            const low = parseFloat(data[idx][CHART_DATA_INDEX.LOW])
            const close = parseFloat(data[idx][CHART_DATA_INDEX.CLOSE])
            const volume = parseFloat(data[idx][CHART_DATA_INDEX.VOLUME])
            
            const typicalPrice = (high + low + close) / 3
            volumePrice += typicalPrice * volume
            totalVolume += volume
          }
          
          result.push(totalVolume > 0 ? volumePrice / totalVolume : 0)
        }
      }
    }
    
    return result
  }

  /**
   * 计算成交量比率 (Volume Ratio)
   * @param {Array} data - K线数据
   * @param {number} period - 周期
   * @returns {Array} 成交量比率数组
   */
  const calculateVolumeRatio = (data, period = 20) => {
    if (!data || data.length === 0) return []
    
    const vma = calculateVMA(data, period)
    const result = []
    
    for (let i = 0; i < data.length; i++) {
      const currentVolume = parseFloat(data[i][CHART_DATA_INDEX.VOLUME])
      const avgVolume = vma[i]
      
      if (avgVolume && avgVolume > 0) {
        result.push(currentVolume / avgVolume)
      } else {
        result.push(null)
      }
    }
    
    return result
  }

  /**
   * 计算成交量震荡器 (Volume Oscillator)
   * @param {Array} data - K线数据
   * @param {number} shortPeriod - 短周期
   * @param {number} longPeriod - 长周期
   * @returns {Array} 成交量震荡器值数组
   */
  const calculateVolumeOscillator = (data, shortPeriod = 5, longPeriod = 20) => {
    if (!data || data.length === 0) return []
    
    const shortVMA = calculateVMA(data, shortPeriod)
    const longVMA = calculateVMA(data, longPeriod)
    const result = []
    
    for (let i = 0; i < data.length; i++) {
      const shortValue = shortVMA[i]
      const longValue = longVMA[i]
      
      if (shortValue !== null && longValue !== null && longValue !== 0) {
        result.push(((shortValue - longValue) / longValue) * 100)
      } else {
        result.push(null)
      }
    }
    
    return result
  }

  /**
   * 计算累积/派发线 (Accumulation/Distribution Line)
   * @param {Array} data - K线数据
   * @returns {Array} A/D线值数组
   */
  const calculateADLine = (data) => {
    if (!data || data.length === 0) return []
    
    const result = []
    let adLine = 0
    
    for (let i = 0; i < data.length; i++) {
      const high = parseFloat(data[i][CHART_DATA_INDEX.HIGH])
      const low = parseFloat(data[i][CHART_DATA_INDEX.LOW])
      const close = parseFloat(data[i][CHART_DATA_INDEX.CLOSE])
      const volume = parseFloat(data[i][CHART_DATA_INDEX.VOLUME])
      
      // 计算货币流量乘数
      const clv = high !== low ? ((close - low) - (high - close)) / (high - low) : 0
      
      // 计算货币流量
      const moneyFlowVolume = clv * volume
      
      adLine += moneyFlowVolume
      result.push(adLine)
    }
    
    return result
  }

  /**
   * 计算成交量价格趋势 (Volume Price Trend)
   * @param {Array} data - K线数据
   * @returns {Array} VPT值数组
   */
  const calculateVPT = (data) => {
    if (!data || data.length === 0) return []
    
    const result = []
    let vpt = 0
    
    for (let i = 0; i < data.length; i++) {
      const close = parseFloat(data[i][CHART_DATA_INDEX.CLOSE])
      const volume = parseFloat(data[i][CHART_DATA_INDEX.VOLUME])
      
      if (i === 0) {
        vpt = volume
      } else {
        const prevClose = parseFloat(data[i - 1][CHART_DATA_INDEX.CLOSE])
        const priceChange = (close - prevClose) / prevClose
        vpt += volume * priceChange
      }
      
      result.push(vpt)
    }
    
    return result
  }

  /**
   * 计算成交量分布
   * @param {Array} data - K线数据
   * @param {number} bins - 价格区间数量
   * @returns {Object} 成交量分布数据
   */
  const calculateVolumeProfile = (data, bins = 20) => {
    if (!data || data.length === 0) return { priceRanges: [], volumes: [], maxVolume: 0 }
    
    // 获取价格范围
    const prices = data.flatMap(item => [
      parseFloat(item[CHART_DATA_INDEX.HIGH]),
      parseFloat(item[CHART_DATA_INDEX.LOW])
    ])
    const minPrice = Math.min(...prices)
    const maxPrice = Math.max(...prices)
    const priceStep = (maxPrice - minPrice) / bins
    
    // 初始化价格区间
    const priceRanges = []
    const volumes = new Array(bins).fill(0)
    
    for (let i = 0; i < bins; i++) {
      priceRanges.push({
        min: minPrice + i * priceStep,
        max: minPrice + (i + 1) * priceStep,
        mid: minPrice + (i + 0.5) * priceStep
      })
    }
    
    // 分配成交量到价格区间
    data.forEach(item => {
      const high = parseFloat(item[CHART_DATA_INDEX.HIGH])
      const low = parseFloat(item[CHART_DATA_INDEX.LOW])
      const volume = parseFloat(item[CHART_DATA_INDEX.VOLUME])
      
      // 简化处理：将成交量平均分配到价格范围内的所有区间
      const startBin = Math.floor((low - minPrice) / priceStep)
      const endBin = Math.floor((high - minPrice) / priceStep)
      
      const affectedBins = Math.max(1, endBin - startBin + 1)
      const volumePerBin = volume / affectedBins
      
      for (let i = Math.max(0, startBin); i <= Math.min(bins - 1, endBin); i++) {
        volumes[i] += volumePerBin
      }
    })
    
    const maxVolume = Math.max(...volumes)
    
    return {
      priceRanges,
      volumes,
      maxVolume
    }
  }

  /**
   * 计算成交量统计信息
   * @param {Array} data - K线数据
   * @returns {Object} 统计信息
   */
  const calculateVolumeStats = (data) => {
    if (!data || data.length === 0) {
      return {
        total: 0,
        average: 0,
        max: 0,
        min: 0,
        median: 0,
        standardDeviation: 0,
        buyVolume: 0,
        sellVolume: 0,
        buyRatio: 50
      }
    }

    const volumes = data.map(item => parseFloat(item[CHART_DATA_INDEX.VOLUME]))
    const total = volumes.reduce((sum, vol) => sum + vol, 0)
    const average = total / volumes.length
    const max = Math.max(...volumes)
    const min = Math.min(...volumes)
    
    // 计算中位数
    const sortedVolumes = [...volumes].sort((a, b) => a - b)
    const median = sortedVolumes.length % 2 === 0
      ? (sortedVolumes[sortedVolumes.length / 2 - 1] + sortedVolumes[sortedVolumes.length / 2]) / 2
      : sortedVolumes[Math.floor(sortedVolumes.length / 2)]
    
    // 计算标准差
    const variance = volumes.reduce((sum, vol) => sum + Math.pow(vol - average, 2), 0) / volumes.length
    const standardDeviation = Math.sqrt(variance)
    
    // 计算买入卖出比例（基于价格变化）
    let buyVolume = 0
    let sellVolume = 0
    
    data.forEach(item => {
      const open = parseFloat(item[CHART_DATA_INDEX.OPEN])
      const close = parseFloat(item[CHART_DATA_INDEX.CLOSE])
      const volume = parseFloat(item[CHART_DATA_INDEX.VOLUME])
      
      if (close >= open) {
        buyVolume += volume
      } else {
        sellVolume += volume
      }
    })
    
    const buyRatio = total > 0 ? (buyVolume / total) * 100 : 50
    
    return {
      total,
      average,
      max,
      min,
      median,
      standardDeviation,
      buyVolume,
      sellVolume,
      buyRatio
    }
  }

  /**
   * 格式化成交量显示
   * @param {number} value - 成交量值
   * @param {number} precision - 精度
   * @returns {string} 格式化后的字符串
   */
  const formatVolume = (value, precision = 1) => {
    if (!value || value === 0) return '0'
    
    const absValue = Math.abs(value)
    
    if (absValue >= 1000000000) {
      return (value / 1000000000).toFixed(precision) + 'B'
    } else if (absValue >= 1000000) {
      return (value / 1000000).toFixed(precision) + 'M'
    } else if (absValue >= 1000) {
      return (value / 1000).toFixed(precision) + 'K'
    }
    return value.toFixed(0)
  }

  /**
   * 检测成交量异常
   * @param {Array} data - K线数据
   * @param {number} threshold - 异常阈值（标准差倍数）
   * @returns {Array} 异常点索引数组
   */
  const detectVolumeAnomalies = (data, threshold = 2) => {
    if (!data || data.length === 0) return []
    
    const stats = calculateVolumeStats(data)
    const anomalies = []
    
    data.forEach((item, index) => {
      const volume = parseFloat(item[CHART_DATA_INDEX.VOLUME])
      const zScore = Math.abs((volume - stats.average) / stats.standardDeviation)
      
      if (zScore > threshold) {
        anomalies.push({
          index,
          volume,
          zScore,
          timestamp: item[CHART_DATA_INDEX.DATETIME]
        })
      }
    })
    
    return anomalies
  }

  return {
    calculateOBV,
    calculateVMA,
    calculateVWAP,
    calculateVolumeRatio,
    calculateVolumeOscillator,
    calculateADLine,
    calculateVPT,
    calculateVolumeProfile,
    calculateVolumeStats,
    formatVolume,
    detectVolumeAnomalies
  }
}

/**
 * 成交量指标常量
 */
export const VOLUME_INDICATORS = {
  OBV: 'obv',
  VMA: 'vma',
  VWAP: 'vwap',
  VOLUME_RATIO: 'volume_ratio',
  VOLUME_OSCILLATOR: 'volume_oscillator',
  AD_LINE: 'ad_line',
  VPT: 'vpt'
}

/**
 * 成交量分析预设配置
 */
export const VOLUME_PRESETS = {
  // 短期分析
  SHORT_TERM: {
    vmaPeriod: 5,
    vwapPeriod: 14,
    oscillatorShort: 3,
    oscillatorLong: 10
  },
  // 中期分析
  MEDIUM_TERM: {
    vmaPeriod: 20,
    vwapPeriod: 30,
    oscillatorShort: 5,
    oscillatorLong: 20
  },
  // 长期分析
  LONG_TERM: {
    vmaPeriod: 50,
    vwapPeriod: 60,
    oscillatorShort: 10,
    oscillatorLong: 50
  }
}