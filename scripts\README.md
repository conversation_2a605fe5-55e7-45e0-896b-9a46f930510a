# 脚本工具集

本目录包含了 OKX 量化交易系统的各种实用脚本，用于自动化开发、部署和维护任务。

## 📋 脚本列表

### 🔧 setup.py - 环境设置脚本

自动化设置开发环境，包括依赖安装、配置文件创建等。

**使用方法:**
```bash
python scripts/setup.py
```

**功能:**
- 检查 Python 和 Node.js 版本
- 创建 Python 虚拟环境
- 安装后端和前端依赖
- 创建配置文件
- 创建必要的项目目录

### 🧹 cleanup.py - 项目清理脚本

清理项目中的临时文件、缓存、日志等。

**使用方法:**
```bash
# 查看帮助
python scripts/cleanup.py --help

# 清理所有文件
python scripts/cleanup.py --all

# 只清理缓存和日志
python scripts/cleanup.py --cache --logs

# 预览清理操作（不实际删除）
python scripts/cleanup.py --all --dry-run
```

**选项:**
- `--all`: 执行所有清理操作
- `--cache`: 清理缓存文件（Python __pycache__, Node.js .cache等）
- `--logs`: 清理日志文件
- `--temp`: 清理临时文件（.tmp, .bak, .old等）
- `--test`: 清理测试文件（谨慎使用）
- `--build`: 清理构建产物（dist, build等）
- `--coverage`: 清理测试覆盖率文件
- `--dry-run`: 预览模式，不实际删除文件

### 🚀 deploy.py - 部署脚本

自动化部署到不同环境（开发、测试、生产）。

**使用方法:**
```bash
# 部署到开发环境
python scripts/deploy.py dev

# 部署到测试环境
python scripts/deploy.py test

# 部署到生产环境
python scripts/deploy.py prod

# 创建 systemd 服务文件
python scripts/deploy.py prod --create-service

# 创建 Nginx 配置文件
python scripts/deploy.py prod --create-nginx
```

**选项:**
- `dev/test/prod`: 目标部署环境
- `--backup-dir`: 指定备份目录（默认: backups）
- `--skip-tests`: 跳过测试步骤
- `--create-service`: 创建 systemd 服务文件
- `--create-nginx`: 创建 Nginx 配置文件

**部署流程:**
1. 检查前提条件
2. 创建备份（生产环境）
3. 安装依赖
4. 运行测试
5. 构建前端（测试/生产环境）
6. 设置数据库

## 🛠️ 开发工作流

### 初始设置
```bash
# 1. 克隆项目后首次设置
python scripts/setup.py

# 2. 编辑配置文件
# 编辑 config.env，填入你的 API 配置
```

### 日常开发
```bash
# 启动开发环境
python scripts/deploy.py dev

# 定期清理临时文件
python scripts/cleanup.py --cache --temp
```

### 测试部署
```bash
# 部署到测试环境
python scripts/deploy.py test

# 清理构建产物
python scripts/cleanup.py --build
```

### 生产部署
```bash
# 部署到生产环境（会自动创建备份）
python scripts/deploy.py prod

# 创建系统服务配置
python scripts/deploy.py prod --create-service
python scripts/deploy.py prod --create-nginx
```

## 📁 生成的文件

脚本运行后会生成以下文件和目录：

```
scripts/
├── okx-trading.service      # systemd 服务文件
├── nginx-okx-trading.conf   # Nginx 配置文件
└── ...

backups/                     # 部署备份
├── backup_20240101_120000/
└── ...

logs/                        # 日志目录
data/                        # 数据目录
```

## ⚠️ 注意事项

### 安全性
- 确保 `config.env` 文件包含正确的 API 密钥
- 生产环境部署前请仔细检查配置
- 定期备份重要数据

### 权限
- Linux/macOS 系统可能需要执行权限：
  ```bash
  chmod +x scripts/*.py
  ```

### 依赖
- Python 3.8+
- Node.js 16+
- npm 或 yarn

## 🔧 自定义脚本

你可以根据项目需要添加自定义脚本：

1. 在 `scripts/` 目录下创建新的 Python 文件
2. 添加适当的文档字符串和帮助信息
3. 更新此 README 文件
4. 确保脚本具有适当的错误处理

### 脚本模板
```python
#!/usr/bin/env python3
"""
脚本描述
"""

import argparse
import sys

def main():
    parser = argparse.ArgumentParser(description="脚本描述")
    # 添加参数
    args = parser.parse_args()
    
    print("🚀 脚本名称")
    print("=" * 50)
    
    # 脚本逻辑
    
    print("✅ 完成")

if __name__ == "__main__":
    main()
```

## 📚 相关文档

- [部署指南](../docs/deployment/DEPLOYMENT-GUIDE.md)
- [开发文档](../docs/development/)
- [API文档](../docs/api/API-DOCUMENTATION.md)
- [项目主页](../README.md)

## 🐛 问题反馈

如果脚本运行遇到问题：

1. 检查 Python 和 Node.js 版本
2. 确保所有依赖已正确安装
3. 查看错误日志
4. 参考相关文档
5. 提交 Issue 或联系开发团队