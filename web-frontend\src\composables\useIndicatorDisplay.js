/**
 * 技术指标显示 Composable
 * 提供指标的颜色、文本和备注映射功能
 */
import { computed } from 'vue'

// 主题颜色配置
export const THEME_COLORS = {
  bullish: { light: '#52c41a', dark: '#73d13d' },
  bearish: { light: '#ff4d4f', dark: '#ff7875' },
  neutral: { light: '#faad14', dark: '#ffc53d' }
}

// 指标标签配置（支持国际化扩展）
export const INDICATOR_LABELS = {
  'zh-CN': {
    emaCross: 'EMA交叉',
    emaTrend: 'EMA慢线状态',
    emaMediumTrend: 'EMA中性线状态',
    maFastStatus: 'MA快线状态',
    maMediumStatus: 'MA中性线状态',
    maSlowStatus: 'MA慢线状态',
    rsiStatus: 'RSI状态',
    macdSignal: 'MACD信号',
    pandaSignal: '熊猫多空指标',
    pandaVolumeSignal: '熊猫能量指标',
    supertrendSignal: '超级趋势指标',
    bullBearStats: '多头空头统计',
    bullish: '多头',
    bearish: '空头',
    neutral: '中立',
    overbought: '超买',
    oversold: '超卖',
    golden_cross: '金叉',
    death_cross: '死叉',
    uptrend: '上升趋势',
    downtrend: '下降趋势',
    sideways: '震荡',
    buy_signal: '买入',
    sell_signal: '卖出',
    consider_buy: '考虑买入',
    consider_sell: '考虑卖出'
  },
  'en-US': {
    emaCross: 'EMA Cross',
    emaTrend: 'EMA Slow Trend',
    emaMediumTrend: 'EMA Medium Trend',
    maFastStatus: 'MA Fast Status',
    maMediumStatus: 'MA Medium Status',
    maSlowStatus: 'MA Slow Status',
    rsiStatus: 'RSI Status',
    macdSignal: 'MACD Signal',
    pandaSignal: 'Panda Bull/Bear',
    pandaVolumeSignal: 'Panda Volume',
    supertrendSignal: 'Supertrend',
    bullBearStats: 'Bull/Bear Stats',
    bullish: 'Bullish',
    bearish: 'Bearish',
    neutral: 'Neutral',
    overbought: 'Overbought',
    oversold: 'Oversold',
    golden_cross: 'Golden Cross',
    death_cross: 'Death Cross',
    uptrend: 'Uptrend',
    downtrend: 'Downtrend',
    sideways: 'Sideways',
    buy_signal: 'Buy',
    sell_signal: 'Sell',
    consider_buy: 'Consider Buy',
    consider_sell: 'Consider Sell'
  }
}

export function useIndicatorDisplay(locale = 'zh-CN', isDarkTheme = false) {
  const labels = computed(() => INDICATOR_LABELS[locale] || INDICATOR_LABELS['zh-CN'])
  
  /**
   * 获取指标颜色
   * @param {string} signal - 信号类型 (bullish/bearish/neutral)
   * @param {boolean} useDarkTheme - 是否使用深色主题
   * @returns {string} 颜色值
   */
  const getIndicatorColor = (signal, useDarkTheme = isDarkTheme) => {
    const colorScheme = useDarkTheme ? 'dark' : 'light'
    return THEME_COLORS[signal]?.[colorScheme] || THEME_COLORS.neutral[colorScheme]
  }

  /**
   * 获取Ant Design标签颜色
   * @param {string} signal - 信号类型
   * @returns {string} Ant Design颜色名称
   */
  const getAntdColor = (signal) => {
    switch (signal) {
      case 'bullish': return 'green'
      case 'bearish': return 'red'
      default: return 'orange'
    }
  }

  // EMA交叉相关函数
  const getEMACrossColor = (signal) => getAntdColor(signal)
  
  const getEMACrossText = (signal) => {
    switch (signal) {
      case 'bullish': return labels.value.golden_cross
      case 'bearish': return labels.value.death_cross
      default: return labels.value.neutral
    }
  }
  
  const getEMACrossRemark = (signal) => {
    switch (signal) {
      case 'bullish': return 'EMA12上穿EMA26'
      case 'bearish': return 'EMA12下穿EMA26'
      default: return '无明显交叉信号'
    }
  }

  // EMA趋势相关函数
  const getEMATrendColor = (signal) => getAntdColor(signal)
  
  const getEMATrendText = (signal) => {
    switch (signal) {
      case 'bullish': return labels.value.bullish
      case 'bearish': return labels.value.bearish
      default: return labels.value.neutral
    }
  }
  
  const getEMATrendRemark = (signal, ema12Value, ema26Value) => {
    const value12 = ema12Value ? ema12Value.toFixed(2) : '0.00'
    const value26 = ema26Value ? ema26Value.toFixed(2) : '0.00'
    
    switch (signal) {
      case 'bullish': return `${value12}/${value26}`
      case 'bearish': return `${value12}/${value26}`
      default: return `${value12}/${value26}`
    }
  }

  // MA状态相关函数
  const getMAStatusColor = (signal) => getAntdColor(signal)
  
  const getMAStatusText = (signal) => {
    switch (signal) {
      case 'bullish': return labels.value.bullish
      case 'bearish': return labels.value.bearish
      default: return labels.value.neutral
    }
  }
  
  const getMAStatusRemark = (signal, ma5Value, ma20Value, ma60Value) => {
    const value5 = ma5Value ? ma5Value.toFixed(1) : '0.0'
    const value20 = ma20Value ? ma20Value.toFixed(1) : '0.0'
    const value60 = ma60Value ? ma60Value.toFixed(1) : '0.0'
    
    return `${value5}/${value20}/${value60}`
  }

  // RSI相关函数
  const getRSIColor = (rsi) => {
    if (rsi >= 70) return 'red'
    if (rsi <= 30) return 'green'
    return 'orange'
  }
  
  const getRSIText = (rsi) => {
    if (rsi >= 70) return labels.value.overbought
    if (rsi <= 30) return labels.value.oversold
    return labels.value.neutral
  }
  
  const getRSIRemark = (rsi) => {
    const rsiValue = rsi ? rsi.toFixed(1) : '50.0'
    if (rsi >= 70) return `RSI ${rsiValue} 可能回调`
    if (rsi <= 30) return `RSI ${rsiValue} 可能反弹`
    return `RSI ${rsiValue} 震荡区间`
  }

  // MACD相关函数
  const getMACDColor = (signal) => getAntdColor(signal)
  
  const getMACDText = (signal) => {
    switch (signal) {
      case 'bullish': return labels.value.golden_cross
      case 'bearish': return labels.value.death_cross
      default: return labels.value.neutral
    }
  }
  
  const getMACDRemark = (signal) => {
    switch (signal) {
      case 'bullish': return 'MACD上穿信号线'
      case 'bearish': return 'MACD下穿信号线'
      default: return '无明显信号'
    }
  }

  // 熊猫指标相关函数
  const getPandaColor = (signal) => getAntdColor(signal)
  
  const getPandaText = (signal) => {
    switch (signal) {
      case 'bullish': return labels.value.bullish
      case 'bearish': return labels.value.bearish
      default: return labels.value.neutral
    }
  }
  
  const getPandaRemark = (signal) => {
    switch (signal) {
      case 'bullish': return labels.value.consider_buy
      case 'bearish': return labels.value.consider_sell
      default: return '价格震荡'
    }
  }

  // 超级趋势相关函数
  const getSupertrendColor = (signal) => getAntdColor(signal)
  
  const getSupertrendText = (signal) => {
    switch (signal) {
      case 'bullish': return labels.value.uptrend
      case 'bearish': return labels.value.downtrend
      default: return labels.value.sideways
    }
  }
  
  const getSupertrendRemark = (signal) => {
    switch (signal) {
      case 'bullish': return '突破上轨'
      case 'bearish': return '跌破下轨'
      default: return '区间震荡'
    }
  }

  // 多空统计相关函数
  const getBullBearRemark = (bullCount, bearCount) => {
    if (bullCount > bearCount) return '多头信号占优'
    if (bearCount > bullCount) return '空头信号占优'
    return '多空平衡'
  }

  /**
   * 格式化更新时间
   * @param {Date} time - 时间对象
   * @returns {string} 格式化后的时间字符串
   */
  const formatUpdateTime = (time) => {
    if (!time || !(time instanceof Date)) {
      return '--:--:--'
    }
    
    return time.toLocaleTimeString(locale, {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  /**
   * 格式化数值显示
   * @param {number} value - 数值
   * @param {number} decimals - 小数位数
   * @returns {string} 格式化后的数值字符串
   */
  const formatValue = (value, decimals = 2) => {
    if (typeof value !== 'number' || isNaN(value)) {
      return '0.00'
    }
    return value.toFixed(decimals)
  }

  /**
   * 获取指标状态图标
   * @param {string} signal - 信号类型
   * @returns {string} 图标类名或Unicode字符
   */
  const getIndicatorIcon = (signal) => {
    switch (signal) {
      case 'bullish': return '↗'
      case 'bearish': return '↘'
      default: return '→'
    }
  }

  /**
   * 获取指标强度等级
   * @param {string} signal - 信号类型
   * @param {number} strength - 强度值 (0-1)
   * @returns {string} 强度描述
   */
  const getSignalStrength = (signal, strength = 0.5) => {
    if (signal === 'neutral') return '中性'
    
    if (strength >= 0.8) return '强'
    if (strength >= 0.6) return '中'
    return '弱'
  }

  return {
    labels,
    getIndicatorColor,
    getAntdColor,
    
    // EMA相关
    getEMACrossColor,
    getEMACrossText,
    getEMACrossRemark,
    getEMATrendColor,
    getEMATrendText,
    getEMATrendRemark,
    
    // MA相关
    getMAStatusColor,
    getMAStatusText,
    getMAStatusRemark,
    
    // RSI相关
    getRSIColor,
    getRSIText,
    getRSIRemark,
    
    // MACD相关
    getMACDColor,
    getMACDText,
    getMACDRemark,
    
    // 熊猫指标相关
    getPandaColor,
    getPandaText,
    getPandaRemark,
    
    // 超级趋势相关
    getSupertrendColor,
    getSupertrendText,
    getSupertrendRemark,
    
    // 多空统计相关
    getBullBearRemark,
    
    // 工具函数
    formatUpdateTime,
    formatValue,
    getIndicatorIcon,
    getSignalStrength
  }
}