<template>
  <div class="simple-charts">
    <a-card title="简化版图表测试">
      <a-space style="margin-bottom: 16px;">
        <a-select v-model:value="selectedSymbol" style="width: 200px;" @change="loadData">
          <a-select-option value="BTC-USDT-SWAP">BTC-USDT-SWAP</a-select-option>
          <a-select-option value="ETH-USDT-SWAP">ETH-USDT-SWAP</a-select-option>
        </a-select>
        <a-button @click="loadData" :loading="loading">刷新数据</a-button>
      </a-space>
      
      <div v-if="loading">
        <a-spin size="large" />
        <p>正在加载数据...</p>
      </div>
      
      <div v-else-if="error">
        <a-alert :message="error" type="error" />
      </div>
      
      <div v-else>
        <div id="simple-chart" style="width: 100%; height: 400px;"></div>
        
        <a-divider />
        
        <div>
          <h3>原始数据 (前5条):</h3>
          <pre>{{ JSON.stringify(rawData.slice(0, 5), null, 2) }}</pre>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import axios from 'axios'
import * as echarts from 'echarts'

const selectedSymbol = ref('BTC-USDT-SWAP')
const loading = ref(false)
const error = ref('')
const rawData = ref([])
let chart = null

const loadData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    console.log('开始加载数据...')
    
    const response = await axios.get('/api/okx/market/candles', {
      params: {
        instId: selectedSymbol.value,
        bar: '1H',
        limit: 20
      }
    })
    
    console.log('API响应:', response.data)
    
    if (response.data.code === '0' && response.data.data) {
      rawData.value = response.data.data
      console.log('获取到数据条数:', rawData.value.length)
      
      // 初始化图表
      initChart()
    } else {
      error.value = `API返回错误: ${response.data.msg || '未知错误'}`
    }
  } catch (err) {
    console.error('加载数据失败:', err)
    error.value = `加载失败: ${err.message}`
  } finally {
    loading.value = false
  }
}

const initChart = () => {
  const container = document.getElementById('simple-chart')
  if (!container) return
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(container)
  
  // 处理数据
  const dates = []
  const candleData = []
  
  rawData.value.forEach(item => {
    const timestamp = parseInt(item[0])
    const date = new Date(timestamp).toLocaleString()
    const open = parseFloat(item[1])
    const high = parseFloat(item[2])
    const low = parseFloat(item[3])
    const close = parseFloat(item[4])
    
    dates.push(date)
    candleData.push([open, close, low, high])
  })
  
  const option = {
    title: {
      text: `${selectedSymbol.value} K线图`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const data = params[0].data
        return `
          开盘: ${data[0]}<br/>
          收盘: ${data[1]}<br/>
          最低: ${data[2]}<br/>
          最高: ${data[3]}
        `
      }
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      scale: true
    },
    series: [{
      name: 'K线',
      type: 'candlestick',
      data: candleData,
      itemStyle: {
        color: '#ec0000',
        color0: '#00da3c',
        borderColor: '#8A0000',
        borderColor0: '#008F28'
      }
    }]
  }
  
  chart.setOption(option)
  console.log('图表初始化完成')
}

onMounted(() => {
  loadData()
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
  }
})
</script>

<style scoped>
.simple-charts {
  padding: 24px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  max-height: 200px;
}
</style>
