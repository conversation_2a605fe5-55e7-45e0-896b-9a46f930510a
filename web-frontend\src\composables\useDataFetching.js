import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useCache } from './useStorage'
import { useWebSocket } from '@/services/webSocketService'

// WebSocket实例
const webSocketService = useWebSocket();

// 存储实时数据
const realtimeData = ref({
  klineData: [],
  tickerData: null,
  currentPrice: null
});

/**
 * @description 数据获取的组合式函数
 * @param {Ref<String>} selectedSymbol - 当前选择的交易对
 * @param {Ref<String>} selectedTimeframe - 当前选择的时间范围
 * @returns {Object} 包含数据获取相关状态和方法的对象
 */
export function useDataFetching(selectedSymbol, selectedTimeframe) {
  const userStore = useUserStore()
  const { setCache, getCache } = useCache()
  
  // 响应式状态
  const loading = ref(false)
  const loadingChart = ref(false)
  const error = ref(null)
  const data = ref([])
  const chartData = ref([])
  const currentPrice = ref(0)
  const instruments = ref([])
  const orderBook = ref({ bids: [], asks: [] })
  const trades = ref([])
  
  // 配置
  const config = reactive({
    symbol: 'BTC-USDT',
    interval: '1H',
    limit: 100,
    useCache: true,
    cacheTTL: 30000 // 30秒缓存
  })
  
  // WebSocket数据配置
  const WEBSOCKET_CONFIG = {
    RECONNECT_INTERVAL: 5000,
    MAX_RECONNECT_ATTEMPTS: 5,
    PING_INTERVAL: 30000
  }
  
  /**
   * @description 获取认证头
   * @returns {Object} 认证头对象
   */
  const getAuthHeaders = () => {
    const headers = {}
    if (userStore.isAuthenticated && userStore.token) {
      headers.Authorization = `Bearer ${userStore.token}`
    }
    return headers
  }
  
  /**
   * @description 通过WebSocket获取K线数据
   * @param {string} symbol - 交易对
   * @param {string} interval - 时间间隔
   * @param {number} limit - 数据条数
   * @returns {Promise<Array>} K线数据
   */
  const fetchKlineData = async (symbol = config.symbol, interval = config.interval, limit = config.limit) => {
    console.log('📡 使用WebSocket获取K线数据:', { symbol, interval, limit })
    
    loading.value = true
    error.value = null
    
    try {
      // 连接WebSocket并订阅K线数据
      webSocketService.connectMarketData(symbol, interval);
      
      // 订阅K线数据更新
      webSocketService.subscribe(`candle${interval}`, (data) => {
        if (data.instId === symbol) {
          // 处理K线数据更新
          const klineData = data.data.map(item => ({
            timestamp: parseInt(item[0]),
            open: parseFloat(item[1]),
            high: parseFloat(item[2]),
            low: parseFloat(item[3]),
            close: parseFloat(item[4]),
            volume: parseFloat(item[5])
          }));
          
          realtimeData.value.klineData = klineData;
          data.value = klineData;
        }
      });
      
      // 订阅ticker数据获取当前价格
       webSocketService.subscribe('tickers', (data) => {
         if (data.instId === symbol) {
           const tickerInfo = data.data[0];
           realtimeData.value.tickerData = tickerInfo;
           realtimeData.value.currentPrice = parseFloat(tickerInfo.last);
           currentPrice.value = parseFloat(tickerInfo.last);
         }
       });
       
       // 等待WebSocket数据，不生成模拟数据
       data.value = realtimeData.value.klineData
       
       console.log('✅ WebSocket连接成功，等待实时数据推送')
       return realtimeData.value.klineData
    } catch (err) {
      console.error('WebSocket连接失败:', err)
      error.value = err.message || 'WebSocket连接失败'
      return []
    } finally {
      loading.value = false
    }
  }
  
  /**
   * @description 通过WebSocket获取K线数据
   * @param {number} limit - 数据条数
   * @returns {Array} 实时K线数据
   */
  const fetchWebSocketKlineData = (limit = 100) => {
    // 返回WebSocket实时数据
    return realtimeData.value.klineData || []
  }

  /**
   * @description 加载市场数据（通过WebSocket获取实时数据）
   */
  const loadRealMarketData = async () => {
    console.log('📡 开始加载市场数据 (WebSocket模式):', {
      symbol: selectedSymbol.value,
      timeframe: selectedTimeframe.value
    })
    
    loadingChart.value = true;
    try {
      // 连接WebSocket并订阅数据
      webSocketService.connectMarketData(selectedSymbol.value, selectedTimeframe.value);
      
      // 使用WebSocket实时数据
      chartData.value = realtimeData.value.klineData || []
      
      console.log('✅ WebSocket连接成功，等待实时数据推送')
    } catch (error) {
      console.error('❌ WebSocket连接失败:', error);
      chartData.value = [];
    } finally {
      loadingChart.value = false;
    }
  };
  
  /**
   * @description 获取图表用的WebSocket实时数据
   * @returns {Array} 图表数据格式的实时数据
   */
  const getChartWebSocketData = () => {
    // 将WebSocket数据转换为图表格式
    return realtimeData.value.klineData.map(item => [
      item.timestamp,
      item.open,
      item.high,
      item.low,
      item.close,
      item.volume
    ]) || []
  }

  /**
   * @description 加载WebSocket实时数据
   */
  const loadWebSocketData = () => {
    loadingChart.value = true;
    try {
      // 连接WebSocket并获取实时数据
      webSocketService.connectMarketData(selectedSymbol.value, selectedTimeframe.value);
      
      // 使用WebSocket实时数据
      chartData.value = getChartWebSocketData();
      
      console.log('✅ WebSocket数据加载完成');
    } catch (error) {
      console.error('❌ WebSocket数据加载失败:', error);
      chartData.value = [];
    } finally {
      loadingChart.value = false;
    }
  };

  /**
   * @description 获取当前价格（通过WebSocket获取实时价格）
   * @param {string} symbol - 交易对
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<Object>} 当前价格数据
   */
  const loadCurrentPrice = async (symbol = config.symbol, useCache = config.useCache) => {
    try {
      // 如果已有实时价格数据，直接返回
      if (realtimeData.value.currentPrice && realtimeData.value.tickerData) {
        return {
          success: true,
          data: realtimeData.value.tickerData,
          message: '实时价格数据获取成功'
        };
      }
      
      // 连接WebSocket并订阅ticker数据
      webSocketService.connectMarketData(symbol, '1m');
      
      // 返回Promise，等待WebSocket数据
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          resolve({
            success: false,
            data: null,
            message: 'WebSocket数据获取超时'
          });
        }, 5000); // 5秒超时
        
        webSocketService.subscribe('tickers', (data) => {
          if (data.instId === symbol) {
            clearTimeout(timeout);
            const tickerInfo = data.data[0];
            realtimeData.value.tickerData = tickerInfo;
            realtimeData.value.currentPrice = parseFloat(tickerInfo.last);
            
            resolve({
              success: true,
              data: tickerInfo,
              message: '实时价格数据获取成功'
            });
          }
        });
      });
    } catch (error) {
      console.error('获取价格数据失败:', error);
      return {
        success: false,
        data: null,
        message: 'WebSocket连接失败'
      };
    }
  }
  
  /**
   * @description 获取交易对列表
   * @param {string} instType - 产品类型（SPOT, FUTURES, SWAP等）
   * @returns {Promise<Array>} 交易对列表
   */
  const fetchInstruments = async (instType = 'SPOT') => {
    const cacheKey = `instruments_${instType}`
    
    // 检查缓存（交易对信息变化较少，可以缓存较长时间）
    const cachedInstruments = getCache(cacheKey)
    if (cachedInstruments) {
      instruments.value = cachedInstruments
      return cachedInstruments
    }
    
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.get('/api/okx/public/instruments', {
        params: {
          instType: instType
        },
        headers: getAuthHeaders()
      })
      
      if (response.data && response.data.success && response.data.data) {
        const instrumentList = response.data.data.map(item => ({
          instId: item.instId,
          baseCcy: item.baseCcy,
          quoteCcy: item.quoteCcy,
          settleCcy: item.settleCcy,
          ctVal: parseFloat(item.ctVal),
          ctMult: parseFloat(item.ctMult),
          tickSz: parseFloat(item.tickSz),
          lotSz: parseFloat(item.lotSz),
          minSz: parseFloat(item.minSz),
          state: item.state
        }))
        
        instruments.value = instrumentList
        
        // 缓存交易对信息（1小时）
        setCache(cacheKey, instrumentList, 3600000)
        
        return instrumentList
      } else {
        throw new Error(response.data?.message || '获取交易对失败')
      }
    } catch (err) {
      console.error('获取交易对失败:', err)
      error.value = err.response?.data?.message || err.message || '获取交易对失败'
      return []
    } finally {
      loading.value = false
    }
  }
  
  /**
   * @description 获取订单簿数据
   * @param {string} symbol - 交易对
   * @param {number} sz - 深度档位数量
   * @returns {Promise<Object>} 订单簿数据
   */
  const fetchOrderBook = async (symbol = config.symbol, sz = 20) => {
    const cacheKey = `orderbook_${symbol}_${sz}`
    
    // 检查缓存（订单簿数据变化快，缓存时间短）
    const cachedOrderBook = getCache(cacheKey)
    if (cachedOrderBook) {
      orderBook.value = cachedOrderBook
      return cachedOrderBook
    }
    
    try {
      const response = await axios.get('/api/okx/market/books', {
        params: {
          instId: symbol,
          sz: sz
        },
        headers: getAuthHeaders()
      })
      
      if (response.data && response.data.success && response.data.data && response.data.data.length > 0) {
        const bookData = response.data.data[0]
        const orderBookData = {
          bids: bookData.bids.map(bid => ({
            price: parseFloat(bid[0]),
            size: parseFloat(bid[1]),
            liquidatedOrders: parseInt(bid[2]),
            orderCount: parseInt(bid[3])
          })),
          asks: bookData.asks.map(ask => ({
            price: parseFloat(ask[0]),
            size: parseFloat(ask[1]),
            liquidatedOrders: parseInt(ask[2]),
            orderCount: parseInt(ask[3])
          })),
          timestamp: parseInt(bookData.ts)
        }
        
        orderBook.value = orderBookData
        
        // 缓存订单簿数据（1秒）
        setCache(cacheKey, orderBookData, 1000)
        
        return orderBookData
      } else {
        throw new Error(response.data?.message || '获取订单簿失败')
      }
    } catch (err) {
      console.error('获取订单簿失败:', err)
      error.value = err.response?.data?.message || err.message || '获取订单簿失败'
      return { bids: [], asks: [], timestamp: Date.now() }
    }
  }
  
  /**
   * @description 获取最近成交记录
   * @param {string} symbol - 交易对
   * @param {number} limit - 记录数量
   * @returns {Promise<Array>} 成交记录
   */
  const fetchTrades = async (symbol = config.symbol, limit = 100) => {
    const cacheKey = `trades_${symbol}_${limit}`
    
    try {
      const response = await axios.get('/api/okx/market/trades', {
        params: {
          instId: symbol,
          limit: limit
        },
        headers: getAuthHeaders()
      })
      
      if (response.data && response.data.success && response.data.data) {
        const tradesData = response.data.data.map(trade => ({
          tradeId: trade.tradeId,
          price: parseFloat(trade.px),
          size: parseFloat(trade.sz),
          side: trade.side,
          timestamp: parseInt(trade.ts)
        }))
        
        trades.value = tradesData
        
        // 缓存成交记录（5秒）
        setCache(cacheKey, tradesData, 5000)
        
        return tradesData
      } else {
        throw new Error(response.data?.message || '获取成交记录失败')
      }
    } catch (err) {
      console.error('获取成交记录失败:', err)
      error.value = err.response?.data?.message || err.message || '获取成交记录失败'
      return []
    }
  }
  
  /**
   * @description 获取账户余额
   * @param {string} ccy - 币种（可选）
   * @returns {Promise<Array>} 账户余额
   */
  const fetchAccountBalance = async (ccy = '') => {
    if (!userStore.isAuthenticated) {
      throw new Error('用户未登录')
    }
    
    try {
      const response = await axios.get('/api/okx/account/balance', {
        params: ccy ? { ccy } : {},
        headers: getAuthHeaders()
      })
      
      if (response.data && response.data.success && response.data.data) {
        const balanceData = response.data.data[0]?.details || []
        return balanceData.map(item => ({
          currency: item.ccy,
          balance: parseFloat(item.bal),
          available: parseFloat(item.availBal),
          frozen: parseFloat(item.frozenBal),
          equity: parseFloat(item.eq),
          usdValue: parseFloat(item.eqUsd)
        }))
      } else {
        throw new Error(response.data?.message || '获取账户余额失败')
      }
    } catch (err) {
      console.error('获取账户余额失败:', err)
      error.value = err.response?.data?.message || err.message || '获取账户余额失败'
      return []
    }
  }
  
  /**
   * @description 获取持仓信息
   * @param {string} instType - 产品类型
   * @param {string} instId - 产品ID
   * @returns {Promise<Array>} 持仓信息
   */
  const fetchPositions = async (instType = '', instId = '') => {
    if (!userStore.isAuthenticated) {
      throw new Error('用户未登录')
    }
    
    try {
      const params = {}
      if (instType) params.instType = instType
      if (instId) params.instId = instId
      
      const response = await axios.get('/api/okx/account/positions', {
        params,
        headers: getAuthHeaders()
      })
      
      if (response.data && response.data.success && response.data.data) {
        return response.data.data.map(pos => ({
          instId: pos.instId,
          instType: pos.instType,
          posSide: pos.posSide,
          pos: parseFloat(pos.pos),
          availPos: parseFloat(pos.availPos),
          avgPx: parseFloat(pos.avgPx),
          markPx: parseFloat(pos.markPx),
          upl: parseFloat(pos.upl),
          uplRatio: parseFloat(pos.uplRatio),
          notionalUsd: parseFloat(pos.notionalUsd),
          lever: parseFloat(pos.lever),
          margin: parseFloat(pos.margin),
          mgnRatio: parseFloat(pos.mgnRatio)
        }))
      } else {
        throw new Error(response.data?.message || '获取持仓信息失败')
      }
    } catch (err) {
      console.error('获取持仓信息失败:', err)
      error.value = err.response?.data?.message || err.message || '获取持仓信息失败'
      return []
    }
  };



  /**
   * @description 下单
   * @param {Object} orderData - 订单数据
   * @returns {Promise<Object>} 订单结果
   */
  const placeOrder = async (orderData) => {
    if (!userStore.isAuthenticated) {
      throw new Error('用户未登录')
    }
    
    try {
      const response = await axios.post('/api/okx/trade/order', orderData, {
        headers: getAuthHeaders()
      })
      
      if (response.data && response.data.success) {
        return response.data.data[0]
      } else {
        throw new Error(response.data?.message || '下单失败')
      }
    } catch (err) {
      console.error('下单失败:', err)
      error.value = err.response?.data?.message || err.message || '下单失败'
      throw err
    }
  }
  
  /**
   * @description 撤销订单
   * @param {string} instId - 产品ID
   * @param {string} ordId - 订单ID
   * @returns {Promise<Object>} 撤销结果
   */
  const cancelOrder = async (instId, ordId) => {
    if (!userStore.isAuthenticated) {
      throw new Error('用户未登录')
    }
    
    try {
      const response = await axios.post('/api/okx/trade/cancel-order', {
        instId,
        ordId
      }, {
        headers: getAuthHeaders()
      })
      
      if (response.data && response.data.success) {
        return response.data.data[0]
      } else {
        throw new Error(response.data?.message || '撤销订单失败')
      }
    } catch (err) {
      console.error('撤销订单失败:', err)
      error.value = err.response?.data?.message || err.message || '撤销订单失败'
      throw err
    }
  }
  
  /**
   * @description 获取订单列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Array>} 订单列表
   */
  const fetchOrders = async (params = {}) => {
    if (!userStore.isAuthenticated) {
      throw new Error('用户未登录')
    }
    
    try {
      const response = await axios.get('/api/okx/trade/orders', {
        params,
        headers: getAuthHeaders()
      })
      
      if (response.data && response.data.success && response.data.data) {
        return response.data.data.map(order => ({
          ordId: order.ordId,
          clOrdId: order.clOrdId,
          instId: order.instId,
          instType: order.instType,
          side: order.side,
          ordType: order.ordType,
          sz: parseFloat(order.sz),
          px: parseFloat(order.px),
          state: order.state,
          accFillSz: parseFloat(order.accFillSz),
          fillPx: parseFloat(order.fillPx),
          fillSz: parseFloat(order.fillSz),
          fillTime: parseInt(order.fillTime),
          cTime: parseInt(order.cTime),
          uTime: parseInt(order.uTime)
        }))
      } else {
        throw new Error(response.data?.message || '获取订单列表失败')
      }
    } catch (err) {
      console.error('获取订单列表失败:', err)
      error.value = err.response?.data?.message || err.message || '获取订单列表失败'
      return []
    }
  }
  
  /**
   * @description 获取成交记录
   * @param {Object} params - 查询参数
   * @returns {Promise<Array>} 成交记录
   */
  const fetchFills = async (params = {}) => {
    if (!userStore.isAuthenticated) {
      throw new Error('用户未登录')
    }
    
    try {
      const response = await axios.get('/api/okx/trade/fills', {
        params,
        headers: getAuthHeaders()
      })
      
      if (response.data && response.data.success && response.data.data) {
        return response.data.data.map(fill => ({
          tradeId: fill.tradeId,
          ordId: fill.ordId,
          clOrdId: fill.clOrdId,
          instId: fill.instId,
          instType: fill.instType,
          side: fill.side,
          fillSz: parseFloat(fill.fillSz),
          fillPx: parseFloat(fill.fillPx),
          fee: parseFloat(fill.fee),
          feeCcy: fill.feeCcy,
          ts: parseInt(fill.ts)
        }))
      } else {
        throw new Error(response.data?.message || '获取成交记录失败')
      }
    } catch (err) {
      console.error('获取成交记录失败:', err)
      error.value = err.response?.data?.message || err.message || '获取成交记录失败'
      return []
    }
  }
  
  /**
   * @description 批量获取多个交易对的价格
   * @param {Array} symbols - 交易对数组
   * @returns {Promise<Object>} 价格对象
   */
  const fetchMultiplePrices = async (symbols) => {
    try {
      const promises = symbols.map(symbol => loadCurrentPrice(symbol, true))
      const results = await Promise.all(promises)
      
      const priceMap = {}
      symbols.forEach((symbol, index) => {
        priceMap[symbol] = results[index]
      })
      
      return priceMap
    } catch (err) {
      console.error('批量获取价格失败:', err)
      error.value = err.message || '批量获取价格失败'
      return {}
    }
  }
  
  /**
   * @description 清除所有缓存
   */
  const clearAllCache = () => {
    const { clearExpiredCache } = useCache()
    clearExpiredCache()
  }
  
  return {
    // 状态
    loading,
    loadingChart,
    error,
    data,
    chartData,
    currentPrice,
    instruments,
    orderBook,
    trades,
    config,
    
    // 市场数据方法
    fetchKlineData,
    loadRealMarketData,
    loadWebSocketData,
    getChartWebSocketData,
    loadCurrentPrice,
    fetchInstruments,
    fetchOrderBook,
    fetchTrades,
    fetchMultiplePrices,
    
    // 账户相关方法
    fetchAccountBalance,
    fetchPositions,
    
    // 交易相关方法
    placeOrder,
    cancelOrder,
    fetchOrders,
    fetchFills,
    
    // 工具方法
    clearAllCache,
    getAuthHeaders
  }
}