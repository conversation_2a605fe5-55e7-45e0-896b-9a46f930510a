from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from sqlalchemy.orm import Session
from webapp.db import get_db
from webapp.api.user_api import get_current_user
from models import Strategy

router = APIRouter()


class StrategyCreate(BaseModel):
    user_id: int
    name: str
    params: dict


@router.post("/create")
def create_strategy(strategy: StrategyCreate, db: Session = Depends(get_db)):
    db_strategy = Strategy(
        user_id=strategy.user_id,
        name=strategy.name,
        params=strategy.params,
        status="stopped"
    )
    db.add(db_strategy)
    db.commit()
    db.refresh(db_strategy)
    return {"code": 0, "msg": "策略创建成功", "strategy_id": db_strategy.id}


@router.get("/list")
def list_strategies(user_id: int = 1, db: Session = Depends(get_db)):
    strategies = db.query(Strategy).filter(
        Strategy.user_id == user_id).all()
    return {"code": 0, "msg": "success", "data": [{"id": s.id, "name": s.name, "status": s.status} for s in strategies]}
