import { ref, reactive, toRefs, watch, nextTick, onMounted, onUnmounted } from 'vue';
import axios from 'axios';
import { useIndicators } from './useIndicators';
import { useCharts } from './useCharts';
import { useDataFetching } from './useDataFetching'; // 新增
import { formatChartTime } from '@/utils/chartUtils';
import { CHART_DATA_INDEX } from '@/constants';

/**
 * @description 协调图表、指标和数据的可组合函数
 * @param {Ref<String>} selectedSymbol - 当前选择的交易对
 * @param {Ref<String>} selectedTimeframe - 当前选择的时间范围
 * @param {Object} refs - 对图表组件的引用
 * @param {Object} indicatorSettings - 指标设置
 * @param {Object} displaySettings - 显示设置
 * @returns {Object} 包含图表状态和方法的对象
 */
export function useChartCoordinator({ selectedSymbol, selectedTimeframe, refs, indicatorSettings, displaySettings }) {
  const { ma, rsi, macd, bollingerBands, kdj, supertrend, tradingSignals: tradingSignalsSettings } = toRefs(indicatorSettings);
  const { calculateAllIndicators } = useIndicators();
  const { loadingChart, chartData, currentPrice, loadRealMarketData, loadWebSocketData, loadCurrentPrice } = useDataFetching(selectedSymbol, selectedTimeframe);

  const technicalIndicators = ref({});
  const marketSentiment = ref({ score: 50, label: '中性' });
  const tradingSignals = ref([]);
  const dataSource = ref('real'); // 'real' or 'mock'

  const { 
    chartInstances,
    initAllCharts,
    updateAllCharts,
    resizeAllCharts,
    disposeAllCharts
  } = useCharts(refs, displaySettings, technicalIndicators, chartData);
  
  console.log('Chart refs passed to useCharts:', refs);

  /**
   * 加载图表数据
   * 通过WebSocket获取实时市场数据
   */
  const loadChartData = async () => {
    // 统一使用WebSocket获取实时数据
    await loadRealMarketData();
    // 数据加载完成后处理数据并更新图表
    processLoadedData();
  };

  /**
   * 加载真实市场K线数据
   */


  /**
   * 生成模拟K线数据
   */


  /**
   * 处理加载后的数据，计算指标并更新图表
   */
  const processLoadedData = () => {
    console.log('processLoadedData called, chartData length:', chartData.value.length);
    if (chartData.value.length > 0) {
      console.log('Processing chart data and calculating indicators...');
      technicalIndicators.value = calculateAllIndicators(chartData.value, {
        ma: ma.value,
        rsi: rsi.value,
        macd: macd.value,
        bollingerBands: bollingerBands.value,
        kdj: kdj.value,
        supertrend: supertrend.value,
      });
      marketSentiment.value = calculateMarketSentiment(technicalIndicators.value, chartData.value);
      tradingSignals.value = generateTradingSignals(technicalIndicators.value, tradingSignalsSettings.value);
      console.log('Indicators calculated, updating charts...');
      nextTick(() => {
        console.log('Chart instances count:', Object.keys(chartInstances.value).length);
        if (Object.keys(chartInstances.value).length === 0) {
          console.log('Initializing charts...');
          initAllCharts();
        }
        console.log('Updating all charts...');
        updateAllCharts();
      });
    } else {
      console.warn('No chart data available for processing');
    }
  };

  /**
   * 计算市场情绪
   * @param {Object} indicators - 计算出的技术指标
   * @param {Array} data - K线数据
   * @returns {Object} - 市场情绪分数和标签
   */
  const calculateMarketSentiment = (indicators, data) => {
    let score = 50; // Base score
    const lastClose = data[data.length - 1][CHART_DATA_INDEX.CLOSE];

    // RSI based sentiment
    if (indicators.rsi && indicators.rsi.length > 0) {
      const lastRsi = indicators.rsi[indicators.rsi.length - 1];
      if (lastRsi > 70) score += 15; // Overbought, potential reversal but currently bullish
      if (lastRsi < 30) score -= 15; // Oversold, potential reversal but currently bearish
    }

    // Price vs MA20
    if (indicators.ma && indicators.ma.ma20 && indicators.ma.ma20.length > 0) {
      const lastMa20 = indicators.ma.ma20[indicators.ma.ma20.length - 1];
      if (lastMa20 && lastClose > lastMa20) score += 10;
      else if (lastMa20) score -= 10;
    }

    // MACD based sentiment
    if (indicators.macd && indicators.macd.macd && indicators.macd.macd.length > 0) {
      const lastMacd = indicators.macd.macd[indicators.macd.macd.length - 1];
      if (lastMacd > 0) score += 10; // Bullish momentum
      else if (lastMacd < 0) score -= 10; // Bearish momentum
    }

    score = Math.max(0, Math.min(100, score));

    let label = '中性';
    if (score > 75) label = '极度看涨';
    else if (score > 60) label = '看涨';
    else if (score < 25) label = '极度看跌';
    else if (score < 40) label = '看跌';

    return { score, label };
  };

  /**
   * 生成交易信号
   * @param {Object} indicators - 计算出的技术指标
   * @param {Object} settings - 交易信号设置
   * @returns {Array} - 交易信号列表
   */
  const generateTradingSignals = (indicators, settings) => {
    const signals = [];
    const dataLength = chartData.value.length;
    if (dataLength < 2) return [];

    for (let i = 1; i < dataLength; i++) {
      const time = formatChartTime(chartData.value[i][CHART_DATA_INDEX.DATETIME]);
      let signal = null;
      let reason = '';

      // RSI Signal
      if (settings.useRSI) {
        const rsi = indicators.rsi?.[i];
        const prevRsi = indicators.rsi?.[i - 1];
        if (rsi && prevRsi && prevRsi < settings.rsi.oversold && rsi >= settings.rsi.oversold) {
          signal = '买入';
          reason = `RSI上穿超卖线(${settings.rsi.oversold})`;
        }
        if (rsi && prevRsi && prevRsi > settings.rsi.overbought && rsi <= settings.rsi.overbought) {
          signal = '卖出';
          reason = `RSI下穿超买线(${settings.rsi.overbought})`;
        }
      }
      
      // MACD Signal
      if (settings.useMACD) {
        const macd = indicators.macd?.macd?.[i];
        const prevMacd = indicators.macd?.macd?.[i - 1];
        if (macd && prevMacd && prevMacd <= 0 && macd > 0) {
          signal = '买入';
          reason = 'MACD金叉';
        }
        if (macd && prevMacd && prevMacd >= 0 && macd < 0) {
          signal = '卖出';
          reason = 'MACD死叉';
        }
      }

      // Supertrend Signal
      if (settings.useSupertrend) {
        const supertrendDir = indicators.supertrend?.direction?.[i];
        const prevSupertrendDir = indicators.supertrend?.direction?.[i - 1];
        if (supertrendDir && prevSupertrendDir && prevSupertrendDir === -1 && supertrendDir === 1) {
          signal = '买入';
          reason = 'Supertrend趋势反转向上';
        }
        if (supertrendDir && prevSupertrendDir && prevSupertrendDir === 1 && supertrendDir === -1) {
          signal = '卖出';
          reason = 'Supertrend趋势反转向下';
        }
      }

      if (signal) {
        signals.push({ time, signal, price: chartData.value[i][CHART_DATA_INDEX.CLOSE], reason });
      }
    }
    return signals.slice(-10); // 只显示最近10条信号
  };

  /**
   * 初始化协调器
   */
  const initializeCoordinator = () => {
    loadChartData();
    loadCurrentPrice();
  };

  onMounted(() => {
    window.addEventListener('resize', resizeAllCharts);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', resizeAllCharts);
    disposeAllCharts();
  });

  watch([selectedSymbol, selectedTimeframe, dataSource], loadChartData);
  watch(chartData, processLoadedData, { deep: true });
  watch(indicatorSettings, processLoadedData, { deep: true });
  watch(displaySettings, () => nextTick(updateAllCharts), { deep: true });

  return {
    loadingChart,
    chartData,
    currentPrice,
    technicalIndicators,
    marketSentiment,
    tradingSignals,
    dataSource,
    loadChartData,
    processLoadedData, // Add this line
    initializeCoordinator,
    initCharts: initAllCharts, // Expose init for manual trigger if needed
    update: updateAllCharts,
    resize: resizeAllCharts,
    dispose: disposeAllCharts,
  };
}