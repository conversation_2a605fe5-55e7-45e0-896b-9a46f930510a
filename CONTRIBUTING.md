# 贡献指南

感谢您对 OKX 量化交易系统的关注！我们欢迎各种形式的贡献。

## 🚀 快速开始

### 开发环境设置

1. **克隆项目**
```bash
git clone <repository-url>
cd python-okx
```

2. **后端环境**
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp config.env.example config.env
# 编辑 config.env 填入你的配置
```

3. **前端环境**
```bash
cd web-frontend
npm install
```

4. **启动开发服务器**
```bash
# 后端
python run_backend.py

# 前端
cd web-frontend
npm run dev
# 或使用批处理文件
start-vite.bat
```

## 📋 贡献类型

### 🐛 Bug 报告
- 使用 GitHub Issues 报告 bug
- 提供详细的重现步骤
- 包含错误信息和环境信息
- 如果可能，提供最小重现示例

### ✨ 功能请求
- 在 Issues 中描述新功能
- 解释功能的用途和价值
- 提供使用场景和示例

### 🔧 代码贡献
- Fork 项目并创建功能分支
- 遵循代码规范
- 添加适当的测试
- 更新相关文档

## 📝 代码规范

### Python (后端)
- 遵循 PEP 8 规范
- 使用类型提示
- 添加文档字符串
- 函数和类名使用描述性命名

```python
def calculate_moving_average(prices: List[float], period: int) -> float:
    """
    计算移动平均值
    
    Args:
        prices: 价格列表
        period: 计算周期
        
    Returns:
        移动平均值
    """
    return sum(prices[-period:]) / period
```

### JavaScript/Vue (前端)
- 使用 ES6+ 语法
- 遵循 Vue 3 组合式 API 规范
- 使用 TypeScript 类型定义
- 组件名使用 PascalCase

```javascript
// 组件示例
<script setup lang="ts">
interface Props {
  data: ChartData[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})
</script>
```

### CSS/样式
- 使用 CSS 模块或 scoped 样式
- 遵循 BEM 命名规范
- 优先使用 Flexbox 和 Grid 布局
- 响应式设计优先

## 🧪 测试

### 后端测试
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/backend/test_api.py

# 生成覆盖率报告
pytest --cov=src
```

### 前端测试
```bash
cd web-frontend

# 运行单元测试
npm run test

# 运行 E2E 测试
npm run test:e2e
```

### 测试要求
- 新功能必须包含测试
- 测试覆盖率应保持在 80% 以上
- 测试应该清晰、独立且可重复

## 📚 文档

### 文档结构
```
docs/
├── README.md              # 文档导航
├── api/                   # API 文档
├── deployment/            # 部署指南
├── development/           # 开发文档
└── user-guide/           # 用户指南
```

### 文档要求
- 使用 Markdown 格式
- 包含代码示例
- 保持文档与代码同步
- 提供清晰的导航链接

## 🔄 提交流程

### 1. 创建分支
```bash
git checkout -b feature/your-feature-name
# 或
git checkout -b fix/your-bug-fix
```

### 2. 提交规范
使用语义化提交信息：

```
type(scope): description

[optional body]

[optional footer]
```

**类型 (type):**
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例:**
```
feat(chart): 添加实时K线图表功能

- 实现WebSocket数据连接
- 添加图表配置选项
- 优化渲染性能

Closes #123
```

### 3. 推送和 PR
```bash
git push origin feature/your-feature-name
```

然后在 GitHub 上创建 Pull Request。

## 📋 PR 检查清单

提交 PR 前请确认：

- [ ] 代码遵循项目规范
- [ ] 添加了适当的测试
- [ ] 测试全部通过
- [ ] 更新了相关文档
- [ ] 提交信息清晰描述性
- [ ] 没有合并冲突
- [ ] 功能在本地环境正常工作

## 🎯 开发重点

当前项目重点关注：

1. **图表系统优化**
   - 性能提升
   - 更多技术指标
   - 自定义绘图工具

2. **AI 分析功能**
   - 模式识别
   - 智能预警
   - 市场情绪分析

3. **交易功能完善**
   - 订单管理
   - 风险控制
   - 策略回测

4. **用户体验**
   - 界面优化
   - 响应式设计
   - 性能优化

## 🤝 社区

- 遇到问题？查看 [Issues](../../issues)
- 有想法？参与 [Discussions](../../discussions)
- 需要帮助？查看 [文档](docs/README.md)

## 📄 许可证

通过贡献代码，您同意您的贡献将在与项目相同的许可证下授权。

---

再次感谢您的贡献！🎉