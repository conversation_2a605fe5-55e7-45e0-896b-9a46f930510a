import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import ChartTradingInterface from '@/components/ChartTradingInterface.vue'
import { useChartStore } from '@/stores/chartStore'

// Mock ECharts
vi.mock('echarts', () => ({
  init: vi.fn(() => ({
    setOption: vi.fn(),
    resize: vi.fn(),
    dispose: vi.fn(),
    on: vi.fn(),
    getDataURL: vi.fn(() => 'data:image/png;base64,mock-image-data'),
    clear: vi.fn()
  })),
  dispose: vi.fn()
}))

// Mock composables
vi.mock('@/composables/useDrawingTools', () => ({
  useDrawingTools: () => ({
    drawingTool: ref('none'),
    drawingSettings: ref({}),
    setDrawingTool: vi.fn(),
    clearAllDrawings: vi.fn(),
    saveChart: vi.fn(),
    updateDrawingSettings: vi.fn()
  })
}))

vi.mock('@/composables/usePatternRecognition', () => ({
  usePatternRecognition: () => ({
    analyzePatterns: vi.fn().mockResolvedValue([]),
    detectedPatterns: ref([]),
    isAnalyzing: ref(false)
  })
}))

vi.mock('@/composables/useMarketSentiment', () => ({
  useMarketSentiment: () => ({
    sentimentData: ref(null),
    isAnalyzing: ref(false),
    analyzeSentiment: vi.fn().mockResolvedValue({})
  })
}))

vi.mock('@/composables/useSmartAlerts', () => ({
  useSmartAlerts: () => ({
    activeAlerts: ref([]),
    isMonitoring: ref(false),
    alertSettings: ref({}),
    startMonitoring: vi.fn(),
    stopMonitoring: vi.fn(),
    analyzeAndGenerateAlerts: vi.fn().mockReturnValue([]),
    acknowledgeAlert: vi.fn(),
    dismissAlert: vi.fn(),
    updateAlertSettings: vi.fn()
  })
}))

vi.mock('@/composables/useAdvancedCharts', () => ({
  useAdvancedCharts: () => ({
    chartInstances: ref({}),
    initializeChart: vi.fn(),
    updateChart: vi.fn()
  })
}))

// Mock Ant Design Vue components
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  },
  Modal: {
    confirm: vi.fn()
  }
}))

describe('ChartTradingInterface', () => {
  let wrapper
  let pinia
  let chartStore

  const mockChartData = [
    {
      timestamp: '2024-01-01 00:00:00',
      open: 100,
      high: 105,
      low: 95,
      close: 102,
      volume: 1000
    },
    {
      timestamp: '2024-01-01 01:00:00',
      open: 102,
      high: 108,
      low: 100,
      close: 106,
      volume: 1200
    }
  ]

  const mockTechnicalIndicators = {
    rsi: [45, 52],
    macd: [
      { macd: 0.5, signal: 0.3, histogram: 0.2 },
      { macd: 0.8, signal: 0.6, histogram: 0.2 }
    ],
    volume: [1000, 1200]
  }

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    chartStore = useChartStore()
    
    // 设置模拟数据
    chartStore.chartData = mockChartData
    chartStore.technicalIndicators = mockTechnicalIndicators
    chartStore.theme = 'light'

    wrapper = mount(ChartTradingInterface, {
      global: {
        plugins: [pinia],
        stubs: {
          ChartComponent: {
            template: '<div class="mock-chart"></div>',
            methods: {
              resize: vi.fn(),
              getDataURL: vi.fn(() => 'mock-data-url')
            }
          },
          DrawingSettingsModal: true,
          AlertSettingsModal: true,
          TradeModal: true,
          EmptyIcon: true
        }
      },
      props: {
        symbol: 'BTC-USDT',
        interval: '1h'
      }
    })
  })

  afterEach(() => {
    wrapper.unmount()
  })

  describe('组件渲染', () => {
    it('应该正确渲染主要组件结构', () => {
      expect(wrapper.find('.chart-trading-interface').exists()).toBe(true)
      expect(wrapper.find('.chart-main-area').exists()).toBe(true)
      expect(wrapper.find('.side-panel').exists()).toBe(true)
      expect(wrapper.find('.chart-toolbar').exists()).toBe(true)
    })

    it('应该渲染绘图工具栏', () => {
      const toolbar = wrapper.find('.chart-toolbar')
      expect(toolbar.exists()).toBe(true)
      
      // 检查绘图工具按钮
      const drawingButtons = toolbar.findAll('.ant-btn')
      expect(drawingButtons.length).toBeGreaterThan(0)
    })

    it('应该渲染侧边面板的所有部分', () => {
      const sidePanel = wrapper.find('.side-panel')
      expect(sidePanel.find('.sentiment-panel').exists()).toBe(true)
      expect(sidePanel.find('.alerts-panel').exists()).toBe(true)
      expect(sidePanel.find('.patterns-panel').exists()).toBe(true)
      expect(sidePanel.find('.quick-trade-panel').exists()).toBe(true)
    })
  })

  describe('绘图工具功能', () => {
    it('应该能够选择不同的绘图工具', async () => {
      const trendlineBtn = wrapper.find('[data-testid="trendline-tool"]')
      if (trendlineBtn.exists()) {
        await trendlineBtn.trigger('click')
        // 验证绘图工具状态变化
      }
    })

    it('应该能够清除所有绘图', async () => {
      const clearBtn = wrapper.find('[data-testid="clear-drawings"]')
      if (clearBtn.exists()) {
        await clearBtn.trigger('click')
        // 验证清除功能
      }
    })

    it('应该能够保存图表', async () => {
      const saveBtn = wrapper.find('[data-testid="save-chart"]')
      if (saveBtn.exists()) {
        await saveBtn.trigger('click')
        // 验证保存功能
      }
    })
  })

  describe('市场情绪分析', () => {
    it('应该显示市场情绪数据', async () => {
      // 设置模拟情绪数据
      await wrapper.setData({
        sentimentData: {
          fearGreedIndex: {
            index: 65,
            level: {
              label: '贪婪',
              color: '#52c41a',
              icon: '😊'
            }
          },
          indicators: {
            rsi: { value: 0.65, signal: 'bullish' },
            macd: { value: 0.8, signal: 'bullish' }
          },
          marketPhase: {
            label: '上升趋势',
            description: '市场处于上升趋势中'
          }
        }
      })

      await wrapper.vm.$nextTick()

      const sentimentPanel = wrapper.find('.sentiment-panel')
      expect(sentimentPanel.exists()).toBe(true)
      
      // 检查恐慌贪婪指数
      const fearGreedIndex = sentimentPanel.find('.fear-greed-index')
      expect(fearGreedIndex.exists()).toBe(true)
    })

    it('应该能够刷新情绪数据', async () => {
      const refreshBtn = wrapper.find('.sentiment-panel .ant-btn')
      if (refreshBtn.exists()) {
        await refreshBtn.trigger('click')
        // 验证刷新功能
      }
    })
  })

  describe('智能预警功能', () => {
    it('应该显示预警列表', async () => {
      // 设置模拟预警数据
      await wrapper.setData({
        activeAlerts: [
          {
            id: '1',
            type: { name: '价格突破', icon: '📈' },
            message: 'BTC价格突破阻力位',
            timestamp: Date.now(),
            acknowledged: false
          }
        ]
      })

      await wrapper.vm.$nextTick()

      const alertsPanel = wrapper.find('.alerts-panel')
      expect(alertsPanel.exists()).toBe(true)
      
      const alertItems = alertsPanel.findAll('.alert-item')
      expect(alertItems.length).toBe(1)
    })

    it('应该能够启动/停止监控', async () => {
      const monitorBtn = wrapper.find('.alerts-panel .alert-controls .ant-btn')
      if (monitorBtn.exists()) {
        await monitorBtn.trigger('click')
        // 验证监控状态变化
      }
    })

    it('应该能够确认预警', async () => {
      // 设置有预警的状态
      await wrapper.setData({
        recentAlerts: [
          {
            id: '1',
            type: { name: '价格突破', icon: '📈' },
            message: 'BTC价格突破阻力位',
            timestamp: Date.now(),
            acknowledged: false
          }
        ]
      })

      await wrapper.vm.$nextTick()

      const acknowledgeBtn = wrapper.find('.alert-actions .ant-btn')
      if (acknowledgeBtn.exists()) {
        await acknowledgeBtn.trigger('click')
        // 验证确认功能
      }
    })
  })

  describe('形态识别功能', () => {
    it('应该显示检测到的形态', async () => {
      // 设置模拟形态数据
      await wrapper.setData({
        detectedPatterns: [
          {
            id: '1',
            name: '头肩顶',
            confidence: 0.85,
            signal: 'bearish',
            description: '经典的反转形态'
          }
        ]
      })

      await wrapper.vm.$nextTick()

      const patternsPanel = wrapper.find('.patterns-panel')
      expect(patternsPanel.exists()).toBe(true)
      
      const patternItems = patternsPanel.findAll('.pattern-item')
      expect(patternItems.length).toBe(1)
    })

    it('应该能够分析形态', async () => {
      const analyzeBtn = wrapper.find('.patterns-panel .ant-btn')
      if (analyzeBtn.exists()) {
        await analyzeBtn.trigger('click')
        // 验证分析功能
      }
    })
  })

  describe('快速交易功能', () => {
    it('应该显示当前价格信息', () => {
      const tradePanel = wrapper.find('.quick-trade-panel')
      expect(tradePanel.exists()).toBe(true)
      
      const currentPrice = tradePanel.find('.current-price')
      expect(currentPrice.exists()).toBe(true)
    })

    it('应该能够打开买入交易模态框', async () => {
      const buyBtn = wrapper.find('.trade-buttons .ant-btn:not(.ant-btn-danger)')
      if (buyBtn.exists()) {
        await buyBtn.trigger('click')
        expect(wrapper.vm.showTradeModalVisible).toBe(true)
        expect(wrapper.vm.tradeType).toBe('buy')
      }
    })

    it('应该能够打开卖出交易模态框', async () => {
      const sellBtn = wrapper.find('.trade-buttons .ant-btn-danger')
      if (sellBtn.exists()) {
        await sellBtn.trigger('click')
        expect(wrapper.vm.showTradeModalVisible).toBe(true)
        expect(wrapper.vm.tradeType).toBe('sell')
      }
    })
  })

  describe('图表交互功能', () => {
    it('应该能够切换全屏模式', async () => {
      const fullscreenBtn = wrapper.find('[data-testid="fullscreen-toggle"]')
      if (fullscreenBtn.exists()) {
        await fullscreenBtn.trigger('click')
        expect(wrapper.vm.isFullscreen).toBe(true)
        
        await fullscreenBtn.trigger('click')
        expect(wrapper.vm.isFullscreen).toBe(false)
      }
    })

    it('应该能够截图', async () => {
      const screenshotBtn = wrapper.find('[data-testid="screenshot"]')
      if (screenshotBtn.exists()) {
        await screenshotBtn.trigger('click')
        // 验证截图功能
      }
    })

    it('应该处理图表点击事件', async () => {
      const chartComponent = wrapper.findComponent({ name: 'ChartComponent' })
      if (chartComponent.exists()) {
        await chartComponent.vm.$emit('chart-click', {
          componentType: 'series',
          seriesType: 'candlestick',
          dataIndex: 0
        })
        // 验证点击处理
      }
    })

    it('应该处理图表刷选事件', async () => {
      const chartComponent = wrapper.findComponent({ name: 'ChartComponent' })
      if (chartComponent.exists()) {
        await chartComponent.vm.$emit('chart-brush', {
          areas: [{
            coordRange: [[0, 10]]
          }]
        })
        // 验证刷选处理
      }
    })
  })

  describe('数据更新', () => {
    it('应该响应图表数据变化', async () => {
      const newData = [
        ...mockChartData,
        {
          timestamp: '2024-01-01 02:00:00',
          open: 106,
          high: 110,
          low: 104,
          close: 108,
          volume: 1500
        }
      ]

      chartStore.chartData = newData
      await wrapper.vm.$nextTick()

      expect(wrapper.vm.chartData).toEqual(newData)
      expect(wrapper.vm.currentPrice).toBe(108)
    })

    it('应该计算价格变化', async () => {
      const newData = [
        { close: 100 },
        { close: 105 }
      ]

      chartStore.chartData = newData
      await wrapper.vm.$nextTick()

      expect(wrapper.vm.priceChange).toBe(5)
    })

    it('应该自动分析新数据', async () => {
      const spy = vi.spyOn(wrapper.vm, 'autoAnalyze')
      
      chartStore.chartData = [...mockChartData, {
        timestamp: '2024-01-01 02:00:00',
        open: 106,
        high: 110,
        low: 104,
        close: 108,
        volume: 1500
      }]
      
      await wrapper.vm.$nextTick()
      expect(spy).toHaveBeenCalled()
    })
  })

  describe('事件发射', () => {
    it('应该发射交易事件', async () => {
      await wrapper.vm.handleTrade({
        type: 'buy',
        symbol: 'BTC-USDT',
        amount: 0.1,
        price: 50000
      })

      expect(wrapper.emitted('trade')).toBeTruthy()
      expect(wrapper.emitted('trade')[0]).toEqual([{
        type: 'buy',
        symbol: 'BTC-USDT',
        amount: 0.1,
        price: 50000
      }])
    })

    it('应该发射预警事件', async () => {
      const mockAlerts = [
        {
          id: '1',
          type: 'price-breakout',
          message: '价格突破阻力位'
        }
      ]

      // 模拟预警生成
      wrapper.vm.$emit('alert', mockAlerts)

      expect(wrapper.emitted('alert')).toBeTruthy()
      expect(wrapper.emitted('alert')[0]).toEqual([mockAlerts])
    })

    it('应该发射形态检测事件', async () => {
      const mockPatterns = [
        {
          id: '1',
          name: '头肩顶',
          confidence: 0.85
        }
      ]

      wrapper.vm.$emit('pattern-detected', mockPatterns)

      expect(wrapper.emitted('pattern-detected')).toBeTruthy()
      expect(wrapper.emitted('pattern-detected')[0]).toEqual([mockPatterns])
    })
  })

  describe('错误处理', () => {
    it('应该处理分析失败', async () => {
      // 模拟分析失败
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      // 触发会导致错误的操作
      wrapper.vm.chartData = null
      await wrapper.vm.autoAnalyze()

      expect(consoleSpy).toHaveBeenCalledWith('自动分析失败:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })

    it('应该处理空数据情况', async () => {
      chartStore.chartData = []
      await wrapper.vm.$nextTick()

      expect(wrapper.vm.chartData).toEqual([])
      expect(wrapper.vm.currentPrice).toBe(0)
    })
  })

  describe('性能优化', () => {
    it('应该在数据量大时限制分析频率', async () => {
      const spy = vi.spyOn(wrapper.vm, 'autoAnalyze')
      
      // 快速更新多次数据
      for (let i = 0; i < 10; i++) {
        chartStore.chartData = [...mockChartData, { close: 100 + i }]
        await wrapper.vm.$nextTick()
      }

      // 验证分析函数没有被过度调用
      expect(spy.mock.calls.length).toBeLessThan(10)
    })

    it('应该正确清理资源', () => {
      const stopMonitoringSpy = vi.spyOn(wrapper.vm, 'stopMonitoring')
      
      wrapper.unmount()
      
      expect(stopMonitoringSpy).toHaveBeenCalled()
    })
  })
})

// 集成测试：完整的用户工作流
describe('ChartTradingInterface - 集成测试', () => {
  let wrapper
  let pinia
  let chartStore

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    chartStore = useChartStore()
    
    wrapper = mount(ChartTradingInterface, {
      global: {
        plugins: [pinia],
        stubs: {
          ChartComponent: {
            template: '<div class="mock-chart"></div>',
            methods: {
              resize: vi.fn(),
              getDataURL: vi.fn(() => 'mock-data-url')
            }
          },
          DrawingSettingsModal: true,
          AlertSettingsModal: true,
          TradeModal: true,
          EmptyIcon: true
        }
      }
    })
  })

  afterEach(() => {
    wrapper.unmount()
  })

  it('完整的交易工作流', async () => {
    // 1. 加载图表数据
    chartStore.chartData = [
      {
        timestamp: '2024-01-01 00:00:00',
        open: 50000,
        high: 51000,
        low: 49000,
        close: 50500,
        volume: 1000
      }
    ]
    
    await wrapper.vm.$nextTick()
    
    // 2. 启动智能预警
    await wrapper.vm.toggleMonitoring()
    expect(wrapper.vm.isMonitoring).toBe(true)
    
    // 3. 分析市场情绪
    await wrapper.vm.refreshSentiment()
    
    // 4. 识别形态
    await wrapper.vm.analyzePatterns()
    
    // 5. 设置绘图工具
    await wrapper.vm.setDrawingTool('trendline')
    expect(wrapper.vm.drawingTool).toBe('trendline')
    
    // 6. 执行交易
    await wrapper.vm.showTradeModal('buy')
    expect(wrapper.vm.showTradeModalVisible).toBe(true)
    expect(wrapper.vm.tradeType).toBe('buy')
    
    // 7. 处理交易数据
    const tradeData = {
      type: 'buy',
      symbol: 'BTC-USDT',
      amount: 0.1,
      price: 50500
    }
    
    await wrapper.vm.handleTrade(tradeData)
    expect(wrapper.emitted('trade')).toBeTruthy()
    expect(wrapper.vm.showTradeModalVisible).toBe(false)
  })

  it('风险管理工作流', async () => {
    // 1. 设置高风险数据
    chartStore.chartData = [
      { close: 50000, volume: 1000 },
      { close: 45000, volume: 2000 }, // 大幅下跌
      { close: 40000, volume: 3000 }  // 继续下跌
    ]
    
    await wrapper.vm.$nextTick()
    
    // 2. 自动分析应该检测到风险
    await wrapper.vm.autoAnalyze()
    
    // 3. 应该生成预警
    expect(wrapper.vm.activeAlerts.length).toBeGreaterThan(0)
    
    // 4. 确认预警
    if (wrapper.vm.activeAlerts.length > 0) {
      await wrapper.vm.acknowledgeAlert(wrapper.vm.activeAlerts[0].id)
    }
    
    // 5. 停止监控
    await wrapper.vm.toggleMonitoring()
    expect(wrapper.vm.isMonitoring).toBe(false)
  })
})