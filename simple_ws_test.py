#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的WebSocket测试脚本
用于快速验证WebSocket连接是否正常
"""

import asyncio
import websockets
import json
import sys

async def test_connection():
    """
    测试WebSocket连接
    """
    uri = "ws://localhost:8080/api/okx/market"
    
    try:
        print(f"正在连接到: {uri}")
        
        # 设置连接超时
        async with websockets.connect(uri, ping_timeout=10, close_timeout=10) as websocket:
            print("✅ WebSocket连接成功建立")
            
            # 发送简单的订阅请求
            request = {
                "op": "subscribe",
                "args": [{"channel": "tickers", "instId": "BTC-USDT"}]
            }
            
            print(f"发送请求: {request}")
            await websocket.send(json.dumps(request))
            print("✅ 请求已发送")
            
            # 等待响应
            print("等待响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                print(f"✅ 收到响应: {response[:100]}...")
                
                # 尝试接收更多数据
                for i in range(2):
                    try:
                        data = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                        print(f"✅ 收到数据 {i+1}: {len(data)} 字符")
                    except asyncio.TimeoutError:
                        print(f"⏰ 数据 {i+1} 接收超时")
                        
            except asyncio.TimeoutError:
                print("❌ 响应超时")
                return False
                
            print("✅ 测试完成")
            return True
            
    except websockets.exceptions.InvalidURI:
        print(f"❌ 无效的URI: {uri}")
        return False
    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ 连接被关闭: {e}")
        return False
    except OSError as e:
        print(f"❌ 网络错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False

if __name__ == "__main__":
    print("=== WebSocket连接测试 ===")
    print("确保后端服务运行在 localhost:8080")
    print()
    
    try:
        result = asyncio.run(test_connection())
        if result:
            print("\n🎉 WebSocket连接测试成功！")
            sys.exit(0)
        else:
            print("\n💥 WebSocket连接测试失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        sys.exit(1)