<template>
  <BaseChart
    title="KDJ 指标"
    :signals="signals"
    :settings="localSettings"
    @save-settings="saveSettings"
    @reset-settings="resetSettings"
  >
    <template #chart>
      <div ref="chartRef" class="kdj-chart" :style="{ height: chartHeight + 'px' }"></div>
    </template>
    <template #settings-form>
      <a-form :model="localSettings" layout="vertical">
        <a-form-item label="K值周期" name="kPeriod">
          <a-slider
            v-model:value="localSettings.kPeriod"
            :min="5"
            :max="20"
            :marks="{ 9: '9', 14: '14' }"
          />
        </a-form-item>
        <a-form-item label="D值周期" name="dPeriod">
          <a-slider
            v-model:value="localSettings.dPeriod"
            :min="2"
            :max="10"
            :marks="{ 3: '3', 5: '5' }"
          />
        </a-form-item>
        <a-form-item label="J值周期" name="jPeriod">
          <a-slider
            v-model:value="localSettings.jPeriod"
            :min="2"
            :max="10"
            :marks="{ 3: '3', 5: '5' }"
          />
        </a-form-item>
        <a-form-item label="超买阈值" name="overboughtLevel">
          <a-slider
            v-model:value="localSettings.overboughtLevel"
            :min="70"
            :max="90"
            :marks="{ 80: '80' }"
          />
        </a-form-item>
        <a-form-item label="超卖阈值" name="oversoldLevel">
          <a-slider
            v-model:value="localSettings.oversoldLevel"
            :min="10"
            :max="30"
            :marks="{ 20: '20' }"
          />
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="localSettings.showSignalLines">
            显示信号线
          </a-checkbox>
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="localSettings.enableAlerts">
            启用交易信号
          </a-checkbox>
        </a-form-item>
      </a-form>
    </template>
  </BaseChart>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  SettingOutlined,
  BellOutlined
} from '@ant-design/icons-vue'
import { useChartCommon } from '@/composables/useChartCommon'
import { CHART_DATA_INDEX } from '@/constants/chartConstants'

// Props
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  theme: {
    type: String,
    default: 'light'
  },
  settings: {
    type: Object,
    default: () => ({
      kPeriod: 9,
      dPeriod: 3,
      jPeriod: 3,
      overboughtLevel: 80,
      oversoldLevel: 20,
      showSignalLines: true,
      enableAlerts: true
    })
  },
  height: {
    type: Number,
    default: 200
  }
})

// Emits
const emit = defineEmits(['signal', 'settings-change'])

// 使用共享逻辑
const { initChart, updateChart, disposeChart } = useChartCommon()

// 本地状态
const chartRef = ref(null)
const chartInstance = ref(null)
const showSettings = ref(false)
const showSignals = ref(false)
const signals = ref([])

// 本地设置（用于模态框编辑）
const localSettings = ref({ ...props.settings })

// 计算属性
const chartHeight = computed(() => props.height)

// KDJ计算函数
const calculateKDJ = (data, kPeriod = 9, dPeriod = 3, jPeriod = 3) => {
  if (!data || data.length < kPeriod) return { kValues: [], dValues: [], jValues: [] }

  const kValues = []
  const dValues = []
  const jValues = []
  const rsvValues = []

  for (let i = 0; i < data.length; i++) {
    if (i < kPeriod - 1) {
      kValues.push(null)
      dValues.push(null)
      jValues.push(null)
      continue
    }

    // 计算RSV
    const period = data.slice(i - kPeriod + 1, i + 1)
    const currentClose = data[i][CHART_DATA_INDEX.CLOSE]
    const highestHigh = Math.max(...period.map(item => item[CHART_DATA_INDEX.HIGH]))
    const lowestLow = Math.min(...period.map(item => item[CHART_DATA_INDEX.LOW]))
    
    const rsv = highestHigh === lowestLow ? 50 : 
                ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100
    rsvValues.push(rsv)

    // 计算K值
    let kValue
    if (i === kPeriod - 1) {
      kValue = rsv
    } else {
      const prevK = kValues[i - 1] || 50
      kValue = (prevK * (dPeriod - 1) + rsv) / dPeriod
    }
    kValues.push(kValue)

    // 计算D值
    let dValue
    if (i === kPeriod - 1) {
      dValue = kValue
    } else {
      const prevD = dValues[i - 1] || 50
      dValue = (prevD * (jPeriod - 1) + kValue) / jPeriod
    }
    dValues.push(dValue)

    // 计算J值
    const jValue = 3 * kValue - 2 * dValue
    jValues.push(jValue)
  }

  return { kValues, dValues, jValues }
}

// 生成交易信号
const generateSignals = (kValues, dValues, jValues) => {
  const newSignals = []
  const { overboughtLevel, oversoldLevel, enableAlerts } = props.settings

  if (!enableAlerts || kValues.length < 2) return newSignals

  for (let i = 1; i < kValues.length; i++) {
    const prevK = kValues[i - 1]
    const currK = kValues[i]
    const prevD = dValues[i - 1]
    const currD = dValues[i]
    const currJ = jValues[i]

    if (prevK == null || currK == null || prevD == null || currD == null) continue

    // 金叉信号（K线上穿D线）
    if (prevK <= prevD && currK > currD && currK < oversoldLevel + 10) {
      newSignals.push({
        id: `kdj_golden_${i}_${Date.now()}`,
        type: 'buy',
        message: 'KDJ金叉，建议买入',
        timestamp: Date.now(),
        kValue: currK,
        dValue: currD,
        jValue: currJ,
        confidence: calculateConfidence('golden', currK, currD, currJ),
        indicator: 'KDJ'
      })
    }

    // 死叉信号（K线下穿D线）
    if (prevK >= prevD && currK < currD && currK > overboughtLevel - 10) {
      newSignals.push({
        id: `kdj_death_${i}_${Date.now()}`,
        type: 'sell',
        message: 'KDJ死叉，建议卖出',
        timestamp: Date.now(),
        kValue: currK,
        dValue: currD,
        jValue: currJ,
        confidence: calculateConfidence('death', currK, currD, currJ),
        indicator: 'KDJ'
      })
    }

    // 超买信号
    if (currK > overboughtLevel && currD > overboughtLevel) {
      newSignals.push({
        id: `kdj_overbought_${i}_${Date.now()}`,
        type: 'sell',
        message: 'KDJ超买，注意风险',
        timestamp: Date.now(),
        kValue: currK,
        dValue: currD,
        jValue: currJ,
        confidence: calculateConfidence('overbought', currK, currD, currJ),
        indicator: 'KDJ'
      })
    }

    // 超卖信号
    if (currK < oversoldLevel && currD < oversoldLevel) {
      newSignals.push({
        id: `kdj_oversold_${i}_${Date.now()}`,
        type: 'buy',
        message: 'KDJ超卖，可能反弹',
        timestamp: Date.now(),
        kValue: currK,
        dValue: currD,
        jValue: currJ,
        confidence: calculateConfidence('oversold', currK, currD, currJ),
        indicator: 'KDJ'
      })
    }
  }

  return newSignals
}

// 计算信号置信度
const calculateConfidence = (signalType, kValue, dValue, jValue) => {
  let confidence = 50

  switch (signalType) {
    case 'golden':
      // 金叉在低位置信度更高
      if (kValue < 30) confidence += 30
      else if (kValue < 50) confidence += 20
      else confidence += 10
      break
    case 'death':
      // 死叉在高位置信度更高
      if (kValue > 70) confidence += 30
      else if (kValue > 50) confidence += 20
      else confidence += 10
      break
    case 'overbought':
      confidence = Math.min(90, 50 + (kValue - 80) * 2)
      break
    case 'oversold':
      confidence = Math.min(90, 50 + (20 - kValue) * 2)
      break
  }

  return Math.max(10, Math.min(95, Math.round(confidence)))
}

// 初始化图表
const initKDJChart = () => {
  if (!chartRef.value || !props.data.length) return

  // 计算KDJ数据
  const { kValues, dValues, jValues } = calculateKDJ(
    props.data,
    props.settings.kPeriod,
    props.settings.dPeriod,
    props.settings.jPeriod
  )

  // 生成交易信号
  const newSignals = generateSignals(kValues, dValues, jValues)
  signals.value = newSignals

  // 发送信号给父组件
  newSignals.forEach(signal => {
    emit('signal', signal)
  })

  // 准备图表数据
  const dates = props.data.map(item => item[CHART_DATA_INDEX.DATETIME])

  const option = {
    animation: true,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function (params) {
        let result = `<div>${params[0].name}</div>`
        params.forEach(param => {
          if (param.value !== null) {
            result += `<div>${param.marker}${param.seriesName}: ${param.value.toFixed(2)}</div>`
          }
        })
        return result
      }
    },
    legend: {
      data: ['K', 'D', 'J'],
      top: 5,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      boundaryGap: false,
      axisLine: { onZero: false },
      splitLine: { show: false },
      axisLabel: {
        fontSize: 10
      }
    },
    yAxis: {
      scale: false,
      min: 0,
      max: 100,
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          opacity: 0.3
        }
      },
      axisLabel: {
        fontSize: 10
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 80,
        end: 100
      }
    ],
    series: [
      {
        name: 'K',
        type: 'line',
        data: kValues,
        lineStyle: {
          color: '#1890ff',
          width: 1.5
        },
        showSymbol: false,
        smooth: true
      },
      {
        name: 'D',
        type: 'line',
        data: dValues,
        lineStyle: {
          color: '#52c41a',
          width: 1.5
        },
        showSymbol: false,
        smooth: true
      },
      {
        name: 'J',
        type: 'line',
        data: jValues,
        lineStyle: {
          color: '#faad14',
          width: 1.5
        },
        showSymbol: false,
        smooth: true
      }
    ]
  }

  // 添加信号线
  if (props.settings.showSignalLines) {
    option.series.push(
      {
        name: '超买线',
        type: 'line',
        data: new Array(dates.length).fill(props.settings.overboughtLevel),
        lineStyle: {
          color: '#ff4d4f',
          type: 'dashed',
          width: 1
        },
        showSymbol: false,
        silent: true
      },
      {
        name: '超卖线',
        type: 'line',
        data: new Array(dates.length).fill(props.settings.oversoldLevel),
        lineStyle: {
          color: '#ff4d4f',
          type: 'dashed',
          width: 1
        },
        showSymbol: false,
        silent: true
      }
    )
  }

  chartInstance.value = initChart(chartRef.value, option, props.theme)
}

// 工具函数
const getSignalColor = (type) => {
  const colors = {
    buy: 'green',
    sell: 'red',
    warning: 'orange'
  }
  return colors[type] || 'blue'
}

const getSignalText = (type) => {
  const texts = {
    buy: '买入',
    sell: '卖出',
    warning: '警告'
  }
  return texts[type] || type
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 设置相关函数
const saveSettings = () => {
  emit('settings-change', { ...localSettings.value })
  showSettings.value = false
  message.success('KDJ设置已保存')
}

const resetSettings = () => {
  localSettings.value = { ...props.settings }
}

// 监听数据变化
watch(
  () => [props.data, props.theme, props.settings],
  () => {
    if (chartInstance.value) {
      initKDJChart()
    }
  },
  { deep: true }
)

// 监听设置变化
watch(
  () => props.settings,
  (newSettings) => {
    localSettings.value = { ...newSettings }
  },
  { deep: true, immediate: true }
)

// 组件挂载
onMounted(() => {
  if (props.data.length > 0) {
    initKDJChart()
  }
})

// 组件卸载
onUnmounted(() => {
  if (chartInstance.value) {
    disposeChart(chartInstance.value)
  }
})
</script>

<style scoped>
.kdj-chart {
  width: 100%;
}
</style>