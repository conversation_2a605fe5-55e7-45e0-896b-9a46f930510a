#!/usr/bin/env python3
"""
修复string用户密码的脚本
"""

import sqlite3
from passlib.hash import bcrypt

def fix_string_user():
    # 连接数据库
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    
    # 删除旧的string用户
    cursor.execute('DELETE FROM user WHERE username = ?', ('string',))
    print("已删除旧的string用户")
    
    # 创建新的string用户
    password = "string"
    hashed_password = bcrypt.hash(password)
    
    cursor.execute(
        'INSERT INTO user (username, password) VALUES (?, ?)',
        ('string', hashed_password)
    )
    
    print(f"已创建新的string用户")
    print(f"用户名: string")
    print(f"密码: string")
    print(f"密码哈希: {hashed_password}")
    
    # 提交更改
    conn.commit()
    conn.close()
    print("修复完成！")

if __name__ == "__main__":
    fix_string_user()
