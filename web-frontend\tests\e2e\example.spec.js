import { test, expect } from '@playwright/test'

/**
 * 基础页面加载测试
 */
test('应用首页加载正常', async ({ page }) => {
  await page.goto('/')
  
  // 检查页面标题
  await expect(page).toHaveTitle(/OKX Trading/)
  
  // 检查主要元素是否存在
  await expect(page.locator('body')).toBeVisible()
})

/**
 * 图表组件测试
 */
test('高级图表组件渲染正常', async ({ page }) => {
  await page.goto('/')
  
  // 等待图表容器加载
  const chartContainer = page.locator('[data-testid="chart-container"]')
  await expect(chartContainer).toBeVisible({ timeout: 10000 })
  
  // 检查时间周期按钮组
  const timeframeButtons = page.locator('.timeframe-buttons')
  await expect(timeframeButtons).toBeVisible()
  
  // 测试时间周期切换
  const fiveMinButton = page.locator('button:has-text("5分")')
  if (await fiveMinButton.isVisible()) {
    await fiveMinButton.click()
    // 验证按钮状态变化
    await expect(fiveMinButton).toHaveClass(/ant-btn-primary/)
  }
})

/**
 * 设置面板测试
 */
test('图表设置面板功能正常', async ({ page }) => {
  await page.goto('/')
  
  // 查找设置按钮
  const settingsButton = page.locator('button:has-text("设置")')
  if (await settingsButton.isVisible()) {
    await settingsButton.click()
    
    // 检查设置面板是否打开
    const settingsPanel = page.locator('.ant-drawer')
    await expect(settingsPanel).toBeVisible()
    
    // 测试设置保存
    const saveButton = page.locator('button:has-text("保存")')
    if (await saveButton.isVisible()) {
      await saveButton.click()
    }
  }
})