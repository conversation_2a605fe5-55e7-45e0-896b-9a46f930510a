/**
 * 支撑阻力位指标 - TradingView风格趋势线
 * 自动识别并绘制倾斜的支撑位和阻力位趋势线
 */
import * as klinecharts from 'klinecharts'

/**
 * 计算支撑阻力位趋势线
 * @param {Array} kLineDataList K线数据数组
 * @param {Object} indicator 指标实例
 * @returns {Array} 计算结果数组
 */
function calculateSupportResistance(kLineDataList, indicator) {
  const params = indicator.calcParams || [50, 3, 0.01]
  const [lookbackPeriod, minTouchCount, tolerance] = params
  
  if (!kLineDataList || kLineDataList.length < lookbackPeriod) {
    return []
  }

  const result = []
  
  // 计算趋势线
  const trendLines = findTrendLines(kLineDataList, lookbackPeriod, minTouchCount, tolerance)
  
  // 为每个数据点计算趋势线上的价格
  for (let i = 0; i < kLineDataList.length; i++) {
    const dataPoint = {}
    
    // 计算支撑线价格
    if (trendLines.supportLine) {
      const supportPrice = calculateTrendLinePrice(trendLines.supportLine, i)
      if (supportPrice > 0) {
        dataPoint.support = supportPrice
      }
    }
    
    // 计算阻力线价格
    if (trendLines.resistanceLine) {
      const resistancePrice = calculateTrendLinePrice(trendLines.resistanceLine, i)
      if (resistancePrice > 0) {
        dataPoint.resistance = resistancePrice
      }
    }
    
    result.push(dataPoint)
  }
  
  return result
}

/**
 * 寻找趋势线（支撑线和阻力线）
 * @param {Array} kLineDataList K线数据数组
 * @param {number} lookbackPeriod 回看周期
 * @param {number} minTouchCount 最小触及次数
 * @param {number} tolerance 价格容差
 * @returns {Object} 包含supportLine和resistanceLine的对象
 */
function findTrendLines(kLineDataList, lookbackPeriod, minTouchCount, tolerance) {
  if (!kLineDataList || kLineDataList.length < lookbackPeriod) {
    return { supportLine: null, resistanceLine: null }
  }
  
  // 识别显著的高点和低点
  const significantPoints = findSignificantPoints(kLineDataList, lookbackPeriod)
  
  // 寻找最佳支撑线
  const supportLine = findBestTrendLine(significantPoints.lows, minTouchCount, tolerance, 'support')
  
  // 寻找最佳阻力线
  const resistanceLine = findBestTrendLine(significantPoints.highs, minTouchCount, tolerance, 'resistance')
  
  return {
    supportLine,
    resistanceLine
  }
}

/**
 * 识别显著的高点和低点
 * @param {Array} kLineDataList K线数据数组
 * @param {number} lookbackPeriod 回看周期
 * @returns {Object} 包含highs和lows数组的对象
 */
function findSignificantPoints(kLineDataList, lookbackPeriod) {
  const highs = []
  const lows = []
  const period = Math.min(5, Math.floor(lookbackPeriod / 10)) // 动态调整周期
  
  for (let i = period; i < kLineDataList.length - period; i++) {
    const current = kLineDataList[i]
    let isHighPoint = true
    let isLowPoint = true
    
    // 检查是否为局部高点
    for (let j = i - period; j <= i + period; j++) {
      if (j !== i && kLineDataList[j].high >= current.high) {
        isHighPoint = false
        break
      }
    }
    
    // 检查是否为局部低点
    for (let j = i - period; j <= i + period; j++) {
      if (j !== i && kLineDataList[j].low <= current.low) {
        isLowPoint = false
        break
      }
    }
    
    if (isHighPoint) {
      highs.push({ index: i, price: current.high, timestamp: current.timestamp })
    }
    
    if (isLowPoint) {
      lows.push({ index: i, price: current.low, timestamp: current.timestamp })
    }
  }
  
  return { highs, lows }
}

/**
 * 寻找最佳趋势线
 * @param {Array} points 价格点数组
 * @param {number} minTouchCount 最小触及次数
 * @param {number} tolerance 价格容差
 * @param {string} type 类型（'support' 或 'resistance'）
 * @returns {Object|null} 趋势线对象或null
 */
function findBestTrendLine(points, minTouchCount, tolerance, type) {
  if (points.length < 2) return null
  
  let bestLine = null
  let bestScore = 0
  
  // 尝试所有可能的点对组合
  for (let i = 0; i < points.length - 1; i++) {
    for (let j = i + 1; j < points.length; j++) {
      const point1 = points[i]
      const point2 = points[j]
      
      // 计算趋势线参数
      const slope = (point2.price - point1.price) / (point2.index - point1.index)
      const intercept = point1.price - slope * point1.index
      
      // 验证趋势线有效性
      const validation = validateTrendLine(points, { slope, intercept, point1, point2 }, tolerance, type)
      
      if (validation.touchCount >= minTouchCount && validation.score > bestScore) {
        bestScore = validation.score
        bestLine = {
          slope,
          intercept,
          point1,
          point2,
          touchCount: validation.touchCount,
          score: validation.score
        }
      }
    }
  }
  
  return bestLine
}

/**
 * 验证趋势线有效性
 * @param {Array} points 所有价格点
 * @param {Object} line 趋势线参数
 * @param {number} tolerance 容差
 * @param {string} type 类型
 * @returns {Object} 验证结果
 */
function validateTrendLine(points, line, tolerance, type) {
  let touchCount = 0
  let validPoints = 0
  let totalDeviation = 0
  
  for (const point of points) {
    const expectedPrice = line.slope * point.index + line.intercept
    const deviation = Math.abs(point.price - expectedPrice) / point.price
    
    if (deviation <= tolerance) {
      touchCount++
    }
    
    // 对于支撑线，价格应该在线上方；对于阻力线，价格应该在线下方
    if (type === 'support') {
      if (point.price >= expectedPrice - expectedPrice * tolerance) {
        validPoints++
      }
    } else {
      if (point.price <= expectedPrice + expectedPrice * tolerance) {
        validPoints++
      }
    }
    
    totalDeviation += deviation
  }
  
  // 计算综合评分
  const avgDeviation = totalDeviation / points.length
  const validRatio = validPoints / points.length
  const score = touchCount * validRatio / (1 + avgDeviation)
  
  return { touchCount, score, validRatio, avgDeviation }
}

/**
 * 计算趋势线在指定索引处的价格
 * @param {Object} trendLine 趋势线对象
 * @param {number} index 索引
 * @returns {number} 价格
 */
function calculateTrendLinePrice(trendLine, index) {
  if (!trendLine) return 0
  return trendLine.slope * index + trendLine.intercept
}

/**
 * 支撑阻力位指标定义 - TradingView风格趋势线
 * 基于历史价格数据识别倾斜的支撑位和阻力位趋势线
 */
const supportResistanceIndicator = {
  name: 'SupportResistanceTrend',
  shortName: 'S/R Trend',
  precision: 4,
  calcParams: [
    50,   // lookbackPeriod - 回看周期（增加到50以获得更好的趋势线）
    3,    // minTouchCount - 最小触及次数
    0.01  // tolerance - 价格容差（1%）
  ],
  figures: [
    {
      key: 'support',
      title: 'Support Trend: ',
      type: 'line',
      baseValue: 0,
      styles: {
        style: 'solid',
        size: 1,
        color: '#26A69A'
      }
    },
    {
      key: 'resistance',
      title: 'Resistance Trend: ',
      type: 'line',
      baseValue: 0,
      styles: {
        style: 'solid',
        size: 1,
        color: '#EF5350'
      }
    }
  ],
  calc: calculateSupportResistance,
  shouldOhlc: false,
  shouldFormatBigNumber: false,
  visible: true,
  zLevel: 0
}

/**
 * 注册支撑阻力位指标
 */
export function registerSupportResistanceIndicator() {
  try {
    klinecharts.registerIndicator(supportResistanceIndicator)
    console.log('支撑阻力位指标注册成功')
  } catch (error) {
    console.error('支撑阻力位指标注册失败:', error)
  }
}

export default supportResistanceIndicator