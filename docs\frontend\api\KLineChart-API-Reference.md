# KLineChart API 接口参考文档

## 📡 后端 API 接口

### 基础配置

**基础URL**: `http://localhost:8080/api`

**认证方式**: Bearer Token (JWT)

**请求头**:
```http
Content-Type: application/json
Authorization: Bearer <your-jwt-token>
```

## 📊 市场数据接口

### 1. 获取K线数据

**接口地址**: `GET /okx/market/candles`

**功能描述**: 获取指定交易对的K线数据，用于图表显示

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| instId | string | 是 | 交易对ID | BTC-USDT |
| bar | string | 否 | 时间粒度 | 1m, 5m, 15m, 30m, 1H, 4H, 1D |
| after | string | 否 | 请求此时间戳之前的数据 | 1640995200000 |
| before | string | 否 | 请求此时间戳之后的数据 | 1640995200000 |
| limit | string | 否 | 返回结果的数量 | 100 (默认), 最大300 |

**请求示例**:
```http
GET /api/okx/market/candles?instId=BTC-USDT&bar=1m&limit=100
```

**响应格式**:
```json
{
  "code": "0",
  "msg": "",
  "data": [
    [
      "1640995200000",  // 时间戳
      "50000",          // 开盘价
      "51000",          // 最高价
      "49000",          // 最低价
      "50500",          // 收盘价
      "1000",           // 成交量
      "50000000",       // 成交额
      "50250",          // 成交量加权平均价
      "1"               // 确认状态
    ]
  ]
}
```

**前端数据转换**:
```javascript
const formatKlineData = (rawData) => {
  return rawData.map(item => ({
    timestamp: parseInt(item[0]),
    open: parseFloat(item[1]),
    high: parseFloat(item[2]),
    low: parseFloat(item[3]),
    close: parseFloat(item[4]),
    volume: parseFloat(item[5]),
    turnover: parseFloat(item[6]),
    vwap: parseFloat(item[7]),
    confirm: item[8] === '1'
  }))
}
```

### 2. 获取实时行情

**接口地址**: `GET /okx/market/ticker`

**功能描述**: 获取指定交易对的实时行情数据

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| instId | string | 是 | 交易对ID | BTC-USDT |

**请求示例**:
```http
GET /api/okx/market/ticker?instId=BTC-USDT
```

**响应格式**:
```json
{
  "code": "0",
  "msg": "",
  "data": [
    {
      "instType": "SPOT",
      "instId": "BTC-USDT",
      "last": "50500",
      "lastSz": "0.1",
      "askPx": "50510",
      "askSz": "1.2",
      "bidPx": "50490",
      "bidSz": "1.5",
      "open24h": "49800",
      "high24h": "51200",
      "low24h": "49500",
      "vol24h": "12345.67",
      "volCcy24h": "623456789.12",
      "ts": "1640995200000"
    }
  ]
}
```

### 3. 获取交易对信息

**接口地址**: `GET /okx/public/instruments`

**功能描述**: 获取交易对基础信息，包括精度设置

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| instType | string | 否 | 产品类型 | SPOT, SWAP, FUTURES |
| uly | string | 否 | 标的指数 | BTC-USD |
| instFamily | string | 否 | 交易品种 | BTC-USD |
| instId | string | 否 | 产品ID | BTC-USDT |

**请求示例**:
```http
GET /api/okx/public/instruments?instType=SPOT
```

**响应格式**:
```json
{
  "code": "0",
  "msg": "",
  "data": [
    {
      "instType": "SPOT",
      "instId": "BTC-USDT",
      "uly": "",
      "instFamily": "",
      "baseCcy": "BTC",
      "quoteCcy": "USDT",
      "settleCcy": "",
      "ctVal": "",
      "ctMult": "",
      "ctValCcy": "",
      "optType": "",
      "stk": "",
      "listTime": "1548133413000",
      "expTime": "",
      "lever": "10",
      "tickSz": "0.1",
      "lotSz": "0.00000001",
      "minSz": "0.00001",
      "ctType": "",
      "alias": "",
      "state": "live",
      "maxLmtSz": "9999999999",
      "maxMktSz": "1000000",
      "maxTwapSz": "9999999999",
      "maxIcebergSz": "9999999999",
      "maxTriggerSz": "9999999999",
      "maxStopSz": "9999999999"
    }
  ]
}
```

**精度计算**:
```javascript
/**
 * 从tickSz计算价格精度
 * @param {string} tickSz - 最小价格变动单位
 * @returns {number} 价格精度位数
 */
const calculatePricePrecision = (tickSz) => {
  const tick = parseFloat(tickSz)
  if (tick >= 1) return 0
  return Math.abs(Math.floor(Math.log10(tick)))
}

/**
 * 从lotSz计算数量精度
 * @param {string} lotSz - 最小下单量
 * @returns {number} 数量精度位数
 */
const calculateSizePrecision = (lotSz) => {
  const lot = parseFloat(lotSz)
  if (lot >= 1) return 0
  return Math.abs(Math.floor(Math.log10(lot)))
}
```

### 4. 获取市场深度

**接口地址**: `GET /okx/market/books`

**功能描述**: 获取指定交易对的市场深度数据

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| instId | string | 是 | 交易对ID | BTC-USDT |
| sz | string | 否 | 深度档位数量 | 5, 10, 20, 50 (默认5) |

**请求示例**:
```http
GET /api/okx/market/books?instId=BTC-USDT&sz=20
```

**响应格式**:
```json
{
  "code": "0",
  "msg": "",
  "data": [
    {
      "asks": [
        ["50510", "1.2", "0", "1"],
        ["50520", "2.5", "0", "2"]
      ],
      "bids": [
        ["50490", "1.5", "0", "1"],
        ["50480", "2.8", "0", "2"]
      ],
      "ts": "1640995200000"
    }
  ]
}
```

### 5. 获取成交记录

**接口地址**: `GET /okx/market/trades`

**功能描述**: 获取指定交易对的最新成交记录

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| instId | string | 是 | 交易对ID | BTC-USDT |
| limit | string | 否 | 返回结果的数量 | 100 (默认), 最大500 |

**请求示例**:
```http
GET /api/okx/market/trades?instId=BTC-USDT&limit=50
```

**响应格式**:
```json
{
  "code": "0",
  "msg": "",
  "data": [
    {
      "instId": "BTC-USDT",
      "tradeId": "123456789",
      "px": "50500",
      "sz": "0.1",
      "side": "buy",
      "ts": "1640995200000"
    }
  ]
}
```

## 🔌 WebSocket 接口

### 连接信息

**WebSocket URL**: `ws://localhost:8000/ws`

**连接参数**:
```javascript
const ws = new WebSocket('ws://localhost:8000/ws')
```

### 订阅K线数据

**订阅消息格式**:
```json
{
  "op": "subscribe",
  "args": [
    {
      "channel": "candle1m",
      "instId": "BTC-USDT"
    }
  ]
}
```

**支持的K线频道**:
- `candle1m` - 1分钟K线
- `candle5m` - 5分钟K线
- `candle15m` - 15分钟K线
- `candle30m` - 30分钟K线
- `candle1h` - 1小时K线
- `candle4h` - 4小时K线
- `candle1d` - 日K线

**推送数据格式**:
```json
{
  "arg": {
    "channel": "candle1m",
    "instId": "BTC-USDT"
  },
  "data": [
    [
      "1640995200000",
      "50000",
      "51000",
      "49000",
      "50500",
      "1000",
      "50000000",
      "50250",
      "1"
    ]
  ]
}
```

### 订阅实时行情

**订阅消息格式**:
```json
{
  "op": "subscribe",
  "args": [
    {
      "channel": "tickers",
      "instId": "BTC-USDT"
    }
  ]
}
```

**推送数据格式**:
```json
{
  "arg": {
    "channel": "tickers",
    "instId": "BTC-USDT"
  },
  "data": [
    {
      "instType": "SPOT",
      "instId": "BTC-USDT",
      "last": "50500",
      "lastSz": "0.1",
      "askPx": "50510",
      "askSz": "1.2",
      "bidPx": "50490",
      "bidSz": "1.5",
      "open24h": "49800",
      "high24h": "51200",
      "low24h": "49500",
      "vol24h": "12345.67",
      "volCcy24h": "623456789.12",
      "ts": "1640995200000"
    }
  ]
}
```

### 取消订阅

**取消订阅消息格式**:
```json
{
  "op": "unsubscribe",
  "args": [
    {
      "channel": "candle1m",
      "instId": "BTC-USDT"
    }
  ]
}
```

## 🛠️ 前端集成示例

### 1. K线数据获取和更新

```javascript
import axios from 'axios'
import { ref, reactive } from 'vue'

/**
 * K线数据管理组合式函数
 */
export function useKlineData() {
  const klineData = ref([])
  const loading = ref(false)
  const error = ref(null)
  
  /**
   * 获取K线数据
   * @param {string} symbol - 交易对
   * @param {string} timeframe - 时间周期
   * @param {number} limit - 数据条数
   */
  const fetchKlineData = async (symbol, timeframe = '1m', limit = 300) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.get('/api/okx/market/candles', {
        params: {
          instId: symbol,
          bar: timeframe,
          limit: limit.toString()
        }
      })
      
      if (response.data.code === '0') {
        const rawData = response.data.data
        klineData.value = formatKlineData(rawData)
      } else {
        throw new Error(response.data.msg || '获取K线数据失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('获取K线数据失败:', err)
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 格式化K线数据
   * @param {Array} rawData - 原始数据
   * @returns {Array} 格式化后的数据
   */
  const formatKlineData = (rawData) => {
    return rawData.map(item => ({
      timestamp: parseInt(item[0]),
      open: parseFloat(item[1]),
      high: parseFloat(item[2]),
      low: parseFloat(item[3]),
      close: parseFloat(item[4]),
      volume: parseFloat(item[5]),
      turnover: parseFloat(item[6])
    })).reverse() // 按时间正序排列
  }
  
  return {
    klineData,
    loading,
    error,
    fetchKlineData
  }
}
```

### 2. WebSocket 实时数据

```javascript
import { ref, onMounted, onUnmounted } from 'vue'

/**
 * WebSocket 实时数据管理
 */
export function useWebSocketData() {
  const ws = ref(null)
  const connected = ref(false)
  const realtimeData = ref({})
  
  /**
   * 连接WebSocket
   */
  const connect = () => {
    ws.value = new WebSocket('ws://localhost:8000/ws')
    
    ws.value.onopen = () => {
      connected.value = true
      console.log('WebSocket连接成功')
    }
    
    ws.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        handleMessage(data)
      } catch (err) {
        console.error('解析WebSocket消息失败:', err)
      }
    }
    
    ws.value.onclose = () => {
      connected.value = false
      console.log('WebSocket连接关闭')
      // 自动重连
      setTimeout(connect, 5000)
    }
    
    ws.value.onerror = (error) => {
      console.error('WebSocket错误:', error)
    }
  }
  
  /**
   * 处理WebSocket消息
   * @param {Object} data - 消息数据
   */
  const handleMessage = (data) => {
    if (data.arg && data.data) {
      const { channel, instId } = data.arg
      
      if (channel.startsWith('candle')) {
        // K线数据更新
        updateKlineData(instId, data.data[0])
      } else if (channel === 'tickers') {
        // 行情数据更新
        updateTickerData(instId, data.data[0])
      }
    }
  }
  
  /**
   * 订阅K线数据
   * @param {string} symbol - 交易对
   * @param {string} timeframe - 时间周期
   */
  const subscribeKline = (symbol, timeframe = '1m') => {
    if (ws.value && connected.value) {
      const message = {
        op: 'subscribe',
        args: [{
          channel: `candle${timeframe}`,
          instId: symbol
        }]
      }
      ws.value.send(JSON.stringify(message))
    }
  }
  
  /**
   * 订阅行情数据
   * @param {string} symbol - 交易对
   */
  const subscribeTicker = (symbol) => {
    if (ws.value && connected.value) {
      const message = {
        op: 'subscribe',
        args: [{
          channel: 'tickers',
          instId: symbol
        }]
      }
      ws.value.send(JSON.stringify(message))
    }
  }
  
  /**
   * 取消订阅
   * @param {string} channel - 频道
   * @param {string} symbol - 交易对
   */
  const unsubscribe = (channel, symbol) => {
    if (ws.value && connected.value) {
      const message = {
        op: 'unsubscribe',
        args: [{
          channel,
          instId: symbol
        }]
      }
      ws.value.send(JSON.stringify(message))
    }
  }
  
  /**
   * 断开连接
   */
  const disconnect = () => {
    if (ws.value) {
      ws.value.close()
      ws.value = null
    }
  }
  
  onMounted(() => {
    connect()
  })
  
  onUnmounted(() => {
    disconnect()
  })
  
  return {
    connected,
    realtimeData,
    subscribeKline,
    subscribeTicker,
    unsubscribe,
    disconnect
  }
}
```

### 3. 图表精度管理

```javascript
import { ref, computed } from 'vue'

/**
 * 图表精度管理
 */
export function useChartPrecision() {
  const instruments = ref([])
  const currentSymbol = ref('')
  
  /**
   * 获取交易对信息
   */
  const fetchInstruments = async () => {
    try {
      const response = await axios.get('/api/okx/public/instruments', {
        params: { instType: 'SPOT' }
      })
      
      if (response.data.code === '0') {
        instruments.value = response.data.data.map(item => ({
          ...item,
          pxPrecision: calculatePricePrecision(item.tickSz),
          szPrecision: calculateSizePrecision(item.lotSz)
        }))
      }
    } catch (err) {
      console.error('获取交易对信息失败:', err)
    }
  }
  
  /**
   * 当前交易对信息
   */
  const currentInstrument = computed(() => {
    return instruments.value.find(item => item.instId === currentSymbol.value)
  })
  
  /**
   * 当前价格精度
   */
  const pricePrecision = computed(() => {
    return currentInstrument.value?.pxPrecision || 2
  })
  
  /**
   * 当前数量精度
   */
  const sizePrecision = computed(() => {
    return currentInstrument.value?.szPrecision || 8
  })
  
  /**
   * 计算价格精度
   * @param {string} tickSz - 最小价格变动单位
   * @returns {number} 精度位数
   */
  const calculatePricePrecision = (tickSz) => {
    const tick = parseFloat(tickSz)
    if (tick >= 1) return 0
    return Math.abs(Math.floor(Math.log10(tick)))
  }
  
  /**
   * 计算数量精度
   * @param {string} lotSz - 最小下单量
   * @returns {number} 精度位数
   */
  const calculateSizePrecision = (lotSz) => {
    const lot = parseFloat(lotSz)
    if (lot >= 1) return 0
    return Math.abs(Math.floor(Math.log10(lot)))
  }
  
  /**
   * 格式化价格
   * @param {number} price - 价格
   * @param {number} precision - 精度
   * @returns {string} 格式化后的价格
   */
  const formatPrice = (price, precision = pricePrecision.value) => {
    return Number(price).toFixed(precision)
  }
  
  /**
   * 格式化数量
   * @param {number} size - 数量
   * @param {number} precision - 精度
   * @returns {string} 格式化后的数量
   */
  const formatSize = (size, precision = sizePrecision.value) => {
    return Number(size).toFixed(precision)
  }
  
  return {
    instruments,
    currentSymbol,
    currentInstrument,
    pricePrecision,
    sizePrecision,
    fetchInstruments,
    formatPrice,
    formatSize
  }
}
```

## 🔧 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 50001 | 请求参数错误 | 检查请求参数格式和必填项 |
| 50002 | 交易对不存在 | 确认交易对ID正确 |
| 50004 | 请求频率过高 | 降低请求频率，添加限流 |
| 50013 | 系统繁忙 | 稍后重试 |
| 50014 | 参数值错误 | 检查参数值范围和格式 |

### 错误处理示例

```javascript
/**
 * API错误处理
 * @param {Error} error - 错误对象
 * @returns {string} 用户友好的错误信息
 */
const handleApiError = (error) => {
  if (error.response) {
    const { status, data } = error.response
    
    switch (status) {
      case 400:
        return '请求参数错误，请检查输入'
      case 401:
        return '认证失败，请重新登录'
      case 403:
        return '权限不足'
      case 429:
        return '请求过于频繁，请稍后再试'
      case 500:
        return '服务器内部错误'
      default:
        return data?.msg || '网络请求失败'
    }
  } else if (error.request) {
    return '网络连接失败，请检查网络'
  } else {
    return error.message || '未知错误'
  }
}

/**
 * 带重试的API请求
 * @param {Function} apiCall - API调用函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试延迟
 */
const apiWithRetry = async (apiCall, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiCall()
    } catch (error) {
      if (i === maxRetries - 1) {
        throw error
      }
      
      // 指数退避
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
    }
  }
}
```

## 📈 性能优化建议

### 1. 请求优化

```javascript
// 使用防抖减少请求频率
const debouncedFetch = debounce(fetchKlineData, 300)

// 缓存交易对信息
const instrumentsCache = new Map()
const getCachedInstrument = (symbol) => {
  if (!instrumentsCache.has(symbol)) {
    fetchInstrumentInfo(symbol).then(data => {
      instrumentsCache.set(symbol, data)
    })
  }
  return instrumentsCache.get(symbol)
}

// 批量请求
const batchFetchTickers = async (symbols) => {
  const requests = symbols.map(symbol => 
    axios.get(`/api/okx/market/ticker?instId=${symbol}`)
  )
  return Promise.all(requests)
}
```

### 2. 数据处理优化

```javascript
// 使用 Web Worker 处理大量数据
const processDataInWorker = (data) => {
  return new Promise((resolve) => {
    const worker = new Worker('/workers/dataProcessor.js')
    worker.postMessage(data)
    worker.onmessage = (e) => {
      resolve(e.data)
      worker.terminate()
    }
  })
}

// 虚拟滚动处理大量K线数据
const useVirtualKlineData = (allData, visibleRange) => {
  return computed(() => {
    const start = Math.max(0, visibleRange.start - 50)
    const end = Math.min(allData.length, visibleRange.end + 50)
    return allData.slice(start, end)
  })
}
```

### 3. 内存管理

```javascript
// 限制数据量
const MAX_KLINE_DATA = 2000
const limitKlineData = (data) => {
  if (data.length > MAX_KLINE_DATA) {
    return data.slice(-MAX_KLINE_DATA)
  }
  return data
}

// 清理过期缓存
const cleanupCache = () => {
  const now = Date.now()
  const CACHE_TTL = 5 * 60 * 1000 // 5分钟
  
  for (const [key, value] of instrumentsCache.entries()) {
    if (now - value.timestamp > CACHE_TTL) {
      instrumentsCache.delete(key)
    }
  }
}
```

---

*本文档基于项目实际API接口编写，如有变更请及时更新。*