<template>
  <div class="drawing-tools">
    <!-- 绘图工具栏 -->
    <div class="tools-toolbar">
      <a-space>
        <!-- 选择工具 -->
        <a-tooltip title="选择工具">
          <a-button 
            :type="currentTool === 'select' ? 'primary' : 'default'"
            size="small"
            @click="setTool('select')"
          >
            <template #icon>
              <SelectOutlined />
            </template>
          </a-button>
        </a-tooltip>

        <!-- 趋势线 -->
        <a-tooltip title="趋势线">
          <a-button 
            :type="currentTool === 'trendline' ? 'primary' : 'default'"
            size="small"
            @click="setTool('trendline')"
          >
            <template #icon>
              <LineChartOutlined />
            </template>
          </a-button>
        </a-tooltip>

        <!-- 水平线 -->
        <a-tooltip title="水平线">
          <a-button 
            :type="currentTool === 'horizontal' ? 'primary' : 'default'"
            size="small"
            @click="setTool('horizontal')"
          >
            <template #icon>
              <MinusOutlined />
            </template>
          </a-button>
        </a-tooltip>

        <!-- 垂直线 -->
        <a-tooltip title="垂直线">
          <a-button 
            :type="currentTool === 'vertical' ? 'primary' : 'default'"
            size="small"
            @click="setTool('vertical')"
          >
            <template #icon>
              <ColumnHeightOutlined />
            </template>
          </a-button>
        </a-tooltip>

        <!-- 斐波那契回调 -->
        <a-tooltip title="斐波那契回调">
          <a-button 
            :type="currentTool === 'fibonacci' ? 'primary' : 'default'"
            size="small"
            @click="setTool('fibonacci')"
          >
            <template #icon>
              <FunctionOutlined />
            </template>
          </a-button>
        </a-tooltip>

        <!-- 矩形 -->
        <a-tooltip title="矩形">
          <a-button 
            :type="currentTool === 'rectangle' ? 'primary' : 'default'"
            size="small"
            @click="setTool('rectangle')"
          >
            <template #icon>
              <BorderOutlined />
            </template>
          </a-button>
        </a-tooltip>

        <!-- 文本标注 -->
        <a-tooltip title="文本标注">
          <a-button 
            :type="currentTool === 'text' ? 'primary' : 'default'"
            size="small"
            @click="setTool('text')"
          >
            <template #icon>
              <FontSizeOutlined />
            </template>
          </a-button>
        </a-tooltip>

        <a-divider type="vertical" />

        <!-- 清除所有 -->
        <a-tooltip title="清除所有绘图">
          <a-button 
            size="small"
            danger
            @click="clearAllDrawings"
          >
            <template #icon>
              <ClearOutlined />
            </template>
          </a-button>
        </a-tooltip>

        <!-- 保存图表 -->
        <a-tooltip title="保存图表">
          <a-button 
            size="small"
            @click="saveChart"
          >
            <template #icon>
              <SaveOutlined />
            </template>
          </a-button>
        </a-tooltip>

        <!-- 设置 -->
        <a-tooltip title="绘图设置">
          <a-button 
            size="small"
            @click="showSettings = true"
          >
            <template #icon>
              <SettingOutlined />
            </template>
          </a-button>
        </a-tooltip>
      </a-space>
    </div>

    <!-- 绘图设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="绘图设置"
      @ok="applySettings"
      @cancel="resetSettings"
    >
      <a-form layout="vertical">
        <a-form-item label="线条颜色">
          <a-input 
            v-model:value="drawingSettings.lineColor" 
            type="color"
          />
        </a-form-item>
        
        <a-form-item label="线条宽度">
          <a-slider 
            v-model:value="drawingSettings.lineWidth" 
            :min="1" 
            :max="5"
          />
        </a-form-item>
        
        <a-form-item label="线条样式">
          <a-select v-model:value="drawingSettings.lineStyle">
            <a-select-option value="solid">实线</a-select-option>
            <a-select-option value="dashed">虚线</a-select-option>
            <a-select-option value="dotted">点线</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="文本大小">
          <a-input-number 
            v-model:value="drawingSettings.fontSize" 
            :min="10" 
            :max="24"
            suffix="px"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 文本输入模态框 -->
    <a-modal
      v-model:open="showTextInput"
      title="添加文本标注"
      @ok="addTextAnnotation"
      @cancel="cancelTextInput"
    >
      <a-form layout="vertical">
        <a-form-item label="标注文本">
          <a-textarea 
            v-model:value="textInput" 
            placeholder="请输入标注文本"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, inject, onMounted, onUnmounted } from 'vue'
import {
  SelectOutlined,
  LineChartOutlined,
  MinusOutlined,
  ColumnHeightOutlined,
  FunctionOutlined,
  BorderOutlined,
  FontSizeOutlined,
  ClearOutlined,
  SaveOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// Props
const props = defineProps({
  chartInstance: {
    type: Object,
    default: null
  },
  theme: {
    type: String,
    default: 'light'
  }
})

// Emits
const emit = defineEmits(['drawing-added', 'drawing-removed', 'chart-saved'])

// 响应式数据
const currentTool = ref('select')
const showSettings = ref(false)
const showTextInput = ref(false)
const textInput = ref('')
const isDrawing = ref(false)
const startPoint = ref(null)
const currentDrawing = ref(null)
const textPosition = ref(null)

// 绘图设置
const drawingSettings = reactive({
  lineColor: '#1890ff',
  lineWidth: 2,
  lineStyle: 'solid',
  fontSize: 14
})

// 绘图数据存储
const drawings = ref([])

/**
 * 设置当前绘图工具
 * @param {string} tool - 工具名称
 */
const setTool = (tool) => {
  currentTool.value = tool
  if (props.chartInstance) {
    // 更新图表鼠标样式
    const chartDom = props.chartInstance.getDom()
    if (chartDom) {
      chartDom.style.cursor = tool === 'select' ? 'default' : 'crosshair'
    }
  }
}

/**
 * 开始绘图
 * @param {Object} params - 鼠标事件参数
 */
const startDrawing = (params) => {
  if (currentTool.value === 'select') return
  
  isDrawing.value = true
  startPoint.value = {
    x: params.offsetX,
    y: params.offsetY,
    dataIndex: params.dataIndex,
    value: params.value
  }
  
  if (currentTool.value === 'text') {
    textPosition.value = startPoint.value
    showTextInput.value = true
    return
  }
}

/**
 * 绘图过程中
 * @param {Object} params - 鼠标事件参数
 */
const onDrawing = (params) => {
  if (!isDrawing.value || currentTool.value === 'select' || currentTool.value === 'text') return
  
  const endPoint = {
    x: params.offsetX,
    y: params.offsetY,
    dataIndex: params.dataIndex,
    value: params.value
  }
  
  // 更新当前绘图预览
  updateDrawingPreview(startPoint.value, endPoint)
}

/**
 * 结束绘图
 * @param {Object} params - 鼠标事件参数
 */
const endDrawing = (params) => {
  if (!isDrawing.value || currentTool.value === 'select' || currentTool.value === 'text') return
  
  const endPoint = {
    x: params.offsetX,
    y: params.offsetY,
    dataIndex: params.dataIndex,
    value: params.value
  }
  
  // 创建绘图对象
  const drawing = createDrawing(startPoint.value, endPoint)
  if (drawing) {
    drawings.value.push(drawing)
    addDrawingToChart(drawing)
    emit('drawing-added', drawing)
  }
  
  isDrawing.value = false
  startPoint.value = null
  currentDrawing.value = null
}

/**
 * 创建绘图对象
 * @param {Object} start - 起始点
 * @param {Object} end - 结束点
 * @returns {Object} 绘图对象
 */
const createDrawing = (start, end) => {
  const id = Date.now() + Math.random()
  const baseDrawing = {
    id,
    type: currentTool.value,
    start,
    end,
    settings: { ...drawingSettings },
    timestamp: Date.now()
  }
  
  switch (currentTool.value) {
    case 'trendline':
      return {
        ...baseDrawing,
        name: '趋势线'
      }
    case 'horizontal':
      return {
        ...baseDrawing,
        name: '水平线',
        value: start.value
      }
    case 'vertical':
      return {
        ...baseDrawing,
        name: '垂直线',
        dataIndex: start.dataIndex
      }
    case 'fibonacci':
      return {
        ...baseDrawing,
        name: '斐波那契回调',
        levels: calculateFibonacciLevels(start.value, end.value)
      }
    case 'rectangle':
      return {
        ...baseDrawing,
        name: '矩形'
      }
    default:
      return null
  }
}

/**
 * 计算斐波那契回调水平
 * @param {number} high - 高点
 * @param {number} low - 低点
 * @returns {Array} 斐波那契水平数组
 */
const calculateFibonacciLevels = (high, low) => {
  const diff = high - low
  const levels = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1]
  
  return levels.map(level => ({
    level,
    value: high - (diff * level),
    label: `${(level * 100).toFixed(1)}%`
  }))
}

/**
 * 更新绘图预览
 * @param {Object} start - 起始点
 * @param {Object} end - 结束点
 */
const updateDrawingPreview = (start, end) => {
  if (!props.chartInstance) return
  
  // 清除之前的预览
  props.chartInstance.dispatchAction({
    type: 'brush',
    command: 'clear',
    areas: []
  })
  
  // 添加新的预览
  const previewDrawing = createDrawing(start, end)
  if (previewDrawing) {
    addDrawingToChart(previewDrawing, true)
  }
}

/**
 * 将绘图添加到图表
 * @param {Object} drawing - 绘图对象
 * @param {boolean} isPreview - 是否为预览
 */
const addDrawingToChart = (drawing, isPreview = false) => {
  if (!props.chartInstance) return
  
  const option = props.chartInstance.getOption()
  if (!option.graphic) {
    option.graphic = []
  }
  
  let graphicElement
  
  switch (drawing.type) {
    case 'trendline':
      graphicElement = createTrendlineGraphic(drawing, isPreview)
      break
    case 'horizontal':
      graphicElement = createHorizontalLineGraphic(drawing, isPreview)
      break
    case 'vertical':
      graphicElement = createVerticalLineGraphic(drawing, isPreview)
      break
    case 'fibonacci':
      graphicElement = createFibonacciGraphic(drawing, isPreview)
      break
    case 'rectangle':
      graphicElement = createRectangleGraphic(drawing, isPreview)
      break
  }
  
  if (graphicElement) {
    if (isPreview) {
      graphicElement.id = 'preview'
      graphicElement.z = 1000
    } else {
      graphicElement.id = drawing.id
    }
    
    option.graphic.push(graphicElement)
    props.chartInstance.setOption(option)
  }
}

/**
 * 创建趋势线图形元素
 * @param {Object} drawing - 绘图对象
 * @param {boolean} isPreview - 是否为预览
 * @returns {Object} 图形元素
 */
const createTrendlineGraphic = (drawing, isPreview) => {
  return {
    type: 'line',
    shape: {
      x1: drawing.start.x,
      y1: drawing.start.y,
      x2: drawing.end.x,
      y2: drawing.end.y
    },
    style: {
      stroke: isPreview ? '#ff4d4f' : drawing.settings.lineColor,
      lineWidth: drawing.settings.lineWidth,
      lineDash: drawing.settings.lineStyle === 'dashed' ? [5, 5] : 
                drawing.settings.lineStyle === 'dotted' ? [2, 2] : null,
      opacity: isPreview ? 0.6 : 1
    }
  }
}

/**
 * 创建水平线图形元素
 * @param {Object} drawing - 绘图对象
 * @param {boolean} isPreview - 是否为预览
 * @returns {Object} 图形元素
 */
const createHorizontalLineGraphic = (drawing, isPreview) => {
  return {
    type: 'line',
    shape: {
      x1: 0,
      y1: drawing.start.y,
      x2: '100%',
      y2: drawing.start.y
    },
    style: {
      stroke: isPreview ? '#ff4d4f' : drawing.settings.lineColor,
      lineWidth: drawing.settings.lineWidth,
      lineDash: drawing.settings.lineStyle === 'dashed' ? [5, 5] : 
                drawing.settings.lineStyle === 'dotted' ? [2, 2] : null,
      opacity: isPreview ? 0.6 : 1
    }
  }
}

/**
 * 创建垂直线图形元素
 * @param {Object} drawing - 绘图对象
 * @param {boolean} isPreview - 是否为预览
 * @returns {Object} 图形元素
 */
const createVerticalLineGraphic = (drawing, isPreview) => {
  return {
    type: 'line',
    shape: {
      x1: drawing.start.x,
      y1: 0,
      x2: drawing.start.x,
      y2: '100%'
    },
    style: {
      stroke: isPreview ? '#ff4d4f' : drawing.settings.lineColor,
      lineWidth: drawing.settings.lineWidth,
      lineDash: drawing.settings.lineStyle === 'dashed' ? [5, 5] : 
                drawing.settings.lineStyle === 'dotted' ? [2, 2] : null,
      opacity: isPreview ? 0.6 : 1
    }
  }
}

/**
 * 创建斐波那契图形元素
 * @param {Object} drawing - 绘图对象
 * @param {boolean} isPreview - 是否为预览
 * @returns {Array} 图形元素数组
 */
const createFibonacciGraphic = (drawing, isPreview) => {
  const graphics = []
  const colors = ['#ff4d4f', '#fa8c16', '#fadb14', '#52c41a', '#1890ff', '#722ed1', '#eb2f96']
  
  drawing.levels.forEach((level, index) => {
    const y = drawing.start.y + (drawing.end.y - drawing.start.y) * level.level
    
    graphics.push({
      type: 'line',
      shape: {
        x1: Math.min(drawing.start.x, drawing.end.x),
        y1: y,
        x2: Math.max(drawing.start.x, drawing.end.x),
        y2: y
      },
      style: {
        stroke: isPreview ? '#ff4d4f' : colors[index % colors.length],
        lineWidth: 1,
        lineDash: [3, 3],
        opacity: isPreview ? 0.6 : 0.8
      }
    })
    
    // 添加标签
    graphics.push({
      type: 'text',
      position: [Math.max(drawing.start.x, drawing.end.x) + 5, y],
      style: {
        text: `${level.label} (${level.value.toFixed(2)})`,
        fontSize: 12,
        fill: isPreview ? '#ff4d4f' : colors[index % colors.length],
        opacity: isPreview ? 0.6 : 1
      }
    })
  })
  
  return graphics
}

/**
 * 创建矩形图形元素
 * @param {Object} drawing - 绘图对象
 * @param {boolean} isPreview - 是否为预览
 * @returns {Object} 图形元素
 */
const createRectangleGraphic = (drawing, isPreview) => {
  return {
    type: 'rect',
    shape: {
      x: Math.min(drawing.start.x, drawing.end.x),
      y: Math.min(drawing.start.y, drawing.end.y),
      width: Math.abs(drawing.end.x - drawing.start.x),
      height: Math.abs(drawing.end.y - drawing.start.y)
    },
    style: {
      stroke: isPreview ? '#ff4d4f' : drawing.settings.lineColor,
      lineWidth: drawing.settings.lineWidth,
      fill: 'transparent',
      lineDash: drawing.settings.lineStyle === 'dashed' ? [5, 5] : 
                drawing.settings.lineStyle === 'dotted' ? [2, 2] : null,
      opacity: isPreview ? 0.6 : 1
    }
  }
}

/**
 * 添加文本标注
 */
const addTextAnnotation = () => {
  if (!textInput.value.trim() || !textPosition.value) {
    message.warning('请输入标注文本')
    return
  }
  
  const drawing = {
    id: Date.now() + Math.random(),
    type: 'text',
    name: '文本标注',
    position: textPosition.value,
    text: textInput.value,
    settings: { ...drawingSettings },
    timestamp: Date.now()
  }
  
  drawings.value.push(drawing)
  addTextToChart(drawing)
  emit('drawing-added', drawing)
  
  showTextInput.value = false
  textInput.value = ''
  textPosition.value = null
}

/**
 * 将文本添加到图表
 * @param {Object} drawing - 绘图对象
 */
const addTextToChart = (drawing) => {
  if (!props.chartInstance) return
  
  const option = props.chartInstance.getOption()
  if (!option.graphic) {
    option.graphic = []
  }
  
  option.graphic.push({
    id: drawing.id,
    type: 'text',
    position: [drawing.position.x, drawing.position.y],
    style: {
      text: drawing.text,
      fontSize: drawing.settings.fontSize,
      fill: drawing.settings.lineColor,
      backgroundColor: props.theme === 'dark' ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)',
      padding: [4, 8],
      borderRadius: 4
    }
  })
  
  props.chartInstance.setOption(option)
}

/**
 * 取消文本输入
 */
const cancelTextInput = () => {
  showTextInput.value = false
  textInput.value = ''
  textPosition.value = null
}

/**
 * 清除所有绘图
 */
const clearAllDrawings = () => {
  drawings.value = []
  
  if (props.chartInstance) {
    const option = props.chartInstance.getOption()
    option.graphic = []
    props.chartInstance.setOption(option)
  }
  
  message.success('已清除所有绘图')
}

/**
 * 保存图表
 */
const saveChart = () => {
  if (!props.chartInstance) {
    message.error('图表实例不存在')
    return
  }
  
  try {
    const dataURL = props.chartInstance.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: props.theme === 'dark' ? '#1e1e1e' : '#ffffff'
    })
    
    // 创建下载链接
    const link = document.createElement('a')
    link.download = `chart_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.png`
    link.href = dataURL
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    emit('chart-saved', {
      dataURL,
      drawings: drawings.value,
      timestamp: Date.now()
    })
    
    message.success('图表已保存')
  } catch (error) {
    console.error('保存图表失败:', error)
    message.error('保存图表失败')
  }
}

/**
 * 应用设置
 */
const applySettings = () => {
  showSettings.value = false
  message.success('设置已应用')
}

/**
 * 重置设置
 */
const resetSettings = () => {
  drawingSettings.lineColor = '#1890ff'
  drawingSettings.lineWidth = 2
  drawingSettings.lineStyle = 'solid'
  drawingSettings.fontSize = 14
  showSettings.value = false
}

/**
 * 绑定图表事件
 */
const bindChartEvents = () => {
  if (!props.chartInstance) return
  
  props.chartInstance.on('mousedown', startDrawing)
  props.chartInstance.on('mousemove', onDrawing)
  props.chartInstance.on('mouseup', endDrawing)
}

/**
 * 解绑图表事件
 */
const unbindChartEvents = () => {
  if (!props.chartInstance) return
  
  props.chartInstance.off('mousedown', startDrawing)
  props.chartInstance.off('mousemove', onDrawing)
  props.chartInstance.off('mouseup', endDrawing)
}

// 生命周期
onMounted(() => {
  bindChartEvents()
})

onUnmounted(() => {
  unbindChartEvents()
})

// 暴露方法
defineExpose({
  setTool,
  clearAllDrawings,
  saveChart,
  drawings
})
</script>

<style scoped>
.drawing-tools {
  margin-bottom: 16px;
}

.tools-toolbar {
  padding: 8px 16px;
  background: var(--bg-color, #fafafa);
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dark-theme .tools-toolbar {
  --bg-color: #1f1f1f;
  --border-color: #434343;
}

.tools-toolbar .ant-btn {
  border-radius: 4px;
}

.tools-toolbar .ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.tools-toolbar .ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}
</style>