/**
 * WebSocket监控和错误处理工具类
 * 提供性能监控、错误处理和日志记录功能
 */

import { WS_CONFIG } from '../config/webSocketConfig.js';

/**
 * WebSocket性能监控类
 */
export class WebSocketMonitor {
  constructor() {
    this.metrics = {
      connectionCount: 0,
      reconnectionCount: 0,
      messageCount: 0,
      errorCount: 0,
      latencyHistory: [],
      lastMessageTime: null,
      connectionStartTime: null
    };
    
    this.errorHistory = [];
    this.latencyWarnings = [];
  }

  /**
   * 记录连接建立
   */
  recordConnection() {
    this.metrics.connectionCount++;
    this.metrics.connectionStartTime = Date.now();
    this.log('info', 'WebSocket connection established');
  }

  /**
   * 记录重连
   */
  recordReconnection() {
    this.metrics.reconnectionCount++;
    this.log('warn', `WebSocket reconnection attempt #${this.metrics.reconnectionCount}`);
  }

  /**
   * 记录消息接收
   * @param {Object} message - 接收到的消息
   */
  recordMessage(message) {
    this.metrics.messageCount++;
    this.metrics.lastMessageTime = Date.now();
    
    // 计算延迟
    if (message.received_at) {
      const latency = Date.now() - message.received_at;
      this.metrics.latencyHistory.push({
        timestamp: Date.now(),
        latency
      });
      
      // 保持延迟历史记录在合理范围内
      if (this.metrics.latencyHistory.length > 100) {
        this.metrics.latencyHistory.shift();
      }
      
      // 检查延迟警告
      this.checkLatency(latency, message);
    }
  }

  /**
   * 记录错误
   * @param {Error|string} error - 错误信息
   * @param {string} context - 错误上下文
   */
  recordError(error, context = 'unknown') {
    this.metrics.errorCount++;
    
    const errorRecord = {
      timestamp: Date.now(),
      error: error instanceof Error ? error.message : error,
      context,
      stack: error instanceof Error ? error.stack : null
    };
    
    this.errorHistory.push(errorRecord);
    
    // 保持错误历史记录在合理范围内
    if (this.errorHistory.length > 50) {
      this.errorHistory.shift();
    }
    
    this.log('error', `WebSocket error in ${context}: ${errorRecord.error}`);
  }

  /**
   * 检查延迟并发出警告
   * @param {number} latency - 延迟时间（毫秒）
   * @param {Object} message - 消息对象
   */
  checkLatency(latency, message) {
    const { LATENCY_WARNING_THRESHOLD, LATENCY_ERROR_THRESHOLD } = WS_CONFIG.MONITORING;
    
    if (latency > LATENCY_ERROR_THRESHOLD) {
      this.log('error', `High latency detected: ${latency}ms for channel ${message.arg?.channel}`);
    } else if (latency > LATENCY_WARNING_THRESHOLD) {
      this.latencyWarnings.push({
        timestamp: Date.now(),
        latency,
        channel: message.arg?.channel
      });
      
      this.log('warn', `Latency warning: ${latency}ms for channel ${message.arg?.channel}`);
    }
  }

  /**
   * 获取性能统计信息
   * @returns {Object} 性能统计
   */
  getMetrics() {
    const now = Date.now();
    const uptime = this.metrics.connectionStartTime ? now - this.metrics.connectionStartTime : 0;
    
    // 计算平均延迟
    const avgLatency = this.metrics.latencyHistory.length > 0
      ? this.metrics.latencyHistory.reduce((sum, record) => sum + record.latency, 0) / this.metrics.latencyHistory.length
      : 0;
    
    // 计算最近的延迟
    const recentLatency = this.metrics.latencyHistory.length > 0
      ? this.metrics.latencyHistory[this.metrics.latencyHistory.length - 1].latency
      : 0;
    
    return {
      ...this.metrics,
      uptime,
      avgLatency: Math.round(avgLatency),
      recentLatency,
      errorRate: this.metrics.messageCount > 0 ? (this.metrics.errorCount / this.metrics.messageCount) * 100 : 0
    };
  }

  /**
   * 获取错误历史
   * @returns {Array} 错误历史记录
   */
  getErrorHistory() {
    return [...this.errorHistory];
  }

  /**
   * 重置监控数据
   */
  reset() {
    this.metrics = {
      connectionCount: 0,
      reconnectionCount: 0,
      messageCount: 0,
      errorCount: 0,
      latencyHistory: [],
      lastMessageTime: null,
      connectionStartTime: null
    };
    
    this.errorHistory = [];
    this.latencyWarnings = [];
  }

  /**
   * 统一日志记录
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   */
  log(level, message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [WebSocket-${level.toUpperCase()}] ${message}`;
    
    switch (level) {
      case 'error':
        console.error(logMessage);
        break;
      case 'warn':
        console.warn(logMessage);
        break;
      case 'info':
        console.info(logMessage);
        break;
      default:
        console.log(logMessage);
    }
  }
}

/**
 * WebSocket错误处理器
 */
export class WebSocketErrorHandler {
  constructor(monitor) {
    this.monitor = monitor;
  }

  /**
   * 处理WebSocket错误
   * @param {Error|Event} error - 错误对象
   * @param {string} context - 错误上下文
   * @returns {Object} 处理结果
   */
  handleError(error, context = 'unknown') {
    this.monitor.recordError(error, context);
    
    // 根据错误类型返回不同的处理策略
    if (error instanceof CloseEvent) {
      return this.handleCloseError(error);
    }
    
    if (error instanceof Error) {
      return this.handleGenericError(error);
    }
    
    return {
      shouldReconnect: true,
      delay: WS_CONFIG.RECONNECT.DELAY,
      message: 'Unknown error occurred'
    };
  }

  /**
   * 处理连接关闭错误
   * @param {CloseEvent} closeEvent - 关闭事件
   * @returns {Object} 处理结果
   */
  handleCloseError(closeEvent) {
    const { code, reason } = closeEvent;
    
    // 根据关闭代码决定是否重连
    const shouldReconnect = this.shouldReconnectOnClose(code);
    const errorMessage = WS_CONFIG.ERROR_CODES[code] || reason || 'Connection closed';
    
    return {
      shouldReconnect,
      delay: this.calculateReconnectDelay(),
      message: errorMessage,
      code
    };
  }

  /**
   * 处理通用错误
   * @param {Error} error - 错误对象
   * @returns {Object} 处理结果
   */
  handleGenericError(error) {
    return {
      shouldReconnect: true,
      delay: this.calculateReconnectDelay(),
      message: error.message || 'Generic WebSocket error'
    };
  }

  /**
   * 判断是否应该在连接关闭时重连
   * @param {number} code - 关闭代码
   * @returns {boolean} 是否应该重连
   */
  shouldReconnectOnClose(code) {
    // 不应该重连的情况
    const noReconnectCodes = [1000, 1001, 4001, 4002, 4004]; // 正常关闭、认证失败等
    return !noReconnectCodes.includes(code);
  }

  /**
   * 计算重连延迟（指数退避）
   * @returns {number} 延迟时间（毫秒）
   */
  calculateReconnectDelay() {
    const { DELAY, BACKOFF_MULTIPLIER, MAX_DELAY } = WS_CONFIG.RECONNECT;
    const attempts = this.monitor.metrics.reconnectionCount;
    
    const delay = Math.min(
      DELAY * Math.pow(BACKOFF_MULTIPLIER, attempts),
      MAX_DELAY
    );
    
    return delay;
  }
}

// 创建全局监控实例
export const globalWebSocketMonitor = new WebSocketMonitor();
export const globalErrorHandler = new WebSocketErrorHandler(globalWebSocketMonitor);