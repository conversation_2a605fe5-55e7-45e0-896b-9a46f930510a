<template>
  <div class="price-alerts">
    <a-row :gutter="24">
      <!-- 左侧：价格预警设置 -->
      <a-col :span="16">
        <a-card title="价格预警管理" style="margin-bottom: 24px;">
          <a-form
            :model="alertForm"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
            @finish="createAlert"
          >
            <a-form-item
              label="交易对"
              name="symbol"
              :rules="[{ required: true, message: '请选择交易对' }]"
            >
              <a-select v-model:value="alertForm.symbol" placeholder="选择交易对">
                <a-select-option value="BTC-USDT-SWAP">BTC-USDT-SWAP</a-select-option>
                <a-select-option value="ETH-USDT-SWAP">ETH-USDT-SWAP</a-select-option>
                <a-select-option value="SOL-USDT-SWAP">SOL-USDT-SWAP</a-select-option>
                <a-select-option value="DOGE-USDT-SWAP">DOGE-USDT-SWAP</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item
              label="预警类型"
              name="type"
              :rules="[{ required: true, message: '请选择预警类型' }]"
            >
              <a-select v-model:value="alertForm.type" placeholder="选择预警类型">
                <a-select-option value="price_above">价格突破</a-select-option>
                <a-select-option value="price_below">价格跌破</a-select-option>
                <a-select-option value="change_rate">涨跌幅度</a-select-option>
                <a-select-option value="volume_spike">成交量异常</a-select-option>
                <a-select-option value="technical">技术指标</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item
              v-if="alertForm.type === 'price_above' || alertForm.type === 'price_below'"
              label="目标价格"
              name="targetPrice"
              :rules="[{ required: true, message: '请输入目标价格' }]"
            >
              <a-input-number
                v-model:value="alertForm.targetPrice"
                placeholder="请输入价格"
                style="width: 100%"
                :min="0"
                :step="0.01"
              />
            </a-form-item>
            
            <a-form-item
              v-if="alertForm.type === 'change_rate'"
              label="涨跌幅度"
              name="changeRate"
              :rules="[{ required: true, message: '请输入涨跌幅度' }]"
            >
              <a-input-number
                v-model:value="alertForm.changeRate"
                placeholder="请输入百分比"
                style="width: 100%"
                :min="-50"
                :max="50"
                :step="0.1"
                addon-after="%"
              />
            </a-form-item>
            
            <a-form-item
              v-if="alertForm.type === 'technical'"
              label="技术指标"
              name="indicator"
            >
              <a-select v-model:value="alertForm.indicator" placeholder="选择技术指标">
                <a-select-option value="rsi_overbought">RSI超买(>70)</a-select-option>
                <a-select-option value="rsi_oversold">RSI超卖(<30)</a-select-option>
                <a-select-option value="macd_golden">MACD金叉</a-select-option>
                <a-select-option value="macd_death">MACD死叉</a-select-option>
                <a-select-option value="ma_cross">均线交叉</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item
              label="通知方式"
              name="notifyMethods"
            >
              <a-checkbox-group v-model:value="alertForm.notifyMethods">
                <a-checkbox value="browser">浏览器通知</a-checkbox>
                <a-checkbox value="email">邮件通知</a-checkbox>
                <a-checkbox value="webhook">Webhook</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
            
            <a-form-item
              label="备注"
              name="note"
            >
              <a-textarea
                v-model:value="alertForm.note"
                placeholder="可选的备注信息"
                :rows="2"
              />
            </a-form-item>
            
            <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
              <a-space>
                <a-button type="primary" html-type="submit" :loading="creating">
                  创建预警
                </a-button>
                <a-button @click="resetForm">重置</a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>
        
        <!-- 预警历史 -->
        <a-card title="预警列表">
          <a-table
            :dataSource="alerts"
            :columns="alertColumns"
            :pagination="{ pageSize: 10 }"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'type'">
                <a-tag>{{ getTypeText(record.type) }}</a-tag>
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button
                    v-if="record.status === 'active'"
                    size="small"
                    @click="toggleAlert(record)"
                  >
                    {{ record.enabled ? '禁用' : '启用' }}
                  </a-button>
                  <a-button
                    size="small"
                    danger
                    @click="deleteAlert(record.id)"
                  >
                    删除
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
      
      <!-- 右侧：实时监控 -->
      <a-col :span="8">
        <a-card title="实时监控" style="margin-bottom: 16px;">
          <div v-for="symbol in watchList" :key="symbol.symbol" class="price-monitor">
            <div class="symbol-header">
              <span class="symbol-name">{{ symbol.symbol }}</span>
              <span class="current-price" :style="{ color: symbol.change >= 0 ? '#52c41a' : '#ff4d4f' }">
                ${{ symbol.price }}
              </span>
            </div>
            <div class="price-change">
              <span :style="{ color: symbol.change >= 0 ? '#52c41a' : '#ff4d4f' }">
                {{ symbol.change >= 0 ? '+' : '' }}{{ symbol.change }}%
              </span>
              <span class="volume">Vol: {{ symbol.volume }}</span>
            </div>
            <div class="alerts-count" v-if="symbol.alertsCount > 0">
              <a-badge :count="symbol.alertsCount" />
              <span style="margin-left: 8px;">个活跃预警</span>
            </div>
          </div>
        </a-card>
        
        <!-- 最近触发的预警 -->
        <a-card title="最近触发">
          <a-timeline size="small">
            <a-timeline-item
              v-for="trigger in recentTriggers"
              :key="trigger.id"
              :color="trigger.type === 'success' ? 'green' : 'red'"
            >
              <div class="trigger-item">
                <div class="trigger-symbol">{{ trigger.symbol }}</div>
                <div class="trigger-message">{{ trigger.message }}</div>
                <div class="trigger-time">{{ trigger.time }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
        
        <!-- 预警统计 -->
        <a-card title="预警统计">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-statistic
                title="总预警数"
                :value="alertStats.total"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :span="12">
              <a-statistic
                title="活跃预警"
                :value="alertStats.active"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
          </a-row>
          <a-divider />
          <a-row :gutter="16">
            <a-col :span="12">
              <a-statistic
                title="今日触发"
                :value="alertStats.todayTriggered"
                :value-style="{ color: '#faad14' }"
              />
            </a-col>
            <a-col :span="12">
              <a-statistic
                title="成功率"
                :value="alertStats.successRate"
                suffix="%"
                :value-style="{ color: '#722ed1' }"
              />
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'

// 响应式数据
const creating = ref(false)
const alerts = ref([])
const watchList = ref([])
const recentTriggers = ref([])

// 预警表单
const alertForm = ref({
  symbol: '',
  type: '',
  targetPrice: null,
  changeRate: null,
  indicator: '',
  notifyMethods: ['browser'],
  note: ''
})

// 预警统计
const alertStats = ref({
  total: 0,
  active: 0,
  todayTriggered: 0,
  successRate: 0
})

// 表格列定义
const alertColumns = [
  { title: '交易对', dataIndex: 'symbol', key: 'symbol' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '条件', dataIndex: 'condition', key: 'condition' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt' },
  { title: '操作', key: 'actions' }
]

// 创建预警
const createAlert = async () => {
  creating.value = true
  try {
    // 这里应该调用API创建预警
    const newAlert = {
      id: Date.now(),
      ...alertForm.value,
      status: 'active',
      enabled: true,
      createdAt: new Date().toLocaleString(),
      condition: getConditionText(alertForm.value)
    }
    
    alerts.value.unshift(newAlert)
    message.success('预警创建成功')
    resetForm()
    updateStats()
  } catch (error) {
    message.error('创建预警失败: ' + error.message)
  } finally {
    creating.value = false
  }
}

// 重置表单
const resetForm = () => {
  alertForm.value = {
    symbol: '',
    type: '',
    targetPrice: null,
    changeRate: null,
    indicator: '',
    notifyMethods: ['browser'],
    note: ''
  }
}

// 切换预警状态
const toggleAlert = (alert) => {
  alert.enabled = !alert.enabled
  message.success(`预警已${alert.enabled ? '启用' : '禁用'}`)
}

// 删除预警
const deleteAlert = (id) => {
  const index = alerts.value.findIndex(alert => alert.id === id)
  if (index > -1) {
    alerts.value.splice(index, 1)
    message.success('预警已删除')
    updateStats()
  }
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colors = {
    active: 'green',
    triggered: 'orange',
    expired: 'red',
    disabled: 'gray'
  }
  return colors[status] || 'default'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    active: '活跃',
    triggered: '已触发',
    expired: '已过期',
    disabled: '已禁用'
  }
  return texts[status] || status
}

// 获取类型文本
const getTypeText = (type) => {
  const texts = {
    price_above: '价格突破',
    price_below: '价格跌破',
    change_rate: '涨跌幅度',
    volume_spike: '成交量异常',
    technical: '技术指标'
  }
  return texts[type] || type
}

// 获取条件文本
const getConditionText = (alert) => {
  switch (alert.type) {
    case 'price_above':
      return `价格 > $${alert.targetPrice}`
    case 'price_below':
      return `价格 < $${alert.targetPrice}`
    case 'change_rate':
      return `涨跌幅 ${alert.changeRate > 0 ? '>' : '<'} ${alert.changeRate}%`
    case 'volume_spike':
      return '成交量异常增长'
    case 'technical':
      return alert.indicator
    default:
      return '-'
  }
}

// 更新统计数据
const updateStats = () => {
  alertStats.value.total = alerts.value.length
  alertStats.value.active = alerts.value.filter(alert => alert.status === 'active').length
  alertStats.value.todayTriggered = recentTriggers.value.filter(trigger => {
    const today = new Date().toDateString()
    return new Date(trigger.time).toDateString() === today
  }).length
  alertStats.value.successRate = alerts.value.length > 0 
    ? Math.round((alertStats.value.todayTriggered / alerts.value.length) * 100) 
    : 0
}

// 初始化数据
const initData = () => {
  // 模拟监控列表
  watchList.value = [
    {
      symbol: 'BTC-USDT-SWAP',
      price: '43250.50',
      change: 2.35,
      volume: '1.2B',
      alertsCount: 3
    },
    {
      symbol: 'ETH-USDT-SWAP',
      price: '2650.80',
      change: -1.25,
      volume: '850M',
      alertsCount: 1
    },
    {
      symbol: 'SOL-USDT-SWAP',
      price: '98.45',
      change: 5.67,
      volume: '320M',
      alertsCount: 2
    }
  ]
  
  // 模拟最近触发
  recentTriggers.value = [
    {
      id: 1,
      symbol: 'BTC-USDT-SWAP',
      message: '价格突破 $43000',
      time: '2024-01-15 14:30:25',
      type: 'success'
    },
    {
      id: 2,
      symbol: 'ETH-USDT-SWAP',
      message: 'RSI超买信号',
      time: '2024-01-15 13:45:10',
      type: 'warning'
    },
    {
      id: 3,
      symbol: 'SOL-USDT-SWAP',
      message: '成交量异常增长',
      time: '2024-01-15 12:20:15',
      type: 'success'
    }
  ]
  
  // 模拟现有预警
  alerts.value = [
    {
      id: 1,
      symbol: 'BTC-USDT-SWAP',
      type: 'price_above',
      targetPrice: 45000,
      condition: '价格 > $45000',
      status: 'active',
      enabled: true,
      createdAt: '2024-01-15 10:30:00'
    },
    {
      id: 2,
      symbol: 'ETH-USDT-SWAP',
      type: 'technical',
      indicator: 'rsi_oversold',
      condition: 'RSI超卖(<30)',
      status: 'triggered',
      enabled: true,
      createdAt: '2024-01-15 09:15:00'
    }
  ]
  
  updateStats()
}

// 页面加载时初始化
onMounted(() => {
  initData()
})
</script>

<style scoped>
.price-alerts {
  padding: 24px;
}

.price-monitor {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 12px;
}

.symbol-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.symbol-name {
  font-weight: bold;
  font-size: 14px;
}

.current-price {
  font-size: 16px;
  font-weight: bold;
}

.price-change {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.alerts-count {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.trigger-item {
  font-size: 12px;
}

.trigger-symbol {
  font-weight: bold;
  color: #1890ff;
}

.trigger-message {
  margin: 4px 0;
}

.trigger-time {
  color: #999;
}
</style>
