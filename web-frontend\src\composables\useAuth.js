import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import axios from 'axios'

/**
 * @description 用户认证管理的组合式函数
 * @returns {Object} 包含认证状态、登录、注册和权限管理的对象
 */
export function useAuth() {
  const router = useRouter()
  const userStore = useUserStore()
  
  // 认证状态
  const loading = ref(false)
  const error = ref(null)
  
  // 计算属性
  const isAuthenticated = computed(() => userStore.isAuthenticated)
  const currentUser = computed(() => userStore.user)
  const token = computed(() => userStore.token)
  
  /**
   * @description 用户登录
   * @param {Object} credentials - 登录凭据
   * @param {string} credentials.username - 用户名
   * @param {string} credentials.password - 密码
   * @returns {Promise<boolean>} 登录是否成功
   */
  const login = async (credentials) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.post('/api/user/login', {
        username: credentials.username,
        password: credentials.password
      })
      
      if (response.data.success) {
        const { token, user } = response.data.data
        
        // 保存用户信息到store
        userStore.setUser(user)
        userStore.setToken(token)
        
        // 设置axios默认请求头
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
        
        console.log('登录成功:', user.username)
        return true
      } else {
        error.value = response.data.message || '登录失败'
        return false
      }
    } catch (err) {
      console.error('登录错误:', err)
      error.value = err.response?.data?.message || '网络错误，请稍后重试'
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * @description 用户注册
   * @param {Object} userData - 注册数据
   * @param {string} userData.username - 用户名
   * @param {string} userData.password - 密码
   * @param {string} userData.email - 邮箱（可选）
   * @returns {Promise<boolean>} 注册是否成功
   */
  const register = async (userData) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.post('/api/user/register', {
        username: userData.username,
        password: userData.password,
        email: userData.email
      })
      
      if (response.data.success) {
        console.log('注册成功:', userData.username)
        return true
      } else {
        error.value = response.data.message || '注册失败'
        return false
      }
    } catch (err) {
      console.error('注册错误:', err)
      error.value = err.response?.data?.message || '网络错误，请稍后重试'
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * @description 用户登出
   */
  const logout = async () => {
    try {
      // 调用后端登出接口（如果有）
      await axios.post('/api/user/logout')
    } catch (err) {
      console.warn('登出接口调用失败:', err)
    }
    
    // 清除本地存储的用户信息
    userStore.clearUser()
    
    // 清除axios默认请求头
    delete axios.defaults.headers.common['Authorization']
    
    // 重定向到登录页
    router.push('/login')
    
    console.log('用户已登出')
  }
  
  /**
   * @description 刷新用户token
   * @returns {Promise<boolean>} 刷新是否成功
   */
  const refreshToken = async () => {
    try {
      const response = await axios.post('/api/user/refresh', {}, {
        headers: {
          'Authorization': `Bearer ${userStore.token}`
        }
      })
      
      if (response.data.success) {
        const { token } = response.data.data
        userStore.setToken(token)
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
        return true
      }
    } catch (err) {
      console.error('Token刷新失败:', err)
      // Token刷新失败，需要重新登录
      logout()
    }
    
    return false
  }
  
  /**
   * @description 获取用户信息
   * @returns {Promise<Object|null>} 用户信息
   */
  const fetchUserInfo = async () => {
    if (!userStore.token) {
      return null
    }
    
    try {
      const response = await axios.get('/api/user/profile')
      
      if (response.data.success) {
        const user = response.data.data
        userStore.setUser(user)
        return user
      }
    } catch (err) {
      console.error('获取用户信息失败:', err)
      if (err.response?.status === 401) {
        // Token无效，需要重新登录
        logout()
      }
    }
    
    return null
  }
  
  /**
   * @description 更新用户信息
   * @param {Object} userData - 要更新的用户数据
   * @returns {Promise<boolean>} 更新是否成功
   */
  const updateProfile = async (userData) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.put('/api/user/profile', userData)
      
      if (response.data.success) {
        const updatedUser = response.data.data
        userStore.setUser(updatedUser)
        console.log('用户信息更新成功')
        return true
      } else {
        error.value = response.data.message || '更新失败'
        return false
      }
    } catch (err) {
      console.error('更新用户信息错误:', err)
      error.value = err.response?.data?.message || '网络错误，请稍后重试'
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * @description 修改密码
   * @param {Object} passwordData - 密码数据
   * @param {string} passwordData.currentPassword - 当前密码
   * @param {string} passwordData.newPassword - 新密码
   * @returns {Promise<boolean>} 修改是否成功
   */
  const changePassword = async (passwordData) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.put('/api/user/password', {
        current_password: passwordData.currentPassword,
        new_password: passwordData.newPassword
      })
      
      if (response.data.success) {
        console.log('密码修改成功')
        return true
      } else {
        error.value = response.data.message || '密码修改失败'
        return false
      }
    } catch (err) {
      console.error('修改密码错误:', err)
      error.value = err.response?.data?.message || '网络错误，请稍后重试'
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * @description 配置OKX API
   * @param {Object} apiConfig - API配置
   * @param {string} apiConfig.api_key - API密钥
   * @param {string} apiConfig.api_secret - API密钥
   * @param {string} apiConfig.passphrase - 密码短语
   * @param {string} apiConfig.environment - 环境（demo/live）
   * @returns {Promise<boolean>} 配置是否成功
   */
  const configureOKXAPI = async (apiConfig) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.post('/api/user/okx-config', {
        api_key: apiConfig.api_key,
        api_secret: apiConfig.api_secret,
        passphrase: apiConfig.passphrase,
        environment: apiConfig.environment || 'demo'
      })
      
      if (response.data.success) {
        console.log('OKX API配置成功')
        // 更新用户信息
        await fetchUserInfo()
        return true
      } else {
        error.value = response.data.message || 'API配置失败'
        return false
      }
    } catch (err) {
      console.error('配置OKX API错误:', err)
      error.value = err.response?.data?.message || '网络错误，请稍后重试'
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * @description 检查权限
   * @param {string|Array} permission - 权限名称或权限数组
   * @returns {boolean} 是否有权限
   */
  const hasPermission = (permission) => {
    if (!isAuthenticated.value) {
      return false
    }
    
    const userPermissions = currentUser.value?.permissions || []
    
    if (Array.isArray(permission)) {
      return permission.some(p => userPermissions.includes(p))
    }
    
    return userPermissions.includes(permission)
  }
  
  /**
   * @description 检查角色
   * @param {string|Array} role - 角色名称或角色数组
   * @returns {boolean} 是否有角色
   */
  const hasRole = (role) => {
    if (!isAuthenticated.value) {
      return false
    }
    
    const userRole = currentUser.value?.role
    
    if (Array.isArray(role)) {
      return role.includes(userRole)
    }
    
    return userRole === role
  }
  
  /**
   * @description 初始化认证状态
   * 从本地存储恢复用户信息和token
   */
  const initAuth = async () => {
    const savedToken = localStorage.getItem('auth_token')
    const savedUser = localStorage.getItem('user_info')
    
    if (savedToken && savedUser) {
      try {
        const user = JSON.parse(savedUser)
        userStore.setUser(user)
        userStore.setToken(savedToken)
        
        // 设置axios默认请求头
        axios.defaults.headers.common['Authorization'] = `Bearer ${savedToken}`
        
        // 验证token是否仍然有效
        await fetchUserInfo()
        
        console.log('认证状态已恢复:', user.username)
      } catch (err) {
        console.error('恢复认证状态失败:', err)
        // 清除无效的本地存储
        localStorage.removeItem('auth_token')
        localStorage.removeItem('user_info')
      }
    }
  }
  
  return {
    // 状态
    loading,
    error,
    isAuthenticated,
    currentUser,
    token,
    
    // 认证方法
    login,
    register,
    logout,
    refreshToken,
    
    // 用户信息管理
    fetchUserInfo,
    updateProfile,
    changePassword,
    configureOKXAPI,
    
    // 权限检查
    hasPermission,
    hasRole,
    
    // 初始化
    initAuth
  }
}

/**
 * @description 路由守卫相关的组合式函数
 * @returns {Object} 包含路由守卫方法的对象
 */
export function useAuthGuard() {
  const { isAuthenticated, hasPermission, hasRole } = useAuth()
  const router = useRouter()
  
  /**
   * @description 需要认证的路由守卫
   * @param {Object} to - 目标路由
   * @param {Object} from - 来源路由
   * @param {Function} next - 下一步函数
   */
  const requireAuth = (to, from, next) => {
    if (isAuthenticated.value) {
      next()
    } else {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    }
  }
  
  /**
   * @description 需要特定权限的路由守卫
   * @param {string|Array} permission - 所需权限
   * @returns {Function} 路由守卫函数
   */
  const requirePermission = (permission) => {
    return (to, from, next) => {
      if (!isAuthenticated.value) {
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
      } else if (hasPermission(permission)) {
        next()
      } else {
        next('/403') // 无权限页面
      }
    }
  }
  
  /**
   * @description 需要特定角色的路由守卫
   * @param {string|Array} role - 所需角色
   * @returns {Function} 路由守卫函数
   */
  const requireRole = (role) => {
    return (to, from, next) => {
      if (!isAuthenticated.value) {
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
      } else if (hasRole(role)) {
        next()
      } else {
        next('/403') // 无权限页面
      }
    }
  }
  
  /**
   * @description 游客路由守卫（已登录用户不能访问）
   * @param {Object} to - 目标路由
   * @param {Object} from - 来源路由
   * @param {Function} next - 下一步函数
   */
  const guestOnly = (to, from, next) => {
    if (isAuthenticated.value) {
      next('/dashboard') // 重定向到仪表板
    } else {
      next()
    }
  }
  
  return {
    requireAuth,
    requirePermission,
    requireRole,
    guestOnly
  }
}