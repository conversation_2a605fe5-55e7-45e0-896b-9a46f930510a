<template>
  <div ref="chartRef" style="width: 100%; height: 100%;"></div>
</template>

<script setup>
import { ref } from 'vue';

defineProps({
  isDarkTheme: {
    type: Boolean,
    default: false
  }
});

const chartRef = ref(null);
const chartInstance = ref(null);

/**
 * 获取图表DOM元素
 * @returns {HTMLElement} 图表容器元素
 */
const getChartElement = () => chartRef.value;

/**
 * 设置图表实例
 * @param {Object} instance - KLineChart实例
 */
const setChartInstance = (instance) => {
  chartInstance.value = instance;
};

/**
 * 获取图表实例
 * @returns {Object} KLineChart实例
 */
const getChartInstance = () => chartInstance.value;

// 暴露方法和属性给父组件
defineExpose({ 
  getChartElement, 
  setChartInstance, 
  getChartInstance,
  chartInstance: chartInstance
});
</script>