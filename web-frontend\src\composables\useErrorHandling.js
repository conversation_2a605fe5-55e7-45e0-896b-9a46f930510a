/**
 * 错误处理和数据验证 Composable
 * 提供统一的错误处理、数据验证和日志记录功能
 */
import { ref, computed } from 'vue'

// 错误类型枚举
export const ERROR_TYPES = {
  DATA_VALIDATION: 'data_validation',
  CALCULATION: 'calculation',
  NETWORK: 'network',
  UNKNOWN: 'unknown'
}

// 错误级别枚举
export const ERROR_LEVELS = {
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
  CRITICAL: 'critical'
}

export function useErrorHandling() {
  const errors = ref([])
  const isLoading = ref(false)
  const hasError = computed(() => errors.value.length > 0)
  const latestError = computed(() => errors.value[errors.value.length - 1] || null)

  /**
   * 添加错误记录
   * @param {string} message - 错误消息
   * @param {string} type - 错误类型
   * @param {string} level - 错误级别
   * @param {Error} originalError - 原始错误对象
   */
  const addError = (message, type = ERROR_TYPES.UNKNOWN, level = ERROR_LEVELS.ERROR, originalError = null) => {
    const error = {
      id: Date.now() + Math.random(),
      message,
      type,
      level,
      timestamp: new Date(),
      originalError: originalError?.message || null,
      stack: originalError?.stack || null
    }
    
    errors.value.push(error)
    
    // 控制台输出（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.group(`[${level.toUpperCase()}] ${type}`)
      console.error(message)
      if (originalError) {
        console.error('Original Error:', originalError)
      }
      console.groupEnd()
    }
    
    // 限制错误记录数量，避免内存泄漏
    if (errors.value.length > 50) {
      errors.value = errors.value.slice(-30)
    }
  }

  /**
   * 清除错误记录
   * @param {string} errorId - 错误ID，不传则清除所有
   */
  const clearErrors = (errorId = null) => {
    if (errorId) {
      errors.value = errors.value.filter(error => error.id !== errorId)
    } else {
      errors.value = []
    }
  }

  /**
   * 安全执行函数，自动捕获错误
   * @param {Function} fn - 要执行的函数
   * @param {string} context - 执行上下文描述
   * @param {*} fallbackValue - 出错时的回退值
   * @returns {*} 函数执行结果或回退值
   */
  const safeExecute = async (fn, context = '未知操作', fallbackValue = null) => {
    try {
      isLoading.value = true
      const result = await fn()
      return result
    } catch (error) {
      addError(
        `${context}执行失败: ${error.message}`,
        ERROR_TYPES.CALCULATION,
        ERROR_LEVELS.ERROR,
        error
      )
      return fallbackValue
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 验证数组数据
   * @param {Array} data - 要验证的数组
   * @param {string} name - 数据名称
   * @param {number} minLength - 最小长度
   * @returns {boolean} 验证结果
   */
  const validateArray = (data, name = '数据', minLength = 1) => {
    if (!Array.isArray(data)) {
      addError(
        `${name}不是有效的数组类型`,
        ERROR_TYPES.DATA_VALIDATION,
        ERROR_LEVELS.WARN
      )
      return false
    }
    
    if (data.length < minLength) {
      addError(
        `${name}长度不足，需要至少${minLength}个元素，当前${data.length}个`,
        ERROR_TYPES.DATA_VALIDATION,
        ERROR_LEVELS.WARN
      )
      return false
    }
    
    return true
  }

  /**
   * 验证数值
   * @param {*} value - 要验证的值
   * @param {string} name - 数值名称
   * @param {Object} options - 验证选项
   * @returns {boolean} 验证结果
   */
  const validateNumber = (value, name = '数值', options = {}) => {
    const { min, max, allowZero = true, allowNegative = true } = options
    
    if (typeof value !== 'number' || isNaN(value)) {
      addError(
        `${name}不是有效的数值: ${value}`,
        ERROR_TYPES.DATA_VALIDATION,
        ERROR_LEVELS.WARN
      )
      return false
    }
    
    if (!allowZero && value === 0) {
      addError(
        `${name}不能为零`,
        ERROR_TYPES.DATA_VALIDATION,
        ERROR_LEVELS.WARN
      )
      return false
    }
    
    if (!allowNegative && value < 0) {
      addError(
        `${name}不能为负数: ${value}`,
        ERROR_TYPES.DATA_VALIDATION,
        ERROR_LEVELS.WARN
      )
      return false
    }
    
    if (typeof min === 'number' && value < min) {
      addError(
        `${name}小于最小值${min}: ${value}`,
        ERROR_TYPES.DATA_VALIDATION,
        ERROR_LEVELS.WARN
      )
      return false
    }
    
    if (typeof max === 'number' && value > max) {
      addError(
        `${name}大于最大值${max}: ${value}`,
        ERROR_TYPES.DATA_VALIDATION,
        ERROR_LEVELS.WARN
      )
      return false
    }
    
    return true
  }

  /**
   * 验证K线数据格式
   * @param {Array} klineData - K线数据
   * @returns {boolean} 验证结果
   */
  const validateKlineData = (klineData) => {
    if (!validateArray(klineData, 'K线数据', 1)) {
      return false
    }
    
    // 检查数据格式
    const sample = klineData[0]
    
    // 支持两种数据格式：数组格式和对象格式
    if (Array.isArray(sample)) {
      // 数组格式：[timestamp, open, high, low, close, volume, ...]
      if (sample.length < 6) {
        addError(
          `K线数据格式错误，数组格式每个元素应包含至少6个字段 [timestamp, open, high, low, close, volume]。当前: ${sample.length} 个字段`,
          ERROR_TYPES.DATA_VALIDATION,
          ERROR_LEVELS.ERROR
        )
        return false
      }
    } else if (typeof sample === 'object' && sample !== null) {
      // 对象格式：{time, open, high, low, close, volume, ...}
      const requiredFields = ['time', 'open', 'high', 'low', 'close', 'volume']
      const missingFields = requiredFields.filter(field => !(field in sample))
      
      if (missingFields.length > 0) {
        addError(
          `K线数据格式错误，对象格式缺少必需字段: ${missingFields.join(', ')}`,
          ERROR_TYPES.DATA_VALIDATION,
          ERROR_LEVELS.ERROR
        )
        return false
      }
    } else {
      addError(
        'K线数据格式错误，每个元素应为数组或对象格式',
        ERROR_TYPES.DATA_VALIDATION,
        ERROR_LEVELS.ERROR
      )
      return false
    }
    
    // 验证数值字段 - 根据数据格式提取字段值
    let timestamp, open, high, low, close, volume
    
    if (Array.isArray(sample)) {
      // 数组格式：[timestamp, open, high, low, close, volume, ...]
      [timestamp, open, high, low, close, volume] = sample
    } else {
      // 对象格式：{time, open, high, low, close, volume, ...}
      timestamp = sample.time
      open = sample.open
      high = sample.high
      low = sample.low
      close = sample.close
      volume = sample.volume
    }
    
    if (!validateNumber(timestamp, '时间戳', { allowNegative: false })) return false
    if (!validateNumber(open, '开盘价', { allowNegative: false, allowZero: false })) return false
    if (!validateNumber(high, '最高价', { allowNegative: false, allowZero: false })) return false
    if (!validateNumber(low, '最低价', { allowNegative: false, allowZero: false })) return false
    if (!validateNumber(close, '收盘价', { allowNegative: false, allowZero: false })) return false
    if (!validateNumber(volume, '成交量', { allowNegative: false })) return false
    
    // 验证价格逻辑
    if (high < Math.max(open, close) || low > Math.min(open, close)) {
      addError(
        'K线数据逻辑错误：最高价应大于等于开盘价和收盘价，最低价应小于等于开盘价和收盘价',
        ERROR_TYPES.DATA_VALIDATION,
        ERROR_LEVELS.WARN
      )
      return false
    }
    
    return true
  }

  /**
   * 防抖函数
   * @param {Function} fn - 要防抖的函数
   * @param {number} delay - 延迟时间（毫秒）
   * @returns {Function} 防抖后的函数
   */
  const debounce = (fn, delay = 300) => {
    let timeoutId = null
    
    return function (...args) {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => fn.apply(this, args), delay)
    }
  }

  /**
   * 节流函数
   * @param {Function} fn - 要节流的函数
   * @param {number} interval - 间隔时间（毫秒）
   * @returns {Function} 节流后的函数
   */
  const throttle = (fn, interval = 1000) => {
    let lastTime = 0
    
    return function (...args) {
      const now = Date.now()
      if (now - lastTime >= interval) {
        lastTime = now
        return fn.apply(this, args)
      }
    }
  }

  /**
   * 重试机制
   * @param {Function} fn - 要重试的函数
   * @param {number} maxRetries - 最大重试次数
   * @param {number} delay - 重试间隔（毫秒）
   * @returns {Promise} 执行结果
   */
  const retry = async (fn, maxRetries = 3, delay = 1000) => {
    let lastError = null
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        
        if (i === maxRetries) {
          addError(
            `重试${maxRetries}次后仍然失败: ${error.message}`,
            ERROR_TYPES.NETWORK,
            ERROR_LEVELS.ERROR,
            error
          )
          throw error
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
      }
    }
    
    throw lastError
  }

  /**
   * 性能监控
   * @param {string} name - 操作名称
   * @param {Function} fn - 要监控的函数
   * @returns {*} 函数执行结果
   */
  const performanceMonitor = async (name, fn) => {
    const startTime = performance.now()
    
    try {
      const result = await fn()
      const endTime = performance.now()
      const duration = endTime - startTime
      
      if (duration > 1000) { // 超过1秒记录警告
        addError(
          `${name}执行时间过长: ${duration.toFixed(2)}ms`,
          ERROR_TYPES.UNKNOWN,
          ERROR_LEVELS.WARN
        )
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      addError(
        `${name}执行失败 (${duration.toFixed(2)}ms): ${error.message}`,
        ERROR_TYPES.CALCULATION,
        ERROR_LEVELS.ERROR,
        error
      )
      
      throw error
    }
  }

  return {
    // 状态
    errors,
    isLoading,
    hasError,
    latestError,
    
    // 错误管理
    addError,
    clearErrors,
    
    // 安全执行
    safeExecute,
    
    // 数据验证
    validateArray,
    validateNumber,
    validateKlineData,
    
    // 工具函数
    debounce,
    throttle,
    retry,
    performanceMonitor,
    
    // 常量
    ERROR_TYPES,
    ERROR_LEVELS
  }
}