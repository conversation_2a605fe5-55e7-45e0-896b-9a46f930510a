from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from typing import List, Dict, Any
from webapp.api.user_api import get_current_user
from webapp.db import get_db
from jose import jwt, JWTError
import json
import asyncio
import logging
from datetime import datetime, timedelta
import time
from webapp.config import settings
import websockets
import base64
import hmac
import hashlib
from sqlalchemy.orm import Session
from ..models import User

router = APIRouter()

# OKX签名算法


def okx_ws_sign(api_secret, timestamp, method, path, body):
    prehash = f"{timestamp}{method}{path}{body}"
    sign = base64.b64encode(
        hmac.new(api_secret.encode(), prehash.encode(),
                 hashlib.sha256).digest()
    ).decode()
    return sign

# 获取用户API信息


def get_user_api(user_id, db):
    user = db.query(User).filter(User.id == user_id).first()
    if not user or not user.api_key or not user.api_secret or not user.passphrase:
        return None, None, None
    return user.api_key, user.api_secret, user.passphrase


@router.websocket("/ws/okx/market")
async def ws_okx_market(websocket: WebSocket):
    """
    OKX市场数据WebSocket代理
    支持ticker和K线数据的实时推送，更新频率100-400毫秒
    """
    await websocket.accept()
    logging.info("Market WebSocket connected")

    # 等待前端发送订阅请求
    try:
        sub_request = await websocket.receive_text()
        sub_data = json.loads(sub_request)
        channels = sub_data.get("channels", [])

        if not channels:
            await websocket.close(code=4001, reason="No channels specified")
            return

        logging.info(f"Subscribing to channels: {channels}")

    except Exception as e:
        logging.error(f"Failed to parse subscription request: {e}")
        await websocket.close(code=4002, reason="Invalid subscription request")
        return

    # 连接到OKX公共WebSocket
    uri = "wss://ws.okx.com:8443/ws/v5/public"
    try:
        async with websockets.connect(uri) as okx_ws:
            # 订阅请求的频道
            sub_msg = {
                "op": "subscribe",
                "args": channels
            }
            await okx_ws.send(json.dumps(sub_msg))
            logging.info(f"Sent subscription to OKX: {sub_msg}")

            # 转发OKX数据到前端
            try:
                while True:
                    data = await okx_ws.recv()
                    # 解析并转发数据
                    okx_data = json.loads(data)

                    # 如果是数据推送（非确认消息）
                    if okx_data.get("data"):
                        # 添加时间戳用于延迟监控
                        okx_data["received_at"] = int(time.time() * 1000)
                        await websocket.send_text(json.dumps(okx_data))
                    else:
                        # 转发确认消息
                        await websocket.send_text(data)

            except websockets.exceptions.ConnectionClosed:
                logging.info("OKX WebSocket connection closed")
            except WebSocketDisconnect:
                logging.info("Client WebSocket disconnected")
            except Exception as e:
                logging.error(f"WebSocket error: {e}")

    except Exception as e:
        logging.error(f"Failed to connect to OKX WebSocket: {e}")
        await websocket.close(code=4003, reason="Failed to connect to OKX")
    finally:
        logging.info("Market WebSocket connection closed")


@router.websocket("/ws/okx/private")
async def ws_okx_private(websocket: WebSocket, token: str = None, db: Session = Depends(get_db)):
    await websocket.accept()
    # 1. 校验token，获取用户
    try:
        if not token:
            await websocket.close(code=4001)
            return
        payload = jwt.decode(token, settings.SECRET_KEY,
                             algorithms=[settings.ALGORITHM])
        user_id = payload.get("user_id")
        if not user_id:
            await websocket.close(code=4002)
            return
    except JWTError:
        await websocket.close(code=4003)
        return
    # 2. 获取用户API信息
    api_key, api_secret, passphrase = get_user_api(user_id, db)
    if not api_key or not api_secret or not passphrase:
        await websocket.close(code=4004)
        return
    # 3. 等待前端发送订阅频道
    try:
        sub_msg = await websocket.receive_text()
        sub_req = json.loads(sub_msg)
        channels = sub_req.get("channels", [])
        if not channels:
            await websocket.close(code=4005)
            return
    except Exception:
        await websocket.close(code=4006)
        return
    # 4. 连接OKX私有WebSocket
    uri = "wss://ws.okx.com:8443/ws/v5/private"
    async with websockets.connect(uri) as okx_ws:
        # 登录
        timestamp = str(int(time.time()))
        sign = okx_ws_sign(api_secret, timestamp, "GET",
                           "/users/self/verify", "")
        login_msg = {
            "op": "login",
            "args": [{
                "apiKey": api_key,
                "passphrase": passphrase,
                "timestamp": timestamp,
                "sign": sign
            }]
        }
        await okx_ws.send(json.dumps(login_msg))
        login_resp = await okx_ws.recv()
        await websocket.send_text(login_resp)  # 可选：推送登录结果给前端
        # 订阅频道
        sub_send = {
            "op": "subscribe",
            "args": channels
        }
        await okx_ws.send(json.dumps(sub_send))
        # 推送OKX数据到前端
        try:
            while True:
                data = await okx_ws.recv()
                await websocket.send_text(data)
        except (WebSocketDisconnect, Exception):
            await websocket.close()
