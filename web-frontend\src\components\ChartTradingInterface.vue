<template>
  <div class="chart-trading-interface">
    <!-- 主要图表区域 -->
    <div class="chart-main-area">
      <!-- 图表工具栏 -->
      <div class="chart-toolbar">
        <div class="toolbar-left">
          <!-- 绘图工具 -->
          <a-button-group>
            <a-tooltip title="趋势线">
              <a-button 
                :type="drawingTool === 'trendline' ? 'primary' : 'default'"
                @click="setDrawingTool('trendline')"
              >
                <LineChartOutlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="水平线">
              <a-button 
                :type="drawingTool === 'horizontal' ? 'primary' : 'default'"
                @click="setDrawingTool('horizontal')"
              >
                <MinusOutlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="垂直线">
              <a-button 
                :type="drawingTool === 'vertical' ? 'primary' : 'default'"
                @click="setDrawingTool('vertical')"
              >
                <ColumnHeightOutlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="斐波那契">
              <a-button 
                :type="drawingTool === 'fibonacci' ? 'primary' : 'default'"
                @click="setDrawingTool('fibonacci')"
              >
                <FunctionOutlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="矩形">
              <a-button 
                :type="drawingTool === 'rectangle' ? 'primary' : 'default'"
                @click="setDrawingTool('rectangle')"
              >
                <BorderOutlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="文本标注">
              <a-button 
                :type="drawingTool === 'text' ? 'primary' : 'default'"
                @click="setDrawingTool('text')"
              >
                <FontSizeOutlined />
              </a-button>
            </a-tooltip>
          </a-button-group>
          
          <a-divider type="vertical" />
          
          <!-- 绘图操作 -->
          <a-button-group>
            <a-tooltip title="清除所有绘图">
              <a-button @click="clearAllDrawings">
                <ClearOutlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="保存图表">
              <a-button @click="saveChart">
                <SaveOutlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="绘图设置">
              <a-button @click="showDrawingSettings = true">
                <SettingOutlined />
              </a-button>
            </a-tooltip>
          </a-button-group>
        </div>
        
        <div class="toolbar-right">
          <!-- 图表控制 -->
          <a-button-group>
            <a-tooltip title="全屏">
              <a-button @click="toggleFullscreen">
                <FullscreenOutlined v-if="!isFullscreen" />
                <FullscreenExitOutlined v-else />
              </a-button>
            </a-tooltip>
            <a-tooltip title="截图">
              <a-button @click="takeScreenshot">
                <CameraOutlined />
              </a-button>
            </a-tooltip>
          </a-button-group>
        </div>
      </div>
      
      <!-- 图表容器 -->
      <div 
        ref="chartContainer" 
        class="chart-container"
        :class="{ 'fullscreen': isFullscreen }"
      >
        <!-- K线图 -->
        <div class="kline-chart">
          <ChartComponent
            ref="klineChart"
            :data="chartData"
            :options="klineOptions"
            :height="chartHeight"
            @chart-ready="onChartReady"
            @chart-click="onChartClick"
            @chart-brush="onChartBrush"
          />
        </div>
        
        <!-- 技术指标图表 -->
        <div class="indicator-charts" v-if="showIndicators">
          <div class="indicator-chart" v-for="indicator in activeIndicators" :key="indicator">
            <ChartComponent
              :ref="`${indicator}Chart`"
              :data="getIndicatorData(indicator)"
              :options="getIndicatorOptions(indicator)"
              :height="indicatorHeight"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 侧边面板 -->
    <div class="side-panel">
      <!-- 市场情绪面板 -->
      <div class="sentiment-panel">
        <div class="panel-header">
          <h3>市场情绪</h3>
          <a-button size="small" @click="refreshSentiment">
            <ReloadOutlined :spin="sentimentLoading" />
          </a-button>
        </div>
        
        <div class="sentiment-content" v-if="sentimentData">
          <!-- 恐慌贪婪指数 -->
          <div class="fear-greed-index">
            <div class="index-header">
              <span>恐慌贪婪指数</span>
              <span class="index-value" :style="{ color: sentimentData.fearGreedIndex.level.color }">
                {{ sentimentData.fearGreedIndex.index }}
              </span>
            </div>
            <a-progress 
              :percent="sentimentData.fearGreedIndex.index" 
              :stroke-color="sentimentData.fearGreedIndex.level.color"
              :show-info="false"
            />
            <div class="index-label">
              {{ sentimentData.fearGreedIndex.level.icon }} 
              {{ sentimentData.fearGreedIndex.level.label }}
            </div>
          </div>
          
          <!-- 情绪指标 -->
          <div class="sentiment-indicators">
            <div 
              class="indicator-item" 
              v-for="(indicator, key) in sentimentData.indicators" 
              :key="key"
            >
              <div class="indicator-name">{{ getIndicatorName(key) }}</div>
              <div class="indicator-value">
                <span :class="`signal-${indicator.signal}`">
                  {{ formatIndicatorValue(key, indicator.value) }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- 市场阶段 -->
          <div class="market-phase">
            <div class="phase-title">市场阶段</div>
            <div class="phase-content">
              <div class="phase-name">{{ sentimentData.marketPhase.label }}</div>
              <div class="phase-description">{{ sentimentData.marketPhase.description }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 智能预警面板 -->
      <div class="alerts-panel">
        <div class="panel-header">
          <h3>智能预警</h3>
          <div class="alert-controls">
            <a-badge :count="activeAlerts.length" :offset="[10, 0]">
              <a-button 
                size="small" 
                :type="isMonitoring ? 'primary' : 'default'"
                @click="toggleMonitoring"
              >
                {{ isMonitoring ? '停止' : '启动' }}
              </a-button>
            </a-badge>
            <a-button size="small" @click="showAlertSettings = true">
              <SettingOutlined />
            </a-button>
          </div>
        </div>
        
        <div class="alerts-content">
          <div class="alert-item" v-for="alert in recentAlerts" :key="alert.id">
            <div class="alert-header">
              <span class="alert-icon">{{ alert.type.icon }}</span>
              <span class="alert-title">{{ alert.type.name }}</span>
              <span class="alert-time">{{ formatTime(alert.timestamp) }}</span>
            </div>
            <div class="alert-message">{{ alert.message }}</div>
            <div class="alert-actions">
              <a-button size="small" @click="acknowledgeAlert(alert.id)" v-if="!alert.acknowledged">
                确认
              </a-button>
              <a-button size="small" @click="dismissAlert(alert.id)">
                关闭
              </a-button>
            </div>
          </div>
          
          <div class="no-alerts" v-if="activeAlerts.length === 0">
            <EmptyIcon />
            <p>暂无预警</p>
          </div>
        </div>
      </div>
      
      <!-- 形态识别面板 -->
      <div class="patterns-panel">
        <div class="panel-header">
          <h3>形态识别</h3>
          <a-button size="small" @click="analyzePatterns">
            <SearchOutlined :spin="patternLoading" />
          </a-button>
        </div>
        
        <div class="patterns-content">
          <div class="pattern-item" v-for="pattern in detectedPatterns" :key="pattern.id">
            <div class="pattern-header">
              <span class="pattern-name">{{ pattern.name }}</span>
              <span class="pattern-confidence">{{ (pattern.confidence * 100).toFixed(0) }}%</span>
            </div>
            <div class="pattern-signal" :class="`signal-${pattern.signal}`">
              {{ getPatternSignalText(pattern.signal) }}
            </div>
            <div class="pattern-description">{{ pattern.description }}</div>
          </div>
          
          <div class="no-patterns" v-if="detectedPatterns.length === 0">
            <EmptyIcon />
            <p>未检测到形态</p>
          </div>
        </div>
      </div>
      
      <!-- 快速交易面板 -->
      <div class="quick-trade-panel">
        <div class="panel-header">
          <h3>快速交易</h3>
        </div>
        
        <div class="trade-content">
          <!-- 当前价格 -->
          <div class="current-price">
            <div class="price-value" :class="priceChangeClass">
              {{ currentPrice }}
            </div>
            <div class="price-change" :class="priceChangeClass">
              {{ priceChange >= 0 ? '+' : '' }}{{ priceChange.toFixed(2) }}%
            </div>
          </div>
          
          <!-- 交易按钮 -->
          <div class="trade-buttons">
            <a-button type="primary" danger block @click="showTradeModal('sell')">
              卖出
            </a-button>
            <a-button type="primary" block @click="showTradeModal('buy')">
              买入
            </a-button>
          </div>
          
          <!-- 风险指标 -->
          <div class="risk-indicators">
            <div class="risk-item">
              <span>波动率</span>
              <span>{{ (volatility * 100).toFixed(2) }}%</span>
            </div>
            <div class="risk-item">
              <span>风险等级</span>
              <span :class="`risk-${riskLevel}`">{{ getRiskLevelText(riskLevel) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 绘图设置模态框 -->
    <DrawingSettingsModal 
      v-model:visible="showDrawingSettings"
      :settings="drawingSettings"
      @update="updateDrawingSettings"
    />
    
    <!-- 预警设置模态框 -->
    <AlertSettingsModal 
      v-model:visible="showAlertSettings"
      :settings="alertSettings"
      @update="updateAlertSettings"
    />
    
    <!-- 交易模态框 -->
    <TradeModal 
      v-model:visible="showTradeModalVisible"
      :type="tradeType"
      :current-price="currentPrice"
      @submit="handleTrade"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  LineChartOutlined,
  MinusOutlined,
  ColumnHeightOutlined,
  FunctionOutlined,
  BorderOutlined,
  FontSizeOutlined,
  ClearOutlined,
  SaveOutlined,
  SettingOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  CameraOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'

import ChartComponent from './ChartComponent.vue'
import DrawingSettingsModal from './DrawingSettingsModal.vue'
import AlertSettingsModal from './AlertSettingsModal.vue'
import TradeModal from './TradeModal.vue'
import EmptyIcon from './EmptyIcon.vue'

import { useDrawingTools } from '@/composables/useDrawingTools'
import { usePatternRecognition } from '@/composables/usePatternRecognition'
import { useMarketSentiment } from '@/composables/useMarketSentiment'
import { useSmartAlerts } from '@/composables/useSmartAlerts'
import { useAdvancedCharts } from '@/composables/useAdvancedCharts'
import { useChartStore } from '@/stores/chartStore'

// Props
const props = defineProps({
  symbol: {
    type: String,
    default: 'BTC-USDT'
  },
  interval: {
    type: String,
    default: '1h'
  }
})

// Emits
const emit = defineEmits(['trade', 'alert', 'pattern-detected'])

// Store
const chartStore = useChartStore()

// Composables
const {
  drawingTool,
  drawingSettings,
  setDrawingTool,
  clearAllDrawings,
  saveChart,
  updateDrawingSettings
} = useDrawingTools()

const {
  analyzePatterns: analyzeChartPatterns,
  detectedPatterns,
  isAnalyzing: patternLoading
} = usePatternRecognition()

const {
  sentimentData,
  isAnalyzing: sentimentLoading,
  analyzeSentiment
} = useMarketSentiment()

const {
  activeAlerts,
  isMonitoring,
  alertSettings,
  startMonitoring,
  stopMonitoring,
  analyzeAndGenerateAlerts,
  acknowledgeAlert,
  dismissAlert,
  updateAlertSettings
} = useSmartAlerts()

const {
  chartInstances,
  initializeChart,
  updateChart
} = useAdvancedCharts()

// 响应式数据
const chartContainer = ref(null)
const klineChart = ref(null)
const isFullscreen = ref(false)
const showDrawingSettings = ref(false)
const showAlertSettings = ref(false)
const showTradeModalVisible = ref(false)
const tradeType = ref('buy')

// 图表数据
const chartData = ref([])
const technicalIndicators = ref({})
const currentPrice = ref(0)
const priceChange = ref(0)
const volatility = ref(0.02)
const riskLevel = ref('medium')

// 图表配置
const chartHeight = ref(400)
const indicatorHeight = ref(150)
const showIndicators = ref(true)
const activeIndicators = ref([])

// K线图配置
const klineOptions = computed(() => ({
  animation: true,
  backgroundColor: chartStore.theme === 'dark' ? '#1f1f1f' : '#ffffff',
  grid: {
    left: '10%',
    right: '10%',
    top: '10%',
    bottom: '15%'
  },
  xAxis: {
    type: 'category',
    data: chartData.value.map(item => item.timestamp),
    axisLine: { lineStyle: { color: '#8392A5' } }
  },
  yAxis: {
    scale: true,
    axisLine: { lineStyle: { color: '#8392A5' } }
  },
  series: [
    {
      type: 'candlestick',
      data: chartData.value.map(item => [item.open, item.close, item.low, item.high]),
      itemStyle: {
        color: '#ef232a',
        color0: '#14b143',
        borderColor: '#ef232a',
        borderColor0: '#14b143'
      }
    }
  ],
  toolbox: {
    feature: {
      brush: {
        type: ['lineX', 'lineY', 'keep', 'clear']
      }
    }
  },
  brush: {
    xAxisIndex: 'all',
    brushLink: 'all',
    outOfBrush: {
      colorAlpha: 0.1
    }
  }
}))

// 计算属性
const recentAlerts = computed(() => {
  return activeAlerts.value.slice(0, 5)
})

const priceChangeClass = computed(() => {
  return priceChange.value >= 0 ? 'price-up' : 'price-down'
})

// 方法
const onChartReady = (chart) => {
  console.log('图表已准备就绪', chart)
  
  // 启用绘图工具
  enableDrawingTools(chart)
}

const onChartClick = (params) => {
  console.log('图表点击', params)
  
  // 处理绘图工具点击
  handleDrawingToolClick(params)
}

const onChartBrush = (params) => {
  console.log('图表刷选', params)
  
  // 处理区域选择
  handleBrushSelection(params)
}

const enableDrawingTools = (chart) => {
  // 启用绘图工具的交互功能
  chart.on('click', (params) => {
    if (drawingTool.value && drawingTool.value !== 'none') {
      handleDrawingToolClick(params)
    }
  })
}

const handleDrawingToolClick = (params) => {
  // 根据当前选择的绘图工具处理点击事件
  switch (drawingTool.value) {
    case 'trendline':
      // 处理趋势线绘制
      break
    case 'horizontal':
      // 处理水平线绘制
      break
    case 'vertical':
      // 处理垂直线绘制
      break
    case 'fibonacci':
      // 处理斐波那契绘制
      break
    case 'rectangle':
      // 处理矩形绘制
      break
    case 'text':
      // 处理文本标注
      break
  }
}

const handleBrushSelection = (params) => {
  // 处理区域选择，可以用于分析选定区域的数据
  if (params.areas && params.areas.length > 0) {
    const area = params.areas[0]
    console.log('选择区域:', area)
    
    // 分析选定区域的形态
    analyzeSelectedArea(area)
  }
}

const analyzeSelectedArea = (area) => {
  // 分析选定区域的数据
  const startIndex = Math.floor(area.coordRange[0][0])
  const endIndex = Math.floor(area.coordRange[0][1])
  
  if (startIndex >= 0 && endIndex < chartData.value.length) {
    const selectedData = chartData.value.slice(startIndex, endIndex + 1)
    
    // 对选定区域进行形态分析
    analyzeChartPatterns(selectedData).then(patterns => {
      if (patterns.length > 0) {
        message.success(`在选定区域检测到 ${patterns.length} 个形态`)
        emit('pattern-detected', patterns)
      }
    })
  }
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  
  if (isFullscreen.value) {
    chartHeight.value = window.innerHeight - 100
  } else {
    chartHeight.value = 400
  }
  
  // 重新调整图表大小
  setTimeout(() => {
    if (klineChart.value) {
      klineChart.value.resize()
    }
  }, 100)
}

const takeScreenshot = () => {
  if (klineChart.value) {
    const dataURL = klineChart.value.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    })
    
    // 下载截图
    const link = document.createElement('a')
    link.download = `chart-${props.symbol}-${Date.now()}.png`
    link.href = dataURL
    link.click()
    
    message.success('图表截图已保存')
  }
}

const refreshSentiment = async () => {
  if (chartData.value.length > 0) {
    await analyzeSentiment(
      chartData.value,
      chartData.value.map(item => item.volume),
      technicalIndicators.value
    )
  }
}

const toggleMonitoring = () => {
  if (isMonitoring.value) {
    stopMonitoring()
  } else {
    startMonitoring()
  }
}

const analyzePatterns = async () => {
  if (chartData.value.length > 0) {
    await analyzeChartPatterns(chartData.value)
  }
}

const showTradeModal = (type) => {
  tradeType.value = type
  showTradeModalVisible.value = true
}

const handleTrade = (tradeData) => {
  emit('trade', tradeData)
  showTradeModalVisible.value = false
}

// 获取指标数据
const getIndicatorData = (indicator) => {
  return technicalIndicators.value[indicator] || []
}

// 获取指标配置
const getIndicatorOptions = (indicator) => {
  const baseOptions = {
    animation: false,
    backgroundColor: chartStore.theme === 'dark' ? '#1f1f1f' : '#ffffff',
    grid: {
      left: '10%',
      right: '10%',
      top: '10%',
      bottom: '15%'
    },
    xAxis: {
      type: 'category',
      data: chartData.value.map(item => item.timestamp),
      axisLine: { lineStyle: { color: '#8392A5' } }
    },
    yAxis: {
      axisLine: { lineStyle: { color: '#8392A5' } }
    }
  }
  
  switch (indicator) {
    case 'volume':
      return {
        ...baseOptions,
        series: [{
          type: 'bar',
          data: getIndicatorData(indicator),
          itemStyle: { color: '#1890ff' }
        }]
      }
    case 'rsi':
      return {
        ...baseOptions,
        yAxis: { ...baseOptions.yAxis, min: 0, max: 100 },
        series: [{
          type: 'line',
          data: getIndicatorData(indicator),
          lineStyle: { color: '#722ed1' }
        }]
      }
    case 'macd':
      return {
        ...baseOptions,
        series: [
          {
            type: 'line',
            data: getIndicatorData(indicator).map(item => item.macd),
            lineStyle: { color: '#1890ff' }
          },
          {
            type: 'line',
            data: getIndicatorData(indicator).map(item => item.signal),
            lineStyle: { color: '#f5222d' }
          },
          {
            type: 'bar',
            data: getIndicatorData(indicator).map(item => item.histogram),
            itemStyle: { color: '#52c41a' }
          }
        ]
      }
    default:
      return baseOptions
  }
}

// 格式化函数
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const getIndicatorName = (key) => {
  const names = {
    rsi: 'RSI',
    macd: 'MACD',
    bollinger: '布林带',
    volume: '成交量',
    priceAction: '价格行为',
    volatility: '波动率',
    momentum: '动量'
  }
  return names[key] || key
}

const formatIndicatorValue = (key, value) => {
  switch (key) {
    case 'rsi':
      return value.toFixed(2)
    case 'volume':
      return (value * 100).toFixed(0) + '%'
    case 'priceAction':
    case 'volatility':
    case 'momentum':
      return (value * 100).toFixed(2) + '%'
    default:
      return value.toFixed(4)
  }
}

const getPatternSignalText = (signal) => {
  const signals = {
    bullish: '看涨',
    bearish: '看跌',
    neutral: '中性'
  }
  return signals[signal] || signal
}

const getRiskLevelText = (level) => {
  const levels = {
    low: '低',
    medium: '中',
    high: '高'
  }
  return levels[level] || level
}

// 监听数据变化
watch(() => chartStore.chartData, (newData) => {
  if (newData && newData.length > 0) {
    chartData.value = newData
    currentPrice.value = newData[newData.length - 1].close
    
    // 计算价格变化
    if (newData.length >= 2) {
      const prevPrice = newData[newData.length - 2].close
      priceChange.value = ((currentPrice.value - prevPrice) / prevPrice) * 100
    }
    
    // 自动分析
    autoAnalyze()
  }
}, { immediate: true })

watch(() => chartStore.technicalIndicators, (newIndicators) => {
  technicalIndicators.value = newIndicators
}, { immediate: true })

// 自动分析
const autoAnalyze = async () => {
  if (chartData.value.length < 20) return
  
  try {
    // 分析市场情绪
    await refreshSentiment()
    
    // 分析形态
    await analyzePatterns()
    
    // 生成预警
    if (isMonitoring.value) {
      const alerts = analyzeAndGenerateAlerts(
        {
          klineData: chartData.value,
          volumeData: chartData.value.map(item => item.volume),
          currentPrice: currentPrice.value
        },
        technicalIndicators.value,
        sentimentData.value,
        detectedPatterns.value
      )
      
      if (alerts.length > 0) {
        emit('alert', alerts)
      }
    }
  } catch (error) {
    console.error('自动分析失败:', error)
  }
}

// 生命周期
onMounted(() => {
  // 初始化图表
  if (chartData.value.length > 0) {
    autoAnalyze()
  }
  
  // 启动预警监控
  startMonitoring()
})

onUnmounted(() => {
  // 停止预警监控
  stopMonitoring()
})
</script>

<style scoped>
.chart-trading-interface {
  display: flex;
  height: 100vh;
  background: var(--bg-color);
}

.chart-main-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chart-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-color-light);
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.chart-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: var(--bg-color);
}

.kline-chart {
  flex: 1;
  min-height: 400px;
}

.indicator-charts {
  display: flex;
  flex-direction: column;
}

.indicator-chart {
  height: 150px;
  border-top: 1px solid var(--border-color);
}

.side-panel {
  width: 320px;
  border-left: 1px solid var(--border-color);
  background: var(--bg-color-light);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sentiment-panel,
.alerts-panel,
.patterns-panel,
.quick-trade-panel {
  border-bottom: 1px solid var(--border-color);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-color);
}

.panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.alert-controls {
  display: flex;
  gap: 8px;
}

.sentiment-content,
.alerts-content,
.patterns-content,
.trade-content {
  padding: 16px;
}

.fear-greed-index {
  margin-bottom: 16px;
}

.index-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.index-value {
  font-size: 18px;
  font-weight: bold;
}

.index-label {
  text-align: center;
  margin-top: 8px;
  font-size: 12px;
  color: var(--text-color-secondary);
}

.sentiment-indicators {
  margin-bottom: 16px;
}

.indicator-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 12px;
}

.indicator-name {
  color: var(--text-color-secondary);
}

.signal-bullish {
  color: #52c41a;
}

.signal-bearish {
  color: #ff4d4f;
}

.signal-neutral {
  color: var(--text-color-secondary);
}

.market-phase {
  padding: 12px;
  background: var(--bg-color);
  border-radius: 6px;
}

.phase-title {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin-bottom: 4px;
}

.phase-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.phase-description {
  font-size: 12px;
  color: var(--text-color-secondary);
}

.alert-item {
  padding: 8px;
  margin-bottom: 8px;
  background: var(--bg-color);
  border-radius: 6px;
  border-left: 3px solid var(--primary-color);
}

.alert-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.alert-icon {
  font-size: 16px;
}

.alert-title {
  font-weight: 600;
  flex: 1;
}

.alert-time {
  font-size: 12px;
  color: var(--text-color-secondary);
}

.alert-message {
  font-size: 12px;
  margin-bottom: 8px;
  color: var(--text-color-secondary);
}

.alert-actions {
  display: flex;
  gap: 8px;
}

.pattern-item {
  padding: 8px;
  margin-bottom: 8px;
  background: var(--bg-color);
  border-radius: 6px;
}

.pattern-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.pattern-name {
  font-weight: 600;
}

.pattern-confidence {
  font-size: 12px;
  color: var(--text-color-secondary);
}

.pattern-signal {
  font-size: 12px;
  margin-bottom: 4px;
}

.pattern-description {
  font-size: 12px;
  color: var(--text-color-secondary);
}

.current-price {
  text-align: center;
  margin-bottom: 16px;
}

.price-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.price-change {
  font-size: 14px;
}

.price-up {
  color: #52c41a;
}

.price-down {
  color: #ff4d4f;
}

.trade-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 16px;
}

.risk-indicators {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.risk-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.risk-low {
  color: #52c41a;
}

.risk-medium {
  color: #faad14;
}

.risk-high {
  color: #ff4d4f;
}

.no-alerts,
.no-patterns {
  text-align: center;
  padding: 20px;
  color: var(--text-color-secondary);
}

.no-alerts p,
.no-patterns p {
  margin: 8px 0 0 0;
  font-size: 12px;
}
</style>