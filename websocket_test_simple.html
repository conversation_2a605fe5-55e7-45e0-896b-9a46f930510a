<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.connecting { background-color: #fff3cd; color: #856404; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket连接测试</h1>
        
        <div id="status" class="status disconnected">状态: 未连接</div>
        
        <div>
            <button id="connectBtn" onclick="connectWebSocket()">连接</button>
            <button id="disconnectBtn" onclick="disconnectWebSocket()" disabled>断开连接</button>
            <button id="subscribeBtn" onclick="subscribeToMarket()" disabled>订阅市场数据</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <h3>连接信息</h3>
        <p><strong>WebSocket URL:</strong> <span id="wsUrl">ws://localhost:8080/api/okx/market</span></p>
        <p><strong>连接状态:</strong> <span id="readyState">-</span></p>
        <p><strong>消息计数:</strong> <span id="messageCount">0</span></p>
        
        <h3>消息日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        let socket = null;
        let messageCount = 0;
        const wsUrl = 'ws://localhost:8080/api/okx/market';
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = `状态: ${status}`;
            statusDiv.className = `status ${className}`;
        }
        
        function updateReadyState() {
            const readyStateDiv = document.getElementById('readyState');
            if (socket) {
                const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
                readyStateDiv.textContent = `${socket.readyState} (${states[socket.readyState]})`;
            } else {
                readyStateDiv.textContent = 'null';
            }
        }
        
        function updateMessageCount() {
            document.getElementById('messageCount').textContent = messageCount;
        }
        
        function updateUI(connected) {
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
            document.getElementById('subscribeBtn').disabled = !connected;
        }
        
        function connectWebSocket() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                log('WebSocket已经连接', 'info');
                return;
            }
            
            log(`正在连接到: ${wsUrl}`, 'info');
            updateStatus('正在连接...', 'connecting');
            
            try {
                socket = new WebSocket(wsUrl);
                
                socket.onopen = function(event) {
                    log('✅ WebSocket连接成功建立', 'success');
                    updateStatus('已连接', 'connected');
                    updateUI(true);
                    updateReadyState();
                };
                
                socket.onmessage = function(event) {
                    messageCount++;
                    updateMessageCount();
                    
                    try {
                        const data = JSON.parse(event.data);
                        log(`📨 收到消息 #${messageCount}: ${JSON.stringify(data, null, 2)}`, 'info');
                    } catch (e) {
                        log(`📨 收到消息 #${messageCount}: ${event.data}`, 'info');
                    }
                };
                
                socket.onclose = function(event) {
                    log(`❌ 连接关闭: code=${event.code}, reason=${event.reason}`, 'error');
                    updateStatus('连接已关闭', 'disconnected');
                    updateUI(false);
                    updateReadyState();
                };
                
                socket.onerror = function(error) {
                    log(`💥 连接错误: ${error}`, 'error');
                    updateStatus('连接错误', 'disconnected');
                    updateUI(false);
                    updateReadyState();
                };
                
            } catch (error) {
                log(`💥 创建WebSocket失败: ${error}`, 'error');
                updateStatus('连接失败', 'disconnected');
            }
        }
        
        function disconnectWebSocket() {
            if (socket) {
                socket.close();
                log('🔌 主动断开连接', 'info');
            }
        }
        
        function subscribeToMarket() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket未连接', 'error');
                return;
            }
            
            const subscriptionRequest = {
                op: "subscribe",
                args: [
                    {
                        channel: "tickers",
                        instId: "BTC-USDT"
                    },
                    {
                        channel: "candle1m",
                        instId: "BTC-USDT"
                    }
                ]
            };
            
            log(`📤 发送订阅请求: ${JSON.stringify(subscriptionRequest, null, 2)}`, 'info');
            socket.send(JSON.stringify(subscriptionRequest));
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            messageCount = 0;
            updateMessageCount();
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，准备测试WebSocket连接', 'info');
            updateReadyState();
        });
    </script>
</body>
</html> 