<template>
  <div class="okx-positions">
    <h2>OKX持仓管理</h2>
    <a-form layout="inline" :model="query" @submit.prevent style="margin-bottom:16px">
      <a-form-item label="产品ID">
        <a-input v-model="query.instId" placeholder="如 BTC-USDT-SWAP" style="width:180px" />
      </a-form-item>
      <a-form-item label="方向">
        <a-select v-model="query.posSide" style="width:120px" allow-clear>
          <a-select-option value="long">多头</a-select-option>
          <a-select-option value="short">空头</a-select-option>
          <a-select-option value="net">净持仓</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="fetchPositions" :loading="loading">查询</a-button>
      </a-form-item>
    </a-form>
    <a-table :columns="columns" :data-source="positions" row-key="posId" :pagination="false" :loading="loading" bordered>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'upl'">
          <span :style="{ color: parseFloat(record.upl) >= 0 ? '#52c41a' : '#ff4d4f' }">{{ record.upl }}</span>
        </template>
        <template v-else-if="column.key === 'posSide'">
          {{ getPosSideText(record.posSide) }}
        </template>
        <template v-else>
          {{ record[column.dataIndex] }}
        </template>
      </template>
      <template #empty>
        <div style="text-align:center; padding:32px;">暂无持仓</div>
      </template>
    </a-table>
    <div v-if="error" style="color:red; margin-top:16px;">{{ error }}</div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const query = ref({ instId: '', posSide: '' })
const positions = ref([])
const loading = ref(false)
const error = ref('')

const columns = [
  { title: '产品ID', dataIndex: 'instId', key: 'instId' },
  { title: '方向', dataIndex: 'posSide', key: 'posSide' },
  { title: '持仓量', dataIndex: 'pos', key: 'pos' },
  { title: '开仓均价', dataIndex: 'avgPx', key: 'avgPx' },
  { title: '未实现盈亏', dataIndex: 'upl', key: 'upl' },
  { title: '杠杆', dataIndex: 'lever', key: 'lever' }
]

function getPosSideText(posSide) {
  if (posSide === 'long') return '多头'
  if (posSide === 'short') return '空头'
  if (posSide === 'net') return '净持仓'
  return posSide
}

function fetchPositions() {
  loading.value = true
  error.value = ''
  let params = []
  if (query.value.instId) params.push('instId=' + encodeURIComponent(query.value.instId))
  if (query.value.posSide) params.push('posSide=' + encodeURIComponent(query.value.posSide))
  fetch('/api/okx/positions?' + params.join('&'), {
    headers: { 'Authorization': 'Bearer ' + localStorage.getItem('token') }
  })
    .then(res => res.json())
    .then(res => {
      if (res.code === '0' && res.data.data) {
        positions.value = res.data.data
        error.value = ''
      } else {
        error.value = res.msg || '查询失败'
      }
    })
    .catch(() => { error.value = '网络异常' })
    .finally(() => { loading.value = false })
}

fetchPositions()
</script>

<style scoped>
.okx-positions {
  max-width: 1100px;
  margin: 0 auto;
  padding: 24px;
}
</style>