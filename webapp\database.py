from sqlalchemy import create_engine, Column, Integer, String, Boolean, DateTime, Text, Float, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from sqlalchemy.sql import func
from datetime import datetime
import os
from typing import Optional

# 数据库配置
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./okx_trading.db")

# 创建数据库引擎
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {}
)

# 创建会话
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基类
Base = declarative_base()

# 用户表
class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    okx_configs = relationship("OKXConfig", back_populates="user")
    user_settings = relationship("UserSettings", back_populates="user")
    trading_history = relationship("TradingHistory", back_populates="user")
    watchlists = relationship("Watchlist", back_populates="user")

# OKX API 配置表
class OKXConfig(Base):
    __tablename__ = "okx_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    api_key = Column(String(255), nullable=False)
    api_secret = Column(Text, nullable=False)  # 加密存储
    passphrase = Column(String(255), nullable=False)  # 加密存储
    is_sandbox = Column(Boolean, default=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="okx_configs")

# 用户设置表
class UserSettings(Base):
    __tablename__ = "user_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    setting_key = Column(String(100), nullable=False)
    setting_value = Column(Text, nullable=True)
    setting_type = Column(String(20), default="string")  # string, number, boolean, json
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="user_settings")

# 交易历史表
class TradingHistory(Base):
    __tablename__ = "trading_history"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    order_id = Column(String(100), nullable=False)
    instrument_id = Column(String(50), nullable=False)
    side = Column(String(10), nullable=False)  # buy, sell
    order_type = Column(String(20), nullable=False)  # market, limit, etc.
    size = Column(Float, nullable=False)
    price = Column(Float, nullable=True)
    filled_size = Column(Float, default=0)
    filled_price = Column(Float, nullable=True)
    status = Column(String(20), nullable=False)  # pending, filled, cancelled, etc.
    fee = Column(Float, default=0)
    pnl = Column(Float, default=0)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="trading_history")

# 观察列表表
class Watchlist(Base):
    __tablename__ = "watchlists"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    instrument_id = Column(String(50), nullable=False)
    name = Column(String(100), nullable=True)
    notes = Column(Text, nullable=True)
    alert_price_high = Column(Float, nullable=True)
    alert_price_low = Column(Float, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="watchlists")

# 图表配置表
class ChartConfig(Base):
    __tablename__ = "chart_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    config_name = Column(String(100), nullable=False)
    instrument_id = Column(String(50), nullable=False)
    timeframe = Column(String(10), nullable=False)
    indicators = Column(Text, nullable=True)  # JSON 格式存储指标配置
    layout = Column(Text, nullable=True)  # JSON 格式存储布局配置
    is_default = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

# 价格提醒表
class PriceAlert(Base):
    __tablename__ = "price_alerts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    instrument_id = Column(String(50), nullable=False)
    alert_type = Column(String(20), nullable=False)  # price_above, price_below, change_percent
    target_value = Column(Float, nullable=False)
    current_value = Column(Float, nullable=True)
    is_triggered = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    message = Column(Text, nullable=True)
    created_at = Column(DateTime, default=func.now())
    triggered_at = Column(DateTime, nullable=True)

# 数据库依赖注入
def get_db() -> Session:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 创建所有表
def create_tables():
    Base.metadata.create_all(bind=engine)

# 初始化数据库
def init_db():
    create_tables()
    print("Database tables created successfully!")

if __name__ == "__main__":
    init_db()