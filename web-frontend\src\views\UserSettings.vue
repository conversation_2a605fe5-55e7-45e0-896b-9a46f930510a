<template>
  <div class="user-settings">
    <a-card title="用户设置" :loading="loading">
      <a-form
        :model="settings"
        layout="vertical"
        @finish="saveSettings"
      >
        <!-- 界面设置 -->
        <a-divider orientation="left">界面设置</a-divider>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="主题">
              <a-select v-model:value="settings.theme" placeholder="选择主题">
                <a-select-option value="light">浅色主题</a-select-option>
                <a-select-option value="dark">深色主题</a-select-option>
                <a-select-option value="auto">跟随系统</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="语言">
              <a-select v-model:value="settings.language" placeholder="选择语言">
                <a-select-option value="zh-CN">简体中文</a-select-option>
                <a-select-option value="en-US">English</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 通知设置 -->
        <a-divider orientation="left">通知设置</a-divider>
        
        <a-form-item>
          <a-checkbox v-model:checked="settings.notifications">启用通知</a-checkbox>
        </a-form-item>

        <!-- 数据设置 -->
        <a-divider orientation="left">数据设置</a-divider>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="自动刷新间隔(秒)">
              <a-input-number 
                v-model:value="settings.auto_refresh_interval" 
                :min="5" 
                :max="300" 
                placeholder="自动刷新间隔"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="默认时间范围">
              <a-select v-model:value="settings.default_timeframe" placeholder="选择时间范围">
                <a-select-option value="1m">1分钟</a-select-option>
                <a-select-option value="5m">5分钟</a-select-option>
                <a-select-option value="15m">15分钟</a-select-option>
                <a-select-option value="1h">1小时</a-select-option>
                <a-select-option value="4h">4小时</a-select-option>
                <a-select-option value="1d">1天</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 风险管理设置 -->
        <a-divider orientation="left">风险管理</a-divider>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="风险等级">
              <a-select v-model:value="settings.risk_level" placeholder="选择风险等级">
                <a-select-option value="low">低风险</a-select-option>
                <a-select-option value="medium">中等风险</a-select-option>
                <a-select-option value="high">高风险</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="最大仓位(%)">
              <a-input-number 
                v-model:value="settings.max_position_size" 
                :min="1" 
                :max="100" 
                placeholder="最大仓位百分比"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="最大杠杆">
              <a-input-number 
                v-model:value="settings.max_leverage" 
                :min="1" 
                :max="125" 
                placeholder="最大杠杆倍数"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="最大日亏损(%)">
              <a-input-number 
                v-model:value="settings.max_daily_loss" 
                :min="1" 
                :max="50" 
                placeholder="最大日亏损百分比"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="止损百分比(%)">
              <a-input-number 
                v-model:value="settings.stop_loss_percentage" 
                :min="0.1" 
                :max="20" 
                :step="0.1"
                placeholder="止损百分比"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="止盈百分比(%)">
              <a-input-number 
                v-model:value="settings.take_profit_percentage" 
                :min="0.1" 
                :max="50" 
                :step="0.1"
                placeholder="止盈百分比"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 操作按钮 -->
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="saving">
              保存设置
            </a-button>
            <a-button @click="resetSettings">
              重置为默认
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { getUserSettings, saveUserSettings } from '../services/apiService'

const loading = ref(false)
const saving = ref(false)

// 默认设置
const defaultSettings = {
  theme: 'light',
  language: 'zh-CN',
  notifications: true,
  auto_refresh_interval: 30,
  default_timeframe: '1h',
  risk_level: 'medium',
  max_position_size: 10,
  max_leverage: 10,
  stop_loss_percentage: 2.0,
  take_profit_percentage: 5.0,
  max_daily_loss: 5
}

const settings = ref({ ...defaultSettings })

// 加载用户设置
const loadSettings = async () => {
  loading.value = true
  try {
    const userSettings = await getUserSettings()
    if (userSettings) {
      settings.value = { ...defaultSettings, ...userSettings }
    }
  } catch (error) {
    console.warn('加载用户设置失败:', error.message)
    message.warning('加载设置失败，使用默认设置')
  } finally {
    loading.value = false
  }
}

// 保存设置
const saveSettings = async () => {
  saving.value = true
  try {
    await saveUserSettings(settings.value)
    message.success('设置保存成功')
  } catch (error) {
    message.error('保存设置失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

// 重置设置
const resetSettings = () => {
  settings.value = { ...defaultSettings }
  message.info('已重置为默认设置，请点击保存按钮确认')
}

onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.user-settings {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

.ant-divider {
  margin: 24px 0 16px 0;
}
</style>