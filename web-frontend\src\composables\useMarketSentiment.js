import { ref, computed, reactive } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 市场情绪分析组合式函数
 * 基于多种技术指标和市场数据分析市场情绪
 */
export function useMarketSentiment() {
  // 响应式数据
  const sentimentData = ref(null)
  const isAnalyzing = ref(false)
  const analysisHistory = ref([])
  const sentimentIndicators = reactive({
    rsi: { value: 50, weight: 0.2, signal: 'neutral' },
    macd: { value: 0, weight: 0.15, signal: 'neutral' },
    bollinger: { value: 0.5, weight: 0.15, signal: 'neutral' },
    volume: { value: 1, weight: 0.1, signal: 'neutral' },
    priceAction: { value: 0, weight: 0.2, signal: 'neutral' },
    volatility: { value: 0.02, weight: 0.1, signal: 'neutral' },
    momentum: { value: 0, weight: 0.1, signal: 'neutral' }
  })
  
  // 情绪等级定义
  const SENTIMENT_LEVELS = {
    EXTREME_FEAR: { min: 0, max: 20, label: '极度恐慌', color: '#ff4d4f', icon: '😱' },
    FEAR: { min: 20, max: 40, label: '恐慌', color: '#ff7875', icon: '😰' },
    NEUTRAL: { min: 40, max: 60, label: '中性', color: '#faad14', icon: '😐' },
    GREED: { min: 60, max: 80, label: '贪婪', color: '#73d13d', icon: '😊' },
    EXTREME_GREED: { min: 80, max: 100, label: '极度贪婪', color: '#52c41a', icon: '🤑' }
  }
  
  // 市场阶段定义
  const MARKET_PHASES = {
    ACCUMULATION: { label: '吸筹阶段', description: '机构和聪明资金开始建仓' },
    MARKUP: { label: '上涨阶段', description: '价格开始上涨，公众开始关注' },
    DISTRIBUTION: { label: '派发阶段', description: '机构开始获利了结，散户接盘' },
    MARKDOWN: { label: '下跌阶段', description: '价格下跌，恐慌情绪蔓延' }
  }
  
  /**
   * 分析市场情绪
   * @param {Array} klineData - K线数据
   * @param {Array} volumeData - 成交量数据
   * @param {Object} technicalIndicators - 技术指标数据
   * @param {Object} options - 分析选项
   * @returns {Promise<Object>} 情绪分析结果
   */
  const analyzeSentiment = async (klineData, volumeData, technicalIndicators, options = {}) => {
    if (!klineData || klineData.length < 20) {
      message.warning('数据不足，无法进行情绪分析')
      return null
    }
    
    isAnalyzing.value = true
    
    try {
      // 计算各项情绪指标
      const rsiSentiment = calculateRSISentiment(technicalIndicators.rsi)
      const macdSentiment = calculateMACDSentiment(technicalIndicators.macd)
      const bollingerSentiment = calculateBollingerSentiment(klineData, technicalIndicators.bollinger)
      const volumeSentiment = calculateVolumeSentiment(klineData, volumeData)
      const priceActionSentiment = calculatePriceActionSentiment(klineData)
      const volatilitySentiment = calculateVolatilitySentiment(klineData)
      const momentumSentiment = calculateMomentumSentiment(klineData)
      
      // 更新指标数据
      sentimentIndicators.rsi = rsiSentiment
      sentimentIndicators.macd = macdSentiment
      sentimentIndicators.bollinger = bollingerSentiment
      sentimentIndicators.volume = volumeSentiment
      sentimentIndicators.priceAction = priceActionSentiment
      sentimentIndicators.volatility = volatilitySentiment
      sentimentIndicators.momentum = momentumSentiment
      
      // 计算综合情绪得分
      const overallSentiment = calculateOverallSentiment()
      
      // 识别市场阶段
      const marketPhase = identifyMarketPhase(klineData, volumeData, overallSentiment)
      
      // 生成情绪报告
      const sentimentReport = generateSentimentReport(overallSentiment, marketPhase)
      
      // 计算恐慌贪婪指数
      const fearGreedIndex = calculateFearGreedIndex()
      
      const result = {
        timestamp: Date.now(),
        overallSentiment,
        fearGreedIndex,
        marketPhase,
        indicators: { ...sentimentIndicators },
        report: sentimentReport,
        signals: generateTradingSignals(overallSentiment, marketPhase),
        confidence: calculateAnalysisConfidence()
      }
      
      sentimentData.value = result
      
      // 添加到历史记录
      analysisHistory.value.unshift(result)
      if (analysisHistory.value.length > 100) {
        analysisHistory.value = analysisHistory.value.slice(0, 100)
      }
      
      return result
      
    } catch (error) {
      console.error('情绪分析失败:', error)
      message.error('情绪分析失败')
      return null
    } finally {
      isAnalyzing.value = false
    }
  }
  
  /**
   * 计算RSI情绪指标
   * @param {Array} rsiData - RSI数据
   * @returns {Object} RSI情绪指标
   */
  const calculateRSISentiment = (rsiData) => {
    if (!rsiData || rsiData.length === 0) {
      return { value: 50, weight: 0.2, signal: 'neutral' }
    }
    
    const latestRSI = rsiData[rsiData.length - 1]
    let signal = 'neutral'
    
    if (latestRSI > 70) {
      signal = 'bearish' // 超买
    } else if (latestRSI < 30) {
      signal = 'bullish' // 超卖
    }
    
    return {
      value: latestRSI,
      weight: 0.2,
      signal,
      description: getRSIDescription(latestRSI)
    }
  }
  
  /**
   * 计算MACD情绪指标
   * @param {Array} macdData - MACD数据
   * @returns {Object} MACD情绪指标
   */
  const calculateMACDSentiment = (macdData) => {
    if (!macdData || macdData.length < 2) {
      return { value: 0, weight: 0.15, signal: 'neutral' }
    }
    
    const latest = macdData[macdData.length - 1]
    const previous = macdData[macdData.length - 2]
    
    let signal = 'neutral'
    
    // MACD线与信号线的关系
    if (latest.macd > latest.signal && previous.macd <= previous.signal) {
      signal = 'bullish' // 金叉
    } else if (latest.macd < latest.signal && previous.macd >= previous.signal) {
      signal = 'bearish' // 死叉
    } else if (latest.macd > latest.signal) {
      signal = 'bullish'
    } else if (latest.macd < latest.signal) {
      signal = 'bearish'
    }
    
    return {
      value: latest.macd - latest.signal,
      weight: 0.15,
      signal,
      description: getMACDDescription(latest, previous)
    }
  }
  
  /**
   * 计算布林带情绪指标
   * @param {Array} klineData - K线数据
   * @param {Array} bollingerData - 布林带数据
   * @returns {Object} 布林带情绪指标
   */
  const calculateBollingerSentiment = (klineData, bollingerData) => {
    if (!klineData || !bollingerData || bollingerData.length === 0) {
      return { value: 0.5, weight: 0.15, signal: 'neutral' }
    }
    
    const latestPrice = klineData[klineData.length - 1].close
    const latestBB = bollingerData[bollingerData.length - 1]
    
    // 计算价格在布林带中的位置（0-1）
    const position = (latestPrice - latestBB.lower) / (latestBB.upper - latestBB.lower)
    
    let signal = 'neutral'
    
    if (position > 0.8) {
      signal = 'bearish' // 接近上轨，可能超买
    } else if (position < 0.2) {
      signal = 'bullish' // 接近下轨，可能超卖
    }
    
    return {
      value: position,
      weight: 0.15,
      signal,
      description: getBollingerDescription(position)
    }
  }
  
  /**
   * 计算成交量情绪指标
   * @param {Array} klineData - K线数据
   * @param {Array} volumeData - 成交量数据
   * @returns {Object} 成交量情绪指标
   */
  const calculateVolumeSentiment = (klineData, volumeData) => {
    if (!volumeData || volumeData.length < 20) {
      return { value: 1, weight: 0.1, signal: 'neutral' }
    }
    
    const recentVolume = volumeData.slice(-10)
    const avgVolume = recentVolume.reduce((sum, vol) => sum + vol, 0) / recentVolume.length
    const latestVolume = volumeData[volumeData.length - 1]
    
    const volumeRatio = latestVolume / avgVolume
    const latestPrice = klineData[klineData.length - 1]
    const isGreenCandle = latestPrice.close > latestPrice.open
    
    let signal = 'neutral'
    
    if (volumeRatio > 1.5) {
      signal = isGreenCandle ? 'bullish' : 'bearish' // 放量上涨看涨，放量下跌看跌
    } else if (volumeRatio < 0.7) {
      signal = 'neutral' // 缩量
    }
    
    return {
      value: volumeRatio,
      weight: 0.1,
      signal,
      description: getVolumeDescription(volumeRatio, isGreenCandle)
    }
  }
  
  /**
   * 计算价格行为情绪指标
   * @param {Array} klineData - K线数据
   * @returns {Object} 价格行为情绪指标
   */
  const calculatePriceActionSentiment = (klineData) => {
    if (!klineData || klineData.length < 10) {
      return { value: 0, weight: 0.2, signal: 'neutral' }
    }
    
    const recent = klineData.slice(-10)
    const latest = klineData[klineData.length - 1]
    
    // 计算价格变化趋势
    const priceChanges = recent.map((candle, index) => {
      if (index === 0) return 0
      return (candle.close - recent[index - 1].close) / recent[index - 1].close
    }).slice(1)
    
    const avgChange = priceChanges.reduce((sum, change) => sum + change, 0) / priceChanges.length
    
    // 计算连续上涨/下跌天数
    let consecutiveDays = 0
    let isUptrend = latest.close > recent[recent.length - 2].close
    
    for (let i = recent.length - 1; i > 0; i--) {
      const currentUp = recent[i].close > recent[i - 1].close
      if (currentUp === isUptrend) {
        consecutiveDays++
      } else {
        break
      }
    }
    
    let signal = 'neutral'
    
    if (avgChange > 0.02 && consecutiveDays >= 3) {
      signal = 'bullish'
    } else if (avgChange < -0.02 && consecutiveDays >= 3) {
      signal = 'bearish'
    }
    
    return {
      value: avgChange,
      weight: 0.2,
      signal,
      description: getPriceActionDescription(avgChange, consecutiveDays, isUptrend)
    }
  }
  
  /**
   * 计算波动率情绪指标
   * @param {Array} klineData - K线数据
   * @returns {Object} 波动率情绪指标
   */
  const calculateVolatilitySentiment = (klineData) => {
    if (!klineData || klineData.length < 20) {
      return { value: 0.02, weight: 0.1, signal: 'neutral' }
    }
    
    const recent = klineData.slice(-20)
    
    // 计算日内波动率
    const dailyVolatilities = recent.map(candle => 
      (candle.high - candle.low) / candle.open
    )
    
    const avgVolatility = dailyVolatilities.reduce((sum, vol) => sum + vol, 0) / dailyVolatilities.length
    const latestVolatility = dailyVolatilities[dailyVolatilities.length - 1]
    
    let signal = 'neutral'
    
    if (latestVolatility > avgVolatility * 1.5) {
      signal = 'bearish' // 高波动率通常表示恐慌
    } else if (latestVolatility < avgVolatility * 0.7) {
      signal = 'neutral' // 低波动率表示平静
    }
    
    return {
      value: latestVolatility,
      weight: 0.1,
      signal,
      description: getVolatilityDescription(latestVolatility, avgVolatility)
    }
  }
  
  /**
   * 计算动量情绪指标
   * @param {Array} klineData - K线数据
   * @returns {Object} 动量情绪指标
   */
  const calculateMomentumSentiment = (klineData) => {
    if (!klineData || klineData.length < 15) {
      return { value: 0, weight: 0.1, signal: 'neutral' }
    }
    
    const current = klineData[klineData.length - 1].close
    const past = klineData[klineData.length - 15].close
    
    const momentum = (current - past) / past
    
    let signal = 'neutral'
    
    if (momentum > 0.05) {
      signal = 'bullish'
    } else if (momentum < -0.05) {
      signal = 'bearish'
    }
    
    return {
      value: momentum,
      weight: 0.1,
      signal,
      description: getMomentumDescription(momentum)
    }
  }
  
  /**
   * 计算综合情绪得分
   * @returns {Object} 综合情绪得分
   */
  const calculateOverallSentiment = () => {
    let bullishScore = 0
    let bearishScore = 0
    let totalWeight = 0
    
    Object.values(sentimentIndicators).forEach(indicator => {
      totalWeight += indicator.weight
      
      if (indicator.signal === 'bullish') {
        bullishScore += indicator.weight
      } else if (indicator.signal === 'bearish') {
        bearishScore += indicator.weight
      }
    })
    
    const netScore = (bullishScore - bearishScore) / totalWeight
    const sentimentScore = 50 + (netScore * 50) // 转换为0-100分
    
    return {
      score: Math.max(0, Math.min(100, sentimentScore)),
      bullishScore,
      bearishScore,
      netScore,
      level: getSentimentLevel(sentimentScore)
    }
  }
  
  /**
   * 计算恐慌贪婪指数
   * @returns {Object} 恐慌贪婪指数
   */
  const calculateFearGreedIndex = () => {
    const indicators = sentimentIndicators
    
    // 恐慌贪婪指数的计算权重
    const weights = {
      volatility: 0.25,  // 波动率（高波动=恐慌）
      momentum: 0.25,    // 动量（负动量=恐慌）
      volume: 0.20,      // 成交量（异常放量=恐慌或贪婪）
      rsi: 0.15,         // RSI（超卖=恐慌，超买=贪婪）
      priceAction: 0.15  // 价格行为
    }
    
    let fearScore = 0
    let greedScore = 0
    
    // 波动率贡献（高波动率增加恐慌）
    if (indicators.volatility.value > 0.03) {
      fearScore += weights.volatility * (indicators.volatility.value / 0.05)
    }
    
    // 动量贡献
    if (indicators.momentum.value < 0) {
      fearScore += weights.momentum * Math.abs(indicators.momentum.value) * 10
    } else {
      greedScore += weights.momentum * indicators.momentum.value * 10
    }
    
    // 成交量贡献
    if (indicators.volume.value > 1.5) {
      if (indicators.priceAction.value < 0) {
        fearScore += weights.volume * (indicators.volume.value - 1)
      } else {
        greedScore += weights.volume * (indicators.volume.value - 1)
      }
    }
    
    // RSI贡献
    if (indicators.rsi.value < 30) {
      fearScore += weights.rsi * (30 - indicators.rsi.value) / 30
    } else if (indicators.rsi.value > 70) {
      greedScore += weights.rsi * (indicators.rsi.value - 70) / 30
    }
    
    // 价格行为贡献
    if (indicators.priceAction.value < -0.02) {
      fearScore += weights.priceAction * Math.abs(indicators.priceAction.value) * 25
    } else if (indicators.priceAction.value > 0.02) {
      greedScore += weights.priceAction * indicators.priceAction.value * 25
    }
    
    const totalScore = fearScore + greedScore
    let index = 50 // 中性
    
    if (totalScore > 0) {
      if (fearScore > greedScore) {
        index = Math.max(0, 50 - (fearScore / totalScore) * 50)
      } else {
        index = Math.min(100, 50 + (greedScore / totalScore) * 50)
      }
    }
    
    return {
      index: Math.round(index),
      level: getSentimentLevel(index),
      fearScore,
      greedScore,
      components: {
        volatility: indicators.volatility.value,
        momentum: indicators.momentum.value,
        volume: indicators.volume.value,
        rsi: indicators.rsi.value,
        priceAction: indicators.priceAction.value
      }
    }
  }
  
  /**
   * 识别市场阶段
   * @param {Array} klineData - K线数据
   * @param {Array} volumeData - 成交量数据
   * @param {Object} sentiment - 情绪数据
   * @returns {Object} 市场阶段
   */
  const identifyMarketPhase = (klineData, volumeData, sentiment) => {
    const recent = klineData.slice(-30)
    const recentVolume = volumeData.slice(-30)
    
    // 计算价格趋势
    const priceStart = recent[0].close
    const priceEnd = recent[recent.length - 1].close
    const priceChange = (priceEnd - priceStart) / priceStart
    
    // 计算成交量趋势
    const volumeFirst = recentVolume.slice(0, 15).reduce((sum, vol) => sum + vol, 0) / 15
    const volumeLast = recentVolume.slice(-15).reduce((sum, vol) => sum + vol, 0) / 15
    const volumeChange = (volumeLast - volumeFirst) / volumeFirst
    
    let phase = MARKET_PHASES.ACCUMULATION
    
    if (priceChange > 0.1 && volumeChange > 0.2 && sentiment.score > 60) {
      phase = MARKET_PHASES.MARKUP
    } else if (priceChange > 0.05 && volumeChange > 0.5 && sentiment.score > 70) {
      phase = MARKET_PHASES.DISTRIBUTION
    } else if (priceChange < -0.1 && sentiment.score < 40) {
      phase = MARKET_PHASES.MARKDOWN
    }
    
    return {
      ...phase,
      confidence: calculatePhaseConfidence(priceChange, volumeChange, sentiment.score),
      metrics: {
        priceChange,
        volumeChange,
        sentimentScore: sentiment.score
      }
    }
  }
  
  /**
   * 生成情绪报告
   * @param {Object} sentiment - 情绪数据
   * @param {Object} phase - 市场阶段
   * @returns {Object} 情绪报告
   */
  const generateSentimentReport = (sentiment, phase) => {
    const level = sentiment.level
    
    let summary = ''
    let recommendations = []
    let risks = []
    
    switch (level.label) {
      case '极度恐慌':
        summary = '市场处于极度恐慌状态，可能存在超卖机会，但需谨慎操作。'
        recommendations = [
          '考虑分批建仓，但要控制仓位',
          '关注技术面的超卖信号',
          '设置严格的止损位'
        ]
        risks = [
          '恐慌情绪可能持续，价格可能继续下跌',
          '市场流动性可能不足',
          '基本面可能存在重大问题'
        ]
        break
        
      case '恐慌':
        summary = '市场情绪偏向恐慌，但可能接近底部区域。'
        recommendations = [
          '可以考虑小仓位试探性建仓',
          '等待更明确的反转信号',
          '关注成交量的变化'
        ]
        risks = [
          '下跌趋势可能尚未结束',
          '需要更多确认信号'
        ]
        break
        
      case '中性':
        summary = '市场情绪相对平衡，处于观望状态。'
        recommendations = [
          '保持观望，等待明确方向',
          '关注突破信号',
          '可以进行区间交易'
        ]
        risks = [
          '方向不明确，可能出现假突破',
          '成交量可能不足'
        ]
        break
        
      case '贪婪':
        summary = '市场情绪偏向贪婪，需要注意风险控制。'
        recommendations = [
          '考虑逐步减仓',
          '提高止盈位',
          '关注反转信号'
        ]
        risks = [
          '可能接近顶部区域',
          '回调风险增加'
        ]
        break
        
      case '极度贪婪':
        summary = '市场处于极度贪婪状态，高位风险极大。'
        recommendations = [
          '建议大幅减仓或清仓',
          '等待回调机会',
          '保持现金为王'
        ]
        risks = [
          '随时可能出现大幅回调',
          '技术面可能严重超买',
          '获利盘压力巨大'
        ]
        break
    }
    
    return {
      summary,
      recommendations,
      risks,
      marketPhase: phase.label,
      phaseDescription: phase.description,
      keyIndicators: getKeyIndicatorsSummary()
    }
  }
  
  /**
   * 生成交易信号
   * @param {Object} sentiment - 情绪数据
   * @param {Object} phase - 市场阶段
   * @returns {Array} 交易信号数组
   */
  const generateTradingSignals = (sentiment, phase) => {
    const signals = []
    
    // 基于情绪的信号
    if (sentiment.score <= 20) {
      signals.push({
        type: 'buy',
        strength: 'strong',
        reason: '极度恐慌，可能存在超卖机会',
        confidence: 0.7
      })
    } else if (sentiment.score >= 80) {
      signals.push({
        type: 'sell',
        strength: 'strong',
        reason: '极度贪婪，建议获利了结',
        confidence: 0.8
      })
    }
    
    // 基于市场阶段的信号
    if (phase.label === '吸筹阶段') {
      signals.push({
        type: 'buy',
        strength: 'medium',
        reason: '处于吸筹阶段，适合建仓',
        confidence: 0.6
      })
    } else if (phase.label === '派发阶段') {
      signals.push({
        type: 'sell',
        strength: 'medium',
        reason: '处于派发阶段，建议减仓',
        confidence: 0.6
      })
    }
    
    // 基于技术指标的信号
    const bullishCount = Object.values(sentimentIndicators).filter(ind => ind.signal === 'bullish').length
    const bearishCount = Object.values(sentimentIndicators).filter(ind => ind.signal === 'bearish').length
    
    if (bullishCount >= 4) {
      signals.push({
        type: 'buy',
        strength: 'medium',
        reason: '多项技术指标显示看涨信号',
        confidence: 0.65
      })
    } else if (bearishCount >= 4) {
      signals.push({
        type: 'sell',
        strength: 'medium',
        reason: '多项技术指标显示看跌信号',
        confidence: 0.65
      })
    }
    
    return signals
  }
  
  /**
   * 计算分析置信度
   * @returns {number} 置信度 (0-1)
   */
  const calculateAnalysisConfidence = () => {
    let confidence = 0.5
    
    // 基于指标一致性
    const signals = Object.values(sentimentIndicators).map(ind => ind.signal)
    const bullishCount = signals.filter(s => s === 'bullish').length
    const bearishCount = signals.filter(s => s === 'bearish').length
    const neutralCount = signals.filter(s => s === 'neutral').length
    
    const maxCount = Math.max(bullishCount, bearishCount, neutralCount)
    const consistency = maxCount / signals.length
    
    confidence = 0.3 + (consistency * 0.7)
    
    return Math.max(0.3, Math.min(1, confidence))
  }
  
  /**
   * 获取情绪等级
   * @param {number} score - 情绪得分
   * @returns {Object} 情绪等级
   */
  const getSentimentLevel = (score) => {
    for (const [key, level] of Object.entries(SENTIMENT_LEVELS)) {
      if (score >= level.min && score < level.max) {
        return level
      }
    }
    return SENTIMENT_LEVELS.EXTREME_GREED // 默认返回极度贪婪
  }
  
  /**
   * 计算市场阶段置信度
   * @param {number} priceChange - 价格变化
   * @param {number} volumeChange - 成交量变化
   * @param {number} sentimentScore - 情绪得分
   * @returns {number} 置信度
   */
  const calculatePhaseConfidence = (priceChange, volumeChange, sentimentScore) => {
    let confidence = 0.5
    
    // 基于价格和成交量的一致性
    if (Math.abs(priceChange) > 0.1 && Math.abs(volumeChange) > 0.2) {
      confidence += 0.2
    }
    
    // 基于情绪的极端程度
    if (sentimentScore < 30 || sentimentScore > 70) {
      confidence += 0.2
    }
    
    return Math.max(0.3, Math.min(1, confidence))
  }
  
  /**
   * 获取关键指标摘要
   * @returns {Object} 关键指标摘要
   */
  const getKeyIndicatorsSummary = () => {
    return {
      rsi: {
        value: sentimentIndicators.rsi.value,
        signal: sentimentIndicators.rsi.signal,
        description: sentimentIndicators.rsi.description
      },
      volume: {
        value: sentimentIndicators.volume.value,
        signal: sentimentIndicators.volume.signal,
        description: sentimentIndicators.volume.description
      },
      priceAction: {
        value: sentimentIndicators.priceAction.value,
        signal: sentimentIndicators.priceAction.signal,
        description: sentimentIndicators.priceAction.description
      }
    }
  }
  
  // 描述生成函数
  const getRSIDescription = (rsi) => {
    if (rsi > 70) return 'RSI超买，可能面临回调压力'
    if (rsi < 30) return 'RSI超卖，可能存在反弹机会'
    return 'RSI处于正常区间'
  }
  
  const getMACDDescription = (latest, previous) => {
    if (latest.macd > latest.signal && previous.macd <= previous.signal) {
      return 'MACD金叉，看涨信号'
    }
    if (latest.macd < latest.signal && previous.macd >= previous.signal) {
      return 'MACD死叉，看跌信号'
    }
    return 'MACD无明显信号'
  }
  
  const getBollingerDescription = (position) => {
    if (position > 0.8) return '价格接近布林带上轨，可能超买'
    if (position < 0.2) return '价格接近布林带下轨，可能超卖'
    return '价格在布林带中轨附近'
  }
  
  const getVolumeDescription = (ratio, isGreen) => {
    if (ratio > 1.5) {
      return isGreen ? '放量上涨，买盘活跃' : '放量下跌，卖压沉重'
    }
    if (ratio < 0.7) return '成交量萎缩，市场观望'
    return '成交量正常'
  }
  
  const getPriceActionDescription = (avgChange, consecutiveDays, isUptrend) => {
    if (consecutiveDays >= 3) {
      return isUptrend ? `连续${consecutiveDays}天上涨，上升动能强劲` : `连续${consecutiveDays}天下跌，下跌压力较大`
    }
    return '价格走势相对平稳'
  }
  
  const getVolatilityDescription = (current, average) => {
    if (current > average * 1.5) return '波动率异常升高，市场恐慌情绪浓厚'
    if (current < average * 0.7) return '波动率较低，市场相对平静'
    return '波动率正常'
  }
  
  const getMomentumDescription = (momentum) => {
    if (momentum > 0.05) return '价格动量强劲，上涨趋势明显'
    if (momentum < -0.05) return '价格动量疲弱，下跌趋势明显'
    return '价格动量中性'
  }
  
  /**
   * 获取历史情绪趋势
   */
  const getSentimentTrend = computed(() => {
    if (analysisHistory.value.length < 2) return 'stable'
    
    const recent = analysisHistory.value.slice(0, 5)
    const scores = recent.map(item => item.overallSentiment.score)
    
    const trend = scores[0] - scores[scores.length - 1]
    
    if (trend > 10) return 'improving'
    if (trend < -10) return 'deteriorating'
    return 'stable'
  })
  
  /**
   * 获取情绪统计
   */
  const getSentimentStatistics = computed(() => {
    if (analysisHistory.value.length === 0) return null
    
    const scores = analysisHistory.value.map(item => item.overallSentiment.score)
    const fearGreedIndexes = analysisHistory.value.map(item => item.fearGreedIndex.index)
    
    return {
      avgSentiment: scores.reduce((sum, score) => sum + score, 0) / scores.length,
      maxSentiment: Math.max(...scores),
      minSentiment: Math.min(...scores),
      avgFearGreed: fearGreedIndexes.reduce((sum, index) => sum + index, 0) / fearGreedIndexes.length,
      volatility: calculateArrayVolatility(scores),
      trend: getSentimentTrend.value
    }
  })
  
  /**
   * 计算数组波动率
   * @param {Array} values - 数值数组
   * @returns {number} 波动率
   */
  const calculateArrayVolatility = (values) => {
    if (values.length < 2) return 0
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    
    return Math.sqrt(variance)
  }
  
  /**
   * 清除分析历史
   */
  const clearHistory = () => {
    analysisHistory.value = []
    sentimentData.value = null
  }
  
  return {
    // 响应式数据
    sentimentData,
    isAnalyzing,
    analysisHistory,
    sentimentIndicators,
    
    // 计算属性
    getSentimentTrend,
    getSentimentStatistics,
    
    // 方法
    analyzeSentiment,
    clearHistory,
    
    // 常量
    SENTIMENT_LEVELS,
    MARKET_PHASES
  }
}

export default useMarketSentiment