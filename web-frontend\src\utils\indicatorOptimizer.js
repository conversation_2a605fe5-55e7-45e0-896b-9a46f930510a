/**
 * 技术指标优化工具
 * 提供实时数据更新、性能优化和显示效果增强功能
 */

/**
 * 指标数据缓存管理器
 */
class IndicatorDataCache {
  constructor() {
    this.cache = new Map()
    this.maxCacheSize = 1000
    this.updateQueue = new Map()
    this.isProcessing = false
  }

  /**
   * 获取缓存键
   * @param {string} indicatorName - 指标名称
   * @param {Array} params - 参数数组
   * @returns {string}
   */
  getCacheKey(indicatorName, params) {
    return `${indicatorName}_${params.join('_')}`
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {Object} data - 数据
   */
  set(key, data) {
    if (this.cache.size >= this.maxCacheSize) {
      // 删除最旧的缓存项
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @param {number} maxAge - 最大缓存时间（毫秒）
   * @returns {Object|null}
   */
  get(key, maxAge = 30000) {
    const cached = this.cache.get(key)
    if (!cached) return null
    
    if (Date.now() - cached.timestamp > maxAge) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data
  }

  /**
   * 清除缓存
   * @param {string} pattern - 匹配模式
   */
  clear(pattern) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key)
        }
      }
    } else {
      this.cache.clear()
    }
  }
}

/**
 * 实时更新队列管理器
 */
class UpdateQueueManager {
  constructor() {
    this.queues = new Map()
    this.timers = new Map()
    this.defaultDelay = 300
  }

  /**
   * 添加更新任务到队列
   * @param {string} indicatorId - 指标ID
   * @param {Function} updateFn - 更新函数
   * @param {number} delay - 延迟时间
   */
  enqueue(indicatorId, updateFn, delay = this.defaultDelay) {
    // 清除之前的定时器
    if (this.timers.has(indicatorId)) {
      clearTimeout(this.timers.get(indicatorId))
    }

    // 设置新的定时器
    const timer = setTimeout(() => {
      updateFn()
      this.timers.delete(indicatorId)
    }, delay)

    this.timers.set(indicatorId, timer)
  }

  /**
   * 取消指标的更新任务
   * @param {string} indicatorId - 指标ID
   */
  cancel(indicatorId) {
    if (this.timers.has(indicatorId)) {
      clearTimeout(this.timers.get(indicatorId))
      this.timers.delete(indicatorId)
    }
  }

  /**
   * 清除所有更新任务
   */
  clear() {
    for (const timer of this.timers.values()) {
      clearTimeout(timer)
    }
    this.timers.clear()
  }
}

/**
 * 指标性能监控器
 */
class IndicatorPerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.maxMetrics = 100
  }

  /**
   * 开始性能监控
   * @param {string} indicatorId - 指标ID
   * @returns {Function} 结束监控函数
   */
  start(indicatorId) {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      this.recordMetric(indicatorId, duration)
      return duration
    }
  }

  /**
   * 记录性能指标
   * @param {string} indicatorId - 指标ID
   * @param {number} duration - 执行时间
   */
  recordMetric(indicatorId, duration) {
    if (!this.metrics.has(indicatorId)) {
      this.metrics.set(indicatorId, [])
    }

    const metrics = this.metrics.get(indicatorId)
    metrics.push({
      duration,
      timestamp: Date.now()
    })

    // 保持最近的指标记录
    if (metrics.length > this.maxMetrics) {
      metrics.shift()
    }
  }

  /**
   * 获取性能统计
   * @param {string} indicatorId - 指标ID
   * @returns {Object}
   */
  getStats(indicatorId) {
    const metrics = this.metrics.get(indicatorId)
    if (!metrics || metrics.length === 0) {
      return null
    }

    const durations = metrics.map(m => m.duration)
    const avg = durations.reduce((a, b) => a + b, 0) / durations.length
    const min = Math.min(...durations)
    const max = Math.max(...durations)

    return {
      average: avg,
      min,
      max,
      count: durations.length,
      recent: durations.slice(-10) // 最近10次
    }
  }
}

/**
 * 指标显示效果优化器
 */
class IndicatorDisplayOptimizer {
  /**
   * 获取优化的MACD样式配置
   * @param {Object} config - 配置参数
   * @returns {Object}
   */
  static getOptimizedMACDStyles(config = {}) {
    return {
      dif: {
        color: config.difColor || '#1890ff',
        size: config.lineWidth || 2,
        style: 'solid'
      },
      dea: {
        color: config.deaColor || '#f5222d',
        size: config.lineWidth || 2,
        style: 'solid'
      },
      macd: {
        upColor: config.upColor || '#26A69A',
        downColor: config.downColor || '#EF5350',
        noChangeColor: config.noChangeColor || '#888888',
        wickColor: {
          upColor: config.upColor || '#26A69A',
          downColor: config.downColor || '#EF5350',
          noChangeColor: config.noChangeColor || '#888888'
        }
      },
      // 添加渐变效果
      gradient: {
        dif: {
          offset: [0, 1],
          color: [
            { offset: 0, color: config.difColor || '#1890ff' },
            { offset: 1, color: `${config.difColor || '#1890ff'}20` }
          ]
        },
        dea: {
          offset: [0, 1],
          color: [
            { offset: 0, color: config.deaColor || '#f5222d' },
            { offset: 1, color: `${config.deaColor || '#f5222d'}20` }
          ]
        }
      },
      // 添加阴影效果
      shadow: {
        color: 'rgba(0, 0, 0, 0.1)',
        blur: 3,
        offsetX: 1,
        offsetY: 1
      }
    }
  }

  /**
   * 获取优化的RSI样式配置
   * @param {Object} config - 配置参数
   * @returns {Object}
   */
  static getOptimizedRSIStyles(config = {}) {
    return {
      line: {
        color: config.lineColor || '#722ed1',
        size: config.lineWidth || 2,
        style: 'solid'
      },
      // 超买超卖区域
      overbought: {
        color: config.overboughtColor || '#ff4d4f',
        value: config.overbought || 70,
        style: 'dashed',
        size: 1
      },
      oversold: {
        color: config.oversoldColor || '#52c41a',
        value: config.oversold || 30,
        style: 'dashed',
        size: 1
      },
      // 背景区域着色
      area: {
        overbought: {
          color: 'rgba(255, 77, 79, 0.1)',
          value: config.overbought || 70
        },
        oversold: {
          color: 'rgba(82, 196, 26, 0.1)',
          value: config.oversold || 30
        },
        neutral: {
          color: 'rgba(114, 46, 209, 0.05)'
        }
      },
      // 数值标签
      label: {
        show: config.showValues || true,
        color: config.labelColor || '#666',
        fontSize: 12,
        position: 'right'
      }
    }
  }

  /**
   * 获取优化的KDJ样式配置
   * @param {Object} config - 配置参数
   * @returns {Object}
   */
  static getOptimizedKDJStyles(config = {}) {
    return {
      k: {
        color: config.kColor || '#1890ff',
        size: config.lineWidth || 2,
        style: 'solid'
      },
      d: {
        color: config.dColor || '#f5222d',
        size: config.lineWidth || 2,
        style: 'solid'
      },
      j: {
        color: config.jColor || '#faad14',
        size: config.lineWidth || 2,
        style: 'solid'
      },
      // 超买超卖线
      overbought: {
        color: config.overboughtColor || '#ff4d4f',
        value: config.overbought || 80,
        style: 'dashed',
        size: 1
      },
      oversold: {
        color: config.oversoldColor || '#52c41a',
        value: config.oversold || 20,
        style: 'dashed',
        size: 1
      },
      // 交叉点标记
      crossPoint: {
        show: config.showCrossPoints || true,
        size: 4,
        color: '#ff7875',
        borderColor: '#fff',
        borderWidth: 1
      },
      // 背景网格
      grid: {
        show: config.showGrid || true,
        color: 'rgba(0, 0, 0, 0.05)',
        style: 'solid',
        size: 1
      }
    }
  }

  /**
   * 获取优化的布林带样式配置
   * @param {Object} config - 配置参数
   * @returns {Object}
   */
  static getOptimizedBOLLStyles(config = {}) {
    return {
      upper: {
        color: config.upperColor || '#f5222d',
        size: config.lineWidth || 1,
        style: 'solid'
      },
      mid: {
        color: config.midColor || '#1890ff',
        size: config.lineWidth || 2,
        style: 'solid'
      },
      lower: {
        color: config.lowerColor || '#52c41a',
        size: config.lineWidth || 1,
        style: 'solid'
      },
      // 填充区域
      fill: {
        show: config.showFill || true,
        color: config.fillColor || 'rgba(24, 144, 255, 0.1)'
      },
      // 突破标记
      breakthrough: {
        show: config.showBreakthrough || true,
        upColor: '#ff4d4f',
        downColor: '#52c41a',
        size: 6
      }
    }
  }
}

/**
 * 数据平滑处理器
 */
class DataSmoothProcessor {
  /**
   * 移动平均平滑
   * @param {Array} data - 原始数据
   * @param {number} period - 平滑周期
   * @returns {Array}
   */
  static movingAverage(data, period = 3) {
    if (!data || data.length < period) return data
    
    const smoothed = []
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        smoothed.push(data[i])
      } else {
        const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)
        smoothed.push(sum / period)
      }
    }
    return smoothed
  }

  /**
   * 指数移动平均平滑
   * @param {Array} data - 原始数据
   * @param {number} alpha - 平滑系数
   * @returns {Array}
   */
  static exponentialMovingAverage(data, alpha = 0.3) {
    if (!data || data.length === 0) return data
    
    const smoothed = [data[0]]
    for (let i = 1; i < data.length; i++) {
      smoothed.push(alpha * data[i] + (1 - alpha) * smoothed[i - 1])
    }
    return smoothed
  }

  /**
   * 异常值检测和处理
   * @param {Array} data - 原始数据
   * @param {number} threshold - 异常值阈值
   * @returns {Array}
   */
  static removeOutliers(data, threshold = 2) {
    if (!data || data.length < 3) return data
    
    const mean = data.reduce((a, b) => a + b, 0) / data.length
    const variance = data.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / data.length
    const stdDev = Math.sqrt(variance)
    
    return data.map(value => {
      if (Math.abs(value - mean) > threshold * stdDev) {
        return mean // 用均值替换异常值
      }
      return value
    })
  }
}

/**
 * 主要的指标优化器类
 */
export class IndicatorOptimizer {
  constructor() {
    this.cache = new IndicatorDataCache()
    this.updateQueue = new UpdateQueueManager()
    this.performanceMonitor = new IndicatorPerformanceMonitor()
    this.displayOptimizer = IndicatorDisplayOptimizer
    this.dataProcessor = DataSmoothProcessor
  }

  /**
   * 优化指标更新
   * @param {string} indicatorId - 指标ID
   * @param {Function} updateFn - 更新函数
   * @param {Object} options - 选项
   */
  optimizeUpdate(indicatorId, updateFn, options = {}) {
    const {
      delay = 300,
      enableCache = true,
      enablePerformanceMonitoring = true
    } = options

    const optimizedUpdateFn = () => {
      let endMonitoring
      if (enablePerformanceMonitoring) {
        endMonitoring = this.performanceMonitor.start(indicatorId)
      }

      try {
        updateFn()
      } finally {
        if (endMonitoring) {
          const duration = endMonitoring()
          console.log(`指标 ${indicatorId} 更新耗时: ${duration.toFixed(2)}ms`)
        }
      }
    }

    this.updateQueue.enqueue(indicatorId, optimizedUpdateFn, delay)
  }

  /**
   * 获取优化的指标样式
   * @param {string} indicatorType - 指标类型
   * @param {Object} config - 配置参数
   * @returns {Object}
   */
  getOptimizedStyles(indicatorType, config = {}) {
    switch (indicatorType.toUpperCase()) {
      case 'MACD':
        return this.displayOptimizer.getOptimizedMACDStyles(config)
      case 'RSI':
        return this.displayOptimizer.getOptimizedRSIStyles(config)
      case 'KDJ':
        return this.displayOptimizer.getOptimizedKDJStyles(config)
      case 'BOLL':
        return this.displayOptimizer.getOptimizedBOLLStyles(config)
      default:
        return {}
    }
  }

  /**
   * 处理数据平滑
   * @param {Array} data - 原始数据
   * @param {Object} options - 处理选项
   * @returns {Array}
   */
  processData(data, options = {}) {
    const {
      smooth = false,
      smoothType = 'ma',
      smoothPeriod = 3,
      removeOutliers = false,
      outlierThreshold = 2
    } = options

    let processedData = [...data]

    if (removeOutliers) {
      processedData = this.dataProcessor.removeOutliers(processedData, outlierThreshold)
    }

    if (smooth) {
      if (smoothType === 'ema') {
        processedData = this.dataProcessor.exponentialMovingAverage(processedData, 0.3)
      } else {
        processedData = this.dataProcessor.movingAverage(processedData, smoothPeriod)
      }
    }

    return processedData
  }

  /**
   * 获取性能统计
   * @param {string} indicatorId - 指标ID
   * @returns {Object}
   */
  getPerformanceStats(indicatorId) {
    return this.performanceMonitor.getStats(indicatorId)
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.updateQueue.clear()
    this.cache.clear()
  }
}

// 创建全局实例
export const indicatorOptimizer = new IndicatorOptimizer()

// 导出工具类
export {
  IndicatorDataCache,
  UpdateQueueManager,
  IndicatorPerformanceMonitor,
  IndicatorDisplayOptimizer,
  DataSmoothProcessor
}