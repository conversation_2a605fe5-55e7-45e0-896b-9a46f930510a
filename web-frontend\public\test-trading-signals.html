<!DOCTYPE html>
<html>
<head>
    <title>交易信号测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .test-result {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .button:hover {
            background: #40a9ff;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin: 2px;
        }
        .status-green { background: #52c41a; color: white; }
        .status-red { background: #ff4d4f; color: white; }
        .status-blue { background: #1890ff; color: white; }
        .status-orange { background: #faad14; color: white; }
        .status-gray { background: #d9d9d9; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>交易信号功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 技术指标计算测试</div>
            <button class="button" onclick="testTechnicalIndicators()">测试技术指标计算</button>
            <div id="technical-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 交易信号生成测试</div>
            <button class="button" onclick="testTradingSignals()">测试交易信号</button>
            <div id="signals-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 实时数据获取测试</div>
            <button class="button" onclick="testRealData()">获取真实数据</button>
            <div id="real-data-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 信号状态显示</div>
            <div id="signal-display"></div>
        </div>
    </div>

    <script>
        // 生成模拟价格数据
        function generateMockPrices(count = 50) {
            const prices = []
            let basePrice = 50000
            
            for (let i = 0; i < count; i++) {
                const change = (Math.random() - 0.5) * 1000
                basePrice += change
                prices.push(basePrice)
            }
            
            return prices
        }

        // 计算移动平均线
        function calculateMA(prices, period) {
            if (prices.length < period) return 0
            const sum = prices.slice(-period).reduce((a, b) => a + b, 0)
            return sum / period
        }

        // 计算EMA
        function calculateEMA(prices, period) {
            if (prices.length < period) return 0
            const multiplier = 2 / (period + 1)
            let ema = prices[0]
            
            for (let i = 1; i < prices.length; i++) {
                ema = (prices[i] * multiplier) + (ema * (1 - multiplier))
            }
            
            return ema
        }

        // 计算RSI
        function calculateRSI(prices, period = 14) {
            if (prices.length < period + 1) return 50

            const changes = []
            for (let i = 1; i < prices.length; i++) {
                changes.push(prices[i] - prices[i - 1])
            }

            let gains = 0
            let losses = 0
            
            for (let i = 0; i < period; i++) {
                if (changes[i] > 0) {
                    gains += changes[i]
                } else {
                    losses += Math.abs(changes[i])
                }
            }

            const avgGain = gains / period
            const avgLoss = losses / period

            if (avgLoss === 0) {
                return avgGain === 0 ? 50 : 100
            }

            const rs = avgGain / avgLoss
            return 100 - (100 / (1 + rs))
        }

        // 测试技术指标计算
        function testTechnicalIndicators() {
            const prices = generateMockPrices(100)
            const currentPrice = prices[prices.length - 1]
            
            const indicators = {
                ma5: calculateMA(prices, 5),
                ma10: calculateMA(prices, 10),
                ma20: calculateMA(prices, 20),
                ema5: calculateEMA(prices, 5),
                ema10: calculateEMA(prices, 10),
                ema20: calculateEMA(prices, 20),
                rsi: calculateRSI(prices, 14),
                currentPrice: currentPrice
            }
            
            const result = `技术指标计算结果:
当前价格: ${currentPrice.toFixed(2)}

移动平均线:
MA5:  ${indicators.ma5.toFixed(2)}
MA10: ${indicators.ma10.toFixed(2)}
MA20: ${indicators.ma20.toFixed(2)}

指数移动平均线:
EMA5:  ${indicators.ema5.toFixed(2)}
EMA10: ${indicators.ema10.toFixed(2)}
EMA20: ${indicators.ema20.toFixed(2)}

其他指标:
RSI: ${indicators.rsi.toFixed(2)}

验证结果:
✓ 所有指标都有有效数值
✓ MA5 < MA10 < MA20: ${indicators.ma5 < indicators.ma10 && indicators.ma10 < indicators.ma20 ? '是' : '否'}
✓ RSI在0-100范围: ${indicators.rsi >= 0 && indicators.rsi <= 100 ? '是' : '否'}`
            
            document.getElementById('technical-result').textContent = result
            
            // 保存数据供其他测试使用
            window.testData = { indicators, currentPrice }
        }

        // 测试交易信号生成
        function testTradingSignals() {
            if (!window.testData) {
                testTechnicalIndicators()
            }
            
            const { indicators, currentPrice } = window.testData
            
            // 生成交易信号
            const signals = {
                emaSignal: indicators.ema5 > indicators.ema10 ? '金叉' : '死叉',
                emaSlowStatus: currentPrice > indicators.ema20 ? '多头' : '空头',
                emaMidStatus: currentPrice > indicators.ema10 ? '多头' : '空头',
                maFastStatus: currentPrice > indicators.ma5 ? '多头' : '空头',
                maMidStatus: currentPrice > indicators.ma10 ? '多头' : '空头',
                maSlowStatus: currentPrice > indicators.ma20 ? '多头' : '空头',
                rsiStatus: indicators.rsi > 70 ? 'RSI超买' : indicators.rsi < 30 ? 'RSI超卖' : 'RSI中立'
            }
            
            // 统计多空信号
            let bullCount = 0
            let bearCount = 0
            
            Object.values(signals).forEach(signal => {
                if (signal.includes('多头') || signal === '金叉') bullCount++
                if (signal.includes('空头') || signal === '死叉') bearCount++
            })
            
            const result = `交易信号生成结果:

EMA信号:
交叉状态: ${signals.emaSignal}
慢线状态: ${signals.emaSlowStatus} (${indicators.ema20.toFixed(2)})
中线状态: ${signals.emaMidStatus} (${indicators.ema10.toFixed(2)})

MA信号:
快线状态: ${signals.maFastStatus} (${indicators.ma5.toFixed(2)})
中线状态: ${signals.maMidStatus} (${indicators.ma10.toFixed(2)})
慢线状态: ${signals.maSlowStatus} (${indicators.ma20.toFixed(2)})

RSI信号:
状态: ${signals.rsiStatus} (${indicators.rsi.toFixed(2)})

统计:
多头信号: ${bullCount}
空头信号: ${bearCount}
综合建议: ${bullCount > bearCount ? '买入' : bearCount > bullCount ? '卖出' : '观望'}`
            
            document.getElementById('signals-result').textContent = result
            
            // 更新信号显示
            updateSignalDisplay(signals, indicators)
        }

        // 更新信号显示
        function updateSignalDisplay(signals, indicators) {
            const display = document.getElementById('signal-display')
            
            const getStatusClass = (signal) => {
                if (signal.includes('多头') || signal === '金叉') return 'status-green'
                if (signal.includes('空头') || signal === '死叉') return 'status-red'
                if (signal.includes('超买')) return 'status-orange'
                if (signal.includes('超卖')) return 'status-green'
                return 'status-blue'
            }
            
            display.innerHTML = `
                <div style="display: grid; gap: 10px;">
                    <div>EMA 交叉: <span class="${getStatusClass(signals.emaSignal)} status">${signals.emaSignal}</span></div>
                    <div>EMA 慢线状态: <span class="${getStatusClass(signals.emaSlowStatus)} status">${signals.emaSlowStatus}</span> ${indicators.ema20.toFixed(2)}</div>
                    <div>EMA 中线状态: <span class="${getStatusClass(signals.emaMidStatus)} status">${signals.emaMidStatus}</span> ${indicators.ema10.toFixed(2)}</div>
                    <div>MA 快线状态: <span class="${getStatusClass(signals.maFastStatus)} status">${signals.maFastStatus}</span> ${indicators.ma5.toFixed(2)}</div>
                    <div>MA 中线状态: <span class="${getStatusClass(signals.maMidStatus)} status">${signals.maMidStatus}</span> ${indicators.ma10.toFixed(2)}</div>
                    <div>MA 慢线状态: <span class="${getStatusClass(signals.maSlowStatus)} status">${signals.maSlowStatus}</span> ${indicators.ma20.toFixed(2)}</div>
                    <div>RSI 状态: <span class="${getStatusClass(signals.rsiStatus)} status">${signals.rsiStatus}</span> ${indicators.rsi.toFixed(2)}</div>
                </div>
            `
        }

        // 测试真实数据获取
        async function testRealData() {
            try {
                const response = await fetch('/api/okx/market/candles?instId=BTC-USDT-SWAP&bar=1H&limit=50')
                const data = await response.json()
                
                if (data.code === '0' && data.data) {
                    const prices = data.data.reverse().map(item => parseFloat(item[4]))
                    const currentPrice = prices[prices.length - 1]
                    
                    const indicators = {
                        ma5: calculateMA(prices, 5),
                        ma10: calculateMA(prices, 10),
                        ma20: calculateMA(prices, 20),
                        ema5: calculateEMA(prices, 5),
                        ema10: calculateEMA(prices, 10),
                        ema20: calculateEMA(prices, 20),
                        rsi: calculateRSI(prices, 14),
                        currentPrice: currentPrice
                    }
                    
                    const result = `真实数据测试结果:
数据来源: OKX BTC-USDT-SWAP 1H
数据点数: ${prices.length}
当前价格: ${currentPrice.toFixed(2)}

计算结果:
MA5:  ${indicators.ma5.toFixed(2)}
MA10: ${indicators.ma10.toFixed(2)}
MA20: ${indicators.ma20.toFixed(2)}
EMA5:  ${indicators.ema5.toFixed(2)}
EMA10: ${indicators.ema10.toFixed(2)}
EMA20: ${indicators.ema20.toFixed(2)}
RSI: ${indicators.rsi.toFixed(2)}

✓ 成功获取真实数据并计算指标`
                    
                    document.getElementById('real-data-result').textContent = result
                    
                    // 更新测试数据
                    window.testData = { indicators, currentPrice }
                } else {
                    document.getElementById('real-data-result').textContent = `获取数据失败: ${data.msg || '未知错误'}`
                }
            } catch (error) {
                document.getElementById('real-data-result').textContent = `网络错误: ${error.message}`
            }
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            testTechnicalIndicators()
            setTimeout(() => {
                testTradingSignals()
            }, 500)
        }
    </script>
</body>
</html>
