import { ref, computed } from 'vue'
import axios from 'axios'

/**
 * 增强版数据获取器
 * 提供更强大的数据获取、缓存和错误处理功能
 * @param {Function} handleError - 错误处理函数
 * @returns {Object} 数据获取器实例
 */
export function useEnhancedDataFetching(handleError) {
  // 状态管理
  const isLoading = ref(false)
  const chartData = ref([])
  const currentPrice = ref(null)
  const lastUpdateTime = ref(null)
  const dataSource = ref('real') // 强制使用真实数据源
  const retryCount = ref(0)
  const maxRetries = 3
  
  // 缓存管理
  const dataCache = ref(new Map())
  const cacheExpiry = 5 * 60 * 1000 // 5分钟缓存
  
  // API配置
  const API_CONFIG = {
    baseURL: 'https://www.okx.com',
    timeout: 10000,
    retryDelay: 1000
  }

  // 支持的交易对和时间周期
  const SUPPORTED_SYMBOLS = [
    // 现货交易对
    'BTC-USDT', 'ETH-USDT', 'BNB-USDT', 'ADA-USDT', 'SOL-USDT',
    'XRP-USDT', 'DOT-USDT', 'DOGE-USDT', 'AVAX-USDT', 'MATIC-USDT',
    // 合约交易对
    'BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'BNB-USDT-SWAP', 'ADA-USDT-SWAP', 'SOL-USDT-SWAP',
    'XRP-USDT-SWAP', 'DOT-USDT-SWAP', 'DOGE-USDT-SWAP', 'AVAX-USDT-SWAP', 'MATIC-USDT-SWAP'
  ]
  
  const SUPPORTED_TIMEFRAMES = {
    '1m': '1m',
    '5m': '5m',
    '15m': '15m',
    '30m': '30m',
    '1h': '1H',
    '4h': '4H',
    '1d': '1D',
    '1w': '1W'
  }

  /**
   * 生成缓存键
   * @param {string} symbol - 交易对
   * @param {string} timeframe - 时间周期
   * @param {number} limit - 数据条数
   * @returns {string} 缓存键
   */
  const getCacheKey = (symbol, timeframe, limit) => {
    return `${symbol}_${timeframe}_${limit}`
  }

  /**
   * 检查缓存是否有效
   * @param {string} key - 缓存键
   * @returns {boolean} 是否有效
   */
  const isCacheValid = (key) => {
    const cached = dataCache.value.get(key)
    if (!cached) return false
    
    const now = Date.now()
    return (now - cached.timestamp) < cacheExpiry
  }

  /**
   * 获取缓存数据
   * @param {string} key - 缓存键
   * @returns {Array|null} 缓存的数据
   */
  const getCachedData = (key) => {
    if (isCacheValid(key)) {
      const cached = dataCache.value.get(key)
      console.log(`使用缓存数据: ${key}`)
      return cached.data
    }
    return null
  }

  /**
   * 设置缓存数据
   * @param {string} key - 缓存键
   * @param {Array} data - 数据
   */
  const setCachedData = (key, data) => {
    dataCache.value.set(key, {
      data: [...data],
      timestamp: Date.now()
    })
    
    // 清理过期缓存
    cleanExpiredCache()
  }

  /**
   * 清理过期缓存
   */
  const cleanExpiredCache = () => {
    const now = Date.now()
    for (const [key, cached] of dataCache.value.entries()) {
      if ((now - cached.timestamp) >= cacheExpiry) {
        dataCache.value.delete(key)
      }
    }
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

  /**
   * 验证交易对
   * @param {string} symbol - 交易对
   * @returns {boolean} 是否支持
   */
  const isValidSymbol = (symbol) => {
    return SUPPORTED_SYMBOLS.includes(symbol.toUpperCase())
  }

  /**
   * 验证时间周期
   * @param {string} timeframe - 时间周期
   * @returns {boolean} 是否支持
   */
  /**
   * 检查时间周期是否有效
   * @param {string} timeframe - 时间周期（可以是键如'1h'或值如'1H'）
   * @returns {boolean} 是否有效
   */
  const isValidTimeframe = (timeframe) => {
    return Object.keys(SUPPORTED_TIMEFRAMES).includes(timeframe) || 
           Object.values(SUPPORTED_TIMEFRAMES).includes(timeframe)
  }

  /**
   * 格式化K线数据
   * @param {Array} rawData - 原始数据
   * @returns {Array} 格式化后的数据
   */
  const formatKlineData = (rawData) => {
    if (!Array.isArray(rawData)) {
      throw new Error('数据格式错误：期望数组格式')
    }

    return rawData.map((item, index) => {
      if (!Array.isArray(item) || item.length < 6) {
        throw new Error(`数据格式错误：第${index}条数据格式不正确`)
      }

      const [timestamp, open, high, low, close, volume] = item
      
      return {
        time: parseInt(timestamp),
        open: parseFloat(open),
        high: parseFloat(high),
        low: parseFloat(low),
        close: parseFloat(close),
        volume: parseFloat(volume),
        // 添加额外的计算字段
        change: parseFloat(close) - parseFloat(open),
        changePercent: ((parseFloat(close) - parseFloat(open)) / parseFloat(open)) * 100,
        amplitude: ((parseFloat(high) - parseFloat(low)) / parseFloat(open)) * 100
      }
    }).sort((a, b) => a.time - b.time) // 确保时间顺序
  }

  /**
   * 加载真实市场数据
   * @param {string} symbol - 交易对
   * @param {string} timeframe - 时间周期
   * @param {number} limit - 数据条数
   * @returns {Promise<Array>} K线数据
   */
  const loadRealMarketData = async (symbol = 'BTC-USDT', timeframe = '1h', limit = 100) => {
    try {
      // 参数验证
      if (!isValidSymbol(symbol)) {
        throw new Error(`不支持的交易对: ${symbol}`)
      }
      
      if (!isValidTimeframe(timeframe)) {
        throw new Error(`不支持的时间周期: ${timeframe}`)
      }

      if (limit <= 0 || limit > 300) {
        throw new Error(`数据条数必须在1-300之间: ${limit}`)
      }

      // 检查缓存
      const cacheKey = getCacheKey(symbol, timeframe, limit)
      const cachedData = getCachedData(cacheKey)
      if (cachedData) {
        chartData.value = cachedData
        return cachedData
      }

      console.log(`加载真实市场数据: ${symbol}, ${timeframe}, ${limit}条`)
      isLoading.value = true
      retryCount.value = 0

      const response = await axios.get('/api/okx/market/candles', {
        timeout: API_CONFIG.timeout,
        params: {
          instId: symbol,
          bar: SUPPORTED_TIMEFRAMES[timeframe] || timeframe,
          limit: limit
        },
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })

      if (response.data.code !== 0) {
        throw new Error(`API错误: ${response.data.msg || '未知错误'}`)
      }

      const formattedData = formatKlineData(response.data.data)
      
      if (formattedData.length === 0) {
        throw new Error('获取到的数据为空')
      }

      // 缓存数据
      setCachedData(cacheKey, formattedData)
      
      chartData.value = formattedData
      lastUpdateTime.value = new Date()
      
      console.log(`成功加载${formattedData.length}条真实数据`)
      return formattedData

    } catch (error) {
      console.error('加载真实市场数据失败:', error)
      
      // 重试逻辑
      if (retryCount.value < maxRetries && error.code !== 'ECONNABORTED') {
        retryCount.value++
        console.log(`第${retryCount.value}次重试...`)
        
        await delay(API_CONFIG.retryDelay * retryCount.value)
        return await loadRealMarketData(symbol, timeframe, limit)
      }
      
      // 如果真实数据加载失败，重试WebSocket连接
      console.warn('真实数据加载失败，重试WebSocket连接')
      dataSource.value = 'real'
      throw error // 抛出错误，让上层处理
      
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 通过WebSocket获取实时数据
   * @param {string} symbol - 交易对
   * @param {string} timeframe - 时间周期
   * @param {number} limit - 数据条数
   * @returns {Promise<Array>} WebSocket实时K线数据
   */
  const fetchWebSocketData = async (symbol = 'BTC-USDT', timeframe = '1h', limit = 100) => {
    try {
      console.log(`通过WebSocket获取实时数据: ${symbol}, ${timeframe}, ${limit}条`)
      isLoading.value = true

      // 连接WebSocket并订阅数据
      const webSocketService = await import('@/services/webSocketService')
      await webSocketService.connectMarketData(symbol, timeframe)
      
      // 等待WebSocket数据推送
      await delay(2000) // 等待数据推送
      
      // 从WebSocket服务获取实时数据
      const realtimeData = webSocketService.getRealtimeData()
      const webSocketData = realtimeData.klineData || []
      
      // 缓存WebSocket数据
      const cacheKey = getCacheKey(symbol, timeframe, limit)
      setCachedData(cacheKey, webSocketData)
      
      chartData.value = webSocketData
      lastUpdateTime.value = new Date()
      
      console.log(`成功获取${webSocketData.length}条WebSocket实时数据`)
      return webSocketData
      
    } catch (error) {
      console.error('WebSocket数据获取失败:', error)
      if (handleError) {
        handleError(error, 'fetchWebSocketData')
      }
      throw error
      
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 获取时间周期对应的毫秒数
   * @param {string} timeframe - 时间周期
   * @returns {number} 毫秒数
   */
  const getTimeframeMs = (timeframe) => {
    const timeframeMap = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000,
      '1w': 7 * 24 * 60 * 60 * 1000
    }
    return timeframeMap[timeframe] || 60 * 60 * 1000
  }

  /**
   * 加载当前最新价格（强制从真实API获取）
   * @param {string} symbol - 交易对
   * @returns {Promise<number>} 当前价格
   */
  const loadCurrentPrice = async (symbol = 'BTC-USDT') => {
    try {
      if (!isValidSymbol(symbol)) {
        throw new Error(`不支持的交易对: ${symbol}`)
      }

      console.log(`获取当前价格: ${symbol}`)
      
      // 强制从OKX API获取实时价格
      const response = await axios.get('/api/okx/market/ticker', {
        timeout: 5000,
        params: {
          instId: symbol
        }
      })

      if (response.data.code !== 0) {
        throw new Error(`API错误: ${response.data.msg || '未知错误'}`)
      }

      const tickerData = response.data.data[0]
      if (!tickerData) {
        throw new Error('获取价格数据为空')
      }

      currentPrice.value = parseFloat(tickerData.last)
      console.log(`✅ 实时价格: ${currentPrice.value}`)
      
      return currentPrice.value

    } catch (error) {
      console.error('❌ 获取实时价格失败:', error)
      
      // 如果获取失败，使用最新K线数据的收盘价作为备选
      if (chartData.value.length > 0) {
        const latestData = chartData.value[chartData.value.length - 1]
        currentPrice.value = latestData.close
        console.warn(`⚠️ 使用最新K线收盘价: ${currentPrice.value}`)
        return currentPrice.value
      }
      
      if (handleError) {
        handleError(error, 'loadCurrentPrice')
      }
      throw error
    }
  }

  /**
   * 切换数据源
   * @param {string} source - 数据源类型 ('real' | 'mock')
   */
  const toggleDataSource = (source) => {
    if (['real', 'mock'].includes(source)) {
      dataSource.value = source
      console.log(`数据源切换为: ${source}`)
    } else {
      console.warn(`无效的数据源: ${source}`)
    }
  }

  /**
   * 清除所有缓存
   */
  const clearCache = () => {
    dataCache.value.clear()
    console.log('缓存已清除')
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  const getCacheStats = () => {
    const totalEntries = dataCache.value.size
    const validEntries = Array.from(dataCache.value.keys()).filter(key => isCacheValid(key)).length
    const expiredEntries = totalEntries - validEntries
    
    return {
      total: totalEntries,
      valid: validEntries,
      expired: expiredEntries,
      hitRate: totalEntries > 0 ? (validEntries / totalEntries * 100).toFixed(2) + '%' : '0%'
    }
  }

  // 计算属性
  const hasData = computed(() => chartData.value.length > 0)
  const latestData = computed(() => {
    return hasData.value ? chartData.value[chartData.value.length - 1] : null
  })
  const dataAge = computed(() => {
    if (!lastUpdateTime.value) return null
    const now = new Date()
    const diff = now - lastUpdateTime.value
    return Math.floor(diff / 1000) // 秒
  })

  return {
    // 状态
    isLoading,
    chartData,
    currentPrice,
    lastUpdateTime,
    dataSource,
    retryCount,
    
    // 计算属性
    hasData,
    latestData,
    dataAge,
    
    // 方法
    loadRealMarketData,
    fetchWebSocketData,
    loadCurrentPrice,
    toggleDataSource,
    clearCache,
    getCacheStats,
    
    // 工具方法
    isValidSymbol,
    isValidTimeframe,
    formatKlineData,
    
    // 常量
    SUPPORTED_SYMBOLS,
    SUPPORTED_TIMEFRAMES
  }
}

export default useEnhancedDataFetching