import { describe, it, expect } from 'vitest';
import { formatPrice, formatVolume } from '../formatters';

describe('formatters', () => {
  describe('formatPrice', () => {
    it('should format a number into a string with 2 decimal places', () => {
      expect(formatPrice(12345.6789)).toBe('12,345.68');
    });

    it('should handle zero', () => {
      expect(formatPrice(0)).toBe('0.00');
    });

    it('should handle numbers less than 1', () => {
      expect(formatPrice(0.12345)).toBe('0.12');
    });

    it('should handle large numbers', () => {
      expect(formatPrice(1000000)).toBe('1,000,000.00');
    });

    it('should return an empty string for null or undefined input', () => {
      expect(formatPrice(null)).toBe('');
      expect(formatPrice(undefined)).toBe('');
    });
  });

  describe('formatVolume', () => {
    it('should format a large number into a string with a K suffix', () => {
      expect(formatVolume(12345)).toBe('12.35K');
    });

    it('should format a very large number into a string with an M suffix', () => {
      expect(formatVolume(12345678)).toBe('12.35M');
    });

    it('should handle numbers less than 1000', () => {
      expect(formatVolume(999)).toBe('999');
    });

    it('should handle zero', () => {
      expect(formatVolume(0)).toBe('0');
    });

    it('should return an empty string for null or undefined input', () => {
      expect(formatVolume(null)).toBe('');
      expect(formatVolume(undefined)).toBe('');
    });
  });
});