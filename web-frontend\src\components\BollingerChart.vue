<template>
  <BaseChart
    title="布林带"
    :signals="signals"
    :settings="localSettings"
    @save-settings="saveSettings"
    @reset-settings="resetSettings"
  >
    <template #chart>
      <div ref="chartRef" class="bollinger-chart" :style="{ height: chartHeight + 'px' }"></div>
    </template>
    <template #settings-form>
      <a-form :model="localSettings" layout="vertical">
        <a-form-item label="周期" name="period">
          <a-slider
            v-model:value="localSettings.period"
            :min="10"
            :max="50"
            :marks="{ 20: '20', 26: '26' }"
          />
        </a-form-item>
        <a-form-item label="标准差倍数" name="stdDev">
          <a-slider
            v-model:value="localSettings.stdDev"
            :min="1"
            :max="3"
            :step="0.1"
            :marks="{ 2: '2.0' }"
          />
        </a-form-item>
        <a-form-item label="移动平均类型" name="maType">
          <a-select v-model:value="localSettings.maType">
            <a-select-option value="sma">简单移动平均(SMA)</a-select-option>
            <a-select-option value="ema">指数移动平均(EMA)</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="localSettings.showBandWidth">
            显示带宽指标
          </a-checkbox>
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="localSettings.showPercentB">
            显示%B指标
          </a-checkbox>
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="localSettings.enableAlerts">
            启用交易信号
          </a-checkbox>
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="localSettings.showFillArea">
            填充带状区域
          </a-checkbox>
        </a-form-item>
      </a-form>
    </template>
    <template #signal-list="{ item }">
        <a-list-item-meta>
          <template #title>
            <a-space>
              <a-tag :color="getSignalColor(item.type)">
                {{ getSignalText(item.type) }}
              </a-tag>
              <span>{{ item.message }}</span>
            </a-space>
          </template>
          <template #description>
            <div>
              <div>时间: {{ formatTime(item.timestamp) }}</div>
              <div>价格: {{ item.price?.toFixed(2) }}</div>
              <div>上轨: {{ item.upperBand?.toFixed(2) }}</div>
              <div>中轨: {{ item.middleBand?.toFixed(2) }}</div>
              <div>下轨: {{ item.lowerBand?.toFixed(2) }}</div>
              <div>%B: {{ item.percentB?.toFixed(2) }}%</div>
              <div>置信度: {{ item.confidence }}%</div>
            </div>
          </template>
        </a-list-item-meta>
    </template>
  </BaseChart>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import BaseChart from './BaseChart.vue'
import { useChartCommon } from '@/composables/useChartCommon'
import { CHART_DATA_INDEX } from '@/constants/chartConstants'

// Props
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  theme: {
    type: String,
    default: 'light'
  },
  settings: {
    type: Object,
    default: () => ({
      period: 20,
      stdDev: 2.0,
      maType: 'sma',
      showBandWidth: false,
      showPercentB: false,
      enableAlerts: true,
      showFillArea: true
    })
  },
  height: {
    type: Number,
    default: 300
  }
})

// Emits
const emit = defineEmits(['signal', 'settings-change'])

// 使用共享逻辑
const { initChart, updateChart, disposeChart, calculateMA, calculateEMA } = useChartCommon()

// 本地状态
const chartRef = ref(null)
const chartInstance = ref(null)
const signals = ref([])

// 本地设置（用于模态框编辑）
const localSettings = ref({ ...props.settings })

// 计算属性
const chartHeight = computed(() => props.height)

// 布林带计算函数
const calculateBollingerBands = (data, period = 20, stdDev = 2.0, maType = 'sma') => {
  if (!data || data.length < period) {
    return {
      upperBand: [],
      middleBand: [],
      lowerBand: [],
      bandWidth: [],
      percentB: []
    }
  }

  const closes = data.map(item => item[CHART_DATA_INDEX.CLOSE])
  
  // 计算移动平均线（中轨）
  const middleBand = maType === 'ema' ? 
    calculateEMA(closes, period) : 
    calculateMA(closes, period)

  const upperBand = []
  const lowerBand = []
  const bandWidth = []
  const percentB = []

  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      upperBand.push(null)
      lowerBand.push(null)
      bandWidth.push(null)
      percentB.push(null)
      continue
    }

    const ma = middleBand[i]
    if (ma === null) {
      upperBand.push(null)
      lowerBand.push(null)
      bandWidth.push(null)
      percentB.push(null)
      continue
    }

    // 计算标准差
    const periodData = closes.slice(i - period + 1, i + 1)
    const variance = periodData.reduce((sum, price) => {
      return sum + Math.pow(price - ma, 2)
    }, 0) / period
    const standardDeviation = Math.sqrt(variance)

    // 计算上下轨
    const upper = ma + (standardDeviation * stdDev)
    const lower = ma - (standardDeviation * stdDev)
    
    upperBand.push(upper)
    lowerBand.push(lower)

    // 计算带宽 (BandWidth = (Upper - Lower) / Middle * 100)
    const bw = ((upper - lower) / ma) * 100
    bandWidth.push(bw)

    // 计算%B = (Price - Lower) / (Upper - Lower) * 100
    const currentPrice = closes[i]
    const pb = ((currentPrice - lower) / (upper - lower)) * 100
    percentB.push(pb)
  }

  return {
    upperBand,
    middleBand,
    lowerBand,
    bandWidth,
    percentB
  }
}

// 生成交易信号
const generateSignals = (data, upperBand, middleBand, lowerBand, percentB) => {
  const newSignals = []
  const { enableAlerts } = props.settings

  if (!enableAlerts || data.length < 2) return newSignals

  for (let i = 1; i < data.length; i++) {
    const currentPrice = data[i][CHART_DATA_INDEX.CLOSE]
    const prevPrice = data[i - 1][CHART_DATA_INDEX.CLOSE]
    const currentUpper = upperBand[i]
    const currentMiddle = middleBand[i]
    const currentLower = lowerBand[i]
    const currentPercentB = percentB[i]
    const prevPercentB = percentB[i - 1]

    if (!currentUpper || !currentMiddle || !currentLower) continue

    // 价格触及上轨（超买信号）
    if (currentPrice >= currentUpper && prevPrice < upperBand[i - 1]) {
      newSignals.push({
        id: `boll_upper_touch_${i}_${Date.now()}`,
        type: 'sell',
        message: '价格触及布林上轨，可能回调',
        timestamp: Date.now(),
        price: currentPrice,
        upperBand: currentUpper,
        middleBand: currentMiddle,
        lowerBand: currentLower,
        percentB: currentPercentB,
        confidence: calculateConfidence('upper_touch', currentPercentB),
        indicator: 'Bollinger'
      })
    }

    // 价格触及下轨（超卖信号）
    if (currentPrice <= currentLower && prevPrice > lowerBand[i - 1]) {
      newSignals.push({
        id: `boll_lower_touch_${i}_${Date.now()}`,
        type: 'buy',
        message: '价格触及布林下轨，可能反弹',
        timestamp: Date.now(),
        price: currentPrice,
        upperBand: currentUpper,
        middleBand: currentMiddle,
        lowerBand: currentLower,
        percentB: currentPercentB,
        confidence: calculateConfidence('lower_touch', currentPercentB),
        indicator: 'Bollinger'
      })
    }

    // 价格突破上轨（强势突破）
    if (currentPrice > currentUpper && currentPercentB > 100) {
      newSignals.push({
        id: `boll_upper_break_${i}_${Date.now()}`,
        type: 'buy',
        message: '价格突破布林上轨，强势上涨',
        timestamp: Date.now(),
        price: currentPrice,
        upperBand: currentUpper,
        middleBand: currentMiddle,
        lowerBand: currentLower,
        percentB: currentPercentB,
        confidence: calculateConfidence('upper_break', currentPercentB),
        indicator: 'Bollinger'
      })
    }

    // 价格跌破下轨（弱势突破）
    if (currentPrice < currentLower && currentPercentB < 0) {
      newSignals.push({
        id: `boll_lower_break_${i}_${Date.now()}`,
        type: 'sell',
        message: '价格跌破布林下轨，弱势下跌',
        timestamp: Date.now(),
        price: currentPrice,
        upperBand: currentUpper,
        middleBand: currentMiddle,
        lowerBand: currentLower,
        percentB: currentPercentB,
        confidence: calculateConfidence('lower_break', currentPercentB),
        indicator: 'Bollinger'
      })
    }

    // 中轨支撑/阻力
    if (prevPrice < middleBand[i - 1] && currentPrice > currentMiddle) {
      newSignals.push({
        id: `boll_middle_support_${i}_${Date.now()}`,
        type: 'buy',
        message: '价格突破中轨，趋势转强',
        timestamp: Date.now(),
        price: currentPrice,
        upperBand: currentUpper,
        middleBand: currentMiddle,
        lowerBand: currentLower,
        percentB: currentPercentB,
        confidence: calculateConfidence('middle_break_up', currentPercentB),
        indicator: 'Bollinger'
      })
    }

    if (prevPrice > middleBand[i - 1] && currentPrice < currentMiddle) {
      newSignals.push({
        id: `boll_middle_resistance_${i}_${Date.now()}`,
        type: 'sell',
        message: '价格跌破中轨，趋势转弱',
        timestamp: Date.now(),
        price: currentPrice,
        upperBand: currentUpper,
        middleBand: currentMiddle,
        lowerBand: currentLower,
        percentB: currentPercentB,
        confidence: calculateConfidence('middle_break_down', currentPercentB),
        indicator: 'Bollinger'
      })
    }
  }

  return newSignals
}

// 计算信号置信度
const calculateConfidence = (signalType, percentB) => {
  let confidence = 50

  switch (signalType) {
    case 'upper_touch':
      confidence = Math.min(90, 50 + Math.abs(percentB - 100) * 0.5)
      break
    case 'lower_touch':
      confidence = Math.min(90, 50 + Math.abs(percentB) * 0.5)
      break
    case 'upper_break':
      confidence = Math.min(95, 60 + (percentB - 100) * 0.3)
      break
    case 'lower_break':
      confidence = Math.min(95, 60 + Math.abs(percentB) * 0.3)
      break
    case 'middle_break_up':
      confidence = 50 + (percentB - 50) * 0.4
      break
    case 'middle_break_down':
      confidence = 50 + (50 - percentB) * 0.4
      break
  }

  return Math.max(10, Math.min(95, Math.round(confidence)))
}

// 初始化图表
const initBollingerChart = () => {
  if (!chartRef.value || !props.data.length) return

  // 计算布林带数据
  const { upperBand, middleBand, lowerBand, bandWidth, percentB } = calculateBollingerBands(
    props.data,
    props.settings.period,
    props.settings.stdDev,
    props.settings.maType
  )

  // 生成交易信号
  const newSignals = generateSignals(props.data, upperBand, middleBand, lowerBand, percentB)
  signals.value = newSignals

  // 发送信号给父组件
  newSignals.forEach(signal => {
    emit('signal', signal)
  })

  // 准备图表数据
  const dates = props.data.map(item => item[CHART_DATA_INDEX.DATETIME])
  const closes = props.data.map(item => item[CHART_DATA_INDEX.CLOSE])

  const series = [
    {
      name: '收盘价',
      type: 'line',
      data: closes,
      lineStyle: {
        color: '#1890ff',
        width: 2
      },
      showSymbol: false,
      z: 10
    },
    {
      name: '上轨',
      type: 'line',
      data: upperBand,
      lineStyle: {
        color: '#ff4d4f',
        width: 1
      },
      showSymbol: false
    },
    {
      name: '中轨',
      type: 'line',
      data: middleBand,
      lineStyle: {
        color: '#52c41a',
        width: 1.5
      },
      showSymbol: false
    },
    {
      name: '下轨',
      type: 'line',
      data: lowerBand,
      lineStyle: {
        color: '#ff4d4f',
        width: 1
      },
      showSymbol: false
    }
  ]

  // 添加填充区域
  if (props.settings.showFillArea) {
    series.push({
      name: '布林带区域',
      type: 'line',
      data: upperBand,
      lineStyle: {
        opacity: 0
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(255, 77, 79, 0.1)'
            },
            {
              offset: 1,
              color: 'rgba(255, 77, 79, 0.05)'
            }
          ]
        }
      },
      stack: 'bollinger',
      showSymbol: false,
      silent: true
    })
  }

  const option = {
    animation: true,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function (params) {
        let result = `<div>${params[0].name}</div>`
        params.forEach(param => {
          if (param.value !== null && param.seriesName !== '布林带区域') {
            result += `<div>${param.marker}${param.seriesName}: ${param.value.toFixed(2)}</div>`
          }
        })
        
        // 添加%B信息
        const dataIndex = params[0].dataIndex
        if (percentB[dataIndex] !== null) {
          result += `<div>%B: ${percentB[dataIndex].toFixed(2)}%</div>`
        }
        if (bandWidth[dataIndex] !== null) {
          result += `<div>带宽: ${bandWidth[dataIndex].toFixed(2)}%</div>`
        }
        
        return result
      }
    },
    legend: {
      data: ['收盘价', '上轨', '中轨', '下轨'],
      top: 5,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      boundaryGap: false,
      axisLine: { onZero: false },
      splitLine: { show: false },
      axisLabel: {
        fontSize: 10
      }
    },
    yAxis: {
      scale: true,
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          opacity: 0.3
        }
      },
      axisLabel: {
        fontSize: 10
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 80,
        end: 100
      }
    ],
    series: series
  }

  chartInstance.value = initChart(chartRef.value, option, props.theme)
}

// 工具函数
const getSignalColor = (type) => {
  const colors = {
    buy: 'green',
    sell: 'red',
    warning: 'orange'
  }
  return colors[type] || 'blue'
}

const getSignalText = (type) => {
  const texts = {
    buy: '买入',
    sell: '卖出',
    warning: '警告'
  }
  return texts[type] || type
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 设置相关函数
const saveSettings = () => {
  emit('settings-change', { ...localSettings.value })
  showSettings.value = false
  message.success('布林带设置已保存')
}

const resetSettings = () => {
  localSettings.value = { ...props.settings }
}

// 监听数据变化
watch(
  () => [props.data, props.theme, props.settings],
  () => {
    if (chartInstance.value) {
      initBollingerChart()
    }
  },
  { deep: true }
)

// 监听设置变化
watch(
  () => props.settings,
  (newSettings) => {
    localSettings.value = { ...newSettings }
  },
  { deep: true, immediate: true }
)

// 组件挂载
onMounted(() => {
  if (props.data.length > 0) {
    initBollingerChart()
  }
})

// 组件卸载
onUnmounted(() => {
  if (chartInstance.value) {
    disposeChart(chartInstance.value)
  }
})
</script>

<style scoped>
.bollinger-chart {
  width: 100%;
}
</style>