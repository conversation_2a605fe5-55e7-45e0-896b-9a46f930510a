<template>
  <div class="indicator-panel" style="margin-top: 16px;">
    <a-row :gutter="16">
      <a-col v-if="displaySettings.showVolume" :span="8">
        <VolumeChart
          :chart-data="chartData"
          :is-dark-theme="isDarkTheme"
          :height="'200px'"
          chart-id="modular-volume-chart"
          @chart-ready="handleChartReady('volume', $event)"
          @chart-error="handleChartError"
        />
      </a-col>
      <a-col v-if="displaySettings.showRSI" :span="8">
        <RSIChart
          :chart-data="chartData"
          :is-dark-theme="isDarkTheme"
          :height="'200px'"
          :rsi-period="indicatorSettings.rsi.period"
          :overbought-level="indicatorSettings.rsi.overbought"
          :oversold-level="indicatorSettings.rsi.oversold"
          chart-id="modular-rsi-chart"
          @chart-ready="handleChartReady('rsi', $event)"
          @chart-error="handleChartError"
          @signal-generated="handleSignalGenerated"
        />
      </a-col>
      <a-col v-if="displaySettings.showMACD" :span="8">
        <MACDChart
          :chart-data="chartData"
          :is-dark-theme="isDarkTheme"
          :height="'200px'"
          :fast-period="indicatorSettings.macd.fastPeriod"
          :slow-period="indicatorSettings.macd.slowPeriod"
          :signal-period="indicatorSettings.macd.signalPeriod"
          chart-id="modular-macd-chart"
          @chart-ready="handleChartReady('macd', $event)"
          @chart-error="handleChartError"
          @signal-generated="handleSignalGenerated"
        />
      </a-col>
    </a-row>
    
    <a-row :gutter="16" style="margin-top: 16px;">
      <a-col v-if="displaySettings.showKDJ" :span="12">
        <KDJChart
          :chart-data="chartData"
          :is-dark-theme="isDarkTheme"
          :height="'200px'"
          :k-period="indicatorSettings.kdj.kPeriod"
          :d-period="indicatorSettings.kdj.dPeriod"
          :j-period="indicatorSettings.kdj.jPeriod"
          chart-id="modular-kdj-chart"
          @chart-ready="handleChartReady('kdj', $event)"
          @chart-error="handleChartError"
          @signal-generated="handleSignalGenerated"
        />
      </a-col>
      <a-col v-if="displaySettings.showBollingerBands" :span="12">
        <BollingerChart
          :chart-data="chartData"
          :is-dark-theme="isDarkTheme"
          :height="'200px'"
          :period="indicatorSettings.bollingerBands.period"
          :std-dev="indicatorSettings.bollingerBands.stdDev"
          chart-id="modular-boll-chart"
          @chart-ready="handleChartReady('bollinger', $event)"
          @chart-error="handleChartError"
          @signal-generated="handleSignalGenerated"
        />
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import VolumeChart from './VolumeChart.vue'
import RSIChart from './RSIChart.vue'
import MACDChart from './MACDChart.vue'
import KDJChart from './KDJChart.vue'
import BollingerChart from './BollingerChart.vue'

// Props
const props = defineProps({
  chartData: {
    type: Array,
    required: true
  },
  isDarkTheme: {
    type: Boolean,
    default: false
  },
  displaySettings: {
    type: Object,
    required: true
  },
  indicatorSettings: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits([
  'chart-ready',
  'chart-error',
  'signal-generated'
])

/**
 * 处理图表就绪事件
 * @param {string} type - 图表类型
 * @param {Object} instance - 图表实例
 */
const handleChartReady = (type, instance) => {
  emit('chart-ready', { type, instance })
}

/**
 * 处理图表错误事件
 * @param {Error} error - 错误对象
 */
const handleChartError = (error) => {
  console.error('指标图表错误:', error)
  emit('chart-error', error)
}

/**
 * 处理信号生成事件
 * @param {Object} signal - 交易信号
 */
const handleSignalGenerated = (signal) => {
  emit('signal-generated', signal)
}
</script>

<style scoped>
.indicator-panel {
  width: 100%;
}

.indicator-panel .ant-col {
  margin-bottom: 16px;
}
</style>