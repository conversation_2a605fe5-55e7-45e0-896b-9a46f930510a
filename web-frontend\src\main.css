:root {
  /* Font */
  --font-family-sans: system-ui, Avenir, Helvetica, Arial, sans-serif;

  /* Colors */
  --color-text: rgba(255, 255, 255, 0.87);
  --color-background: #242424;
  --color-primary: #646cff;
  --color-primary-hover: #535bf2;
  --color-button-bg: #1a1a1a;

  /* Other */
  --border-radius: 8px;

  font-family: var(--font-family-sans);
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: var(--color-text);
  background-color: var(--color-background);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: var(--color-primary);
  text-decoration: inherit;
}
a:hover {
  color: var(--color-primary-hover);
}

body {
  margin: 0;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: var(--border-radius);
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--color-button-bg);
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: var(--color-primary);
}

#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}