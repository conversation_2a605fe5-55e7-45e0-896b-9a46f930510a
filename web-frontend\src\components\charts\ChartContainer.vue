<template>
  <div class="chart-container">
    <!-- 控制面板 -->
    <ChartControls 
      @symbol-change="handleSymbolChange"
      @timeframe-change="handleTimeframeChange"
      @theme-toggle="handleThemeToggle"
      @indicators-toggle="handleIndicatorsToggle"
      @refresh="handleRefresh"
      @settings-open="$emit('settings-open')"
    />

    <!-- K线图容器 -->
    <div 
      id="main-candlestick-chart" 
      data-testid="chart-container"
      style="height: 500px; width: 100%; margin-bottom: 16px;"
    ></div>

    <!-- 技术指标图表 -->
    <IndicatorPanel 
      v-if="showIndicators"
      :chart-data="chartData"
      :is-dark-theme="isDarkTheme"
      :display-settings="displaySettings"
      :indicator-settings="indicatorSettings"
      @chart-ready="handleChartReady"
      @chart-error="handleChartError"
      @signal-generated="handleSignalGenerated"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useChartStore } from '@/stores/chartStore'
import { useChartData } from '@/composables/useChartData'
import { useChartTheme } from '@/composables/useChartTheme'
import ChartControls from '../ChartControls.vue'
import IndicatorPanel from './IndicatorPanel.vue'

// Props
const props = defineProps({
  showIndicators: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits([
  'settings-open',
  'chart-ready',
  'chart-error',
  'signal-generated'
])

// 使用状态管理
const chartStore = useChartStore()
const {
  chartData,
  selectedSymbol,
  selectedTimeframe,
  isDarkTheme,
  isLoading,
  error,
  displaySettings,
  indicatorSettings
} = storeToRefs(chartStore)

// 使用组合式函数
const { loadChartData, refreshData } = useChartData()
const { getDarkTheme, getLightTheme } = useChartTheme()

// 本地状态
const showIndicators = ref(props.showIndicators)
const mainChartInstance = ref(null)
const priceUpdateInterval = ref(null)

/**
 * 初始化主K线图表
 */
const initMainCandlestickChart = async () => {
  await nextTick()
  
  if (!chartData.value || chartData.value.length === 0) {
    console.warn('K线图表数据为空')
    return
  }

  try {
    const echarts = await import('echarts')
    const chartDom = document.getElementById('main-candlestick-chart')
    
    if (!chartDom) {
      console.error('找不到图表容器元素')
      return
    }

    // 销毁现有图表实例
    if (mainChartInstance.value) {
      mainChartInstance.value.dispose()
    }

    // 创建新的图表实例
    mainChartInstance.value = echarts.init(chartDom)
    
    const option = getCandlestickOption()
    mainChartInstance.value.setOption(option)
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
    
    emit('chart-ready', mainChartInstance.value)
  } catch (error) {
    console.error('初始化K线图表失败:', error)
    emit('chart-error', error)
  }
}

/**
 * 获取K线图表配置
 */
const getCandlestickOption = () => {
  const theme = isDarkTheme.value ? getDarkTheme() : getLightTheme()
  
  return {
    backgroundColor: theme.backgroundColor,
    title: {
      text: `${selectedSymbol.value}`,
      left: 'left',
      top: 10,
      textStyle: {
        color: theme.textColor,
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        lineStyle: {
          color: theme.borderColor,
          width: 1,
          type: 'dashed'
        }
      },
      backgroundColor: isDarkTheme.value ? 'rgba(30, 34, 45, 0.9)' : 'rgba(255, 255, 255, 0.9)',
      borderColor: theme.borderColor,
      textStyle: {
        color: theme.textColor
      },
      formatter: formatTooltip
    },
    legend: {
      data: ['K线', 'MA5', 'MA10', 'MA20'],
      top: 10,
      right: 20,
      textStyle: {
        color: theme.textColor
      },
      itemWidth: 14,
      itemHeight: 8
    },
    grid: {
      left: '3%',
      right: '8%',
      bottom: '8%',
      top: '12%',
      containLabel: true,
      backgroundColor: 'transparent',
      borderColor: theme.borderColor
    },
    xAxis: {
      type: 'category',
      data: chartData.value.map(item => item.time),
      scale: true,
      boundaryGap: false,
      axisLine: {
        onZero: false,
        lineStyle: {
          color: theme.borderColor
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: theme.textColor,
        fontSize: 11
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: theme.gridColor,
          width: 1
        }
      }
    },
    yAxis: {
      scale: true,
      position: 'right',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: theme.textColor,
        fontSize: 11,
        formatter: function(value) {
          return value.toFixed(2)
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: theme.gridColor,
          width: 1
        }
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 70,
        end: 100,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true
      },
      {
        show: true,
        type: 'slider',
        top: '92%',
        start: 70,
        end: 100,
        backgroundColor: theme.backgroundColor,
        borderColor: theme.borderColor,
        fillerColor: 'rgba(135, 206, 250, 0.2)',
        handleStyle: {
          color: theme.primaryColor
        },
        textStyle: {
          color: theme.textColor
        }
      }
    ],
    series: getCandlestickSeries()
  }
}

/**
 * 获取K线图表系列数据
 */
const getCandlestickSeries = () => {
  const candlestickData = chartData.value.map(item => [
    item.open,
    item.close,
    item.low,
    item.high
  ])

  return [
    {
      name: 'K线',
      type: 'candlestick',
      data: candlestickData,
      itemStyle: {
        color: '#26a69a',
        color0: '#ef5350',
        borderColor: '#26a69a',
        borderColor0: '#ef5350'
      }
    }
  ]
}

/**
 * 格式化提示框内容
 */
const formatTooltip = (params) => {
  if (params && params.length > 0) {
    const data = params[0]
    if (data.value && Array.isArray(data.value)) {
      return `
        时间: ${data.name}<br/>
        开盘: ${data.value[0]}<br/>
        收盘: ${data.value[1]}<br/>
        最低: ${data.value[2]}<br/>
        最高: ${data.value[3]}<br/>
        涨跌: ${(data.value[1] - data.value[0]).toFixed(2)}<br/>
        涨跌%: ${(((data.value[1] - data.value[0]) / data.value[0]) * 100).toFixed(2)}%
      `
    }
  }
  return ''
}

/**
 * 处理窗口大小变化
 */
const handleResize = () => {
  if (mainChartInstance.value) {
    mainChartInstance.value.resize()
  }
}

/**
 * 事件处理函数
 */
const handleSymbolChange = (symbol) => {
  chartStore.setSelectedSymbol(symbol)
  loadChartData()
}

const handleTimeframeChange = (timeframe) => {
  chartStore.setSelectedTimeframe(timeframe)
  loadChartData()
}

const handleThemeToggle = () => {
  chartStore.toggleTheme()
}

const handleIndicatorsToggle = () => {
  showIndicators.value = !showIndicators.value
}

const handleRefresh = () => {
  refreshData()
}

const handleChartReady = (type, instance) => {
  emit('chart-ready', { type, instance })
}

const handleChartError = (error) => {
  emit('chart-error', error)
}

const handleSignalGenerated = (signal) => {
  emit('signal-generated', signal)
}

// 监听数据变化
watch([chartData, isDarkTheme], () => {
  if (chartData.value && chartData.value.length > 0) {
    initMainCandlestickChart()
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  loadChartData()
})

onUnmounted(() => {
  if (mainChartInstance.value) {
    mainChartInstance.value.dispose()
  }
  if (priceUpdateInterval.value) {
    clearInterval(priceUpdateInterval.value)
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>