:root {
    --bg-color-darkest: #12151a;
    --bg-color-dark: #1a1e26;
    --bg-color-medium: #242a36;
    --bg-color-light: #2d3445;
    --text-color-primary: #e0e0e0;
    --text-color-secondary: #a0a0a0;
    --accent-color-primary: #4a90e2;
    --accent-color-secondary: #50e3c2;
    --color-green: #2ebd85;
    --color-red: #f6465d;
    --border-color: #363c4d;
    --font-family-base: 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    --border-radius-base: 4px;
    --transition-speed: 0.3s;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    background-color: var(--bg-color-darkest);
    color: var(--text-color-primary);
    font-family: var(--font-family-base);
    line-height: 1.6;
    overflow-x: hidden;
}

#app-container {
    display: flex;
    height: 100vh;
}

/* --- Sidebar --- */
.sidebar {
    width: 280px;
    background-color: var(--bg-color-dark);
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 30px;
    border-right: 1px solid var(--border-color);
}

.logo {
    font-size: 1.5em;
    font-weight: bold;
    color: var(--accent-color-primary);
    text-align: center;
    letter-spacing: 2px;
}

.sidebar-module h2 {
    font-size: 1.1em;
    margin-bottom: 12px;
    color: var(--text-color-secondary);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.content-box {
    background-color: var(--bg-color-darkest);
    padding: 15px;
    border-radius: var(--border-radius-base);
    min-height: 80px;
    font-size: 0.9em;
}

#balance-content p, #status-content p {
    margin-bottom: 8px;
}

.sidebar-button {
    width: 100%;
    padding: 10px;
    background-color: var(--accent-color-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius-base);
    cursor: pointer;
    transition: background-color var(--transition-speed);
    margin-top: 15px;
}

.sidebar-button:hover {
    background-color: #3a7bc8;
}

/* --- Main Content --- */
.main-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    gap: 20px;
    overflow-y: auto;
}

.main-module {
    background-color: var(--bg-color-dark);
    border-radius: var(--border-radius-base);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: 10px;
}

.module-header h2 {
    font-size: 1.2em;
}

.module-content {
    padding: 20px;
    flex-grow: 1;
    overflow: auto;
}


/* --- Tabs --- */
.tabs-container {
    display: flex;
    gap: 10px;
}

.tab-link {
    padding: 8px 16px;
    cursor: pointer;
    background-color: transparent;
    border: none;
    color: var(--text-color-secondary);
    font-size: 1em;
    border-bottom: 2px solid transparent;
    transition: all var(--transition-speed);
}

.tab-link:hover {
    color: var(--text-color-primary);
}

.tab-link.active {
    color: var(--accent-color-primary);
    border-bottom-color: var(--accent-color-primary);
    font-weight: bold;
}

/* --- Market Data --- */
#market-data-container {
    flex-basis: 50%;
    min-height: 400px;
}

.market-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

#market-search-input {
    min-width: 200px;
}

.filter-tags {
    display: flex;
    gap: 8px;
}

.filter-tags button {
    padding: 5px 12px;
    background-color: var(--bg-color-medium);
    border: 1px solid var(--border-color);
    color: var(--text-color-secondary);
    border-radius: 15px;
    cursor: pointer;
    transition: all var(--transition-speed);
}

.filter-tags button:hover {
    background-color: var(--bg-color-light);
    color: var(--text-color-primary);
}

.filter-tags button.active {
    background-color: var(--accent-color-primary);
    color: white;
    border-color: var(--accent-color-primary);
}

.market-list-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    padding: 0 15px 10px 15px;
    font-weight: bold;
    color: var(--text-color-secondary);
    font-size: 0.9em;
    border-bottom: 1px solid var(--border-color);
}

.col-change, .col-price {
    text-align: right;
}

.market-list-container {
    height: calc(100% - 40px);
    overflow-y: auto;
}

.market-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    padding: 12px 15px;
    cursor: pointer;
    transition: background-color var(--transition-speed);
    border-bottom: 1px solid var(--bg-color-darkest);
}

.market-row:hover {
    background-color: var(--bg-color-medium);
}

.market-row .instrument-name {
    font-weight: bold;
}

.market-row .instrument-vol {
    font-size: 0.8em;
    color: var(--text-color-secondary);
    margin-top: 4px;
}

.market-row .price {
    text-align: right;
    font-weight: bold;
}

.market-row .change {
    text-align: right;
    font-weight: bold;
}

.market-row .change.positive {
    color: var(--color-green);
}

.market-row .change.negative {
    color: var(--color-red);
}

.favorite-star {
    cursor: pointer;
    margin-right: 10px;
    color: var(--text-color-secondary);
    transition: color var(--transition-speed);
}

.favorite-star.favorited {
    color: var(--accent-color-secondary);
}


/* --- Bottom Zone (Trade & Order Form) --- */
.bottom-zone {
    flex-basis: 50%;
    display: flex;
    gap: 20px;
    min-height: 350px;
}

#trade-container {
    flex-grow: 3; /* Takes more space than order form */
}

#order-form-container {
    flex-grow: 2;
}

/* --- Trade Details Table --- */
#trade-content table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
}

#trade-content th, #trade-content td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

#trade-content th {
    font-weight: bold;
    color: var(--text-color-secondary);
    background-color: var(--bg-color-medium);
}

#trade-content tr:hover td {
    background-color: var(--bg-color-light);
}

#trade-content .cancel-btn {
    padding: 4px 8px;
    background-color: var(--color-red);
    color: white;
    border: none;
    border-radius: var(--border-radius-base);
    cursor: pointer;
    transition: background-color var(--transition-speed);
}

#trade-content .cancel-btn:hover {
    background-color: #c5374a;
}

/* --- Order Form --- */
#orderForm {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-input {
    flex-grow: 1;
}

input[type="text"],
input[type="number"],
input[type="search"],
select {
    width: 100%;
    padding: 10px;
    background-color: var(--bg-color-darkest);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-base);
    color: var(--text-color-primary);
    font-size: 1em;
}

input:focus, select:focus {
    outline: none;
    border-color: var(--accent-color-primary);
}

input::placeholder {
    color: var(--text-color-secondary);
}

.submit-btn {
    padding: 12px;
    font-size: 1.1em;
    font-weight: bold;
    background-color: var(--accent-color-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius-base);
    cursor: pointer;
    transition: background-color var(--transition-speed);
}

.submit-btn:hover {
    background-color: #3a7bc8;
}

#order-result-box {
    margin-top: 10px;
    padding: 10px;
    background-color: var(--bg-color-darkest);
    border-radius: var(--border-radius-base);
    border: 1px solid var(--border-color);
    font-family: monospace;
    white-space: pre-wrap;
    word-wrap: break-word;
    min-height: 40px;
}

/* --- General utility classes --- */
.scrollable {
    overflow-y: auto;
    max-height: 200px; /* Default max-height */
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-color-dark);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-color-light);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-color-secondary);
}

/* Loading spinner */
.spinner {
    border: 4px solid var(--bg-color-light);
    border-top: 4px solid var(--accent-color-primary);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 50px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 历史订单表格美化 */
.history-orders-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: var(--bg-color-darkest);
    color: var(--text-color-primary);
    font-size: 15px;
    min-width: 1200px;
    margin-bottom: 20px;
}
.history-orders-table th, .history-orders-table td {
    padding: 10px 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    vertical-align: middle;
}
.history-orders-table th {
    background: var(--bg-color-dark);
    font-weight: bold;
    color: var(--text-color-secondary);
    font-size: 14px;
}
.history-orders-table tr:hover td {
    background: var(--bg-color-medium);
}
.positive { color: var(--color-green) !important; }
.negative { color: var(--color-red) !important; }
.copy-btn {
    background: none;
    border: none;
    color: var(--accent-color-primary);
    cursor: pointer;
    font-size: 1em;
    margin-left: 4px;
    vertical-align: middle;
    transition: color 0.2s;
}
.copy-btn:hover { color: var(--accent-color-secondary); }
.leverage-tag, .lever {
    display: inline-block;
    background: var(--bg-color-light);
    color: var(--accent-color-primary);
    border-radius: 3px;
    font-size: 12px;
    padding: 1px 5px;
    margin-left: 4px;
    margin-top: 2px;
}
.pos-mode {
    color: var(--text-color-secondary);
    font-size: 12px;
    margin-left: 2px;
}
.history-table-scroll {
    overflow-x: auto;
    width: 100%;
}
.symbol-cell {
    text-align: left;
    font-weight: bold;
}

.export-csv-btn {
    background: var(--accent-color-primary);
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 18px;
    font-size: 15px;
    margin-bottom: 12px;
    margin-right: 10px;
    cursor: pointer;
    transition: background 0.2s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.export-csv-btn:hover {
    background: var(--accent-color-secondary);
}

/* 持仓表格美化 */
.positions-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: var(--bg-color-darkest);
    color: var(--text-color-primary);
    font-size: 15px;
    min-width: 1200px;
    margin-bottom: 20px;
}
.positions-table th, .positions-table td {
    padding: 10px 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    vertical-align: middle;
}
.positions-table th {
    background: var(--bg-color-dark);
    font-weight: bold;
    color: var(--text-color-secondary);
    font-size: 14px;
}
.positions-table tr:hover td {
    background: var(--bg-color-medium);
}
.pos-type {
    display: inline-block;
    background: #222;
    color: #fff;
    border-radius: 3px;
    font-size: 12px;
    padding: 1px 6px;
    margin-left: 6px;
}
.lever-tag {
    display: inline-block;
    background: var(--bg-color-light);
    color: var(--accent-color-primary);
    border-radius: 3px;
    font-size: 12px;
    padding: 1px 5px;
    margin-left: 4px;
    margin-top: 2px;
}
.edit-icon {
    font-size: 13px;
    color: var(--text-color-secondary);
    margin-left: 4px;
    cursor: pointer;
    vertical-align: middle;
}
.pos-qty {
    font-weight: bold;
    color: var(--accent-color-primary);
}
.close-btn {
    padding: 6px 18px;
    background: var(--color-red);
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 15px;
    cursor: pointer;
    transition: background 0.2s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.close-btn:hover {
    background: #c5374a;
}

/* 交易区美化 */
.trade-tabs-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}
.trade-tab {
    flex: 1;
    padding: 10px 0;
    background: var(--bg-color-darkest);
    color: var(--text-color-secondary);
    border: none;
    border-radius: 6px 6px 0 0;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
}
.trade-tab.active {
    background: var(--accent-color-primary);
    color: #fff;
}
.order-type-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}
.order-type-tab {
    flex: 1;
    padding: 8px 0;
    background: var(--bg-color-darkest);
    color: var(--text-color-secondary);
    border: none;
    border-radius: 6px;
    font-size: 1em;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
}
.order-type-tab.active {
    background: var(--accent-color-primary);
    color: #fff;
}
#orderForm .form-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}
#orderForm .form-input {
    flex: 1;
    padding: 10px;
    background: var(--bg-color-darkest);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-color-primary);
    font-size: 1em;
}
#orderForm .form-input:disabled {
    background: var(--bg-color-medium);
    color: var(--text-color-secondary);
}
#tp-sl-box {
    background: var(--bg-color-darkest);
    border-radius: 6px;
    padding: 10px 0 0 0;
    margin-bottom: 10px;
}
.trade-info-row {
    display: flex;
    gap: 18px;
    font-size: 0.98em;
    color: var(--text-color-secondary);
    margin-bottom: 10px;
}
#buy-btn {
    background: #2ecc71;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 1.1em;
    font-weight: bold;
    padding: 12px 0;
    flex: 1;
    margin-right: 10px;
    cursor: pointer;
    transition: background 0.2s;
}
#buy-btn:hover {
    background: #27ae60;
}
#sell-btn {
    background: #e74c3c;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 1.1em;
    font-weight: bold;
    padding: 12px 0;
    flex: 1;
    cursor: pointer;
    transition: background 0.2s;
}
#sell-btn:hover {
    background: #c0392b;
}
#order-result-box {
    margin-top: 10px;
    padding: 10px;
    background: var(--bg-color-darkest);
    border-radius: 4px;
    border: 1px solid var(--border-color);
    font-family: monospace;
    min-height: 36px;
    color: var(--text-color-primary);
} 