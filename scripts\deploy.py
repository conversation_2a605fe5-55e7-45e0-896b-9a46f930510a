#!/usr/bin/env python3
"""
项目部署脚本
自动化部署到不同环境（开发、测试、生产）
"""

import os
import sys
import subprocess
import json
import shutil
from pathlib import Path
import argparse
from datetime import datetime

def run_command(command, cwd=None, check=True):
    """执行命令并返回结果"""
    print(f"🔧 执行: {command}")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True, 
            check=check
        )
        if result.stdout:
            print(result.stdout)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e.stderr}")
        return False, e.stderr

def check_prerequisites():
    """检查部署前提条件"""
    print("🔍 检查部署前提条件...")
    
    # 检查必要文件
    required_files = [
        "requirements.txt",
        "run_backend.py",
        "web-frontend/package.json"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 缺少必要文件: {file_path}")
            return False
    
    # 检查配置文件
    if not Path("config.env").exists():
        print("⚠️ 未找到config.env文件，请确保已正确配置")
        return False
    
    print("✅ 前提条件检查通过")
    return True

def backup_current_deployment(backup_dir):
    """备份当前部署"""
    print("💾 创建部署备份...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = Path(backup_dir) / f"backup_{timestamp}"
    backup_path.mkdir(parents=True, exist_ok=True)
    
    # 备份关键文件和目录
    backup_items = [
        "config.env",
        "data",
        "logs"
    ]
    
    for item in backup_items:
        item_path = Path(item)
        if item_path.exists():
            if item_path.is_file():
                shutil.copy2(item_path, backup_path)
            else:
                shutil.copytree(item_path, backup_path / item, dirs_exist_ok=True)
            print(f"  备份: {item}")
    
    print(f"✅ 备份完成: {backup_path}")
    return backup_path

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖...")
    
    # 安装Python依赖
    print("📦 安装Python依赖...")
    success, _ = run_command("pip install -r requirements.txt")
    if not success:
        return False
    
    # 安装前端依赖
    print("📦 安装前端依赖...")
    success, _ = run_command("npm install", cwd="web-frontend")
    if not success:
        return False
    
    print("✅ 依赖安装完成")
    return True

def run_tests():
    """运行测试"""
    print("🧪 运行测试...")
    
    # 运行后端测试
    if Path("tests/backend").exists():
        print("🧪 运行后端测试...")
        success, _ = run_command("python -m pytest tests/backend -v", check=False)
        if not success:
            print("⚠️ 后端测试失败，但继续部署")
    
    # 运行前端测试
    if Path("web-frontend/tests").exists():
        print("🧪 运行前端测试...")
        success, _ = run_command("npm test", cwd="web-frontend", check=False)
        if not success:
            print("⚠️ 前端测试失败，但继续部署")
    
    print("✅ 测试完成")
    return True

def build_frontend(environment):
    """构建前端"""
    print(f"🏗️ 构建前端 ({environment})...")
    
    # 设置环境变量
    env_vars = {
        "development": "development",
        "testing": "testing", 
        "production": "production"
    }
    
    build_env = env_vars.get(environment, "production")
    
    # 构建前端
    success, _ = run_command(f"npm run build", cwd="web-frontend")
    if not success:
        return False
    
    print("✅ 前端构建完成")
    return True

def setup_database():
    """设置数据库"""
    print("🗄️ 设置数据库...")
    
    # 检查是否有数据库迁移脚本
    if Path("scripts/migrate_db.py").exists():
        success, _ = run_command("python scripts/migrate_db.py")
        if not success:
            return False
    
    print("✅ 数据库设置完成")
    return True

def deploy_to_development():
    """部署到开发环境"""
    print("🚀 部署到开发环境...")
    
    steps = [
        ("安装依赖", install_dependencies),
        ("运行测试", run_tests),
        ("设置数据库", setup_database)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"❌ {step_name}失败")
            return False
    
    print("\n🎉 开发环境部署完成！")
    print("\n📋 启动服务:")
    print("  后端: python run_backend.py")
    print("  前端: cd web-frontend && npm run dev")
    return True

def deploy_to_testing():
    """部署到测试环境"""
    print("🚀 部署到测试环境...")
    
    steps = [
        ("安装依赖", install_dependencies),
        ("运行测试", run_tests),
        ("构建前端", lambda: build_frontend("testing")),
        ("设置数据库", setup_database)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"❌ {step_name}失败")
            return False
    
    print("\n🎉 测试环境部署完成！")
    return True

def deploy_to_production(backup_dir):
    """部署到生产环境"""
    print("🚀 部署到生产环境...")
    
    # 创建备份
    backup_path = backup_current_deployment(backup_dir)
    
    steps = [
        ("安装依赖", install_dependencies),
        ("运行测试", run_tests),
        ("构建前端", lambda: build_frontend("production")),
        ("设置数据库", setup_database)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"❌ {step_name}失败")
            print(f"💾 可以从备份恢复: {backup_path}")
            return False
    
    print("\n🎉 生产环境部署完成！")
    print(f"💾 备份位置: {backup_path}")
    return True

def create_systemd_service():
    """创建systemd服务文件（Linux）"""
    print("🔧 创建systemd服务文件...")
    
    current_dir = Path.cwd()
    service_content = f"""[Unit]
Description=OKX Trading System Backend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory={current_dir}
Environment=PATH={current_dir}/venv/bin
ExecStart={current_dir}/venv/bin/python run_backend.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    service_file = Path("scripts/okx-trading.service")
    service_file.write_text(service_content)
    
    print(f"✅ 服务文件创建: {service_file}")
    print("\n📋 安装服务:")
    print(f"  sudo cp {service_file} /etc/systemd/system/")
    print("  sudo systemctl daemon-reload")
    print("  sudo systemctl enable okx-trading")
    print("  sudo systemctl start okx-trading")

def create_nginx_config():
    """创建Nginx配置文件"""
    print("🔧 创建Nginx配置文件...")
    
    nginx_content = """server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/your/project/web-frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # 后端API
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket
    location /ws/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
"""
    
    nginx_file = Path("scripts/nginx-okx-trading.conf")
    nginx_file.write_text(nginx_content)
    
    print(f"✅ Nginx配置文件创建: {nginx_file}")
    print("\n📋 安装配置:")
    print(f"  sudo cp {nginx_file} /etc/nginx/sites-available/okx-trading")
    print("  sudo ln -s /etc/nginx/sites-available/okx-trading /etc/nginx/sites-enabled/")
    print("  sudo nginx -t")
    print("  sudo systemctl reload nginx")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OKX量化交易系统部署脚本")
    parser.add_argument('environment', choices=['dev', 'test', 'prod'], 
                       help='部署环境')
    parser.add_argument('--backup-dir', default='backups', 
                       help='备份目录')
    parser.add_argument('--skip-tests', action='store_true', 
                       help='跳过测试')
    parser.add_argument('--create-service', action='store_true', 
                       help='创建systemd服务文件')
    parser.add_argument('--create-nginx', action='store_true', 
                       help='创建Nginx配置文件')
    
    args = parser.parse_args()
    
    print("🚀 OKX量化交易系统 - 自动化部署")
    print("=" * 50)
    print(f"📋 目标环境: {args.environment}")
    print(f"📋 备份目录: {args.backup_dir}")
    print()
    
    # 检查前提条件
    if not check_prerequisites():
        sys.exit(1)
    
    # 创建配置文件
    if args.create_service:
        create_systemd_service()
        return
    
    if args.create_nginx:
        create_nginx_config()
        return
    
    # 执行部署
    success = False
    
    if args.environment == 'dev':
        success = deploy_to_development()
    elif args.environment == 'test':
        success = deploy_to_testing()
    elif args.environment == 'prod':
        success = deploy_to_production(args.backup_dir)
    
    if success:
        print("\n🎉 部署成功完成！")
        print("\n📚 更多信息请查看: docs/deployment/DEPLOYMENT-GUIDE.md")
    else:
        print("\n❌ 部署失败")
        sys.exit(1)

if __name__ == "__main__":
    main()