<template>
  <div class="price-info-panel" :class="{ 'dark-theme': isDarkTheme }">
    <div v-if="chartDataStore.isLoading" class="panel-skeleton">
      <a-skeleton active :paragraph="{ rows: 10 }" />
    </div>
    <div v-else>
    <a-card title="实时行情" size="small">
      <div class="price-display">
        <!-- 主要价格信息 -->
        <div class="main-price">
          <div class="symbol-name">{{ selectedSymbol }}</div>
          <div class="current-price" :style="{ color: getPriceColor(priceChangeDirection) }" :key="currentPrice?.last">
            {{ formatPrice(currentPrice?.last) }}
            <span class="price-currency">USDT</span>
          </div>
          <div class="price-change" :style="{ color: getPriceColor(priceChangeDirection) }">
            <template v-if="priceChangeDirection > 0">
              <ArrowUpOutlined />
            </template>
            <template v-else-if="priceChangeDirection < 0">
              <ArrowDownOutlined />
            </template>
            <span class="change-amount">{{ formatPriceChange(currentPrice?.change) }}</span>
            <span class="change-percent">({{ formatPercentChange(currentPrice?.changePercent) }}%)</span>
          </div>
        </div>

        <!-- 24小时统计 -->
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-label">24h最高</div>
            <div class="stat-value high">{{ formatPrice(currentPrice?.high24h) }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">24h最低</div>
            <div class="stat-value low">{{ formatPrice(currentPrice?.low24h) }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">24h成交量</div>
            <div class="stat-value">{{ formatVolume(currentPrice?.vol24h) }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">24h成交额</div>
            <div class="stat-value">{{ formatAmount(currentPrice?.volCcy24h) }}</div>
          </div>
        </div>

                <RealtimeVolume />

        <!-- 技术指标快览已移除 -->
      </div>
    </a-card>

    <div class="extra-panels">
      <MarketSentiment />
      <TechnicalIndicatorsPanel />
      <SupportResistance />
      <PriceAlerts />
    </div>
    </div>

    
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'
import { CHART_DATA_INDEX } from '@/constants/chartConstants'
import RealtimeVolume from './RealtimeVolume.vue';
import MarketSentiment from './MarketSentiment.vue';
import TechnicalIndicatorsPanel from './TechnicalIndicatorsPanel.vue';
import SupportResistance from './SupportResistance.vue';
import PriceAlerts from './PriceAlerts.vue';

import { formatPrice } from '@/utils/formatters'
import { useChartSettingsStore } from '@/stores/chartSettingsStore';
import { useChartDataStore } from '@/stores/chartDataStore';
import { useInfoPanelStore } from '@/stores/infoPanelStore';
import { storeToRefs } from 'pinia'

const chartSettingsStore = useChartSettingsStore();
const chartDataStore = useChartDataStore();
const infoPanelStore = useInfoPanelStore();

const { isDarkTheme, selectedSymbol } = storeToRefs(chartSettingsStore);
const { currentPrice, priceChangeDirection } = storeToRefs(chartDataStore);

// 监听价格变化
watch(() => currentPrice.value, (newPrice) => {
  console.log('💰 PriceInfoPanel监听到价格变化:', newPrice);
}, { deep: true });
const { 
  realtimeVolume, 
  marketSentiment, 
  supportResistance, 
  priceAlerts 
} = storeToRefs(infoPanelStore);

// 格式化函数
const formatPriceChange = (change) => {
  if (!change) return '0.00';
  const num = parseFloat(change);
  return (num >= 0 ? '+' : '') + num.toFixed(2);
};

const formatPercentChange = (percent) => {
  if (!percent) return '0.00';
  const num = parseFloat(percent);
  return (num >= 0 ? '+' : '') + num.toFixed(2);
};

const formatVolume = (volume) => {
  if (!volume) return '0';
  const num = parseFloat(volume);
  if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
  if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
  if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
  return num.toFixed(2);
};

const formatAmount = (amount) => {
  if (!amount) return '0';
  const num = parseFloat(amount);
  if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
  if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
  if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
  return num.toFixed(2);
};

/**
 * 获取价格颜色（优化：使用内联样式替代动态CSS类）
 * @param {number} direction - 价格方向 1(上涨) | -1(下跌) | 0(中性)
 * @returns {string} 颜色值
 */
const getPriceColor = (direction) => {
  if (direction > 0) return '#52c41a';
  if (direction < 0) return '#ff4d4f';
  return '#666';
};

// 定义 props
const props = defineProps({
  priceChangeDirection: {
    type: String,
    default: 'neutral' // 'up', 'down', 'neutral'
  },
  selectedSymbol: {
    type: String,
    default: 'BTC-USDT'
  },
  selectedTimeframe: {
    type: String,
    default: '1m'
  },
  chartData: {
    type: Array,
    default: () => []
  }
})

// 本地状态

const priceHistory = ref([])
const updateInterval = ref(null)

onMounted(() => {
  chartDataStore.startRealtimeUpdates();
});

onUnmounted(() => {
  chartDataStore.stopRealtimeUpdates();
});

// 模拟数据





// 技术指标状态
const getRSIStatus = (rsi) => {
  if (rsi >= 70) return 'overbought'
  if (rsi <= 30) return 'oversold'
  return 'neutral'
}

const getMACDStatus = (macd) => {
  return macd > 0 ? 'bullish' : 'bearish'
}

const getBollingerStatus = (position) => {
  if (position >= 0.8) return 'overbought'
  if (position <= 0.2) return 'oversold'
  return 'neutral'
}

// 市场情绪










// 组件卸载
onUnmounted(() => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
  }
})
</script>

<style scoped>
.panel-skeleton {
  padding: 20px;
}
.price-info-panel {
  height: auto;
  min-height: fit-content;
}

.price-display {
  display: flex;
  flex-direction: column;
  gap: 16px;
}



.main-price {
  text-align: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.symbol-name {
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.current-price {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 4px;
}

.current-price.up {
  color: #52c41a;
}

.current-price.down {
  color: #ff4d4f;
}

.current-price.neutral {
  color: #666;
}

.price-currency {
  font-size: 16px;
  color: #8c8c8c;
  margin-left: 4px;
}

.price-change {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.price-change.up {
  color: #52c41a;
}

.price-change.down {
  color: #ff4d4f;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.stat-value.high {
  color: #52c41a;
}

.stat-value.low {
  color: #ff4d4f;
}

.indicators-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: #fafafa;
  border-radius: 4px;
}

.indicator-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.indicator-label {
  color: #8c8c8c;
}

.indicator-value {
  font-weight: 500;
}

.indicator-value.overbought {
  color: #ff4d4f;
}

.indicator-value.oversold {
  color: #52c41a;
}

.indicator-value.bullish {
  color: #52c41a;
}

.indicator-value.bearish {
  color: #ff4d4f;
}

.indicator-value.neutral {
  color: #666;
}







/* 深色主题样式 */
.price-info-panel.dark-theme .main-price {
  border-bottom-color: #434651;
}

.price-info-panel.dark-theme .stat-item,
.price-info-panel.dark-theme .indicators-summary {
  background: #2a2e39;
}

.price-info-panel.dark-theme .stat-value {
  color: #ffffff;
}

.extra-panels {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.price-info-panel.dark-theme .sentiment-bar {
  background: #434651;
}


</style>