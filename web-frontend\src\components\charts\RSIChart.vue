<template>
  <BaseChart
    title="RSI 指标"
    :signals="signals"
    :settings="localSettings"
    @save-settings="saveSettings"
    @reset-settings="resetSettings"
  >
    <template #chart>
      <div ref="chartRef" class="rsi-chart" :style="{ height: chartHeight + 'px' }"></div>
    </template>
    <template #settings-form>
      <a-form :model="localSettings" layout="vertical">
        <a-form-item label="周期" name="period">
          <a-slider v-model:value="localSettings.period" :min="6" :max="30" :marks="{ 14: '14' }" />
        </a-form-item>
        <a-form-item label="超买阈值" name="overbought">
          <a-slider v-model:value="localSettings.overbought" :min="70" :max="90" :marks="{ 80: '80' }" />
        </a-form-item>
        <a-form-item label="超卖阈值" name="oversold">
          <a-slider v-model:value="localSettings.oversold" :min="10" :max="30" :marks="{ 20: '20' }" />
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="localSettings.enableAlerts">启用交易信号</a-checkbox>
        </a-form-item>
      </a-form>
    </template>
    <template #signal-list="{ item }">
      <a-list-item-meta>
        <template #title>
          <a-space>
            <a-tag :color="getSignalColor(item.type)">{{ getSignalText(item.type) }}</a-tag>
            <span>{{ item.message }}</span>
          </a-space>
        </template>
        <template #description>
          <div>
            <div>时间: {{ formatTime(item.timestamp) }}</div>
            <div>价格: {{ item.price?.toFixed(2) }}</div>
            <div>RSI: {{ item.rsi?.toFixed(2) }}</div>
            <div>置信度: {{ item.confidence }}%</div>
          </div>
        </template>
      </a-list-item-meta>
    </template>
  </BaseChart>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import BaseChart from './BaseChart.vue'

// Props
const props = defineProps({
  chartData: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: '200px'
  },
  isDarkTheme: {
    type: Boolean,
    default: false
  },
  chartId: {
    type: String,
    default: () => `rsi-chart-${Date.now()}`
  },
  rsiPeriod: {
    type: Number,
    default: INDICATOR_DEFAULTS.RSI.period
  },
  overboughtLevel: {
    type: Number,
    default: INDICATOR_DEFAULTS.RSI.overbought
  },
  oversoldLevel: {
    type: Number,
    default: INDICATOR_DEFAULTS.RSI.oversold
  }
})

// Emits
const emit = defineEmits(['chartReady', 'chartError', 'signalGenerated'])

const chartInstance = ref(null)

// 计算RSI数据
const calculateRSIData = () => {
  if (!props.chartData || props.chartData.length === 0) {
    rsiData.value = []
    return
  }

  try {
    rsiData.value = indicatorCalculators.calculateRSI(props.chartData, props.rsiPeriod)
    
    // 生成交易信号
    generateRSISignals()
  } catch (err) {
    console.error('计算RSI失败:', err)
    emit('chartError', err.message)
  }
}

// 生成RSI交易信号
const generateRSISignals = () => {
  if (rsiData.value.length === 0) return

  const signals = []
  const latestRSI = rsiData.value[rsiData.value.length - 1]
  
  if (latestRSI !== null) {
    if (latestRSI <= props.oversoldLevel) {
      signals.push({
        type: 'BUY',
        indicator: 'RSI',
        value: latestRSI,
        strength: Math.max(0, (props.oversoldLevel - latestRSI) / props.oversoldLevel),
        description: `RSI超卖信号 (${latestRSI.toFixed(2)})`
      })
    } else if (latestRSI >= props.overboughtLevel) {
      signals.push({
        type: 'SELL',
        indicator: 'RSI',
        value: latestRSI,
        strength: Math.max(0, (latestRSI - props.overboughtLevel) / (100 - props.overboughtLevel)),
        description: `RSI超买信号 (${latestRSI.toFixed(2)})`
      })
    }
  }

  if (signals.length > 0) {
    emit('signalGenerated', signals)
  }
}

// 初始化RSI图表
const initRSIChart = async () => {
  await nextTick()
  
  calculateRSIData()
  
  if (rsiData.value.length === 0) {
    console.warn('RSI图表数据为空')
    return
  }

  try {
    const theme = themeConfig.value
    const baseOption = getBaseChartOption('RSI', {
      yAxis: {
        type: 'value',
        position: 'right',
        scale: true,
        min: 0,
        max: 100,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: theme.textColor,
          fontSize: 10,
          formatter: function(value) {
            return value.toFixed(0)
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: theme.gridColor
          }
        }
      }
    })

    const rsiOption = {
      ...baseOption,
      xAxis: {
        ...baseOption.xAxis,
        data: formatChartData.timeAxis(props.chartData)
      },
      series: [
        {
          name: 'RSI',
          type: 'line',
          data: rsiData.value,
          lineStyle: {
            color: theme.indicators.rsi,
            width: 2
          },
          symbol: 'none',
          smooth: true
        }
      ],
      // 添加超买超卖线
      graphic: [
        {
          type: 'line',
          shape: {
            x1: 0,
            y1: 0,
            x2: 0,
            y2: 0
          },
          style: {
            stroke: '#ff4d4f',
            lineWidth: 1,
            lineDash: [5, 5]
          },
          z: 1
        },
        {
          type: 'line',
          shape: {
            x1: 0,
            y1: 0,
            x2: 0,
            y2: 0
          },
          style: {
            stroke: '#52c41a',
            lineWidth: 1,
            lineDash: [5, 5]
          },
          z: 1
        }
      ]
    }

    // 添加超买超卖水平线
    rsiOption.series.push(
      {
        name: '超买线',
        type: 'line',
        data: new Array(props.chartData.length).fill(props.overboughtLevel),
        lineStyle: {
          color: '#ff4d4f',
          width: 1,
          type: 'dashed'
        },
        symbol: 'none',
        silent: true
      },
      {
        name: '超卖线',
        type: 'line',
        data: new Array(props.chartData.length).fill(props.oversoldLevel),
        lineStyle: {
          color: '#52c41a',
          width: 1,
          type: 'dashed'
        },
        symbol: 'none',
        silent: true
      },
      {
        name: '中线',
        type: 'line',
        data: new Array(props.chartData.length).fill(50),
        lineStyle: {
          color: theme.textColor,
          width: 1,
          type: 'dotted',
          opacity: 0.5
        },
        symbol: 'none',
        silent: true
      }
    )

    const chart = initChart(props.chartId, rsiOption)
    if (chart) {
      emit('chartReady', chart)
    } else {
      emit('chartError', error.value)
    }
  } catch (err) {
    console.error('初始化RSI图表失败:', err)
    emit('chartError', err.message)
  }
}

// 更新图表数据
const updateRSIChart = () => {
  if (!chartInstance.value) {
    return
  }

  calculateRSIData()
  
  if (rsiData.value.length === 0) {
    return
  }

  try {
    const updateOption = {
      xAxis: {
        data: formatChartData.timeAxis(props.chartData)
      },
      series: [
        {
          data: rsiData.value
        },
        {
          data: new Array(props.chartData.length).fill(props.overboughtLevel)
        },
        {
          data: new Array(props.chartData.length).fill(props.oversoldLevel)
        },
        {
          data: new Array(props.chartData.length).fill(50)
        }
      ]
    }

    updateChart(updateOption)
  } catch (err) {
    console.error('更新RSI图表失败:', err)
    emit('chartError', err.message)
  }
}

// 监听数据变化
watch(
  () => props.chartData,
  (newData) => {
    if (newData && newData.length > 0) {
      if (chartInstance.value) {
        updateRSIChart()
      } else {
        initRSIChart()
      }
    }
  },
  { deep: true }
)

// 监听RSI参数变化
watch(
  [() => props.rsiPeriod, () => props.overboughtLevel, () => props.oversoldLevel],
  () => {
    if (chartInstance.value && props.chartData && props.chartData.length > 0) {
      updateRSIChart()
    }
  }
)

// 监听主题变化
watch(
  () => props.isDarkTheme,
  () => {
    if (chartInstance.value && props.chartData && props.chartData.length > 0) {
      initRSIChart()
    }
  }
)

// 窗口大小变化时调整图表
const handleResize = () => {
  resizeChart()
}

// 组件挂载
onMounted(() => {
  if (props.chartData && props.chartData.length > 0) {
    initRSIChart()
  }
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  disposeChart()
})

// 暴露方法给父组件
defineExpose({
  initChart: initRSIChart,
  updateChart: updateRSIChart,
  resizeChart,
  disposeChart,
  chartInstance,
  rsiData
})
</script>

<style scoped>
.rsi-chart-container {
  position: relative;
  width: 100%;
}

.rsi-chart {
  width: 100%;
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ff4d4f;
  font-size: 14px;
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.rsi-chart-container:hover .rsi-chart {
  cursor: crosshair;
}
</style>