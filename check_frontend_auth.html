<!DOCTYPE html>
<html>
<head>
    <title>检查前端认证状态</title>
</head>
<body>
    <h1>前端认证状态检查</h1>
    <div id="result"></div>
    
    <script>
        function checkAuth() {
            const token = localStorage.getItem('token');
            const result = document.getElementById('result');
            
            if (token) {
                result.innerHTML = `
                    <p><strong>Token存在:</strong> ${token.substring(0, 50)}...</p>
                    <p><strong>Token长度:</strong> ${token.length}</p>
                    <p><strong>存储时间:</strong> ${localStorage.getItem('token_time') || '未知'}</p>
                `;
                
                // 测试API调用
                fetch('/api/ai/predictions', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                })
                .then(response => {
                    result.innerHTML += `<p><strong>API测试状态:</strong> ${response.status}</p>`;
                    return response.text();
                })
                .then(text => {
                    result.innerHTML += `<p><strong>API响应:</strong> ${text.substring(0, 200)}...</p>`;
                })
                .catch(error => {
                    result.innerHTML += `<p><strong>API错误:</strong> ${error.message}</p>`;
                });
            } else {
                result.innerHTML = '<p><strong>未找到Token</strong> - 用户可能未登录</p>';
            }
        }
        
        checkAuth();
    </script>
</body>
</html>
