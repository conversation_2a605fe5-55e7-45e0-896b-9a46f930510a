/**
 * 提供计算各种技术指标的函数
 * @param {Object} settings - 指标设置参数
 * @returns {Object} - 包含所有指标计算方法的对象
 */
export function useIndicators(settings = {}) {
  
  // 默认指标设置
  const defaultSettings = {
    ma: { periods: [5, 10, 20, 60] },
    rsi: { period: 14 },
    macd: { short: 12, long: 26, signal: 9 },
    bollingerBands: { period: 20, stdDev: 2 },
    kdj: { n: 9, m1: 3, m2: 3 },
    supertrend: { atrPeriod: 10, multiplier: 3 }
  }
  
  // 合并用户设置和默认设置
  const indicatorSettings = {
    ma: { ...defaultSettings.ma, ...settings.ma },
    rsi: { ...defaultSettings.rsi, ...settings.rsi },
    macd: { ...defaultSettings.macd, ...settings.macd },
    bollingerBands: { ...defaultSettings.bollingerBands, ...settings.bollingerBands },
    kdj: { ...defaultSettings.kdj, ...settings.kdj },
    supertrend: { ...defaultSettings.supertrend, ...settings.supertrend }
  }

  /**
   * 计算移动平均线 (MA)
   * @param {number[]} data - 收盘价数组
   * @param {number} period - 周期
   * @returns {Array<number|null>} MA数组
   */
  const calculateMA = (data, period) => {
    const result = [];
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.push(null);
        continue;
      }
      const slice = data.slice(i - period + 1, i + 1);
      const sum = slice.reduce((a, b) => a + b, 0);
      result.push(sum / period);
    }
    return result;
  };

  /**
   * 计算指数移动平均线 (EMA)
   * @param {number[]} data - 收盘价数组
   * @param {number} period - 周期
   * @returns {Array<number|null>} EMA数组
   */
  const calculateEMA = (data, period) => {
    const result = Array(data.length).fill(null);
    const k = 2 / (period + 1);
    let ema = null;

    for (let i = 0; i < data.length; i++) {
      if (data[i] !== null) {
        if (ema === null) {
          // 寻找第一个有效的ema初始值
          const initialSlice = data.slice(i, i + period).filter(v => v !== null);
          if (initialSlice.length >= period * 0.8) { // 放宽初始值计算条件
            ema = initialSlice.reduce((s, v) => s + v, 0) / initialSlice.length;
            result[i + period - 1] = ema;
            i = i + period -1; //  跳到ema计算出的位置
          } 
        } else {
          ema = data[i] * k + ema * (1 - k);
          result[i] = ema;
        }
      }
    }
    return result;
  };

  /**
   * 计算相对强弱指数 (RSI)
   * @param {number[]} data - 收盘价数组
   * @param {number} period - 周期
   * @returns {Array<number|null>} RSI数组
   */
  const calculateRSI = (data, period = 14) => {
    const result = [];
    if (data.length < period) return Array(data.length).fill(null);

    let gains = 0;
    let losses = 0;

    for (let i = 1; i < period; i++) {
        const diff = data[i] - data[i-1];
        if (diff > 0) gains += diff;
        else losses -= diff;
    }

    let avgGain = gains / period;
    let avgLoss = losses / period;

    for(let i=0; i<period-1; i++) result.push(null);

    for (let i = period -1; i < data.length; i++) {
        const diff = data[i] - data[i-1];
        if (diff > 0) {
            avgGain = (avgGain * (period - 1) + diff) / period;
            avgLoss = (avgLoss * (period - 1)) / period;
        } else {
            avgGain = (avgGain * (period - 1)) / period;
            avgLoss = (avgLoss * (period - 1) - diff) / period;
        }
        const rs = avgLoss === 0 ? 100 : avgGain / avgLoss;
        result.push(100 - (100 / (1 + rs)));
    }
    return result;
  };

  /**
   * 计算MACD
   * @param {number[]} data - 收盘价数组
   * @param {number} shortPeriod - 短周期
   * @param {number} longPeriod - 长周期
   * @param {number} signalPeriod - 信号周期
   * @returns {Object} 包含DIF, DEA, MACD数组的对象
   */
  const calculateMACD = (data, shortPeriod = 12, longPeriod = 26, signalPeriod = 9) => {
    const emaShort = calculateEMA(data, shortPeriod);
    const emaLong = calculateEMA(data, longPeriod);
    const dif = emaShort.map((val, i) => (val !== null && emaLong[i] !== null) ? val - emaLong[i] : null);
    const dea = calculateEMA(dif, signalPeriod);
    const histogram = dif.map((val, i) => (val !== null && dea[i] !== null) ? 2 * (val - dea[i]) : null);
    return { dif, dea, macd: histogram };
  };

  /**
   * 计算布林带 (Bollinger Bands)
   * @param {number[]} data - 收盘价数组
   * @param {number} period - 周期
   * @param {number} multiplier - 标准差倍数
   * @returns {Object} 包含upper, mid, lower数组的对象
   */
  const calculateBollingerBands = (data, period = 20, multiplier = 2) => {
    const mid = calculateMA(data, period);
    const upper = [];
    const lower = [];

    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        upper.push(null);
        lower.push(null);
        continue;
      }
      const slice = data.slice(i - period + 1, i + 1);
      const ma = mid[i];
      const stdDev = Math.sqrt(slice.reduce((sum, val) => sum + Math.pow(val - ma, 2), 0) / period);
      upper.push(ma + stdDev * multiplier);
      lower.push(ma - stdDev * multiplier);
    }
    return { upper, mid, lower };
  };

  /**
   * 计算KDJ
   * @param {Array} data - K线数据数组
   * @param {number} n - RSV周期
   * @param {number} m1 - K周期
   * @param {number} m2 - D周期
   * @returns {Object} 包含k, d, j数组的对象
   */
  const calculateKDJ = (data, n = 9, m1 = 3, m2 = 3) => {
    const k = [];
    const d = [];
    const j = [];
    if (data.length < n) return { k, d, j };

    const rsv = [];
    for (let i = 0; i < data.length; i++) {
        if (i < n - 1) {
            rsv.push(null);
            continue;
        }
        const slice = data.slice(i - n + 1, i + 1);
        const highs = slice.map(d => parseFloat(d.high)).filter(h => !isNaN(h));
        const lows = slice.map(d => parseFloat(d.low)).filter(l => !isNaN(l));
        
        if (highs.length === 0 || lows.length === 0) {
            rsv.push(50);
            continue;
        }
        
        const high = Math.max.apply(null, highs);
        const low = Math.min.apply(null, lows);
        const close = parseFloat(data[i].close);
        rsv.push(high === low ? 50 : ((close - low) / (high - low)) * 100);
    }

    let prevK = 50;
    let prevD = 50;
    for (let i = 0; i < rsv.length; i++) {
        if (rsv[i] === null) {
            k.push(null); d.push(null); j.push(null);
            continue;
        }
        const newK = (prevK * (m1 - 1) + rsv[i]) / m1;
        const newD = (prevD * (m2 - 1) + newK) / m2;
        const newJ = 3 * newK - 2 * newD;
        k.push(newK); d.push(newD); j.push(newJ);
        prevK = newK;
        prevD = newD;
    }
    return { k, d, j };
  };

  /**
   * 计算ATR (Average True Range)
   * @param {Array} data - K线数据数组
   * @param {number} period - 周期
   * @returns {Array<number|null>} ATR数组
   */
  const calculateATR = (data, period = 14) => {
    const atr = [];
    if (data.length < period) return Array(data.length).fill(null);

    let trs = [];
    for (let i = 1; i < data.length; i++) {
        const high = parseFloat(data[i].high);
        const low = parseFloat(data[i].low);
        const prevClose = parseFloat(data[i-1].close);
        trs.push(Math.max(high - low, Math.abs(high - prevClose), Math.abs(low - prevClose)));
    }

    let prevAtr = trs.slice(0, period - 1).reduce((a, b) => a + b, 0) / (period - 1);
    for(let i=0; i<period; i++) atr.push(null);
    atr[period-1] = prevAtr;

    for (let i = period - 1; i < trs.length; i++) {
        const currentAtr = (prevAtr * (period - 1) + trs[i]) / period;
        atr.push(currentAtr);
        prevAtr = currentAtr;
    }
    return atr;
  };

  /**
   * 计算Supertrend
   * @param {Array} data - K线数据数组
   * @param {number} atrPeriod - ATR周期
   * @param {number} multiplier - ATR乘数
   * @returns {Object} 包含up, down, direction数组的对象
   */
  const calculateSupertrend = (data, atrPeriod = 10, multiplier = 3) => {
    const atr = calculateATR(data, atrPeriod);
    const up = [];
    const down = [];
    const direction = [];
    let upperBand = null;
    let lowerBand = null;
    let currentDirection = 1;

    for (let i = 0; i < data.length; i++) {
        if (atr[i] === null) {
            up.push(null); down.push(null); direction.push(null);
            continue;
        }

        const high = parseFloat(data[i].high);
        const low = parseFloat(data[i].low);
        const close = parseFloat(data[i].close);

        const basicUpperBand = (high + low) / 2 + multiplier * atr[i];
        const basicLowerBand = (high + low) / 2 - multiplier * atr[i];

        if (currentDirection === 1) {
            lowerBand = lowerBand === null ? basicLowerBand : Math.max(lowerBand, basicLowerBand);
            if (close < lowerBand) {
                currentDirection = -1;
                upperBand = basicUpperBand;
                up.push(null); down.push(upperBand);
            } else {
                up.push(lowerBand); down.push(null);
            }
        } else {
            upperBand = upperBand === null ? basicUpperBand : Math.min(upperBand, basicUpperBand);
            if (close > upperBand) {
                currentDirection = 1;
                lowerBand = basicLowerBand;
                up.push(lowerBand); down.push(null);
            } else {
                up.push(null); down.push(upperBand);
            }
        }
        direction.push(currentDirection);
    }
    return { up, down, direction };
  };

  /**
   * 计算所有技术指标
   * @param {Array} data - K线数据
   * @returns {Object} 所有指标数据
   */
  const calculateAllIndicators = (data) => {
    if (!data || data.length === 0) {
      return {
        ma: {},
        rsi: null,
        macd: null,
        bollingerBands: null,
        kdj: null,
        supertrend: null
      }
    }

    // 预处理并验证价格数据
    const processedData = data.map(item => {
      try {
        const close = parseFloat(item.close)
        const high = parseFloat(item.high)
        const low = parseFloat(item.low)
        
        if (isNaN(close) || isNaN(high) || isNaN(low)) {
          console.warn('发现无效价格数据:', item)
          return null
        }
        
        return { close, high, low }
      } catch (error) {
        console.error('处理价格数据时出错:', error)
        return null
      }
    })

    // 过滤出有效的收盘价数据
    const closes = processedData.map(item => item ? item.close : null)
    const validData = processedData.filter(item => item !== null)

    // 如果有效数据太少，返回空结果
    if (validData.length < 10) {
      console.warn('有效数据点数量不足，无法计算指标')
      return {
        ma: {},
        rsi: null,
        macd: null,
        bollingerBands: null,
        kdj: null,
        supertrend: null
      }
    }

    // 计算移动平均线
    const ma = {}
    if (indicatorSettings.ma && indicatorSettings.ma.periods) {
      indicatorSettings.ma.periods.forEach(period => {
        if (validData.length >= period) {
          ma[`ma${period}`] = calculateMA(closes, period)
        } else {
          console.warn(`数据长度 ${validData.length} 小于 MA 周期 ${period}，跳过计算`)
          ma[`ma${period}`] = Array(closes.length).fill(null)
        }
      })
    }

    return {
      ma,
      rsi: validData.length >= indicatorSettings.rsi.period ? 
           calculateRSI(closes, indicatorSettings.rsi.period) : null,
      macd: validData.length >= Math.max(indicatorSettings.macd.long, indicatorSettings.macd.short) ? 
            calculateMACD(closes, indicatorSettings.macd.short, indicatorSettings.macd.long, indicatorSettings.macd.signal) : null,
      bollingerBands: validData.length >= indicatorSettings.bollingerBands.period ? 
                     calculateBollingerBands(closes, indicatorSettings.bollingerBands.period, indicatorSettings.bollingerBands.stdDev) : null,
      kdj: validData.length >= indicatorSettings.kdj.n ? 
           calculateKDJ(data, indicatorSettings.kdj.n, indicatorSettings.kdj.m1, indicatorSettings.kdj.m2) : null,
      supertrend: validData.length >= indicatorSettings.supertrend.atrPeriod ? 
                 calculateSupertrend(data, indicatorSettings.supertrend.atrPeriod, indicatorSettings.supertrend.multiplier) : null,
    };
  };

  return {
    calculateAllIndicators,
    calculateMA,
    calculateEMA,
    calculateRSI,
    calculateMACD,
    calculateBollingerBands,
    calculateKDJ,
    calculateATR,
    calculateSupertrend,
  };
}