import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';
import { useChartDataStore } from './chartDataStore';
import { useChartSettingsStore } from './chartSettingsStore';

export const useChartStore = defineStore('chart', () => {
  const dataStore = useChartDataStore();
  const settingsStore = useChartSettingsStore();

  // 从其他 store 中暴露 state 和 getter
  const chartData = dataStore.chartData;
  const isLoading = dataStore.isLoading;
  const error = dataStore.error;
  const currentPrice = dataStore.currentPrice;
  const priceChangeDirection = dataStore.priceChangeDirection;
  const selectedSymbol = settingsStore.selectedSymbol;
  const selectedTimeframe = settingsStore.selectedTimeframe;
  const isDarkTheme = settingsStore.isDarkTheme;

  // 新增缺失的状态
  const tradingSignals = ref([]);
  const chartInstances = reactive({});
  const realTimePriceData = reactive({
    price: 0,
    direction: 'neutral'
  });
  
  // 显示设置
  const displaySettings = reactive({
    showRSI: true,
    showMACD: true,
    showKDJ: true,
    showBoll: true
  });
  // 注意：成交量已集成在K线图中，无需单独控制显示
  
  // 指标设置
  const indicatorSettings = reactive({
    rsi: {
      period: 14,
      overbought: 70,
      oversold: 30
    },
    boll: {
      period: 20,
      stdDev: 2
    },
    macd: {
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9
    },
    kdj: {
      kPeriod: 9,
      dPeriod: 3,
      jPeriod: 3
    }
  });

  // Actions
  async function loadChartData() {
    await dataStore.fetchCandlestickData();
  }

  function startRealTimeUpdate() {
    dataStore.startRealtimeUpdates();
  }

  function stopRealTimeUpdate() {
    dataStore.stopRealtimeUpdates();
  }

  function setSelectedSymbol(symbol) {
    settingsStore.setSelectedSymbol(symbol);
  }

  function setSelectedTimeframe(timeframe) {
    settingsStore.setSelectedTimeframe(timeframe);
  }

  function toggleTheme() {
    settingsStore.toggleTheme();
  }

  // 交易信号相关方法
  function addTradingSignal(signal) {
    const newSignal = {
      id: Date.now() + Math.random(),
      timestamp: Date.now(),
      ...signal
    };
    tradingSignals.value.unshift(newSignal);
    // 保持最多50个信号
    if (tradingSignals.value.length > 50) {
      tradingSignals.value = tradingSignals.value.slice(0, 50);
    }
  }

  function removeSignal(signalId) {
    const index = tradingSignals.value.findIndex(s => s.id === signalId);
    if (index > -1) {
      tradingSignals.value.splice(index, 1);
    }
  }

  function clearSignals() {
    tradingSignals.value = [];
  }

  // 图表实例管理
  function setChartInstance(key, instance) {
    chartInstances[key] = instance;
  }

  function getChartInstance(key) {
    return chartInstances[key];
  }

  function disposeAllCharts() {
    Object.values(chartInstances).forEach(chart => {
      if (chart && typeof chart.dispose === 'function') {
        chart.dispose();
      }
    });
    Object.keys(chartInstances).forEach(key => {
      delete chartInstances[key];
    });
  }

  return {
    // State & Getters
    chartData,
    isLoading,
    error,
    currentPrice,
    priceChangeDirection,
    selectedSymbol,
    selectedTimeframe,
    isDarkTheme,
    tradingSignals,
    chartInstances,
    realTimePriceData,
    displaySettings,
    indicatorSettings,

    // Actions
    loadChartData,
    startRealTimeUpdate,
    stopRealTimeUpdate,
    setSelectedSymbol,
    setSelectedTimeframe,
    toggleTheme,
    addTradingSignal,
    removeSignal,
    clearSignals,
    setChartInstance,
    getChartInstance,
    disposeAllCharts,
  };
});