import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import PriceInfoPanel from './PriceInfoPanel.vue'
import { createPinia, setActivePinia } from 'pinia'
import { useChartSettingsStore } from '@/stores/chartSettingsStore'
import { useChartDataStore } from '@/stores/chartDataStore'
import { useInfoPanelStore } from '@/stores/infoPanelStore'

describe('PriceInfoPanel.vue', () => {
  beforeEach(() => {
    const pinia = createPinia()
    setActivePinia(pinia)

    // 模拟 store 的状态
    const chartSettingsStore = useChartSettingsStore()
    const chartDataStore = useChartDataStore()
    const infoPanelStore = useInfoPanelStore()

    // 设置初始状态
    chartSettingsStore.$patch({
      isDarkTheme: false,
      selectedSymbol: 'ETH-USDT'
    })

    chartDataStore.$patch({
      isLoading: false,
      currentPrice: {
        last: 2000,
        change: 100,
        changePercent: 5,
        high24h: 2100,
        low24h: 1900,
        vol24h: 1000000,
        volCcy24h: 2000000000
      },
      priceChangeDirection: 1 // 1 for up, -1 for down, 0 for neutral
    })
  })

  it('renders symbol name from store', () => {
    const wrapper = mount(PriceInfoPanel)
    expect(wrapper.find('.symbol-name').text()).toBe('ETH-USDT')
  })

  it('displays the current price with correct formatting', () => {
    const wrapper = mount(PriceInfoPanel)
    const priceElement = wrapper.find('.current-price')
    expect(priceElement.text()).toContain('2,000.00')
  })

  it('shows price change with correct class for upward trend', () => {
    const wrapper = mount(PriceInfoPanel)
    const priceChangeElement = wrapper.find('.price-change')
    expect(priceChangeElement.exists()).toBe(true)
    expect(priceChangeElement.classes()).toContain('up')
    expect(priceChangeElement.text()).toContain('+100.00')
    expect(priceChangeElement.text()).toContain('5.00%')
  })
})