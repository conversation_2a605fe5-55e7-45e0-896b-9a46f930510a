import { ref, reactive, computed, watch, nextTick } from 'vue'
import { useIndicators } from './useIndicators'
import { useAdvancedCharts } from './useAdvancedCharts'
import { useEnhancedDataFetching } from './useEnhancedDataFetching'
import { useChartErrorHandler } from './useChartErrorHandler'

/**
 * 高级图表协调器
 * 统一管理图表、指标、数据获取和错误处理
 * @param {Object} options - 配置选项
 * @returns {Object} 协调器实例
 */
export function useAdvancedChartCoordinator({
  selectedSymbol,
  selectedTimeframe,
  refs,
  indicatorSettings,
  displaySettings,
  errorHandler
}) {
  // 状态管理
  const loadingChart = ref(false)
  const isInitialized = ref(false)
  const lastUpdateTime = ref(null)
  const retryCount = ref(0)
  const maxRetries = 3

  // 数据状态
  const chartData = ref([])
  const currentPrice = ref(null)
  const technicalIndicators = ref({})
  const marketSentiment = ref({ score: 0, label: '中性' })
  const tradingSignals = ref([])
  const dataSource = ref('real')

  // 性能监控
  const performanceMetrics = reactive({
    loadTime: 0,
    updateTime: 0,
    errorCount: 0,
    lastError: null
  })

  // 初始化子模块
  const indicators = useIndicators(indicatorSettings)
  const charts = useAdvancedCharts(refs, displaySettings)
  const dataFetching = useEnhancedDataFetching()

  /**
   * 计算市场情绪
   * @param {Object} indicators - 技术指标数据
   * @returns {Object} 市场情绪
   */
  const calculateMarketSentiment = (indicators) => {
    try {
      if (!indicators || !indicators.rsi || !indicators.ma || !indicators.macd) {
        return { score: 0, label: '中性' }
      }

      let score = 0
      const weights = { rsi: 0.4, ma: 0.3, macd: 0.3 }

      // RSI 分析
      const rsiValues = indicators.rsi?.filter(v => v !== null) || []
      if (rsiValues.length > 0) {
        const rsi = rsiValues[rsiValues.length - 1]
        if (rsi > 70) score -= 2 * weights.rsi
        else if (rsi > 60) score -= 1 * weights.rsi
        else if (rsi < 30) score += 2 * weights.rsi
        else if (rsi < 40) score += 1 * weights.rsi
      }

      // MA 分析
      const ma5Values = indicators.ma?.ma5?.filter(v => v !== null) || []
      const ma20Values = indicators.ma?.ma20?.filter(v => v !== null) || []
      if (ma5Values.length > 0 && ma20Values.length > 0) {
        const ma5 = ma5Values[ma5Values.length - 1]
        const ma20 = ma20Values[ma20Values.length - 1]
        if (ma5 > ma20) score += 1 * weights.ma
        else score -= 1 * weights.ma
      }

      // MACD 分析
      const difValues = indicators.macd?.dif?.filter(v => v !== null) || []
      const deaValues = indicators.macd?.dea?.filter(v => v !== null) || []
      if (difValues.length > 0 && deaValues.length > 0) {
        const difLine = difValues[difValues.length - 1]
        const deaLine = deaValues[deaValues.length - 1]
        if (difLine > deaLine) score += 1 * weights.macd
        else score -= 1 * weights.macd
      }

      // 确定情绪标签
      let label
      if (score > 1.5) label = '极度看涨'
      else if (score > 0.5) label = '看涨'
      else if (score < -1.5) label = '极度看跌'
      else if (score < -0.5) label = '看跌'
      else label = '中性'

      return { score: Math.round(score * 100) / 100, label }
    } catch (error) {
      console.error('计算市场情绪失败:', error)
      return { score: 0, label: '中性' }
    }
  }

  /**
   * 生成交易信号
   * @param {Array} data - K线数据
   * @param {Object} indicators - 技术指标
   * @returns {Array} 交易信号列表
   */
  const generateTradingSignals = (data, indicators) => {
    try {
      const signals = []
      const settings = indicatorSettings?.tradingSignals || {
        useRSI: true,
        useMACD: true,
        rsi: { overbought: 70, oversold: 30 }
      }

      if (!data || data.length < 2 || !indicators) {
        return signals
      }

      const lastDataPoint = data[data.length - 1]

      // RSI 信号
      const rsiValues = indicators.rsi?.filter(v => v !== null) || []
      if (settings.useRSI && rsiValues.length > 1) {
        const currentRSI = rsiValues[rsiValues.length - 1]
        const prevRSI = rsiValues[rsiValues.length - 2]

        if (prevRSI > settings.rsi.overbought && currentRSI <= settings.rsi.overbought) {
          signals.push({
            time: lastDataPoint.time,
            signal: '卖出',
            price: lastDataPoint.close,
            reason: `RSI从超买区域回落 (${currentRSI.toFixed(2)})`,
            strength: 'medium',
            type: 'rsi'
          })
        }

        if (prevRSI < settings.rsi.oversold && currentRSI >= settings.rsi.oversold) {
          signals.push({
            time: lastDataPoint.time,
            signal: '买入',
            price: lastDataPoint.close,
            reason: `RSI从超卖区域反弹 (${currentRSI.toFixed(2)})`,
            strength: 'medium',
            type: 'rsi'
          })
        }
      }

      // MACD 信号
      const difValues = indicators.macd?.dif?.filter(v => v !== null) || []
      const deaValues = indicators.macd?.dea?.filter(v => v !== null) || []
      if (settings.useMACD && difValues.length > 1 && deaValues.length > 1) {
        const currentDIF = difValues[difValues.length - 1]
        const currentDEA = deaValues[deaValues.length - 1]
        const prevDIF = difValues[difValues.length - 2]
        const prevDEA = deaValues[deaValues.length - 2]

        // 金叉
        if (prevDIF <= prevDEA && currentDIF > currentDEA) {
          signals.push({
            time: lastDataPoint.time,
            signal: '买入',
            price: lastDataPoint.close,
            reason: 'MACD金叉信号',
            strength: 'strong',
            type: 'macd'
          })
        }

        // 死叉
        if (prevDIF >= prevDEA && currentDIF < currentDEA) {
          signals.push({
            time: lastDataPoint.time,
            signal: '卖出',
            price: lastDataPoint.close,
            reason: 'MACD死叉信号',
            strength: 'strong',
            type: 'macd'
          })
        }
      }

      // Supertrend 信号
      if (settings.useSupertrend && indicators.supertrend && indicators.supertrend.length > 1) {
        const supertrendLength = indicators.supertrend.length
        const currentSTIndex = Math.min(currentDataIndex, supertrendLength - 1)
        const prevSTIndex = Math.min(prevDataIndex, supertrendLength - 2)
        
        if (currentSTIndex >= 0 && prevSTIndex >= 0) {
          const currentST = indicators.supertrend[currentSTIndex]
          const prevST = indicators.supertrend[prevSTIndex]
          const currentPrice = data[currentDataIndex].close
          const prevPrice = data[prevDataIndex].close

          if (prevPrice <= prevST && currentPrice > currentST) {
            signals.push({
              time: data[currentDataIndex].time,
              signal: '买入',
              price: currentPrice,
              reason: '价格突破Supertrend上轨',
              strength: 'strong',
              type: 'supertrend'
            })
          }

          if (prevPrice >= prevST && currentPrice < currentST) {
            signals.push({
              time: data[currentDataIndex].time,
              signal: '卖出',
              price: currentPrice,
              reason: '价格跌破Supertrend下轨',
              strength: 'strong',
              type: 'supertrend'
            })
          }
        }
      }

      return signals
    } catch (error) {
      console.error('生成交易信号失败:', error)
      return []
    }
  }

  /**
   * 处理加载的数据
   * @param {Array} data - K线数据
   */
  const processLoadedData = async (data) => {
    try {
      const startTime = performance.now()

      // 计算技术指标
      const calculatedIndicators = indicators.calculateAllIndicators(data)
      technicalIndicators.value = calculatedIndicators

      // 计算市场情绪
      marketSentiment.value = calculateMarketSentiment(calculatedIndicators)

      // 生成交易信号
      const newSignals = generateTradingSignals(data, calculatedIndicators)
      if (newSignals.length > 0) {
        tradingSignals.value = [...tradingSignals.value, ...newSignals]
        // 保持最近100个信号
        if (tradingSignals.value.length > 100) {
          tradingSignals.value = tradingSignals.value.slice(-100)
        }
      }

      // 更新当前价格
      if (data && data.length > 0) {
        const latestData = data[data.length - 1]
        const prevData = data[data.length - 2]
        
        currentPrice.value = {
          price: latestData.close,
          change: latestData.close - (prevData?.close || latestData.close),
          changePercent: prevData ? ((latestData.close - prevData.close) / prevData.close * 100) : 0,
          direction: latestData.close > (prevData?.close || latestData.close) ? 'up' : 
                    latestData.close < (prevData?.close || latestData.close) ? 'down' : 'neutral'
        }
      }

      // 更新图表
      await charts.updateAllCharts(data, calculatedIndicators, displaySettings)

      // 记录性能指标
      performanceMetrics.updateTime = performance.now() - startTime
      lastUpdateTime.value = new Date().toISOString()

    } catch (error) {
      performanceMetrics.errorCount++
      performanceMetrics.lastError = error.message
      throw error
    }
  }

  /**
   * 加载图表数据
   */
  const loadChartData = async () => {
    try {
      loadingChart.value = true
      const startTime = performance.now()

      let data
      if (dataSource.value === 'real') {
        data = await dataFetching.loadRealMarketData(
          selectedSymbol.value,
          selectedTimeframe.value
        )
      } else {
        // 使用WebSocket获取实时数据
        await dataFetching.loadWebSocketData()
        data = dataFetching.chartData.value
      }

      if (!data || data.length === 0) {
        throw new Error('未获取到有效数据')
      }

      chartData.value = data
      await processLoadedData(data)

      // 记录性能指标
      performanceMetrics.loadTime = performance.now() - startTime
      retryCount.value = 0

    } catch (error) {
      console.error('加载图表数据失败:', error)
      
      if (retryCount.value < maxRetries) {
        retryCount.value++
        console.log(`重试加载数据 (${retryCount.value}/${maxRetries})`)
        setTimeout(() => loadChartData(), 2000 * retryCount.value)
      } else {
        if (errorHandler) {
          errorHandler(error, 'loadChartData', () => {
            retryCount.value = 0
            loadChartData()
          })
        } else {
          throw error
        }
      }
    } finally {
      loadingChart.value = false
    }
  }

  /**
   * 初始化协调器
   */
  const initializeCoordinator = async () => {
    try {
      console.log('初始化高级图表协调器...')
      
      // 等待DOM更新
      await nextTick()
      
      // 初始化图表
      await charts.initializeAllCharts()
      
      // 加载数据
      await loadChartData()
      
      isInitialized.value = true
      console.log('高级图表协调器初始化完成')
      
    } catch (error) {
      console.error('初始化协调器失败:', error)
      if (errorHandler) {
        errorHandler(error, 'initializeCoordinator', () => {
          setTimeout(initializeCoordinator, 3000)
        })
      } else {
        throw error
      }
    }
  }

  /**
   * 刷新数据
   */
  const refreshData = async () => {
    if (!isInitialized.value) {
      await initializeCoordinator()
      return
    }
    await loadChartData()
  }

  /**
   * 更新图表
   */
  const updateCharts = async () => {
    if (!isInitialized.value || !chartData.value.length) return
    
    try {
      await processLoadedData(chartData.value)
    } catch (error) {
      console.error('更新图表失败:', error)
      if (errorHandler) {
        errorHandler(error, 'updateCharts')
      }
    }
  }

  /**
   * 销毁图表
   */
  const destroyCharts = () => {
    try {
      charts.destroyAllCharts()
      isInitialized.value = false
      console.log('图表已销毁')
    } catch (error) {
      console.error('销毁图表失败:', error)
    }
  }

  /**
   * 刷新WebSocket数据源
   */
  const refreshDataSource = async () => {
    // 统一使用WebSocket实时数据源
    dataSource.value = 'real'
    await refreshData()
  }

  // 监听配置变化
  watch([selectedSymbol, selectedTimeframe], async () => {
    if (isInitialized.value) {
      await refreshData()
    }
  })

  watch(dataSource, async () => {
    if (isInitialized.value) {
      await refreshData()
    }
  })

  // 监听指标设置变化
  watch(
    () => [indicatorSettings, displaySettings],
    async () => {
      if (isInitialized.value && chartData.value.length > 0) {
        await updateCharts()
      }
    },
    { deep: true }
  )

  return {
    // 状态
    loadingChart,
    isInitialized,
    chartData,
    currentPrice,
    technicalIndicators,
    marketSentiment,
    tradingSignals,
    dataSource,
    performanceMetrics,
    
    // 方法
    initializeCoordinator,
    refreshData,
    updateCharts,
    destroyCharts,
    toggleDataSource,
    
    // 计算属性
    hasData: computed(() => chartData.value.length > 0),
    isLoading: computed(() => loadingChart.value),
    lastUpdate: computed(() => lastUpdateTime.value)
  }
}

export default useAdvancedChartCoordinator