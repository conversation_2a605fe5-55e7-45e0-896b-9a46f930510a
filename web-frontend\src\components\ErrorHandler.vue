<template>
  <div>
    <!-- 全局错误提示 -->
    <a-modal
      v-model:open="errorModal.visible"
      title="错误提示"
      :footer="null"
      @cancel="closeErrorModal"
    >
      <div class="error-content">
        <a-result
          :status="errorModal.type"
          :title="errorModal.title"
          :sub-title="errorModal.message"
        >
          <template #extra>
            <a-space>
              <a-button type="primary" @click="closeErrorModal">
                确定
              </a-button>
              <a-button v-if="errorModal.retry" @click="retryAction">
                重试
              </a-button>
            </a-space>
          </template>
        </a-result>
      </div>
    </a-modal>

    <!-- 网络状态提示 -->
    <a-alert
      v-if="!isOnline"
      message="网络连接已断开"
      description="请检查您的网络连接，部分功能可能无法正常使用"
      type="warning"
      show-icon
      closable
      style="position: fixed; top: 64px; left: 0; right: 0; z-index: 1000;"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'

const isOnline = ref(navigator.onLine)
const errorModal = ref({
  visible: false,
  type: 'error',
  title: '',
  message: '',
  retry: null
})

// 网络状态监听
const handleOnline = () => {
  isOnline.value = true
  message.success('网络连接已恢复')
}

const handleOffline = () => {
  isOnline.value = false
  message.warning('网络连接已断开')
}

// 显示错误模态框
const showError = (error, retryCallback = null) => {
  let title = '操作失败'
  let type = 'error'
  let errorMessage = '未知错误'

  if (error.response) {
    // HTTP 错误响应
    const status = error.response.status
    const data = error.response.data

    switch (status) {
      case 400:
        title = '请求错误'
        errorMessage = data.msg || data.message || '请求参数有误'
        break
      case 401:
        title = '认证失败'
        errorMessage = '登录已过期，请重新登录'
        type = 'warning'
        // 自动跳转到登录页
        setTimeout(() => {
          window.location.href = '/login'
        }, 2000)
        break
      case 403:
        title = '权限不足'
        errorMessage = '您没有权限执行此操作'
        type = 'warning'
        break
      case 404:
        title = '资源不存在'
        errorMessage = '请求的资源不存在'
        break
      case 429:
        title = '请求过于频繁'
        errorMessage = '请求过于频繁，请稍后再试'
        type = 'warning'
        break
      case 500:
        title = '服务器错误'
        errorMessage = '服务器内部错误，请稍后重试'
        break
      case 502:
      case 503:
      case 504:
        title = '服务不可用'
        errorMessage = '服务暂时不可用，请稍后重试'
        break
      default:
        errorMessage = data.msg || data.message || `HTTP ${status} 错误`
    }
  } else if (error.request) {
    // 网络错误
    title = '网络错误'
    errorMessage = '网络连接失败，请检查网络设置'
    type = 'warning'
  } else {
    // 其他错误
    errorMessage = error.message || '操作失败'
  }

  errorModal.value = {
    visible: true,
    type,
    title,
    message: errorMessage,
    retry: retryCallback
  }
}

// 关闭错误模态框
const closeErrorModal = () => {
  errorModal.value.visible = false
}

// 重试操作
const retryAction = () => {
  if (errorModal.value.retry) {
    errorModal.value.retry()
  }
  closeErrorModal()
}

// 全局错误处理函数
const handleGlobalError = (error, retryCallback = null) => {
  console.error('Global error:', error)
  
  // 对于某些轻微错误，只显示消息提示
  if (error.response && [400, 422].includes(error.response.status)) {
    const errorMsg = error.response.data?.msg || error.response.data?.message || '操作失败'
    message.error(errorMsg)
    return
  }
  
  // 对于严重错误，显示模态框
  showError(error, retryCallback)
}

// 暴露给全局使用
window.handleGlobalError = handleGlobalError

onMounted(() => {
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 全局未捕获的 Promise 错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    handleGlobalError(event.reason)
    event.preventDefault()
  })
  
  // 全局 JavaScript 错误
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error)
    handleGlobalError(event.error)
  })
})

onUnmounted(() => {
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
})

// 导出错误处理函数供其他组件使用
defineExpose({
  showError,
  handleGlobalError
})
</script>

<style scoped>
.error-content {
  text-align: center;
}
</style>