/**
 * 格式化价格
 * @param {number | string | null | undefined} price - 需要格式化的价格
 * @returns {string} 格式化后的价格字符串，或 'N/A'
 */
export const formatPrice = (price) => {
  if (price === null || price === undefined || isNaN(price)) return '';
  const priceNumber = typeof price === 'string' ? parseFloat(price) : price;

  return priceNumber.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

/**
 * 格式化成交量
 * @param {number | null | undefined} volume - 需要格式化的成交量
 * @returns {string} 格式化后的成交量字符串
 */
export const formatVolume = (volume) => {
  if (volume === null || volume === undefined || isNaN(volume)) return '';
  if (volume === 0) return '0';
  if (volume > 1000000000) {
    return (volume / 1000000000).toFixed(2) + 'B';
  } else if (volume > 1000000) {
    return (volume / 1000000).toFixed(2) + 'M';
  } else if (volume > 1000) {
    return (volume / 1000).toFixed(2) + 'K';
  } else {
    return String(volume);
  }
};