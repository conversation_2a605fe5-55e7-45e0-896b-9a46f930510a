<template>
  <BaseChart
    title="MACD 指标"
    :signals="signals"
    :settings="localSettings"
    @save-settings="saveSettings"
    @reset-settings="resetSettings"
  >
    <template #chart>
      <div ref="chartRef" class="macd-chart" :style="{ height: chartHeight + 'px' }"></div>
    </template>
    <template #settings-form>
      <a-form :model="localSettings" layout="vertical">
        <a-form-item label="快线周期(DIF)" name="fastPeriod">
          <a-slider v-model:value="localSettings.fastPeriod" :min="5" :max="20" :marks="{ 12: '12' }" />
        </a-form-item>
        <a-form-item label="慢线周期(DEA)" name="slowPeriod">
          <a-slider v-model:value="localSettings.slowPeriod" :min="20" :max="40" :marks="{ 26: '26' }" />
        </a-form-item>
        <a-form-item label="信号线周期(MACD)" name="signalPeriod">
          <a-slider v-model:value="localSettings.signalPeriod" :min="5" :max="15" :marks="{ 9: '9' }" />
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="localSettings.enableAlerts">启用交易信号</a-checkbox>
        </a-form-item>
      </a-form>
    </template>
    <template #signal-list="{ item }">
      <a-list-item-meta>
        <template #title>
          <a-space>
            <a-tag :color="getSignalColor(item.type)">{{ getSignalText(item.type) }}</a-tag>
            <span>{{ item.message }}</span>
          </a-space>
        </template>
        <template #description>
          <div>
            <div>时间: {{ formatTime(item.timestamp) }}</div>
            <div>价格: {{ item.price?.toFixed(2) }}</div>
            <div>DIF: {{ item.dif?.toFixed(4) }}</div>
            <div>DEA: {{ item.dea?.toFixed(4) }}</div>
            <div>MACD: {{ item.macd?.toFixed(4) }}</div>
            <div>置信度: {{ item.confidence }}%</div>
          </div>
        </template>
      </a-list-item-meta>
    </template>
  </BaseChart>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import BaseChart from './BaseChart.vue'

// Props
const props = defineProps({
  chartData: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: '200px'
  },
  isDarkTheme: {
    type: Boolean,
    default: false
  },
  chartId: {
    type: String,
    default: () => `macd-chart-${Date.now()}`
  },
  fastPeriod: {
    type: Number,
    default: INDICATOR_DEFAULTS.MACD.fastPeriod
  },
  slowPeriod: {
    type: Number,
    default: INDICATOR_DEFAULTS.MACD.slowPeriod
  },
  signalPeriod: {
    type: Number,
    default: INDICATOR_DEFAULTS.MACD.signalPeriod
  }
})

// Emits
const emit = defineEmits(['chartReady', 'chartError', 'signalGenerated'])

const chartInstance = ref(null)

// MACD数据
const macdData = ref({
  dif: [],
  dea: [],
  macd: []
})

// 计算MACD数据
const calculateMACDData = () => {
  if (!props.chartData || props.chartData.length === 0) {
    macdData.value = { dif: [], dea: [], macd: [] }
    return
  }

  try {
    const ema12 = indicatorCalculators.calculateEMA(props.chartData, props.fastPeriod)
    const ema26 = indicatorCalculators.calculateEMA(props.chartData, props.slowPeriod)
    
    // 计算DIF (MACD线)
    const dif = []
    for (let i = 0; i < ema12.length; i++) {
      if (ema12[i] !== null && ema26[i] !== null) {
        dif.push(ema12[i] - ema26[i])
      } else {
        dif.push(null)
      }
    }
    
    // 计算DEA (信号线) - DIF的EMA
    const dea = []
    const multiplier = 2 / (props.signalPeriod + 1)
    let emaValue = null
    
    for (let i = 0; i < dif.length; i++) {
      if (dif[i] !== null) {
        if (emaValue === null) {
          emaValue = dif[i]
        } else {
          emaValue = (dif[i] - emaValue) * multiplier + emaValue
        }
        dea.push(emaValue)
      } else {
        dea.push(null)
      }
    }
    
    // 计算MACD柱状图
    const macd = []
    for (let i = 0; i < dif.length; i++) {
      if (dif[i] !== null && dea[i] !== null) {
        macd.push((dif[i] - dea[i]) * 2)
      } else {
        macd.push(null)
      }
    }
    
    macdData.value = { dif, dea, macd }
    
    // 生成交易信号
    generateMACDSignals()
  } catch (err) {
    console.error('计算MACD失败:', err)
    emit('chartError', err.message)
  }
}

// 生成MACD交易信号
const generateMACDSignals = () => {
  const { dif, dea, macd } = macdData.value
  if (dif.length < 2 || dea.length < 2) return

  const signals = []
  const currentIndex = dif.length - 1
  const prevIndex = currentIndex - 1
  
  const currentDIF = dif[currentIndex]
  const currentDEA = dea[currentIndex]
  const prevDIF = dif[prevIndex]
  const prevDEA = dea[prevIndex]
  const currentMACD = macd[currentIndex]
  
  if (currentDIF !== null && currentDEA !== null && prevDIF !== null && prevDEA !== null) {
    // 金叉信号 (DIF上穿DEA)
    if (prevDIF <= prevDEA && currentDIF > currentDEA) {
      signals.push({
        type: 'BUY',
        indicator: 'MACD',
        value: currentDIF - currentDEA,
        strength: Math.min(1, Math.abs(currentDIF - currentDEA) / 0.1),
        description: `MACD金叉信号 (DIF: ${currentDIF.toFixed(4)}, DEA: ${currentDEA.toFixed(4)})`
      })
    }
    
    // 死叉信号 (DIF下穿DEA)
    if (prevDIF >= prevDEA && currentDIF < currentDEA) {
      signals.push({
        type: 'SELL',
        indicator: 'MACD',
        value: currentDIF - currentDEA,
        strength: Math.min(1, Math.abs(currentDIF - currentDEA) / 0.1),
        description: `MACD死叉信号 (DIF: ${currentDIF.toFixed(4)}, DEA: ${currentDEA.toFixed(4)})`
      })
    }
    
    // MACD柱状图信号
    if (currentMACD !== null) {
      if (currentMACD > 0 && macd[prevIndex] <= 0) {
        signals.push({
          type: 'BUY',
          indicator: 'MACD_HISTOGRAM',
          value: currentMACD,
          strength: Math.min(1, Math.abs(currentMACD) / 0.05),
          description: `MACD柱状图转正信号 (${currentMACD.toFixed(4)})`
        })
      } else if (currentMACD < 0 && macd[prevIndex] >= 0) {
        signals.push({
          type: 'SELL',
          indicator: 'MACD_HISTOGRAM',
          value: currentMACD,
          strength: Math.min(1, Math.abs(currentMACD) / 0.05),
          description: `MACD柱状图转负信号 (${currentMACD.toFixed(4)})`
        })
      }
    }
  }

  if (signals.length > 0) {
    emit('signalGenerated', signals)
  }
}

// 初始化MACD图表
const initMACDChart = async () => {
  await nextTick()
  
  calculateMACDData()
  
  if (macdData.value.dif.length === 0) {
    console.warn('MACD图表数据为空')
    return
  }

  try {
    const theme = themeConfig.value
    const baseOption = getBaseChartOption('MACD', {
      yAxis: {
        type: 'value',
        position: 'right',
        scale: true,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: theme.textColor,
          fontSize: 10,
          formatter: function(value) {
            return value.toFixed(4)
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: theme.gridColor
          }
        }
      }
    })

    const macdOption = {
      ...baseOption,
      legend: {
        data: ['DIF', 'DEA', 'MACD'],
        top: 5,
        right: 20,
        textStyle: {
          color: theme.textColor,
          fontSize: 12
        },
        itemWidth: 14,
        itemHeight: 8
      },
      xAxis: {
        ...baseOption.xAxis,
        data: formatChartData.timeAxis(props.chartData)
      },
      series: [
        {
          name: 'DIF',
          type: 'line',
          data: macdData.value.dif,
          lineStyle: {
            color: '#2196F3',
            width: 2
          },
          symbol: 'none',
          smooth: true
        },
        {
          name: 'DEA',
          type: 'line',
          data: macdData.value.dea,
          lineStyle: {
            color: '#FF9800',
            width: 2
          },
          symbol: 'none',
          smooth: true
        },
        {
          name: 'MACD',
          type: 'bar',
          data: macdData.value.macd,
          barWidth: '60%',
          itemStyle: {
            color: function(params) {
              return params.value >= 0 ? '#4CAF50' : '#F44336'
            }
          }
        },
        {
          name: '零轴',
          type: 'line',
          data: new Array(props.chartData.length).fill(0),
          lineStyle: {
            color: theme.textColor,
            width: 1,
            type: 'dotted',
            opacity: 0.5
          },
          symbol: 'none',
          silent: true
        }
      ]
    }

    const chart = initChart(props.chartId, macdOption)
    if (chart) {
      emit('chartReady', chart)
    } else {
      emit('chartError', error.value)
    }
  } catch (err) {
    console.error('初始化MACD图表失败:', err)
    emit('chartError', err.message)
  }
}

// 更新图表数据
const updateMACDChart = () => {
  if (!chartInstance.value) {
    return
  }

  calculateMACDData()
  
  if (macdData.value.dif.length === 0) {
    return
  }

  try {
    const updateOption = {
      xAxis: {
        data: formatChartData.timeAxis(props.chartData)
      },
      series: [
        {
          data: macdData.value.dif
        },
        {
          data: macdData.value.dea
        },
        {
          data: macdData.value.macd
        },
        {
          data: new Array(props.chartData.length).fill(0)
        }
      ]
    }

    updateChart(updateOption)
  } catch (err) {
    console.error('更新MACD图表失败:', err)
    emit('chartError', err.message)
  }
}

// 监听数据变化
watch(
  () => props.chartData,
  (newData) => {
    if (newData && newData.length > 0) {
      if (chartInstance.value) {
        updateMACDChart()
      } else {
        initMACDChart()
      }
    }
  },
  { deep: true }
)

// 监听MACD参数变化
watch(
  [() => props.fastPeriod, () => props.slowPeriod, () => props.signalPeriod],
  () => {
    if (chartInstance.value && props.chartData && props.chartData.length > 0) {
      updateMACDChart()
    }
  }
)

// 监听主题变化
watch(
  () => props.isDarkTheme,
  () => {
    if (chartInstance.value && props.chartData && props.chartData.length > 0) {
      initMACDChart()
    }
  }
)

// 窗口大小变化时调整图表
const handleResize = () => {
  resizeChart()
}

// 组件挂载
onMounted(() => {
  if (props.chartData && props.chartData.length > 0) {
    initMACDChart()
  }
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  disposeChart()
})

// 暴露方法给父组件
defineExpose({
  initChart: initMACDChart,
  updateChart: updateMACDChart,
  resizeChart,
  disposeChart,
  chartInstance,
  macdData
})
</script>

<style scoped>
.macd-chart-container {
  position: relative;
  width: 100%;
}

.macd-chart {
  width: 100%;
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ff4d4f;
  font-size: 14px;
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.macd-chart-container:hover .macd-chart {
  cursor: crosshair;
}
</style>