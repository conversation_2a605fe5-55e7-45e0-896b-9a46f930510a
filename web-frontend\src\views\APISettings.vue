<template>
  <div class="api-settings">
    <a-card title="OKX API 配置" style="margin-bottom: 24px;">
      <a-alert
        message="安全提示"
        description="请确保您的API密钥具有适当的权限。建议只开启读取和交易权限，不要开启提现权限。"
        type="warning"
        show-icon
        style="margin-bottom: 24px;"
      />
      
      <a-form
        :model="apiForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        @finish="saveApiConfig"
      >
        <a-form-item
          label="API Key"
          name="apiKey"
          :rules="[{ required: true, message: '请输入API Key' }]"
        >
          <a-input
            v-model:value="apiForm.apiKey"
            placeholder="请输入您的OKX API Key"
            :disabled="loading"
          />
        </a-form-item>
        
        <a-form-item
          label="Secret Key"
          name="secretKey"
          :rules="[{ required: true, message: '请输入Secret Key' }]"
        >
          <a-input-password
            v-model:value="apiForm.secretKey"
            placeholder="请输入您的OKX Secret Key"
            :disabled="loading"
          />
        </a-form-item>
        
        <a-form-item
          label="Passphrase"
          name="passphrase"
          :rules="[{ required: true, message: '请输入Passphrase' }]"
        >
          <a-input-password
            v-model:value="apiForm.passphrase"
            placeholder="请输入您的OKX Passphrase"
            :disabled="loading"
          />
        </a-form-item>
        
        <a-form-item
          label="环境"
          name="environment"
        >
          <a-radio-group v-model:value="apiForm.environment" :disabled="loading">
            <a-radio value="live">实盘环境</a-radio>
            <a-radio value="demo">模拟环境</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              保存配置
            </a-button>
            <a-button @click="testConnection" :loading="testLoading">
              测试连接
            </a-button>
            <a-button @click="clearConfig" :disabled="loading">
              清除配置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
    
    <!-- API状态显示 -->
    <a-card title="API 状态" v-if="apiStatus">
      <a-descriptions bordered>
        <a-descriptions-item label="连接状态">
          <a-tag :color="apiStatus.connected ? 'green' : 'red'">
            {{ apiStatus.connected ? '已连接' : '未连接' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="最后测试时间">
          {{ apiStatus.lastTestTime }}
        </a-descriptions-item>
        <a-descriptions-item label="账户类型">
          {{ apiStatus.accountType || '未知' }}
        </a-descriptions-item>
        <a-descriptions-item label="权限">
          <a-space>
            <a-tag v-for="permission in apiStatus.permissions" :key="permission" color="blue">
              {{ permission }}
            </a-tag>
          </a-space>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import axios from 'axios'

const loading = ref(false)
const testLoading = ref(false)
const apiStatus = ref(null)

const apiForm = ref({
  apiKey: '',
  secretKey: '',
  passphrase: '',
  environment: 'demo'
})

// 加载现有配置
const loadApiConfig = async () => {
  try {
    const response = await axios.get('/api/user/api-config')
    if (response.data.code === 0 && response.data.data) {
      const config = response.data.data
      apiForm.value = {
        apiKey: config.api_key || '',
        secretKey: '', // 出于安全考虑，不显示密钥
        passphrase: '', // 出于安全考虑，不显示密码
        environment: config.environment || 'demo'
      }
      
      // 如果有配置，显示遮罩
      if (config.api_key) {
        apiForm.value.apiKey = config.api_key.substring(0, 8) + '****'
        message.info('已加载现有配置，如需修改请重新输入完整信息')
      }
    }
  } catch (error) {
    console.warn('加载API配置失败:', error.message)
  }
}

// 保存API配置
const saveApiConfig = async () => {
  loading.value = true
  try {
    const response = await axios.post('/api/user/api-config', {
      api_key: apiForm.value.apiKey,
      api_secret: apiForm.value.secretKey,
      passphrase: apiForm.value.passphrase,
      environment: apiForm.value.environment
    })
    
    if (response.data.code === 0) {
      message.success('API配置保存成功')
      // 清空敏感信息显示
      apiForm.value.secretKey = ''
      apiForm.value.passphrase = ''
      // 遮罩API Key
      if (apiForm.value.apiKey.length > 8) {
        apiForm.value.apiKey = apiForm.value.apiKey.substring(0, 8) + '****'
      }
    } else {
      message.error(response.data.msg || '保存失败')
    }
  } catch (error) {
    message.error('保存失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 测试连接
const testConnection = async () => {
  testLoading.value = true
  try {
    const response = await axios.post('/api/user/test-api')
    
    if (response.data.code === 0) {
      apiStatus.value = {
        connected: true,
        lastTestTime: new Date().toLocaleString(),
        accountType: response.data.data.accountType || '交易账户',
        permissions: response.data.data.permissions || ['读取', '交易']
      }
      message.success('API连接测试成功')
    } else {
      apiStatus.value = {
        connected: false,
        lastTestTime: new Date().toLocaleString(),
        accountType: '未知',
        permissions: []
      }
      message.error(response.data.msg || '连接测试失败')
    }
  } catch (error) {
    apiStatus.value = {
      connected: false,
      lastTestTime: new Date().toLocaleString(),
      accountType: '未知',
      permissions: []
    }
    message.error('连接测试失败: ' + error.message)
  } finally {
    testLoading.value = false
  }
}

// 清除配置
const clearConfig = async () => {
  try {
    await axios.delete('/api/user/api-config')
    apiForm.value = {
      apiKey: '',
      secretKey: '',
      passphrase: '',
      environment: 'demo'
    }
    apiStatus.value = null
    message.success('配置已清除')
  } catch (error) {
    message.error('清除配置失败: ' + error.message)
  }
}

onMounted(() => {
  loadApiConfig()
})
</script>

<style scoped>
.api-settings {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}
</style>
