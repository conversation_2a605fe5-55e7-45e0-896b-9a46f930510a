<template>
  <div class="unified-indicator-manager">
    <!-- 指标快捷按钮栏 -->
    <div class="indicator-toolbar">
      <div class="indicator-buttons">
        <a-button
          v-for="indicator in availableIndicators"
          :key="indicator.name"
          :type="isIndicatorActive(indicator.name) ? 'primary' : 'default'"
          :class="{
            'indicator-btn': true,
            'active': isIndicatorActive(indicator.name)
          }"
          size="small"
          @click="toggleIndicator(indicator.name)"
        >
          <component :is="indicator.icon" />
          {{ indicator.displayName }}
        </a-button>
      </div>
      
      <!-- 设置按钮 -->
      <a-button 
        type="text" 
        size="small" 
        @click="showSettings = true"
        class="settings-btn"
      >
        <SettingOutlined />
        设置
      </a-button>
      
      <!-- 截图功能按钮 -->
      <a-dropdown :trigger="['click']" placement="bottomRight">
        <a-button class="screenshot-btn">
          <CameraOutlined />
          截图
        </a-button>
        <template #overlay>
          <a-menu @click="handleScreenshot">
            <a-menu-item key="chart">
              <LineChartOutlined />
              截取图表
            </a-menu-item>
            <a-menu-item key="indicators">
              <BarChartOutlined />
              截取指标面板
            </a-menu-item>
            <a-menu-item key="full">
              <StockOutlined />
              截取完整页面
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>

    <!-- 设置面板 -->
    <a-drawer
      v-model:open="showSettings"
      title="指标设置"
      placement="right"
      width="400"
    >
      <div class="settings-content">
        <!-- 活跃指标列表 -->
        <div class="active-indicators-section">
          <h4>活跃指标</h4>
          <a-list 
            :data-source="activeIndicators" 
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <template #actions>
                  <a-switch 
                    v-model:checked="item.visible"
                    size="small"
                    @change="toggleIndicatorVisibility(item)"
                  />
                  <a-button 
                    type="text" 
                    size="small" 
                    @click="removeIndicator(item.name)"
                    danger
                  >
                    <DeleteOutlined />
                  </a-button>
                </template>
                <a-list-item-meta>
                  <template #title>
                    <span>{{ item.displayName }}</span>
                    <a-tag :color="getIndicatorColor(item.name)" size="small">
                      {{ item.name }}
                    </a-tag>
                  </template>
                  <template #description>
                    <div class="indicator-params">
                      <!-- 动态参数配置 -->
                      <div v-if="item.name === 'VOL'">
                        <a-row :gutter="8">
                          <a-col :span="12">
                            <a-form-item label="短期MA" size="small">
                              <a-input-number 
                                v-model:value="item.params[0]"
                                :min="3" 
                                :max="10"
                                size="small"
                                @change="updateIndicatorParams(item)"
                              />
                            </a-form-item>
                          </a-col>
                          <a-col :span="12">
                            <a-form-item label="长期MA" size="small">
                              <a-input-number 
                                v-model:value="item.params[1]"
                                :min="10" 
                                :max="30"
                                size="small"
                                @change="updateIndicatorParams(item)"
                              />
                            </a-form-item>
                          </a-col>
                        </a-row>
                      </div>
                      
                      <div v-else-if="item.name === 'MACD'">
                        <a-row :gutter="8">
                          <a-col :span="8">
                            <a-form-item label="快线" size="small">
                              <a-input-number 
                                v-model:value="item.params[0]"
                                :min="5" 
                                :max="20"
                                size="small"
                                @change="updateIndicatorParams(item)"
                              />
                            </a-form-item>
                          </a-col>
                          <a-col :span="8">
                            <a-form-item label="慢线" size="small">
                              <a-input-number 
                                v-model:value="item.params[1]"
                                :min="20" 
                                :max="40"
                                size="small"
                                @change="updateIndicatorParams(item)"
                              />
                            </a-form-item>
                          </a-col>
                          <a-col :span="8">
                            <a-form-item label="信号线" size="small">
                              <a-input-number 
                                v-model:value="item.params[2]"
                                :min="5" 
                                :max="15"
                                size="small"
                                @change="updateIndicatorParams(item)"
                              />
                            </a-form-item>
                          </a-col>
                        </a-row>
                      </div>
                      
                      <div v-else-if="item.name === 'RSI'">
                        <a-form-item label="周期" size="small">
                          <a-slider 
                            v-model:value="item.params[0]" 
                            :min="6" 
                            :max="30" 
                            @change="updateIndicatorParams(item)"
                          />
                        </a-form-item>
                      </div>
                      
                      <div v-else-if="item.name === 'SuperTrend'">
                        <a-row :gutter="8">
                          <a-col :span="12">
                            <a-form-item label="周期" size="small">
                              <a-input-number 
                                v-model:value="item.params[0]"
                                :min="5" 
                                :max="20"
                                size="small"
                                @change="updateIndicatorParams(item)"
                              />
                            </a-form-item>
                          </a-col>
                          <a-col :span="12">
                            <a-form-item label="倍数" size="small">
                              <a-input-number 
                                v-model:value="item.params[1]"
                                :min="1.0" 
                                :max="5.0"
                                :step="0.1"
                                size="small"
                                @change="updateIndicatorParams(item)"
                              />
                            </a-form-item>
                          </a-col>
                        </a-row>
                      </div>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
        
        <!-- 预设配置 -->
        <div class="preset-section">
          <h4>预设配置</h4>
          <a-space wrap>
            <a-button 
              v-for="preset in presetConfigs"
              :key="preset.name"
              size="small"
              @click="applyPreset(preset)"
            >
              {{ preset.displayName }}
            </a-button>
          </a-space>
        </div>
        
        <!-- 重置按钮 -->
        <div class="reset-section">
          <a-button 
            type="primary" 
            danger 
            @click="resetAllIndicators"
            block
          >
            重置所有指标
          </a-button>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  LineChartOutlined,
  BarChartOutlined,
  StockOutlined,
  FundOutlined,
  AreaChartOutlined,
  RiseOutlined,
  SettingOutlined,
  DeleteOutlined,
  CameraOutlined
} from '@ant-design/icons-vue'

/**
 * 统一指标管理器组件
 * 提供类似OKE交易所的指标管理体验
 */
const props = defineProps({
  chartInstance: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['indicator-changed', 'settings-updated'])

// 响应式数据
const showSettings = ref(false)
const activeIndicators = ref([])
const updateTimer = ref(null)

// 可用指标定义
const availableIndicators = ref([
  {
    name: 'VOL',
    displayName: '成交量',
    icon: BarChartOutlined,
    type: 'volume',
    defaultParams: [5, 10],
    paneOptions: {
      id: 'volume_pane',
      height: 120,
      minHeight: 80,
      dragEnabled: true,
      gap: { top: 0.2, bottom: 0.1 }
    }
  },
  {
    name: 'MACD',
    displayName: 'MACD',
    icon: LineChartOutlined,
    type: 'oscillator',
    defaultParams: [12, 26, 9],
    paneOptions: {
      id: 'macd_pane',
      height: 120,
      minHeight: 80,
      dragEnabled: true,
      gap: { top: 0.2, bottom: 0.1 }
    }
  },
  {
    name: 'RSI',
    displayName: 'RSI',
    icon: StockOutlined,
    type: 'oscillator',
    defaultParams: [14],
    paneOptions: {
      id: 'rsi_pane',
      height: 100,
      minHeight: 60,
      dragEnabled: true,
      gap: { top: 0.2, bottom: 0.1 }
    }
  },
  {
    name: 'SuperTrend',
    displayName: '超级趋势',
    icon: RiseOutlined,
    type: 'overlay',
    defaultParams: [10, 3.0],
    overlayOnMain: true
  },
  {
    name: 'MA',
    displayName: '移动平均线',
    icon: LineChartOutlined,
    type: 'overlay',
    defaultParams: [20],
    overlayOnMain: true
  },
  {
    name: 'BOLL',
    displayName: '布林带',
    icon: LineChartOutlined,
    type: 'overlay',
    defaultParams: [20, 2],
    overlayOnMain: true
  },
  {
    name: 'SupportResistance',
    displayName: '支撑阻力位',
    icon: LineChartOutlined,
    type: 'overlay',
    defaultParams: [50, 2],
    overlayOnMain: true
  }
])

// 预设配置
const presetConfigs = ref([
  {
    name: 'basic',
    displayName: '基础配置',
    indicators: ['VOL', 'SuperTrend']
  },
  {
    name: 'advanced',
    displayName: '高级分析',
    indicators: ['VOL', 'MACD', 'RSI', 'SuperTrend']
  },
  {
    name: 'scalping',
    displayName: '短线交易',
    indicators: ['VOL', 'RSI', 'SuperTrend']
  },
  {
    name: 'trend',
    displayName: '趋势分析',
    indicators: ['MACD', 'SuperTrend']
  }
])

// 计算属性
const isIndicatorActive = (indicatorName) => {
  return activeIndicators.value.some(indicator => indicator.name === indicatorName)
}

// 指标颜色映射
const getIndicatorColor = (indicatorName) => {
  const colors = {
    VOL: 'blue',
    MACD: 'purple',
    RSI: 'orange',
    SuperTrend: 'green'
  }
  return colors[indicatorName] || 'default'
}

/**
 * 切换指标状态（添加/移除）
 * @param {string} indicatorName - 指标名称
 */
const toggleIndicator = async (indicatorName) => {
  if (!props.chartInstance) {
    message.error('图表实例未初始化')
    return
  }

  const isActive = isIndicatorActive(indicatorName)
  
  if (isActive) {
    await removeIndicator(indicatorName)
  } else {
    await addIndicator(indicatorName)
  }
}

/**
 * 添加指标
 * @param {string} indicatorName - 指标名称
 */
const addIndicator = async (indicatorName) => {
  try {
    // 检查指标是否已经存在
    if (isIndicatorActive(indicatorName)) {
      message.warning(`${indicatorName}指标已存在`)
      return
    }
    
    const indicatorDef = availableIndicators.value.find(ind => ind.name === indicatorName)
    if (!indicatorDef) {
      message.error(`未知的指标类型: ${indicatorName}`)
      return
    }

    let indicatorId
    
    // 特殊处理不同类型的指标
    if (indicatorName === 'VOL') {
      // 成交量指标添加到独立面板
      indicatorId = props.chartInstance.createIndicator('VOL', false, {
        ...indicatorDef.paneOptions,
        calcParams: indicatorDef.defaultParams
      })
    } else if (indicatorName === 'SuperTrend') {
      // 超级趋势指标添加到主面板
      indicatorId = props.chartInstance.createIndicator('SuperTrend', false, {
        id: 'candle_pane',
        calcParams: indicatorDef.defaultParams
      })
    } else if (indicatorName === 'MA') {
      // 移动平均线指标添加到主面板
      indicatorId = props.chartInstance.createIndicator('MA', false, {
        id: 'candle_pane',
        calcParams: indicatorDef.defaultParams
      })
    } else if (indicatorName === 'BOLL') {
      // 布林带指标添加到主面板
      indicatorId = props.chartInstance.createIndicator('BOLL', false, {
        id: 'candle_pane',
        calcParams: indicatorDef.defaultParams
      })
    } else if (indicatorName === 'SupportResistance') {
      // 支撑阻力位指标添加到主面板
      indicatorId = props.chartInstance.createIndicator('SupportResistanceTrend', false, {
        id: 'candle_pane',
        calcParams: indicatorDef.defaultParams
      })
    } else if (indicatorDef.paneOptions) {
      // 有独立面板配置的指标（MACD、RSI等）
      indicatorId = props.chartInstance.createIndicator(indicatorName, false, {
        ...indicatorDef.paneOptions,
        calcParams: indicatorDef.defaultParams
      })
    } else {
      // 其他指标添加到主面板
      indicatorId = props.chartInstance.createIndicator(indicatorName, false, {
        id: 'candle_pane',
        calcParams: indicatorDef.defaultParams
      })
    }

    if (indicatorId) {
      const indicator = {
        name: indicatorName,
        displayName: indicatorDef.displayName,
        params: [...(indicatorDef.defaultParams || [])],
        periods: indicatorDef.periods ? [...indicatorDef.periods] : undefined,
        paneHeight: indicatorDef.paneOptions?.height || 100,
        indicatorId,
        visible: true,
        type: indicatorDef.type
      }
      
      activeIndicators.value.push(indicator)
      emit('indicator-changed', { action: 'add', indicator })
      message.success(`${indicatorDef.displayName}添加成功`)
    } else {
      message.error(`${indicatorDef.displayName}添加失败`)
    }
  } catch (error) {
    console.error('添加指标失败:', error)
    message.error(`添加${indicatorName}指标失败: ${error.message}`)
  }
}

/**
 * 移除指标
 * @param {string} indicatorName - 指标名称
 */
const removeIndicator = async (indicatorName) => {
  try {
    const indicatorIndex = activeIndicators.value.findIndex(ind => ind.name === indicatorName)
    if (indicatorIndex === -1) {
      message.warning(`${indicatorName}指标不存在`)
      return
    }

    const indicator = activeIndicators.value[indicatorIndex]
    
    // 移除指标
    try {
      if (indicator.indicatorId) {
        props.chartInstance.removeIndicator(indicator.indicatorId)
      } else {
        // 兼容旧版本，尝试按面板ID移除
        const paneId = indicator.name === 'SuperTrend' ? 'candle_pane' : `${indicator.name.toLowerCase()}_pane`
        props.chartInstance.removeIndicator(paneId, indicator.name)
      }
    } catch (e) {
      console.warn(`移除${indicatorName}指标失败:`, e)
    }
    
    activeIndicators.value.splice(indicatorIndex, 1)
    emit('indicator-changed', { action: 'remove', indicator })
    message.success(`${indicator.displayName}移除成功`)
  } catch (error) {
    console.error('移除指标失败:', error)
    message.error(`移除${indicatorName}指标失败: ${error.message}`)
  }
}

/**
 * 切换指标可见性
 * @param {Object} indicator - 指标对象
 */
const toggleIndicatorVisibility = (indicator) => {
  try {
    // 这里可以调用图表实例的方法来切换指标可见性
    // props.chartInstance.setIndicatorVisible(indicator.indicatorId, indicator.visible)
    emit('indicator-changed', { action: 'visibility', indicator })
  } catch (error) {
    console.error('切换指标可见性失败:', error)
    message.error('切换指标可见性失败')
  }
}

/**
 * 更新指标参数
 * @param {Object} indicator - 指标对象
 */
const updateIndicatorParams = (indicator) => {
  if (updateTimer.value) {
    clearTimeout(updateTimer.value)
  }
  
  updateTimer.value = setTimeout(() => {
    updateIndicatorConfig(indicator)
  }, 2000) // 防抖处理
}

// updateVolumePane方法已移除，因为不再支持VOL指标

/**
 * 更新指标配置
 * @param {Object} indicator - 指标对象
 */
const updateIndicatorConfig = (indicator) => {
  try {
    // 重新创建指标以应用新配置
    const indicatorDef = availableIndicators.value.find(ind => ind.name === indicator.name)
    if (!indicatorDef) return
    
    // 先移除现有指标
    if (indicator.indicatorId) {
      props.chartInstance.removeIndicator(indicator.indicatorId)
    }
    
    // 重新创建指标
    let newIndicatorId
    if (indicator.name === 'VOL') {
      newIndicatorId = props.chartInstance.createIndicator('VOL', false, {
        ...indicatorDef.paneOptions,
        calcParams: indicator.params
      })
    } else if (indicator.name === 'SuperTrend') {
      newIndicatorId = props.chartInstance.createIndicator('SuperTrend', false, {
        id: 'candle_pane',
        calcParams: indicator.params
      })
    } else if (indicatorDef.paneOptions) {
      newIndicatorId = props.chartInstance.createIndicator(indicator.name, false, {
        ...indicatorDef.paneOptions,
        calcParams: indicator.params
      })
    }
    
    if (newIndicatorId) {
      indicator.indicatorId = newIndicatorId
      emit('indicator-changed', { action: 'update', indicator })
    }
  } catch (error) {
    console.error('更新指标配置失败:', error)
    message.error('更新指标配置失败')
  }
}

/**
 * 应用预设配置
 * @param {Object} preset - 预设配置
 */
const applyPreset = async (preset) => {
  try {
    // 先清除所有指标
    await resetAllIndicators()
    
    // 添加预设指标
    for (const indicatorName of preset.indicators) {
      await addIndicator(indicatorName)
    }
    
    message.success(`已应用${preset.displayName}配置`)
  } catch (error) {
    console.error('应用预设配置失败:', error)
    message.error('应用预设配置失败')
  }
}

/**
 * 重置所有指标
 */
const resetAllIndicators = async () => {
  try {
    const indicatorsToRemove = [...activeIndicators.value]
    
    for (const indicator of indicatorsToRemove) {
      await removeIndicator(indicator.name)
    }
    
    message.success('所有指标已重置')
  } catch (error) {
    console.error('重置指标失败:', error)
    message.error('重置指标失败')
  }
}

/**
 * 截图功能
 * @param {Object} param - 截图参数
 * @param {string} param.key - 截图类型
 */
const handleScreenshot = async ({ key }) => {
  try {
    let element
    let filename
    
    switch (key) {
      case 'chart':
        element = document.querySelector('.kline-chart-container, .chart-container, #chart')
        filename = `chart_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.png`
        break
      case 'indicators':
        element = document.querySelector('.unified-indicator-manager')
        filename = `indicators_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.png`
        break
      case 'full':
        element = document.body
        filename = `full_page_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.png`
        break
      default:
        return
    }
    
    if (!element) {
      message.warning('未找到要截图的元素')
      return
    }
    
    // 动态导入html2canvas
    const html2canvas = await import('html2canvas')
    
    // 显示加载提示
    const hide = message.loading('正在生成截图...', 0)
    
    try {
      const canvas = await html2canvas.default(element, {
        backgroundColor: null,
        scale: 2, // 提高清晰度
        useCORS: true,
        allowTaint: true,
        scrollX: 0,
        scrollY: 0,
        width: element.scrollWidth,
        height: element.scrollHeight
      })
      
      // 创建下载链接
      const link = document.createElement('a')
      link.download = filename
      link.href = canvas.toDataURL('image/png')
      
      // 触发下载
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      message.success('截图已保存')
    } finally {
      hide()
    }
  } catch (error) {
    console.error('截图失败:', error)
    message.error('截图失败，请重试')
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 不再默认添加任何指标，让用户手动选择
  console.log('UnifiedIndicatorManager mounted')
})

// 监听图表实例变化
watch(() => props.chartInstance, (newInstance) => {
  if (newInstance) {
    // 重新初始化指标列表，但不自动添加任何指标
    activeIndicators.value = []
    console.log('Chart instance changed, indicators cleared')
  }
})
</script>

<style scoped>
.unified-indicator-manager {
  width: 100%;
  position: relative;
}

/* 工具栏模式样式 */
.toolbar-indicator-manager .unified-indicator-manager {
  padding: 8px 12px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar-indicator-manager .indicator-toolbar {
  gap: 8px;
}

.toolbar-indicator-manager .indicator-buttons {
  gap: 6px;
}

.toolbar-indicator-manager .indicator-btn {
  height: 32px;
  padding: 0 12px;
  font-size: 12px;
  border-radius: 6px;
  min-width: auto;
}

.toolbar-indicator-manager .settings-btn,
.toolbar-indicator-manager .screenshot-btn {
  height: 32px;
  padding: 0 12px;
  font-size: 12px;
  border-radius: 6px;
}



.indicator-toolbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  margin-bottom: 0;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.indicator-toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.indicator-toolbar:hover::before {
  left: 100%;
}

.indicator-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  flex: 1;
}

.indicator-btn {
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 6px;
  font-weight: 600;
  min-width: 70px;
  height: 32px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
  overflow: hidden;
  font-size: 13px;
  letter-spacing: 0.5px;
}

.indicator-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.3s;
}

.indicator-btn:hover::before {
  left: 100%;
}

.indicator-btn.active {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border-color: #ff6b6b;
  color: white;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
  transform: translateY(-2px) scale(1.05);
}

.indicator-btn.active::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  to {
    width: 100px;
    height: 100px;
    opacity: 0;
  }
}

.indicator-btn:hover:not(.active) {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 30px rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.2);
}

.indicator-btn:active {
  transform: translateY(-1px) scale(0.98);
}

.settings-btn {
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  height: 32px;
  padding: 0 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  font-size: 12px;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.settings-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.3s;
}

.settings-btn:hover::before {
  left: 100%;
}

.settings-btn:hover {
  color: white;
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.screenshot-btn {
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  height: 32px;
  padding: 0 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  font-size: 12px;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.screenshot-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.3s;
}

.screenshot-btn:hover::before {
  left: 100%;
}

.screenshot-btn:hover {
  color: white;
  border-color: rgba(255, 255, 255, 0.4);
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(82, 196, 26, 0.3);
}

.settings-content {
  padding: 20px 0;
}

.active-indicators-section,
.preset-section,
.reset-section {
  margin-bottom: 32px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.active-indicators-section::before,
.preset-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 2px 2px 0;
}

.active-indicators-section h4,
.preset-section h4 {
  margin-bottom: 16px;
  color: #2c3e50;
  font-weight: 700;
  font-size: 16px;
  letter-spacing: 0.5px;
  position: relative;
  padding-left: 12px;
}

.active-indicators-section h4::after,
.preset-section h4::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 12px;
  width: 30px;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 1px;
}

.indicator-params {
  margin-top: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.indicator-params .ant-form-item {
  margin-bottom: 12px;
}

.indicator-params .ant-input-number,
.indicator-params .ant-slider {
  transition: all 0.3s ease;
}

.indicator-params .ant-input-number:hover,
.indicator-params .ant-input-number:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 列表项动画 */
.ant-list-item {
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.ant-list-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

/* 预设按钮样式 */
.preset-section .ant-btn {
  margin: 4px;
  border-radius: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid #667eea;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  font-weight: 600;
}

.preset-section .ant-btn:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* 深色主题适配 */
.unified-indicator-manager.dark {
  background: linear-gradient(135deg, #2a2d3a 0%, #1e1e2e 100%);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* 工具栏模式深色主题 */
.toolbar-indicator-manager .unified-indicator-manager.dark {
  background: linear-gradient(135deg, #2a2d3a 0%, #1e1e2e 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.dark-theme .indicator-toolbar {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

.dark-theme .indicator-btn {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: #ecf0f1;
}

.dark-theme .indicator-btn.active {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  border-color: #e74c3c;
}

.dark-theme .settings-btn {
  color: #ecf0f1;
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .screenshot-btn {
  color: #ecf0f1;
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .screenshot-btn:hover {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  border-color: #27ae60;
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.dark-theme .active-indicators-section,
.dark-theme .preset-section {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .active-indicators-section h4,
.dark-theme .preset-section h4 {
  color: #ecf0f1;
}

.dark-theme .indicator-params {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .ant-list-item {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .ant-list-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .indicator-toolbar {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }
  
  .indicator-buttons {
    justify-content: center;
    width: 100%;
  }
  
  .indicator-btn {
    min-width: 70px;
    height: 36px;
    font-size: 12px;
  }
  
  .settings-btn {
    height: 36px;
    padding: 0 12px;
    font-size: 12px;
  }
  
  .active-indicators-section,
  .preset-section {
    padding: 16px;
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .indicator-buttons {
    gap: 8px;
  }
  
  .indicator-btn {
    min-width: 60px;
    height: 32px;
    font-size: 11px;
    gap: 4px;
  }
  
  .settings-btn {
    height: 32px;
    padding: 0 10px;
    font-size: 11px;
  }
}
</style>