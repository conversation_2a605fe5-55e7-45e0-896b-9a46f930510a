<template>
  <div class="volume-chart-container">
    <!-- 图表控制面板 -->
    <div class="chart-controls" v-if="showControls">
      <div class="control-group">
        <label>成交量指标:</label>
        <a-select 
           v-model:value="selectedIndicator" 
           size="small" 
           style="width: 120px"
           @change="handleIndicatorChange"
         >
           <a-select-option value="volume">成交量</a-select-option>
           <a-select-option value="obv">OBV</a-select-option>
           <a-select-option value="vma">成交量MA</a-select-option>
           <a-select-option value="vwap">VWAP</a-select-option>
           <a-select-option value="volume_ratio">成交量比率</a-select-option>
           <a-select-option value="volume_oscillator">成交量震荡器</a-select-option>
           <a-select-option value="ad_line">A/D线</a-select-option>
           <a-select-option value="vpt">VPT</a-select-option>
         </a-select>
      </div>
      
      <div class="control-group" v-if="needsPeriodSetting">
         <label>{{ periodLabel }}:</label>
         <a-input-number 
           v-model:value="currentPeriod" 
           :min="5" 
           :max="200" 
           size="small" 
           style="width: 80px"
           @change="handleParameterChange"
         />
       </div>
       
       <div class="control-group" v-if="selectedIndicator === 'volume_oscillator'">
         <label>长周期:</label>
         <a-input-number 
           v-model:value="longPeriod" 
           :min="10" 
           :max="200" 
           size="small" 
           style="width: 80px"
           @change="handleParameterChange"
         />
       </div>
       
       <div class="control-group">
         <label>预设:</label>
         <a-select 
           v-model:value="selectedPreset" 
           size="small" 
           style="width: 100px"
           @change="handlePresetChange"
         >
           <a-select-option value="custom">自定义</a-select-option>
           <a-select-option value="short">短期</a-select-option>
           <a-select-option value="medium">中期</a-select-option>
           <a-select-option value="long">长期</a-select-option>
         </a-select>
       </div>
      
      <div class="control-group">
        <a-checkbox 
          v-model:checked="showVolumeProfile" 
          @change="handleVolumeProfileToggle"
        >
          成交量分布
        </a-checkbox>
      </div>
      
      <div class="control-group">
        <a-tooltip title="刷新数据">
          <a-button 
            type="text" 
            size="small" 
            :loading="isLoading" 
            @click="refreshChart"
          >
            <template #icon>
              <ReloadOutlined />
            </template>
          </a-button>
        </a-tooltip>
      </div>
    </div>

    <!-- 主图表区域 -->
    <div 
      :id="chartId" 
      :style="{ height: height, width: '100%' }"
      class="volume-chart"
      @contextmenu="handleContextMenu"
    ></div>
    
    <!-- 成交量统计信息 -->
    <div class="volume-stats" v-if="showStats && volumeStats">
      <div class="stat-item">
        <span class="label">总量:</span>
        <span class="value">{{ formatVolume(volumeStats.total) }}</span>
      </div>
      <div class="stat-item">
        <span class="label">平均:</span>
        <span class="value">{{ formatVolume(volumeStats.average) }}</span>
      </div>
      <div class="stat-item">
        <span class="label">最大:</span>
        <span class="value">{{ formatVolume(volumeStats.max) }}</span>
      </div>
      <div class="stat-item">
        <span class="label">买入比:</span>
        <span class="value" :class="{ 'positive': volumeStats.buyRatio > 50 }">
          {{ volumeStats.buyRatio.toFixed(1) }}%
        </span>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
      <ExclamationCircleOutlined />
      {{ error }}
    </div>
    
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <a-spin size="large" />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { Select as ASelect, InputNumber as AInputNumber, Checkbox as ACheckbox, Button as AButton, Tooltip as ATooltip, Spin as ASpin } from 'ant-design-vue'
import { ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { useChartCommon } from '@/composables/useChartCommon'
import { useVolumeIndicators, VOLUME_INDICATORS, VOLUME_PRESETS } from '@/composables/useVolumeIndicators'
import { CHART_DATA_INDEX } from '@/constants/chartConstants'

// Props
const props = defineProps({
  /** 图表数据数组 */
  chartData: {
    type: Array,
    default: () => []
  },
  /** 图表高度 */
  height: {
    type: String,
    default: '200px'
  },
  /** 是否为深色主题 */
  isDarkTheme: {
    type: Boolean,
    default: false
  },
  /** 图表容器ID */
  chartId: {
    type: String,
    default: () => `volume-chart-${Date.now()}`
  },
  /** 是否显示控制面板 */
  showControls: {
    type: Boolean,
    default: true
  },
  /** 是否显示统计信息 */
  showStats: {
    type: Boolean,
    default: true
  },
  /** 成交量数据精度 */
  volumePrecision: {
    type: Number,
    default: 2
  }
})

// Emits
const emit = defineEmits([
  'chartReady',
  'chartError', 
  'volumeClick',
  'indicatorChange',
  'contextMenu'
])

// 使用通用图表逻辑
const {
  chartInstance,
  isLoading,
  error,
  themeConfig,
  initChart,
  updateChart,
  resizeChart,
  disposeChart,
  getBaseChartOption,
  formatChartData
} = useChartCommon({ isDarkTheme: props.isDarkTheme })

// 使用成交量指标工具
const {
  calculateOBV,
  calculateVMA,
  calculateVWAP,
  calculateVolumeRatio,
  calculateVolumeOscillator,
  calculateADLine,
  calculateVPT,
  calculateVolumeProfile,
  calculateVolumeStats,
  formatVolume: formatVolumeValue,
  detectVolumeAnomalies
} = useVolumeIndicators()

// 响应式数据
const selectedIndicator = ref('volume')
const currentPeriod = ref(20)
const longPeriod = ref(50)
const selectedPreset = ref('custom')
const showVolumeProfile = ref(false)
const volumeStats = ref(null)
const volumeAnomalies = ref([])

// 计算属性
const needsPeriodSetting = computed(() => {
  return ['vma', 'vwap', 'volume_ratio'].includes(selectedIndicator.value)
})

const periodLabel = computed(() => {
  switch (selectedIndicator.value) {
    case 'vma': return 'MA周期'
    case 'vwap': return 'VWAP周期'
    case 'volume_ratio': return '比率周期'
    case 'volume_oscillator': return '短周期'
    default: return '周期'
  }
})

/**
 * 更新成交量统计信息和异常检测
 */
const updateVolumeAnalysis = () => {
  if (!props.chartData || props.chartData.length === 0) {
    volumeStats.value = null
    volumeAnomalies.value = []
    return
  }

  // 计算统计信息
  volumeStats.value = calculateVolumeStats(props.chartData)
  
  // 检测异常成交量
  volumeAnomalies.value = detectVolumeAnomalies(props.chartData, 2)
}



/**
 * 格式化成交量显示（包装函数）
 * @param {number} value - 成交量值
 * @returns {string} 格式化后的字符串
 */
const formatVolume = (value) => {
  return formatVolumeValue(value, props.volumePrecision)
}

/**
 * 初始化成交量图表
 */
const initVolumeChart = async () => {
  await nextTick()
  
  if (!props.chartData || props.chartData.length === 0) {
    console.warn('成交量图表数据为空')
    return
  }

  try {
    const theme = themeConfig.value
    const baseOption = getBaseChartOption('成交量分析', {
      yAxis: {
        type: 'value',
        position: 'right',
        scale: true,
        min: 0,
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: theme.textColor,
          fontSize: 10,
          formatter: (value) => formatVolume(value)
        },
        splitLine: {
          show: true,
          lineStyle: { color: theme.gridColor }
        }
      }
    })

    // 根据选择的指标生成不同的图表配置
    const volumeOption = generateChartOption(baseOption)
    
    const chart = initChart(props.chartId, volumeOption)
    if (chart) {
      // 添加点击事件
      chart.on('click', handleChartClick)
      emit('chartReady', chart)
    } else {
      emit('chartError', error.value)
    }
    
    // 计算统计信息和异常检测
    updateVolumeAnalysis()
  } catch (err) {
    console.error('初始化成交量图表失败:', err)
    emit('chartError', err.message)
  }
}

/**
 * 生成图表配置选项
 * @param {Object} baseOption - 基础配置
 * @returns {Object} 完整的图表配置
 */
const generateChartOption = (baseOption) => {
  const theme = themeConfig.value
  const timeAxis = formatChartData.timeAxis(props.chartData)
  
  const option = {
    ...baseOption,
    xAxis: {
      ...baseOption.xAxis,
      data: timeAxis
    },
    series: []
  }
  
  // 根据选择的指标添加不同的系列
  switch (selectedIndicator.value) {
    case 'volume':
      option.series.push({
        name: '成交量',
        type: 'bar',
        barWidth: '60%',
        data: formatChartData.volume(props.chartData),
        itemStyle: {
          color: (params) => {
            const index = params.dataIndex
            if (index < props.chartData.length) {
              const open = parseFloat(props.chartData[index][CHART_DATA_INDEX.OPEN])
              const close = parseFloat(props.chartData[index][CHART_DATA_INDEX.CLOSE])
              return close >= open ? theme.volume.up : theme.volume.down
            }
            return theme.volume.up
          }
        },
        emphasis: {
          itemStyle: { opacity: 0.8 }
        }
      })
      break
      
    case 'obv':
      const obvData = calculateOBV(props.chartData)
      option.series.push({
        name: 'OBV',
        type: 'line',
        data: obvData,
        lineStyle: {
          color: theme.indicators.rsi,
          width: 2
        },
        symbol: 'none'
      })
      break
      
    case 'vma':
       const volumeData = formatChartData.volume(props.chartData)
       const vmaData = calculateVMA(props.chartData, currentPeriod.value)
       
       option.series.push(
         {
           name: '成交量',
           type: 'bar',
           barWidth: '60%',
           data: volumeData,
           itemStyle: {
             color: (params) => {
               const index = params.dataIndex
               if (index < props.chartData.length) {
                 const open = parseFloat(props.chartData[index][CHART_DATA_INDEX.OPEN])
                 const close = parseFloat(props.chartData[index][CHART_DATA_INDEX.CLOSE])
                 return close >= open ? theme.volume.up : theme.volume.down
               }
               return theme.volume.up
             },
             opacity: 0.7
           }
         },
         {
           name: `VMA${currentPeriod.value}`,
           type: 'line',
           data: vmaData,
           lineStyle: {
             color: theme.ma.ma20,
             width: 2
           },
           symbol: 'none'
         }
       )
       break
       
     case 'vwap':
       const vwapData = calculateVWAP(props.chartData, currentPeriod.value)
       option.yAxis.scale = false // VWAP使用价格刻度
       option.series.push({
         name: 'VWAP',
         type: 'line',
         data: vwapData,
         lineStyle: {
           color: theme.indicators.macd,
           width: 2
         },
         symbol: 'none'
       })
       break
       
     case 'volume_ratio':
       const ratioData = calculateVolumeRatio(props.chartData, currentPeriod.value)
       option.series.push({
         name: '成交量比率',
         type: 'line',
         data: ratioData,
         lineStyle: {
           color: theme.indicators.rsi,
           width: 2
         },
         symbol: 'none',
         markLine: {
           data: [
             { yAxis: 1, lineStyle: { color: '#999', type: 'dashed' } },
             { yAxis: 2, lineStyle: { color: '#ff4d4f', type: 'dashed' } }
           ]
         }
       })
       break
       
     case 'volume_oscillator':
       const oscillatorData = calculateVolumeOscillator(props.chartData, currentPeriod.value, longPeriod.value)
       option.series.push({
         name: '成交量震荡器',
         type: 'line',
         data: oscillatorData,
         lineStyle: {
           color: theme.indicators.macd,
           width: 2
         },
         symbol: 'none',
         markLine: {
           data: [
             { yAxis: 0, lineStyle: { color: '#999', type: 'dashed' } }
           ]
         }
       })
       break
       
     case 'ad_line':
       const adData = calculateADLine(props.chartData)
       option.series.push({
         name: 'A/D线',
         type: 'line',
         data: adData,
         lineStyle: {
           color: theme.indicators.kdj.k,
           width: 2
         },
         symbol: 'none'
       })
       break
       
     case 'vpt':
       const vptData = calculateVPT(props.chartData)
       option.series.push({
         name: 'VPT',
         type: 'line',
         data: vptData,
         lineStyle: {
           color: theme.indicators.kdj.d,
           width: 2
         },
         symbol: 'none'
       })
       break
  }
  
  return option
}

/**
 * 更新图表数据
 */
const updateVolumeChart = () => {
  if (!chartInstance.value || !props.chartData || props.chartData.length === 0) {
    return
  }

  try {
    const baseOption = getBaseChartOption('成交量分析')
    const updateOption = generateChartOption(baseOption)
    updateChart(updateOption)
    updateVolumeAnalysis()
  } catch (err) {
    console.error('更新成交量图表失败:', err)
    emit('chartError', err.message)
  }
}

/**
 * 处理指标变化
 */
const handleIndicatorChange = () => {
  updateVolumeChart()
  emit('indicatorChange', selectedIndicator.value)
}

/**
 * 处理参数变化
 */
const handleParameterChange = () => {
  updateVolumeChart()
}

/**
 * 处理预设配置变化
 */
const handlePresetChange = (presetValue) => {
  selectedPreset.value = presetValue;
  if (presetValue === 'custom') return;

  const config = VOLUME_PRESETS[presetValue.toUpperCase()];
  if (config) {
    currentPeriod.value = config.period;
    longPeriod.value = config.longPeriod || config.period * 2;
    updateVolumeChart();
  }
};

/**
 * 处理成交量分布切换
 */
const handleVolumeProfileToggle = () => {
  // TODO: 实现成交量分布功能
  console.log('成交量分布功能待实现')
}

/**
 * 刷新图表
 */
const refreshChart = () => {
  if (props.chartData && props.chartData.length > 0) {
    initVolumeChart()
  }
}

/**
 * 处理图表点击事件
 * @param {Object} params - 点击参数
 */
const handleChartClick = (params) => {
  if (params.componentType === 'series') {
    const dataIndex = params.dataIndex
    const volumeData = {
      index: dataIndex,
      volume: parseFloat(props.chartData[dataIndex][CHART_DATA_INDEX.VOLUME]),
      timestamp: props.chartData[dataIndex][CHART_DATA_INDEX.DATETIME]
    }
    emit('volumeClick', volumeData)
  }
}

/**
 * 处理右键菜单
 * @param {Event} event - 右键事件
 */
const handleContextMenu = (event) => {
  event.preventDefault()
  emit('contextMenu', {
    x: event.clientX,
    y: event.clientY,
    chartType: 'volume'
  })
}

// 监听数据变化
watch(
  () => props.chartData,
  (newData) => {
    if (newData && newData.length > 0) {
      if (chartInstance.value) {
        updateVolumeChart()
      } else {
        initVolumeChart()
      }
    }
  },
  { deep: true }
)

// 监听主题变化
watch(
  () => props.isDarkTheme,
  () => {
    if (chartInstance.value && props.chartData && props.chartData.length > 0) {
      initVolumeChart()
    }
  }
)

// 窗口大小变化时调整图表
const handleResize = () => {
  resizeChart()
}

// 组件挂载
onMounted(() => {
  if (props.chartData && props.chartData.length > 0) {
    initVolumeChart()
  }
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  disposeChart()
})

// 暴露方法给父组件
defineExpose({
  initChart: initVolumeChart,
  updateChart: updateVolumeChart,
  resizeChart,
  disposeChart,
  chartInstance,
  refreshChart,
  setIndicator: (indicator) => {
    selectedIndicator.value = indicator
    handleIndicatorChange()
  },
  getVolumeStats: () => volumeStats.value,
  getVolumeAnomalies: () => volumeAnomalies.value,
  setPreset: handlePresetChange,
  setPeriod: (period) => {
    currentPeriod.value = period
    updateVolumeChart()
  },
  setLongPeriod: (period) => {
    longPeriod.value = period
    updateVolumeChart()
  }
})
</script>

<style scoped>
.volume-chart-container {
  position: relative;
  width: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 12px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  font-size: 12px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 6px;
}

.control-group label {
  color: #666;
  white-space: nowrap;
}

.volume-chart {
  width: 100%;
  position: relative;
}

.volume-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 12px;
  background: #f9f9f9;
  border-top: 1px solid #e8e8e8;
  font-size: 11px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-item .label {
  color: #666;
}

.stat-item .value {
  font-weight: 500;
  color: #333;
}

.stat-item .value.positive {
  color: #52c41a;
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ff4d4f;
  font-size: 14px;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 12px 20px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 10;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.volume-chart-container:hover .volume-chart {
  cursor: crosshair;
}

/* 深色主题样式 */
.volume-chart-container[data-theme="dark"] {
  border-color: #434651;
}

.volume-chart-container[data-theme="dark"] .chart-controls {
  background: #2a2e39;
  border-bottom-color: #434651;
}

.volume-chart-container[data-theme="dark"] .control-group label {
  color: #bbb;
}

.volume-chart-container[data-theme="dark"] .volume-stats {
  background: #2a2e39;
  border-top-color: #434651;
}

.volume-chart-container[data-theme="dark"] .stat-item .label {
  color: #bbb;
}

.volume-chart-container[data-theme="dark"] .stat-item .value {
  color: #fff;
}

.volume-chart-container[data-theme="dark"] .error-message {
  background: rgba(30, 34, 45, 0.95);
  color: #ff7875;
}

.volume-chart-container[data-theme="dark"] .loading-overlay {
  background: rgba(30, 34, 45, 0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-controls {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .volume-stats {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .control-group {
    gap: 4px;
  }
}
</style>