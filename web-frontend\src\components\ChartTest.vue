<template>
  <div class="chart-test">
    <h2>图表测试页面</h2>
    <div class="debug-info">
      <h3>调试信息:</h3>
      <div v-for="(log, index) in debugLogs" :key="index" class="log-entry">
        {{ log }}
      </div>
    </div>
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'ChartTest',
  setup() {
    const chartContainer = ref(null)
    const debugLogs = ref([])
    
    const addLog = (message) => {
      const timestamp = new Date().toLocaleTimeString()
      const logMessage = `${timestamp}: ${message}`
      console.log(logMessage)
      debugLogs.value.push(logMessage)
    }
    
    const initTestChart = async () => {
      try {
        addLog('开始初始化图表...')
        
        // 检查容器
        if (!chartContainer.value) {
          addLog('错误: 图表容器未找到')
          return
        }
        addLog('图表容器已找到')
        
        // 检查ECharts
        if (typeof echarts === 'undefined') {
          addLog('错误: ECharts 未加载')
          return
        }
        addLog('ECharts 库已加载')
        
        // 初始化图表
        const chart = echarts.init(chartContainer.value)
        addLog('图表实例创建成功')
        
        // 配置选项
        const option = {
          title: {
            text: 'Vue 测试图表'
          },
          tooltip: {},
          xAxis: {
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
          },
          yAxis: {},
          series: [{
            name: '数据',
            type: 'bar',
            data: [120, 200, 150, 80, 70, 110, 130]
          }]
        }
        
        addLog('图表配置准备完成')
        
        // 设置选项
        chart.setOption(option)
        addLog('图表渲染完成!')
        
      } catch (error) {
        addLog(`错误: ${error.message}`)
        console.error('图表初始化错误:', error)
      }
    }
    
    onMounted(async () => {
      addLog('组件已挂载')
      await nextTick()
      addLog('DOM 更新完成')
      initTestChart()
    })
    
    return {
      chartContainer,
      debugLogs
    }
  }
}
</script>

<style scoped>
.chart-test {
  padding: 20px;
}

.debug-info {
  background: #f5f5f5;
  padding: 15px;
  margin: 20px 0;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.log-entry {
  font-family: monospace;
  font-size: 12px;
  margin: 2px 0;
  padding: 2px 0;
  border-bottom: 1px solid #eee;
}

.chart-container {
  width: 800px;
  height: 400px;
  border: 1px solid #ddd;
  margin: 20px 0;
}
</style>