<template>
  <div class="okx-leverage">
    <h2>OKX杠杆设置</h2>
    <a-form layout="inline" :model="query" @submit.prevent style="margin-bottom:16px">
      <a-form-item label="产品ID">
        <a-input v-model="query.instId" placeholder="如 BTC-USDT-SWAP" style="width:180px" />
      </a-form-item>
      <a-form-item label="保证金模式">
        <a-select v-model="query.mgnMode" style="width:120px" allow-clear>
          <a-select-option value="cross">全仓</a-select-option>
          <a-select-option value="isolated">逐仓</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="fetchLeverage" :loading="loading">查询</a-button>
      </a-form-item>
    </a-form>

    <a-table :dataSource="leverageList" :columns="columns" :loading="loading">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-button type="link" @click="showSetLeverageModal(record)">设置杠杆</a-button>
        </template>
        <template v-else>
          {{ record[column.dataIndex] }}
        </template>
      </template>
    </a-table>

    <a-modal
      title="设置杠杆倍数"
      :open="modalVisible"
      @update:open="modalVisible = $event"
      @ok="handleSetLeverage"
      :confirmLoading="confirmLoading"
    >
      <a-form :model="form">
        <a-form-item label="产品ID">
          <span>{{ form.instId }}</span>
        </a-form-item>
        <a-form-item label="保证金模式">
          <span>{{ form.mgnMode === 'cross' ? '全仓' : '逐仓' }}</span>
        </a-form-item>
        <a-form-item label="杠杆倍数">
          <a-input-number v-model="form.lever" :min="1" :max="125" style="width:200px" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const confirmLoading = ref(false)
const leverageList = ref([])

const query = reactive({
  instId: '',
  mgnMode: undefined
})

const form = reactive({
  instId: '',
  mgnMode: '',
  lever: 1
})

const columns = [
  { title: '产品ID', dataIndex: 'instId', key: 'instId' },
  { title: '保证金模式', dataIndex: 'mgnMode', key: 'mgnMode' },
  { title: '杠杆倍数', dataIndex: 'lever', key: 'lever' },
  { title: '操作', key: 'action' }
]

function fetchLeverage() {
  loading.value = true
  const params = {}
  if (query.instId) params.instId = query.instId
  if (query.mgnMode) params.mgnMode = query.mgnMode
  fetch('/api/okx/leverage?' + new URLSearchParams(params))
    .then(res => res.json())
    .then(data => {
      if (data.code === 0) {
        leverageList.value = data.data
      } else {
        message.error(data.msg || '查询失败')
      }
    })
    .catch(err => {
      console.error(err)
      message.error('查询失败')
    })
    .finally(() => {
      loading.value = false
    })
}

function showSetLeverageModal(record) {
  form.instId = record.instId
  form.mgnMode = record.mgnMode
  form.lever = record.lever
  modalVisible.value = true
}

function handleSetLeverage() {
  confirmLoading.value = true
  fetch('/api/okx/leverage', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(form)
  })
    .then(res => res.json())
    .then(data => {
      if (data.code === 0) {
        message.success('设置成功')
        modalVisible.value = false
        fetchLeverage()
      } else {
        message.error(data.msg || '设置失败')
      }
    })
    .catch(err => {
      console.error(err)
      message.error('设置失败')
    })
    .finally(() => {
      confirmLoading.value = false
    })
}
</script>

<style scoped>
.okx-leverage {
  max-width: 900px;
  margin: 0 auto;
  padding: 24px;
}
</style>