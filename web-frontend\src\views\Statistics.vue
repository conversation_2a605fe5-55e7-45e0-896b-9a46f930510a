<template>
  <div class="statistics">
    <a-row :gutter="16">
      <!-- 用户统计概览 -->
      <a-col :span="24">
        <a-card title="账户概览" :loading="loading">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic
                title="总交易次数"
                :value="userStats.total_trades || 0"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="盈利交易"
                :value="userStats.profitable_trades || 0"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="胜率"
                :value="userStats.win_rate || 0"
                suffix="%"
                :precision="2"
                :value-style="{ color: getWinRateColor(userStats.win_rate) }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="总盈亏"
                :value="userStats.total_pnl || 0"
                :precision="2"
                suffix="USDT"
                :value-style="{ color: getPnlColor(userStats.total_pnl) }"
              />
            </a-col>
          </a-row>
          
          <a-divider />
          
          <a-row :gutter="16">
            <a-col :span="8">
              <a-statistic
                title="最大回撤"
                :value="userStats.max_drawdown || 0"
                :precision="2"
                suffix="%"
                :value-style="{ color: '#ff4d4f' }"
              />
            </a-col>
            <a-col :span="8">
              <a-statistic
                title="总交易量"
                :value="userStats.total_volume || 0"
                :precision="2"
                suffix="USDT"
                :value-style="{ color: '#722ed1' }"
              />
            </a-col>
            <a-col :span="8">
              <a-statistic
                title="平均收益率"
                :value="getAverageReturn()"
                :precision="2"
                suffix="%"
                :value-style="{ color: getPnlColor(getAverageReturn()) }"
              />
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>

    <!-- 每日统计 -->
    <a-row :gutter="16" style="margin-top: 16px;">
      <a-col :span="24">
        <a-card title="每日统计" :loading="dailyLoading">
          <div style="margin-bottom: 16px;">
            <a-space>
              <a-button @click="loadDailyStats" :loading="dailyLoading">
                <template #icon><ReloadOutlined /></template>
                刷新数据
              </a-button>
              <a-select 
                v-model:value="selectedDays" 
                @change="loadDailyStats"
                style="width: 120px;"
              >
                <a-select-option value="7">最近7天</a-select-option>
                <a-select-option value="30">最近30天</a-select-option>
                <a-select-option value="90">最近90天</a-select-option>
              </a-select>
            </a-space>
          </div>
          
          <a-table 
            :columns="dailyColumns" 
            :data-source="dailyStats" 
            :pagination="false"
            size="small"
            :scroll="{ x: 800 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'pnl'">
                <span :style="{ color: getPnlColor(record.total_pnl) }">
                  {{ formatNumber(record.total_pnl, 2) }} USDT
                </span>
              </template>
              <template v-else-if="column.key === 'win_rate'">
                <span :style="{ color: getWinRateColor(record.win_rate) }">
                  {{ formatNumber(record.win_rate, 2) }}%
                </span>
              </template>
              <template v-else-if="column.key === 'volume'">
                <span>{{ formatNumber(record.total_volume, 2) }} USDT</span>
              </template>
              <template v-else-if="column.key === 'drawdown'">
                <span style="color: #ff4d4f;">{{ formatNumber(record.max_drawdown, 2) }}%</span>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>

    <!-- 交易分析图表 -->
    <a-row :gutter="16" style="margin-top: 16px;">
      <a-col :span="12">
        <a-card title="盈亏分布" :loading="loading">
          <div id="pnl-chart" style="height: 300px;"></div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="交易频率" :loading="loading">
          <div id="frequency-chart" style="height: 300px;"></div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import { getUserStats, getDailyStats } from '../services/apiService'

const loading = ref(false)
const dailyLoading = ref(false)
const selectedDays = ref('30')

const userStats = ref({})
const dailyStats = ref([])

// 每日统计表格列定义
const dailyColumns = [
  {
    title: '日期',
    dataIndex: 'date',
    key: 'date',
    width: 120,
  },
  {
    title: '交易次数',
    dataIndex: 'total_trades',
    key: 'trades',
    width: 100,
  },
  {
    title: '盈利交易',
    dataIndex: 'profitable_trades',
    key: 'profitable',
    width: 100,
  },
  {
    title: '胜率',
    key: 'win_rate',
    width: 80,
  },
  {
    title: '总盈亏',
    key: 'pnl',
    width: 120,
  },
  {
    title: '交易量',
    key: 'volume',
    width: 120,
  },
  {
    title: '最大回撤',
    key: 'drawdown',
    width: 100,
  },
]

// 加载用户统计
const loadUserStats = async () => {
  loading.value = true
  try {
    const stats = await getUserStats()
    userStats.value = stats || {}
  } catch (error) {
    message.error('加载统计数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 加载每日统计
const loadDailyStats = async () => {
  dailyLoading.value = true
  try {
    const stats = await getDailyStats(parseInt(selectedDays.value))
    dailyStats.value = (stats || []).map((item, index) => ({
      ...item,
      key: index,
      date: new Date(item.date).toLocaleDateString('zh-CN')
    }))
  } catch (error) {
    message.error('加载每日统计失败: ' + error.message)
  } finally {
    dailyLoading.value = false
  }
}

// 工具函数
const getPnlColor = (value) => {
  if (!value) return '#666'
  return value >= 0 ? '#52c41a' : '#ff4d4f'
}

const getWinRateColor = (rate) => {
  if (!rate) return '#666'
  if (rate >= 60) return '#52c41a'
  if (rate >= 40) return '#faad14'
  return '#ff4d4f'
}

const getAverageReturn = () => {
  if (!userStats.value.total_trades || userStats.value.total_trades === 0) return 0
  return (userStats.value.total_pnl || 0) / userStats.value.total_trades
}

const formatNumber = (value, precision = 2) => {
  if (value === null || value === undefined) return '0'
  return Number(value).toFixed(precision)
}

// 初始化图表（简化版，实际项目中可以使用 ECharts 等图表库）
const initCharts = async () => {
  await nextTick()
  
  // 这里可以集成 ECharts 或其他图表库
  // 暂时显示占位文本
  const pnlChart = document.getElementById('pnl-chart')
  const frequencyChart = document.getElementById('frequency-chart')
  
  if (pnlChart) {
    pnlChart.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">盈亏分布图表<br/>（可集成 ECharts）</div>'
  }
  
  if (frequencyChart) {
    frequencyChart.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">交易频率图表<br/>（可集成 ECharts）</div>'
  }
}

onMounted(async () => {
  await Promise.all([
    loadUserStats(),
    loadDailyStats()
  ])
  initCharts()
})
</script>

<style scoped>
.statistics {
  padding: 24px;
}

.ant-statistic {
  text-align: center;
}

.ant-divider {
  margin: 16px 0;
}

#pnl-chart,
#frequency-chart {
  background: #fafafa;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1.5;
}
</style>