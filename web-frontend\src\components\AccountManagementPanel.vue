<template>
  <div class="account-management-panel" :class="{ 'dark-theme': isDarkTheme }">
    <a-card title="账户管理" size="small" :bordered="false">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新数据">
            <a-button size="small" type="text" @click="refreshData">
              <ReloadOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="账户设置">
            <a-button size="small" type="text" @click="showSettings = true">
              <SettingOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="导出报告">
            <a-button size="small" type="primary" @click="exportReport">
              <ExportOutlined /> 导出
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <div class="account-container">
        <!-- 账户概览 -->
        <div class="account-overview">
          <div class="overview-cards">
            <div class="account-card">
              <div class="card-header">
                <h4>总资产</h4>
                <a-tooltip title="包含现货和合约账户">
                  <InfoCircleOutlined class="info-icon" />
                </a-tooltip>
              </div>
              <div class="card-value">
                <span class="amount">{{ formatCurrency(totalAssets) }}</span>
                <span class="change" :class="totalAssetsChange >= 0 ? 'positive' : 'negative'">
                  {{ formatPercentage(totalAssetsChange) }}
                </span>
              </div>
            </div>
            
            <div class="account-card">
              <div class="card-header">
                <h4>可用余额</h4>
                <a-tooltip title="可用于交易的余额">
                  <InfoCircleOutlined class="info-icon" />
                </a-tooltip>
              </div>
              <div class="card-value">
                <span class="amount">{{ formatCurrency(availableBalance) }}</span>
                <span class="ratio">{{ formatPercentage(availableBalance / totalAssets * 100) }}</span>
              </div>
            </div>
            
            <div class="account-card">
              <div class="card-header">
                <h4>冻结资金</h4>
                <a-tooltip title="订单占用和保证金">
                  <InfoCircleOutlined class="info-icon" />
                </a-tooltip>
              </div>
              <div class="card-value">
                <span class="amount">{{ formatCurrency(frozenBalance) }}</span>
                <span class="ratio">{{ formatPercentage(frozenBalance / totalAssets * 100) }}</span>
              </div>
            </div>
            
            <div class="account-card">
              <div class="card-header">
                <h4>今日盈亏</h4>
                <a-tooltip title="今日交易盈亏">
                  <InfoCircleOutlined class="info-icon" />
                </a-tooltip>
              </div>
              <div class="card-value">
                <span class="amount" :class="todayPnl >= 0 ? 'positive' : 'negative'">
                  {{ formatCurrency(todayPnl) }}
                </span>
                <span class="ratio" :class="todayPnlRatio >= 0 ? 'positive' : 'negative'">
                  {{ formatPercentage(todayPnlRatio) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 资产分布 -->
        <div class="asset-distribution">
          <div class="section-header">
            <h4>资产分布</h4>
            <a-radio-group v-model:value="assetViewType" size="small">
              <a-radio-button value="pie">饼图</a-radio-button>
              <a-radio-button value="table">列表</a-radio-button>
            </a-radio-group>
          </div>
          
          <div v-if="assetViewType === 'pie'" class="asset-chart">
            <div ref="assetChart" class="chart-container"></div>
          </div>
          
          <div v-else class="asset-table">
            <a-table
              :columns="assetColumns"
              :data-source="assetList"
              size="small"
              :pagination="false"
              :scroll="{ y: 300 }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'balance'">
                  <div class="balance-cell">
                    <div class="balance-amount">{{ formatNumber(record.balance) }}</div>
                    <div class="balance-value">{{ formatCurrency(record.value) }}</div>
                  </div>
                </template>
                <template v-else-if="column.key === 'change24h'">
                  <span :class="record.change24h >= 0 ? 'positive' : 'negative'">
                    {{ formatPercentage(record.change24h) }}
                  </span>
                </template>
                <template v-else-if="column.key === 'actions'">
                  <a-space>
                    <a-button size="small" @click="showTransferModal(record)">
                      转账
                    </a-button>
                    <a-button size="small" @click="showTradeModal(record)">
                      交易
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </div>

        <!-- 交易记录 -->
        <div class="trading-history">
          <div class="section-header">
            <h4>交易记录</h4>
            <a-space>
              <a-select
                v-model:value="historyFilter"
                size="small"
                style="width: 120px"
                @change="filterHistory"
              >
                <a-select-option value="all">全部</a-select-option>
                <a-select-option value="spot">现货</a-select-option>
                <a-select-option value="futures">合约</a-select-option>
                <a-select-option value="options">期权</a-select-option>
              </a-select>
              <a-range-picker
                v-model:value="dateRange"
                size="small"
                @change="filterByDate"
              />
              <a-input-search
                v-model:value="searchKeyword"
                placeholder="搜索交易对"
                size="small"
                style="width: 150px"
                @search="searchHistory"
              />
            </a-space>
          </div>
          
          <a-table
            :columns="historyColumns"
            :data-source="filteredHistory"
            size="small"
            :pagination="{ pageSize: 20, showSizeChanger: true, showQuickJumper: true }"
            :scroll="{ x: 1000 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'side'">
                <a-tag :color="record.side === 'buy' ? 'green' : 'red'">
                  {{ record.side === 'buy' ? '买入' : '卖出' }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'type'">
                <a-tag :color="getTypeColor(record.type)">
                  {{ getTypeText(record.type) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'amount'">
                <div class="amount-cell">
                  <div>{{ formatNumber(record.amount) }}</div>
                  <div class="amount-value">{{ formatCurrency(record.amount * record.price) }}</div>
                </div>
              </template>
              <template v-else-if="column.key === 'price'">
                <span>{{ formatNumber(record.price) }}</span>
              </template>
              <template v-else-if="column.key === 'fee'">
                <span>{{ formatNumber(record.fee) }} {{ record.feeCurrency }}</span>
              </template>
              <template v-else-if="column.key === 'pnl'">
                <span :class="record.pnl >= 0 ? 'positive' : 'negative'">
                  {{ formatCurrency(record.pnl) }}
                </span>
              </template>
              <template v-else-if="column.key === 'time'">
                <span>{{ formatTime(record.time) }}</span>
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-button size="small" @click="viewTradeDetail(record)">
                  详情
                </a-button>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 资金流水 -->
        <div class="fund-flow">
          <div class="section-header">
            <h4>资金流水</h4>
            <a-space>
              <a-select
                v-model:value="flowTypeFilter"
                size="small"
                style="width: 120px"
                @change="filterFlowType"
              >
                <a-select-option value="all">全部</a-select-option>
                <a-select-option value="deposit">充值</a-select-option>
                <a-select-option value="withdraw">提现</a-select-option>
                <a-select-option value="transfer">转账</a-select-option>
                <a-select-option value="trade">交易</a-select-option>
                <a-select-option value="fee">手续费</a-select-option>
              </a-select>
              <a-button size="small" type="primary" @click="showDepositModal = true">
                <PlusOutlined /> 充值
              </a-button>
              <a-button size="small" @click="showWithdrawModal = true">
                <MinusOutlined /> 提现
              </a-button>
            </a-space>
          </div>
          
          <a-table
            :columns="flowColumns"
            :data-source="filteredFlowHistory"
            size="small"
            :pagination="{ pageSize: 15, showSizeChanger: true }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'type'">
                <a-tag :color="getFlowTypeColor(record.type)">
                  {{ getFlowTypeText(record.type) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'amount'">
                <span :class="record.amount >= 0 ? 'positive' : 'negative'">
                  {{ formatCurrency(Math.abs(record.amount)) }}
                </span>
              </template>
              <template v-else-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'time'">
                <span>{{ formatTime(record.time) }}</span>
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-button size="small" @click="viewFlowDetail(record)">
                  详情
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-card>

    <!-- 转账模态框 -->
    <a-modal
      v-model:open="showTransferModal"
      title="资产转账"
      @ok="executeTransfer"
      @cancel="resetTransferForm"
      width="500px"
    >
      <a-form :model="transferForm" layout="vertical">
        <a-form-item label="转出账户" required>
          <a-select v-model:value="transferForm.fromAccount" placeholder="选择转出账户">
            <a-select-option value="spot">现货账户</a-select-option>
            <a-select-option value="futures">合约账户</a-select-option>
            <a-select-option value="margin">杠杆账户</a-select-option>
            <a-select-option value="funding">资金账户</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="转入账户" required>
          <a-select v-model:value="transferForm.toAccount" placeholder="选择转入账户">
            <a-select-option value="spot">现货账户</a-select-option>
            <a-select-option value="futures">合约账户</a-select-option>
            <a-select-option value="margin">杠杆账户</a-select-option>
            <a-select-option value="funding">资金账户</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="币种" required>
          <a-select v-model:value="transferForm.currency" placeholder="选择币种">
            <a-select-option v-for="asset in assetList" :key="asset.currency" :value="asset.currency">
              {{ asset.currency }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="转账金额" required>
          <a-input-number
            v-model:value="transferForm.amount"
            :min="0"
            :max="getMaxTransferAmount()"
            :precision="8"
            style="width: 100%"
            placeholder="请输入转账金额"
          />
          <div class="transfer-info">
            <span>可用余额: {{ formatNumber(getAvailableBalance()) }}</span>
            <a @click="setMaxAmount">全部</a>
          </div>
        </a-form-item>
        
        <a-form-item label="备注">
          <a-textarea
            v-model:value="transferForm.remark"
            :rows="2"
            placeholder="转账备注（可选）"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 充值模态框 -->
    <a-modal
      v-model:open="showDepositModal"
      title="资产充值"
      :footer="null"
      width="600px"
    >
      <a-form :model="depositForm" layout="vertical">
        <a-form-item label="选择币种" required>
          <a-select v-model:value="depositForm.currency" placeholder="选择要充值的币种">
            <a-select-option value="USDT">USDT</a-select-option>
            <a-select-option value="BTC">BTC</a-select-option>
            <a-select-option value="ETH">ETH</a-select-option>
            <a-select-option value="BNB">BNB</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="选择网络" required>
          <a-select v-model:value="depositForm.network" placeholder="选择充值网络">
            <a-select-option value="TRC20">TRC20</a-select-option>
            <a-select-option value="ERC20">ERC20</a-select-option>
            <a-select-option value="BSC">BSC</a-select-option>
            <a-select-option value="POLYGON">POLYGON</a-select-option>
          </a-select>
        </a-form-item>
        
        <div v-if="depositForm.currency && depositForm.network" class="deposit-info">
          <div class="deposit-address">
            <div class="address-label">充值地址:</div>
            <div class="address-value">
              <a-input
                :value="getDepositAddress()"
                readonly
                addon-after="复制"
                @click="copyAddress"
              />
            </div>
          </div>
          
          <div class="deposit-qr">
            <div class="qr-label">二维码:</div>
            <div class="qr-code">
              <div class="qr-placeholder">二维码占位符</div>
            </div>
          </div>
          
          <div class="deposit-notice">
            <a-alert
              message="充值须知"
              type="warning"
              show-icon
            >
              <template #description>
                <ul>
                  <li>请确保选择正确的网络，否则资产可能丢失</li>
                  <li>最小充值金额: 10 {{ depositForm.currency }}</li>
                  <li>到账时间: 1-3个网络确认</li>
                  <li>充值地址仅支持 {{ depositForm.currency }} 充值</li>
                </ul>
              </template>
            </a-alert>
          </div>
        </div>
      </a-form>
    </a-modal>

    <!-- 提现模态框 -->
    <a-modal
      v-model:open="showWithdrawModal"
      title="资产提现"
      @ok="executeWithdraw"
      @cancel="resetWithdrawForm"
      width="600px"
    >
      <a-form :model="withdrawForm" layout="vertical">
        <a-form-item label="提现币种" required>
          <a-select v-model:value="withdrawForm.currency" placeholder="选择提现币种">
            <a-select-option v-for="asset in assetList" :key="asset.currency" :value="asset.currency">
              {{ asset.currency }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="提现网络" required>
          <a-select v-model:value="withdrawForm.network" placeholder="选择提现网络">
            <a-select-option value="TRC20">TRC20</a-select-option>
            <a-select-option value="ERC20">ERC20</a-select-option>
            <a-select-option value="BSC">BSC</a-select-option>
            <a-select-option value="POLYGON">POLYGON</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="提现地址" required>
          <a-input
            v-model:value="withdrawForm.address"
            placeholder="请输入提现地址"
          />
          <div class="address-book">
            <a @click="showAddressBook = true">从地址簿选择</a>
          </div>
        </a-form-item>
        
        <a-form-item label="提现金额" required>
          <a-input-number
            v-model:value="withdrawForm.amount"
            :min="getMinWithdrawAmount()"
            :max="getMaxWithdrawAmount()"
            :precision="8"
            style="width: 100%"
            placeholder="请输入提现金额"
          />
          <div class="withdraw-info">
            <div>可提现余额: {{ formatNumber(getWithdrawableBalance()) }}</div>
            <div>手续费: {{ formatNumber(getWithdrawFee()) }} {{ withdrawForm.currency }}</div>
            <div>实际到账: {{ formatNumber(withdrawForm.amount - getWithdrawFee()) }} {{ withdrawForm.currency }}</div>
          </div>
        </a-form-item>
        
        <a-form-item label="资金密码" required>
          <a-input-password
            v-model:value="withdrawForm.password"
            placeholder="请输入资金密码"
          />
        </a-form-item>
        
        <a-form-item label="验证码" required>
          <a-input
            v-model:value="withdrawForm.code"
            placeholder="请输入验证码"
            style="width: 60%"
          />
          <a-button style="width: 35%; margin-left: 5%" @click="sendVerificationCode">
            {{ codeCountdown > 0 ? `${codeCountdown}s` : '发送验证码' }}
          </a-button>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 设置模态框 -->
    <a-modal
      v-model:open="showSettings"
      title="账户设置"
      @ok="saveSettings"
      @cancel="resetSettings"
      width="600px"
    >
      <a-form :model="settings" layout="vertical">
        <a-form-item label="显示设置">
          <a-space direction="vertical" style="width: 100%">
            <div>
              <a-switch v-model:checked="settings.hideSmallBalances" />
              <span class="setting-label">隐藏小额资产</span>
            </div>
            <div v-if="settings.hideSmallBalances" style="margin-left: 24px;">
              <a-form-item label="隐藏阈值 (USDT)" style="margin-bottom: 8px">
                <a-input-number
                  v-model:value="settings.smallBalanceThreshold"
                  :min="0.01"
                  :max="100"
                  :step="0.01"
                  style="width: 200px"
                />
              </a-form-item>
            </div>
            
            <div>
              <a-switch v-model:checked="settings.showPnlInPercent" />
              <span class="setting-label">盈亏显示为百分比</span>
            </div>
            
            <div>
              <a-switch v-model:checked="settings.autoRefresh" />
              <span class="setting-label">自动刷新数据</span>
            </div>
            <div v-if="settings.autoRefresh" style="margin-left: 24px;">
              <a-form-item label="刷新间隔 (秒)" style="margin-bottom: 8px">
                <a-input-number
                  v-model:value="settings.refreshInterval"
                  :min="5"
                  :max="300"
                  style="width: 200px"
                />
              </a-form-item>
            </div>
          </a-space>
        </a-form-item>
        
        <a-form-item label="安全设置">
          <a-space direction="vertical" style="width: 100%">
            <div>
              <a-switch v-model:checked="settings.enableTwoFA" />
              <span class="setting-label">启用双重验证</span>
            </div>
            
            <div>
              <a-switch v-model:checked="settings.enableWithdrawWhitelist" />
              <span class="setting-label">启用提现白名单</span>
            </div>
            
            <div>
              <a-switch v-model:checked="settings.enableLoginNotification" />
              <span class="setting-label">登录通知</span>
            </div>
          </a-space>
        </a-form-item>
        
        <a-form-item label="通知设置">
          <a-checkbox-group v-model:value="settings.notifications">
            <a-checkbox value="deposit">充值到账通知</a-checkbox>
            <a-checkbox value="withdraw">提现成功通知</a-checkbox>
            <a-checkbox value="trade">交易成交通知</a-checkbox>
            <a-checkbox value="security">安全事件通知</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { useChartStore } from '@/stores/chartStore'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import {
  ReloadOutlined,
  SettingOutlined,
  ExportOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  MinusOutlined
} from '@ant-design/icons-vue'

// 定义事件
const emit = defineEmits(['transfer-completed', 'deposit-initiated', 'withdraw-completed'])

// 使用状态管理
const chartStore = useChartStore()
const { isDarkTheme } = storeToRefs(chartStore)

// 本地状态
const showSettings = ref(false)
const showTransferModal = ref(false)
const showDepositModal = ref(false)
const showWithdrawModal = ref(false)
const showAddressBook = ref(false)
const assetChart = ref(null)
const updateInterval = ref(null)
const codeCountdown = ref(0)

// 视图类型
const assetViewType = ref('pie')

// 过滤和搜索
const historyFilter = ref('all')
const flowTypeFilter = ref('all')
const dateRange = ref([])
const searchKeyword = ref('')

// 账户数据
const totalAssets = ref(125680.50)
const totalAssetsChange = ref(2.35)
const availableBalance = ref(98420.30)
const frozenBalance = ref(27260.20)
const todayPnl = ref(1250.80)
const todayPnlRatio = ref(1.02)

// 资产列表
const assetList = ref([
  {
    currency: 'USDT',
    balance: 45680.50,
    value: 45680.50,
    change24h: 0.02,
    available: 38420.30,
    frozen: 7260.20
  },
  {
    currency: 'BTC',
    balance: 1.25680,
    value: 52800.40,
    change24h: 3.25,
    available: 1.05680,
    frozen: 0.20000
  },
  {
    currency: 'ETH',
    balance: 8.56420,
    value: 21450.80,
    change24h: -1.85,
    available: 7.26420,
    frozen: 1.30000
  },
  {
    currency: 'BNB',
    balance: 15.2580,
    value: 5748.90,
    change24h: 0.95,
    available: 12.2580,
    frozen: 3.00000
  }
])

// 交易记录
const tradingHistory = ref([
  {
    id: 1,
    symbol: 'BTC-USDT',
    side: 'buy',
    type: 'market',
    amount: 0.1,
    price: 42000,
    fee: 4.2,
    feeCurrency: 'USDT',
    pnl: 150.50,
    time: Date.now() - 3600000,
    account: 'spot'
  },
  {
    id: 2,
    symbol: 'ETH-USDT',
    side: 'sell',
    type: 'limit',
    amount: 2.5,
    price: 2500,
    fee: 6.25,
    feeCurrency: 'USDT',
    pnl: -85.20,
    time: Date.now() - 7200000,
    account: 'spot'
  }
])

// 资金流水
const fundFlowHistory = ref([
  {
    id: 1,
    type: 'deposit',
    currency: 'USDT',
    amount: 10000,
    status: 'completed',
    txHash: '0x1234...5678',
    time: Date.now() - ********,
    remark: '银行卡充值'
  },
  {
    id: 2,
    type: 'withdraw',
    currency: 'BTC',
    amount: -0.5,
    status: 'pending',
    txHash: '',
    time: Date.now() - 3600000,
    remark: '提现到钱包'
  }
])

// 表单数据
const transferForm = ref({
  fromAccount: '',
  toAccount: '',
  currency: '',
  amount: null,
  remark: ''
})

const depositForm = ref({
  currency: '',
  network: ''
})

const withdrawForm = ref({
  currency: '',
  network: '',
  address: '',
  amount: null,
  password: '',
  code: ''
})

// 设置
const settings = ref({
  hideSmallBalances: false,
  smallBalanceThreshold: 1,
  showPnlInPercent: true,
  autoRefresh: true,
  refreshInterval: 30,
  enableTwoFA: true,
  enableWithdrawWhitelist: false,
  enableLoginNotification: true,
  notifications: ['deposit', 'withdraw', 'security']
})

// 表格列定义
const assetColumns = [
  { title: '币种', dataIndex: 'currency', key: 'currency', width: 80 },
  { title: '余额/价值', dataIndex: 'balance', key: 'balance', width: 150 },
  { title: '24h涨跌', dataIndex: 'change24h', key: 'change24h', width: 100 },
  { title: '操作', key: 'actions', width: 120 }
]

const historyColumns = [
  { title: '交易对', dataIndex: 'symbol', key: 'symbol', width: 100 },
  { title: '方向', dataIndex: 'side', key: 'side', width: 80 },
  { title: '类型', dataIndex: 'type', key: 'type', width: 80 },
  { title: '数量', dataIndex: 'amount', key: 'amount', width: 120 },
  { title: '价格', dataIndex: 'price', key: 'price', width: 100 },
  { title: '手续费', dataIndex: 'fee', key: 'fee', width: 100 },
  { title: '盈亏', dataIndex: 'pnl', key: 'pnl', width: 100 },
  { title: '时间', dataIndex: 'time', key: 'time', width: 150 },
  { title: '操作', key: 'actions', width: 80 }
]

const flowColumns = [
  { title: '类型', dataIndex: 'type', key: 'type', width: 80 },
  { title: '币种', dataIndex: 'currency', key: 'currency', width: 80 },
  { title: '金额', dataIndex: 'amount', key: 'amount', width: 120 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 80 },
  { title: '时间', dataIndex: 'time', key: 'time', width: 150 },
  { title: '备注', dataIndex: 'remark', key: 'remark', width: 150 },
  { title: '操作', key: 'actions', width: 80 }
]

// 计算属性
const filteredHistory = computed(() => {
  let filtered = tradingHistory.value
  
  // 账户类型过滤
  if (historyFilter.value !== 'all') {
    filtered = filtered.filter(h => h.account === historyFilter.value)
  }
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(h => 
      h.symbol.toLowerCase().includes(keyword)
    )
  }
  
  // 日期范围过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const [start, end] = dateRange.value
    filtered = filtered.filter(h => {
      const time = dayjs(h.time)
      return time.isAfter(start) && time.isBefore(end)
    })
  }
  
  return filtered
})

const filteredFlowHistory = computed(() => {
  let filtered = fundFlowHistory.value
  
  // 类型过滤
  if (flowTypeFilter.value !== 'all') {
    filtered = filtered.filter(f => f.type === flowTypeFilter.value)
  }
  
  return filtered
})

/**
 * 获取类型颜色
 */
const getTypeColor = (type) => {
  switch (type) {
    case 'market': return 'blue'
    case 'limit': return 'green'
    case 'stop': return 'orange'
    default: return 'gray'
  }
}

/**
 * 获取类型文本
 */
const getTypeText = (type) => {
  switch (type) {
    case 'market': return '市价'
    case 'limit': return '限价'
    case 'stop': return '止损'
    default: return '未知'
  }
}

/**
 * 获取流水类型颜色
 */
const getFlowTypeColor = (type) => {
  switch (type) {
    case 'deposit': return 'green'
    case 'withdraw': return 'red'
    case 'transfer': return 'blue'
    case 'trade': return 'purple'
    case 'fee': return 'orange'
    default: return 'gray'
  }
}

/**
 * 获取流水类型文本
 */
const getFlowTypeText = (type) => {
  switch (type) {
    case 'deposit': return '充值'
    case 'withdraw': return '提现'
    case 'transfer': return '转账'
    case 'trade': return '交易'
    case 'fee': return '手续费'
    default: return '未知'
  }
}

/**
 * 获取状态颜色
 */
const getStatusColor = (status) => {
  switch (status) {
    case 'completed': return 'green'
    case 'pending': return 'orange'
    case 'failed': return 'red'
    default: return 'gray'
  }
}

/**
 * 获取状态文本
 */
const getStatusText = (status) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'pending': return '处理中'
    case 'failed': return '失败'
    default: return '未知'
  }
}

/**
 * 格式化货币
 */
const formatCurrency = (value) => {
  return `$${value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

/**
 * 格式化数字
 */
const formatNumber = (value, precision = 8) => {
  return value.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: precision })
}

/**
 * 格式化百分比
 */
const formatPercentage = (value) => {
  return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`
}

/**
 * 格式化时间
 */
const formatTime = (timestamp) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 获取充值地址
 */
const getDepositAddress = () => {
  // 模拟生成充值地址
  return 'TXYZabcd1234567890efghijklmnopqrstuvwxyz'
}

/**
 * 获取最大转账金额
 */
const getMaxTransferAmount = () => {
  const asset = assetList.value.find(a => a.currency === transferForm.value.currency)
  return asset ? asset.available : 0
}

/**
 * 获取可用余额
 */
const getAvailableBalance = () => {
  const asset = assetList.value.find(a => a.currency === transferForm.value.currency)
  return asset ? asset.available : 0
}

/**
 * 获取最小提现金额
 */
const getMinWithdrawAmount = () => {
  return 10 // 最小提现金额
}

/**
 * 获取最大提现金额
 */
const getMaxWithdrawAmount = () => {
  const asset = assetList.value.find(a => a.currency === withdrawForm.value.currency)
  return asset ? asset.available : 0
}

/**
 * 获取可提现余额
 */
const getWithdrawableBalance = () => {
  const asset = assetList.value.find(a => a.currency === withdrawForm.value.currency)
  return asset ? asset.available : 0
}

/**
 * 获取提现手续费
 */
const getWithdrawFee = () => {
  const fees = {
    'USDT': 1,
    'BTC': 0.0005,
    'ETH': 0.005,
    'BNB': 0.1
  }
  return fees[withdrawForm.value.currency] || 0
}

/**
 * 设置最大金额
 */
const setMaxAmount = () => {
  transferForm.value.amount = getAvailableBalance()
}

/**
 * 复制地址
 */
const copyAddress = () => {
  navigator.clipboard.writeText(getDepositAddress())
  message.success('地址已复制到剪贴板')
}

/**
 * 发送验证码
 */
const sendVerificationCode = () => {
  if (codeCountdown.value > 0) return
  
  codeCountdown.value = 60
  const timer = setInterval(() => {
    codeCountdown.value--
    if (codeCountdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
  
  message.success('验证码已发送')
}

/**
 * 过滤历史记录
 */
const filterHistory = () => {
  // 过滤逻辑在计算属性中处理
}

/**
 * 按日期过滤
 */
const filterByDate = () => {
  // 过滤逻辑在计算属性中处理
}

/**
 * 搜索历史记录
 */
const searchHistory = () => {
  // 搜索逻辑在计算属性中处理
}

/**
 * 过滤流水类型
 */
const filterFlowType = () => {
  // 过滤逻辑在计算属性中处理
}

/**
 * 显示转账模态框
 */
const showTransferModal = (asset) => {
  transferForm.value.currency = asset.currency
  showTransferModal.value = true
}

/**
 * 显示交易模态框
 */
const showTradeModal = (asset) => {
  message.info(`跳转到 ${asset.currency} 交易页面`)
  // 这里可以实现跳转到交易页面的逻辑
}

/**
 * 查看交易详情
 */
const viewTradeDetail = (trade) => {
  message.info(`查看交易详情: ${trade.id}`)
  // 这里可以实现查看交易详情的逻辑
}

/**
 * 查看流水详情
 */
const viewFlowDetail = (flow) => {
  message.info(`查看流水详情: ${flow.id}`)
  // 这里可以实现查看流水详情的逻辑
}

/**
 * 执行转账
 */
const executeTransfer = () => {
  if (!transferForm.value.fromAccount || !transferForm.value.toAccount || 
      !transferForm.value.currency || !transferForm.value.amount) {
    message.error('请填写完整的转账信息')
    return
  }
  
  if (transferForm.value.fromAccount === transferForm.value.toAccount) {
    message.error('转出账户和转入账户不能相同')
    return
  }
  
  if (transferForm.value.amount > getMaxTransferAmount()) {
    message.error('转账金额超过可用余额')
    return
  }
  
  // 模拟转账
  message.success('转账成功')
  showTransferModal.value = false
  resetTransferForm()
  emit('transfer-completed', transferForm.value)
}

/**
 * 执行提现
 */
const executeWithdraw = () => {
  if (!withdrawForm.value.currency || !withdrawForm.value.network || 
      !withdrawForm.value.address || !withdrawForm.value.amount ||
      !withdrawForm.value.password || !withdrawForm.value.code) {
    message.error('请填写完整的提现信息')
    return
  }
  
  if (withdrawForm.value.amount < getMinWithdrawAmount()) {
    message.error(`最小提现金额为 ${getMinWithdrawAmount()} ${withdrawForm.value.currency}`)
    return
  }
  
  if (withdrawForm.value.amount > getMaxWithdrawAmount()) {
    message.error('提现金额超过可用余额')
    return
  }
  
  // 模拟提现
  message.success('提现申请已提交，请等待审核')
  showWithdrawModal.value = false
  resetWithdrawForm()
  emit('withdraw-completed', withdrawForm.value)
}

/**
 * 重置转账表单
 */
const resetTransferForm = () => {
  transferForm.value = {
    fromAccount: '',
    toAccount: '',
    currency: '',
    amount: null,
    remark: ''
  }
}

/**
 * 重置提现表单
 */
const resetWithdrawForm = () => {
  withdrawForm.value = {
    currency: '',
    network: '',
    address: '',
    amount: null,
    password: '',
    code: ''
  }
}

/**
 * 初始化资产图表
 */
const initAssetChart = () => {
  if (!assetChart.value) return
  
  const chart = echarts.init(assetChart.value)
  
  const data = assetList.value.map(asset => ({
    name: asset.currency,
    value: asset.value
  }))
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: ${c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        color: isDarkTheme.value ? '#fff' : '#333'
      }
    },
    series: [
      {
        name: '资产分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式调整
  const resizeChart = () => {
    chart.resize()
  }
  
  window.addEventListener('resize', resizeChart)
  
  return () => {
    window.removeEventListener('resize', resizeChart)
    chart.dispose()
  }
}

/**
 * 导出报告
 */
const exportReport = () => {
  message.info('正在生成账户报告...')
  // 这里可以实现导出报告的逻辑
}

/**
 * 刷新数据
 */
const refreshData = () => {
  message.success('数据已刷新')
  // 这里可以实现实际的数据刷新逻辑
}

/**
 * 保存设置
 */
const saveSettings = () => {
  localStorage.setItem('account-settings', JSON.stringify(settings.value))
  showSettings.value = false
  message.success('设置已保存')
}

/**
 * 重置设置
 */
const resetSettings = () => {
  settings.value = {
    hideSmallBalances: false,
    smallBalanceThreshold: 1,
    showPnlInPercent: true,
    autoRefresh: true,
    refreshInterval: 30,
    enableTwoFA: true,
    enableWithdrawWhitelist: false,
    enableLoginNotification: true,
    notifications: ['deposit', 'withdraw', 'security']
  }
}

/**
 * 加载设置
 */
const loadSettings = () => {
  const saved = localStorage.getItem('account-settings')
  if (saved) {
    try {
      settings.value = { ...settings.value, ...JSON.parse(saved) }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }
}

/**
 * 启动自动刷新
 */
const startAutoRefresh = () => {
  if (!settings.value.autoRefresh || updateInterval.value) return
  
  updateInterval.value = setInterval(() => {
    // 模拟数据更新
    totalAssets.value += (Math.random() - 0.5) * 100
    totalAssetsChange.value = (Math.random() - 0.5) * 5
    todayPnl.value += (Math.random() - 0.5) * 50
    todayPnlRatio.value = (Math.random() - 0.5) * 2
    
    // 更新资产价格
    assetList.value.forEach(asset => {
      asset.change24h += (Math.random() - 0.5) * 2
      asset.value = asset.balance * (1 + asset.change24h / 100) * 1000 // 模拟价格
    })
  }, settings.value.refreshInterval * 1000)
}

/**
 * 停止自动刷新
 */
const stopAutoRefresh = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
    updateInterval.value = null
  }
}

// 生命周期
onMounted(() => {
  loadSettings()
  nextTick(() => {
    if (assetViewType.value === 'pie') {
      initAssetChart()
    }
  })
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})

// 监听视图类型变化
watch(assetViewType, (newType) => {
  if (newType === 'pie') {
    nextTick(() => {
      initAssetChart()
    })
  }
})

// 监听设置变化
watch(() => settings.value.autoRefresh, (newValue) => {
  if (newValue) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})

watch(() => settings.value.refreshInterval, () => {
  if (settings.value.autoRefresh) {
    stopAutoRefresh()
    startAutoRefresh()
  }
})
</script>

<style scoped>
.account-management-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.account-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.account-overview {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.account-card {
  background: white;
  border-radius: 6px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.card-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.info-icon {
  color: #999;
  font-size: 12px;
}

.card-value {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}

.amount {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.change {
  font-size: 12px;
  font-weight: 500;
}

.ratio {
  font-size: 12px;
  color: #999;
}

.asset-distribution {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.asset-chart {
  flex: 1;
  min-height: 300px;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.asset-table {
  flex: 1;
}

.balance-cell {
  display: flex;
  flex-direction: column;
}

.balance-amount {
  font-weight: 500;
}

.balance-value {
  font-size: 12px;
  color: #999;
}

.trading-history {
  margin-top: 16px;
}

.amount-cell {
  display: flex;
  flex-direction: column;
}

.amount-value {
  font-size: 12px;
  color: #999;
}

.fund-flow {
  margin-top: 16px;
}

.transfer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.transfer-info a {
  color: #1890ff;
  cursor: pointer;
}

.deposit-info {
  margin-top: 16px;
}

.deposit-address {
  margin-bottom: 16px;
}

.address-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.address-value {
  margin-bottom: 8px;
}

.deposit-qr {
  margin-bottom: 16px;
}

.qr-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.qr-code {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.qr-placeholder {
  color: #999;
  font-size: 14px;
}

.deposit-notice {
  margin-top: 16px;
}

.address-book {
  margin-top: 8px;
}

.address-book a {
  color: #1890ff;
  font-size: 12px;
  cursor: pointer;
}

.withdraw-info {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.withdraw-info > div {
  margin-bottom: 4px;
}

.setting-label {
  margin-left: 8px;
  font-size: 13px;
}

.positive {
  color: #52c41a;
}

.negative {
  color: #f5222d;
}

/* 深色主题 */
.dark-theme .account-overview {
  background: #1f1f1f;
}

.dark-theme .account-card {
  background: #262626;
  color: #fff;
}

.dark-theme .card-header h4 {
  color: #999;
}

.dark-theme .amount {
  color: #fff;
}

.dark-theme .info-icon {
  color: #666;
}

.dark-theme .ratio {
  color: #666;
}

.dark-theme .balance-value {
  color: #666;
}

.dark-theme .amount-value {
  color: #666;
}

.dark-theme .transfer-info {
  color: #999;
}

.dark-theme .withdraw-info {
  color: #999;
}

.dark-theme .setting-label {
  color: #fff;
}

.dark-theme .qr-placeholder {
  color: #666;
}

.dark-theme .qr-code {
  border-color: #434343;
}
</style>