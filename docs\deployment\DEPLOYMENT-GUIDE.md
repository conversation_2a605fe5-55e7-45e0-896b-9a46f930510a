# OKX 智能量化交易系统 - 部署指南

本文档提供了 OKX 智能量化交易系统的完整部署指南，包括前端、后端的开发环境、测试环境和生产环境部署说明。

## 📋 系统要求

### 前端要求
- **Node.js**: >= 18.0.0 (推荐 18.x LTS)
- **npm**: >= 9.0.0
- **操作系统**: Windows 10/11, macOS 11+, Ubuntu 20.04+
- **浏览器**: Chrome 100+, Firefox 100+, Safari 15+, Edge 100+

### 后端要求
- **Python**: >= 3.9.0 (推荐 3.11+)
- **pip**: >= 21.0.0
- **数据库**: SQLite (内置) 或 MySQL/PostgreSQL

### 推荐配置
- **CPU**: 4核心以上
- **内存**: >= 8GB RAM (开发环境4GB可用)
- **存储**: >= 5GB 可用空间
- **网络**: 稳定的互联网连接 (用于OKX API访问)

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd python-okx
```

### 2. 后端环境配置

#### 2.1 创建虚拟环境
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

#### 2.2 安装后端依赖
```bash
pip install -r requirements.txt
```

#### 2.3 配置环境变量
创建 `.env` 文件：
```env
# OKX API 配置
OKX_API_KEY=your_api_key
OKX_SECRET_KEY=your_secret_key
OKX_PASSPHRASE=your_passphrase
OKX_SANDBOX=true  # 测试环境设为true，生产环境设为false

# 数据库配置
DATABASE_URL=sqlite:///./trading.db

# JWT 配置
SECRET_KEY=your_jwt_secret_key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### 3. 前端环境配置

#### 3.1 进入前端目录
```bash
cd web-frontend
```

#### 3.2 安装前端依赖
```bash
# 使用 npm (推荐)
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

### 4. 启动服务

#### 4.1 启动后端服务
```bash
# 在项目根目录
python run_backend.py

# 或者
uvicorn webapp.main:app --reload --host 0.0.0.0 --port 8000
```

#### 4.2 启动前端服务
```bash
# 在 web-frontend 目录
npm run dev

# Windows 用户可以直接运行
start-vite.bat
```

### 5. 访问应用
- **前端界面**: http://localhost:5173
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 生产环境部署

### 前端生产构建

```bash
# 进入前端目录
cd web-frontend

# 构建生产版本
npm run build

# 构建完成后，dist目录包含生产文件
# 可以部署到任何静态文件服务器
```

### 后端生产部署

```bash
# 使用 Gunicorn 部署（推荐）
pip install gunicorn

# 启动生产服务器
gunicorn -w 4 -k uvicorn.workers.UnicornWorker run_backend:app --bind 0.0.0.0:8000

# 或使用 uvicorn 生产模式
uvicorn run_backend:app --host 0.0.0.0 --port 8000 --workers 4
```

### 环境变量配置

生产环境需要配置以下环境变量：

```bash
# 数据库配置
DATABASE_URL=sqlite:///./okx_trading.db

# JWT密钥（生产环境必须更改）
SECRET_KEY=your-super-secret-key-here

# OKX API配置
OKX_API_KEY=your-okx-api-key
OKX_SECRET_KEY=your-okx-secret-key
OKX_PASSPHRASE=your-okx-passphrase
OKX_SANDBOX=false

# 跨域配置
CORS_ORIGINS=https://yourdomain.com
```

### Docker 部署（可选）

创建 `Dockerfile`：

```dockerfile
# 后端 Dockerfile
FROM python:3.10-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "run_backend:app", "--host", "0.0.0.0", "--port", "8000"]
```

创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./okx_trading.db
      - SECRET_KEY=your-secret-key
    volumes:
      - ./data:/app/data

  frontend:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./web-frontend/dist:/usr/share/nginx/html
    depends_on:
      - backend
```

## 故障排除

### 常见问题

#### 1. 端口占用问题

```bash
# 检查端口占用
netstat -ano | findstr :5173
netstat -ano | findstr :8000

# 终止占用进程
taskkill /PID <进程ID> /F
```

#### 2. 依赖安装失败

```bash
# 清理 npm 缓存
npm cache clean --force

# 删除 node_modules 重新安装
rmdir /s node_modules
npm install

# Python 依赖问题
pip cache purge
pip install -r requirements.txt --force-reinstall
```

#### 3. 数据库连接问题

```bash
# 检查数据库文件权限
# 确保 okx_trading.db 文件可读写

# 重新初始化数据库
python init_database.py
```

#### 4. API 连接问题

```bash
# 检查 OKX API 配置
python test_okx_connection.py

# 验证 API 密钥
python configure_real_api.py
```

#### 5. 前端构建问题

```bash
# 检查 Node.js 版本
node --version
npm --version

# 更新到推荐版本
nvm install 18.17.0
nvm use 18.17.0
```

### 性能优化

#### 前端优化

1. **启用 Gzip 压缩**
   ```nginx
   gzip on;
   gzip_types text/css application/javascript application/json;
   ```

2. **配置缓存策略**
   ```nginx
   location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

#### 后端优化

1. **数据库连接池**
   ```python
   # 在生产环境中配置连接池
   DATABASE_POOL_SIZE=20
   DATABASE_MAX_OVERFLOW=30
   ```

2. **启用响应缓存**
   ```python
   # 对静态数据启用缓存
   @lru_cache(maxsize=128)
   def get_market_data():
       # 缓存市场数据
       pass
   ```

### 监控和日志

#### 日志配置

```python
# logging.conf
[loggers]
keys=root,uvicorn

[handlers]
keys=default,access

[formatters]
keys=default,access

[logger_root]
level=INFO
handlers=default

[logger_uvicorn]
level=INFO
handlers=access
qualname=uvicorn.access
propagate=0

[handler_default]
class=StreamHandler
formatter=default
args=(sys.stdout,)

[handler_access]
class=handlers.RotatingFileHandler
formatter=access
args=('access.log', 'a', 10485760, 5)

[formatter_default]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s

[formatter_access]
format=%(asctime)s - %(message)s
```

#### 健康检查

```bash
# 创建健康检查脚本
curl -f http://localhost:8000/health || exit 1
```

## 安全配置

### HTTPS 配置

```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:5173;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 防火墙配置

```bash
# 只开放必要端口
# 80 (HTTP), 443 (HTTPS)
# 关闭开发端口 5173, 8000
```

## 备份和恢复

### 数据备份

```bash
# 备份数据库
cp okx_trading.db backup/okx_trading_$(date +%Y%m%d_%H%M%S).db

# 备份配置文件
cp config.env backup/config_$(date +%Y%m%d_%H%M%S).env
```

### 自动备份脚本

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/path/to/backup"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp okx_trading.db $BACKUP_DIR/okx_trading_$DATE.db

# 备份配置
cp config.env $BACKUP_DIR/config_$DATE.env

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
find $BACKUP_DIR -name "*.env" -mtime +30 -delete

echo "Backup completed: $DATE"
```

## 🛠️ 开发环境部署

### 环境变量配置
创建 `.env.development` 文件：

```env
# API 配置
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws

# 功能开关
VITE_ENABLE_DRAWING_TOOLS=true
VITE_ENABLE_PATTERN_RECOGNITION=true
VITE_ENABLE_SMART_ALERTS=true
VITE_ENABLE_RISK_MANAGEMENT=true

# 调试模式
VITE_DEBUG_MODE=true
VITE_LOG_LEVEL=debug

# OKX API 配置（开发环境使用模拟数据）
VITE_OKX_API_KEY=your_dev_api_key
VITE_OKX_SECRET_KEY=your_dev_secret_key
VITE_OKX_PASSPHRASE=your_dev_passphrase
VITE_OKX_SANDBOX=true
```

### 开发工具配置

#### VS Code 推荐插件
```json
{
  "recommendations": [
    "vue.volar",
    "vue.vscode-typescript-vue-plugin",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint"
  ]
}
```

#### 调试配置
创建 `.vscode/launch.json`：

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Chrome",
      "request": "launch",
      "type": "chrome",
      "url": "http://localhost:5173",
      "webRoot": "${workspaceFolder}/src"
    }
  ]
}
```

## 🧪 测试环境部署

### 运行测试
```bash
# 运行单元测试
npm run test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch

# 运行特定测试文件
npm run test -- ModularTradingDashboard.test.js
```

### 测试环境配置
创建 `.env.test` 文件：

```env
# 测试环境配置
VITE_API_BASE_URL=http://localhost:8001
VITE_WS_URL=ws://localhost:8001/ws

# 使用模拟数据
VITE_USE_MOCK_DATA=true
VITE_MOCK_DELAY=100

# 测试功能开关
VITE_ENABLE_ALL_FEATURES=true
```

### 端到端测试（可选）
```bash
# 安装 Playwright（如果需要 E2E 测试）
npm install -D @playwright/test

# 运行 E2E 测试
npm run test:e2e
```

## 🏭 生产环境部署

### 1. 构建生产版本
```bash
# 构建项目
npm run build

# 预览构建结果
npm run preview
```

### 2. 生产环境配置
创建 `.env.production` 文件：

```env
# 生产 API 配置
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_WS_URL=wss://ws.yourdomain.com

# 功能开关
VITE_ENABLE_DRAWING_TOOLS=true
VITE_ENABLE_PATTERN_RECOGNITION=true
VITE_ENABLE_SMART_ALERTS=true
VITE_ENABLE_RISK_MANAGEMENT=true

# 生产模式
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=error

# OKX API 配置（生产环境）
VITE_OKX_API_KEY=your_prod_api_key
VITE_OKX_SECRET_KEY=your_prod_secret_key
VITE_OKX_PASSPHRASE=your_prod_passphrase
VITE_OKX_SANDBOX=false

# 性能优化
VITE_ENABLE_PWA=true
VITE_ENABLE_COMPRESSION=true
```

### 3. 静态文件服务器部署

#### Nginx 配置
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    # SSL 证书配置
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # 静态文件目录
    root /var/www/trading-frontend/dist;
    index index.html;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 缓存配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://backend-server:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket 代理
    location /ws/ {
        proxy_pass http://backend-server:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

#### Apache 配置
```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    Redirect permanent / https://yourdomain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName yourdomain.com
    DocumentRoot /var/www/trading-frontend/dist
    
    # SSL 配置
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    # 启用压缩
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>
    
    # SPA 路由支持
    <Directory "/var/www/trading-frontend/dist">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # 缓存配置
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </FilesMatch>
</VirtualHost>
```

### 4. Docker 部署

#### Dockerfile
```dockerfile
# 构建阶段
FROM node:18-alpine as build-stage

WORKDIR /app

# 复制 package 文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:alpine as production-stage

# 复制构建结果
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 复制 Nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  frontend:
    build: .
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    
  backend:
    image: your-backend-image
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************/trading
    depends_on:
      - db
    restart: unless-stopped
    
  db:
    image: postgres:14
    environment:
      - POSTGRES_DB=trading
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data:
```

### 5. CDN 部署

#### 使用 Vercel
```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署到 Vercel
vercel --prod
```

#### 使用 Netlify
```bash
# 安装 Netlify CLI
npm i -g netlify-cli

# 构建并部署
npm run build
netlify deploy --prod --dir=dist
```

## 🔧 配置优化

### Vite 生产配置优化
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  
  // 构建优化
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    
    // 代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          charts: ['echarts', 'klinecharts'],
          ui: ['ant-design-vue']
        }
      }
    },
    
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  
  // 路径别名
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  
  // 服务器配置
  server: {
    port: 5173,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
})
```

## 📊 监控和日志

### 性能监控
```javascript
// 添加性能监控
if (import.meta.env.PROD) {
  // Google Analytics
  gtag('config', 'GA_MEASUREMENT_ID')
  
  // 性能指标收集
  new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      // 发送性能数据到监控服务
      console.log(entry.name, entry.duration)
    }
  }).observe({ entryTypes: ['measure', 'navigation'] })
}
```

### 错误监控
```javascript
// 全局错误处理
window.addEventListener('error', (event) => {
  // 发送错误信息到监控服务
  console.error('Global error:', event.error)
})

window.addEventListener('unhandledrejection', (event) => {
  // 处理未捕获的 Promise 错误
  console.error('Unhandled promise rejection:', event.reason)
})
```

## 🔒 安全配置

### 内容安全策略 (CSP)
```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' 'unsafe-eval';
  style-src 'self' 'unsafe-inline';
  img-src 'self' data: https:;
  connect-src 'self' wss: https:;
  font-src 'self';
">
```

### 环境变量安全
```bash
# 生产环境不要提交敏感信息到代码库
# 使用环境变量或密钥管理服务

# 示例：使用 Docker secrets
docker secret create okx_api_key /path/to/api_key.txt
```

## 🚨 故障排除

### 常见问题

#### 1. 依赖安装失败
```bash
# 清除缓存
npm cache clean --force

# 删除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 2. 构建失败
```bash
# 检查 Node.js 版本
node --version

# 更新依赖
npm update

# 检查内存使用
node --max-old-space-size=4096 node_modules/.bin/vite build
```

#### 3. 运行时错误
```bash
# 启用调试模式
VITE_DEBUG_MODE=true npm run dev

# 检查浏览器控制台
# 检查网络请求
# 检查 WebSocket 连接
```

### 日志收集
```javascript
// 开发环境日志
if (import.meta.env.DEV) {
  console.log('Development mode enabled')
}

// 生产环境日志
if (import.meta.env.PROD) {
  // 只记录错误和警告
  console.log = () => {}
  console.info = () => {}
}
```

## 📞 支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查项目的 Issues 页面
3. 联系开发团队获取支持
4. 提供详细的错误信息和环境配置

---

**最后更新**: 2024年1月  
**版本**: 1.0.0  
**维护**: 开发团队