<template>
  <div class="chart-controls">
    <a-select :value="symbol" @change="$emit('update:symbol', $event)" style="width: 120px">
      <a-select-option value="BTC-USDT">BTC-USDT</a-select-option>
      <a-select-option value="ETH-USDT">ETH-USDT</a-select-option>
    </a-select>
    <a-select :value="timeframe" @change="$emit('update:timeframe', $event)" style="width: 100px">
      <a-select-option value="1m">1m</a-select-option>
      <a-select-option value="5m">5m</a-select-option>
      <a-select-option value="15m">15m</a-select-option>
      <a-select-option value="30m">30m</a-select-option>
      <a-select-option value="1h">1H</a-select-option>
      <a-select-option value="4h">4H</a-select-option>
      <a-select-option value="1d">1D</a-select-option>
    </a-select>
    <a-button @click="showDisplaySettings = true">Display</a-button>
    <a-button @click="showIndicatorSettings = true">Indicators</a-button>

    <a-modal v-model:open="showDisplaySettings" title="Display Settings">
      <!-- Display settings form -->
    </a-modal>
    <a-modal v-model:open="showIndicatorSettings" title="Indicator Settings">
      <!-- Indicator settings form -->
    </a-modal>
  </div>
</template>

<script setup>
import { ref } from 'vue';

defineProps(['symbol', 'timeframe', 'display', 'indicators']);
defineEmits(['update:symbol', 'update:timeframe', 'update:display', 'update:indicators']);

const showDisplaySettings = ref(false);
const showIndicatorSettings = ref(false);
</script>

<style scoped>
.chart-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}
</style>