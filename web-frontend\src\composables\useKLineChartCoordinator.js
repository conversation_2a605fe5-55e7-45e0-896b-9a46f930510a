import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useIndicators } from './useIndicators'
import { useKLineCharts } from './useKLineCharts'
import { useEnhancedDataFetching } from './useEnhancedDataFetching'
import { useChartErrorHandler } from './useChartErrorHandler'
import { useChartDataStore } from '@/stores/chartDataStore'

/**
 * KLineChart协调器
 * 统一管理KLineChart图表、指标、数据获取和错误处理
 * @param {Object} options - 配置选项
 * @returns {Object} 协调器实例
 */
export function useKLineChartCoordinator({
  selectedSymbol,
  selectedTimeframe,
  refs,
  indicatorSettings,
  displaySettings,
  errorHandler
}) {
  // 状态管理
  const loadingChart = ref(false)
  const isInitialized = ref(false)
  const lastUpdateTime = ref(null)
  const retryCount = ref(0)
  const maxRetries = 3

  // 数据状态
  const chartData = ref([])
  const currentPrice = ref(null)
  const technicalIndicators = ref({})
  const marketSentiment = ref({ score: 0, label: '中性' })
  const tradingSignals = ref([])
  const dataSource = ref('real')

  // 性能监控
  const performanceMetrics = reactive({
    loadTime: 0,
    updateTime: 0,
    errorCount: 0,
    lastError: null
  })

  // 初始化子模块
  const indicators = useIndicators(indicatorSettings)
  const charts = useKLineCharts(refs, displaySettings)
  const dataFetching = useEnhancedDataFetching()

  /**
   * 生成示例K线数据
   * @param {number} count - 数据点数量
   * @param {string} symbol - 交易对
   * @returns {Array} K线数据
   */
  const generateSampleKLineData = (count = 200, symbol = 'BTC-USDT') => {
    const data = []
    const basePrice = symbol.includes('BTC') ? 50000 : symbol.includes('ETH') ? 3000 : 100
    let currentPrice = basePrice
    const now = Date.now()
    
    for (let i = 0; i < count; i++) {
      const timestamp = now - (count - i) * 60 * 1000 // 每分钟一个数据点
      const change = (Math.random() - 0.5) * (basePrice * 0.02) // 2%的价格波动
      const open = currentPrice
      const close = currentPrice + change
      const high = Math.max(open, close) + Math.random() * (basePrice * 0.01)
      const low = Math.min(open, close) - Math.random() * (basePrice * 0.01)
      const volume = Math.random() * 1000000 + 100000
      
      data.push({
        timestamp,
        open: parseFloat(open.toFixed(2)),
        high: parseFloat(high.toFixed(2)),
        low: parseFloat(low.toFixed(2)),
        close: parseFloat(close.toFixed(2)),
        volume: parseFloat(volume.toFixed(0))
      })
      
      currentPrice = close
    }
    
    return data
  }

  /**
   * 计算市场情绪
   * @param {Object} indicators - 技术指标数据
   * @returns {Object} 市场情绪
   */
  const calculateMarketSentiment = (indicators) => {
    try {
      if (!indicators || !indicators.rsi || !indicators.ma || !indicators.macd) {
        return { score: 0, label: '中性' }
      }

      let score = 0
      const weights = { rsi: 0.4, ma: 0.3, macd: 0.3 }

      // RSI 分析
      const rsiValues = indicators.rsi?.filter(v => v !== null) || []
      if (rsiValues.length > 0) {
        const rsi = rsiValues[rsiValues.length - 1]
        if (rsi > 70) score -= 2 * weights.rsi
        else if (rsi > 60) score -= 1 * weights.rsi
        else if (rsi < 30) score += 2 * weights.rsi
        else if (rsi < 40) score += 1 * weights.rsi
      }

      // MA 分析
      const ma5Values = indicators.ma?.ma5?.filter(v => v !== null) || []
      const ma20Values = indicators.ma?.ma20?.filter(v => v !== null) || []
      if (ma5Values.length > 0 && ma20Values.length > 0) {
        const ma5 = ma5Values[ma5Values.length - 1]
        const ma20 = ma20Values[ma20Values.length - 1]
        if (ma5 > ma20) score += 1 * weights.ma
        else score -= 1 * weights.ma
      }

      // MACD 分析
      const difValues = indicators.macd?.dif?.filter(v => v !== null) || []
      const deaValues = indicators.macd?.dea?.filter(v => v !== null) || []
      if (difValues.length > 0 && deaValues.length > 0) {
        const difLine = difValues[difValues.length - 1]
        const deaLine = deaValues[deaValues.length - 1]
        if (difLine > deaLine) score += 1 * weights.macd
        else score -= 1 * weights.macd
      }

      // 确定情绪标签
      let label
      if (score > 1.5) label = '极度看涨'
      else if (score > 0.5) label = '看涨'
      else if (score < -1.5) label = '极度看跌'
      else if (score < -0.5) label = '看跌'
      else label = '中性'

      return {
        score: Math.round(score * 100) / 100,
        label
      }
    } catch (error) {
      console.error('计算市场情绪失败:', error)
      return { score: 0, label: '中性' }
    }
  }

  /**
   * 生成交易信号
   * @param {Array} klineData - K线数据
   * @param {Object} indicators - 技术指标
   * @returns {Array} 交易信号
   */
  const generateTradingSignals = (klineData, indicators) => {
    try {
      const signals = []
      
      if (!klineData || klineData.length < 2) {
        return signals
      }

      const latestData = klineData[klineData.length - 1]
      const previousData = klineData[klineData.length - 2]
      
      // RSI信号
      if (indicators.rsi && indicators.rsi.length > 0) {
        const rsi = indicators.rsi[indicators.rsi.length - 1]
        if (rsi < 30) {
          signals.push({
            time: new Date(latestData.timestamp).toLocaleTimeString(),
            signal: '买入',
            price: latestData.close,
            reason: `RSI超卖 (${rsi.toFixed(2)})`
          })
        } else if (rsi > 70) {
          signals.push({
            time: new Date(latestData.timestamp).toLocaleTimeString(),
            signal: '卖出',
            price: latestData.close,
            reason: `RSI超买 (${rsi.toFixed(2)})`
          })
        }
      }

      // 价格突破信号
      if (latestData.close > previousData.high) {
        signals.push({
          time: new Date(latestData.timestamp).toLocaleTimeString(),
          signal: '买入',
          price: latestData.close,
          reason: '价格向上突破'
        })
      } else if (latestData.close < previousData.low) {
        signals.push({
          time: new Date(latestData.timestamp).toLocaleTimeString(),
          signal: '卖出',
          price: latestData.close,
          reason: '价格向下突破'
        })
      }

      return signals
    } catch (error) {
      console.error('生成交易信号失败:', error)
      return []
    }
  }

  /**
   * 更新当前价格信息
   * @param {Array} klineData - K线数据
   */
  const updateCurrentPrice = async (klineData) => {
    try {
      if (!klineData || klineData.length === 0) {
        return
      }

      const latest = klineData[klineData.length - 1]
      const previous = klineData.length > 1 ? klineData[klineData.length - 2] : latest
      
      const change = latest.close - previous.close
      const changePercent = (change / previous.close) * 100
      
      const priceData = {
        last: latest.close,
        open: latest.open,
        high: latest.high,
        low: latest.low,
        price: latest.close,
        change,
        changePercent,
        direction: change >= 0 ? 'up' : 'down',
        high24h: (() => {
          const highs = klineData.slice(-24).map(d => d.high).filter(h => typeof h === 'number' && !isNaN(h));
          return highs.length > 0 ? Math.max.apply(null, highs) : 0;
        })(),
        low24h: (() => {
          const lows = klineData.slice(-24).map(d => d.low).filter(l => typeof l === 'number' && !isNaN(l));
          return lows.length > 0 ? Math.min.apply(null, lows) : 0;
        })(),
        volume24h: klineData.slice(-24).reduce((sum, d) => sum + d.volume, 0),
        vol24h: klineData.slice(-24).reduce((sum, d) => sum + d.volume, 0),
        volCcy24h: klineData.slice(-24).reduce((sum, d) => sum + d.volume * d.close, 0)
      }
      
      currentPrice.value = priceData
      
      // 同步价格数据到chartDataStore
      const chartDataStore = useChartDataStore()
      chartDataStore.currentPrice = priceData
    } catch (error) {
      console.error('更新当前价格失败:', error)
    }
  }

  /**
   * 获取数据并更新图表
   */
  const fetchAndUpdateData = async () => {
    try {
      loadingChart.value = true
      const startTime = Date.now()

      // 强制使用真实数据源，确保与交易所同步
      let klineData
      try {
        // 获取真实数据
        await dataFetching.loadRealMarketData(selectedSymbol.value, selectedTimeframe.value, 200)
        klineData = dataFetching.chartData.value
        
        if (!klineData || klineData.length === 0) {
          throw new Error('获取的真实数据为空')
        }
        
        console.log(`✅ 成功获取真实数据: ${klineData.length} 条K线数据`)
      } catch (error) {
        console.error('❌ 获取真实数据失败:', error)
        // 如果真实数据获取失败，抛出错误而不是使用模拟数据
        throw new Error(`无法获取交易所实时数据: ${error.message}`)
      }

      // 更新图表数据
      await charts.updateChartData(klineData)
      
      // 计算技术指标
      const calculatedIndicators = indicators.calculateAllIndicators(klineData)
      technicalIndicators.value = calculatedIndicators
      
      // 更新当前价格
      await updateCurrentPrice(klineData)
      
      // 计算市场情绪
      marketSentiment.value = calculateMarketSentiment(calculatedIndicators)
      
      // 生成交易信号
      const newSignals = generateTradingSignals(klineData, calculatedIndicators)
      tradingSignals.value = [...tradingSignals.value, ...newSignals].slice(-20) // 保留最近20个信号
      
      // 更新性能指标
      performanceMetrics.loadTime = Date.now() - startTime
      lastUpdateTime.value = new Date()
      retryCount.value = 0
      
      chartData.value = klineData
      
      // 同步数据到chartDataStore，确保infoPanelStore能监听到变化
      const chartDataStore = useChartDataStore()
      chartDataStore.setChartData(klineData)
      
      console.log('数据更新成功，数据点数量:', klineData.length)
      
    } catch (error) {
      console.error('获取和更新数据失败:', error)
      performanceMetrics.errorCount++
      performanceMetrics.lastError = error.message
      
      if (errorHandler) {
        errorHandler(error)
      }
      
      // 重试逻辑
      if (retryCount.value < maxRetries) {
        retryCount.value++
        console.log(`重试获取数据 (${retryCount.value}/${maxRetries})`)
        setTimeout(() => fetchAndUpdateData(), 2000 * retryCount.value)
      }
    } finally {
      loadingChart.value = false
    }
  }

  /**
   * 初始化协调器
   * @param {boolean} isDark - 是否为暗色主题
   */
  const initializeCoordinator = async (isDark = false) => {
    try {
      console.log('开始初始化KLineChart协调器...')
      
      // 初始化图表
      await charts.initializeCharts(isDark)
      
      // 获取初始数据
      await fetchAndUpdateData()
      
      isInitialized.value = true
      console.log('KLineChart协调器初始化完成')
      
    } catch (error) {
      console.error('初始化KLineChart协调器失败:', error)
      if (errorHandler) {
        errorHandler(error)
      }
      throw error
    }
  }

  /**
   * 刷新数据
   */
  const refreshData = async () => {
    try {
      console.log('刷新图表数据...')
      await fetchAndUpdateData()
    } catch (error) {
      console.error('刷新数据失败:', error)
      if (errorHandler) {
        errorHandler(error)
      }
    }
  }

  /**
   * 更新图表主题
   * @param {boolean} isDark - 是否为暗色主题
   */
  const updateChartTheme = async (isDark) => {
    try {
      await charts.updateTheme(isDark)
    } catch (error) {
      console.error('更新图表主题失败:', error)
    }
  }

  /**
   * 销毁协调器
   */
  const destroyCoordinator = () => {
    try {
      charts.destroyCharts()
      isInitialized.value = false
      console.log('KLineChart协调器已销毁')
    } catch (error) {
      console.error('销毁KLineChart协调器失败:', error)
    }
  }

  // 监听交易对和时间周期变化
  watch([selectedSymbol, selectedTimeframe], async () => {
    if (isInitialized.value) {
      await refreshData()
    }
  })

  // 监听数据源变化
  watch(dataSource, async () => {
    if (isInitialized.value) {
      await refreshData()
    }
  })

  return {
    // 状态
    loadingChart,
    isInitialized,
    chartData,
    currentPrice,
    technicalIndicators,
    marketSentiment,
    tradingSignals,
    dataSource,
    performanceMetrics,
    
    // 方法
    initializeCoordinator,
    refreshData,
    updateChartTheme,
    destroyCoordinator,
    
    // 图表管理器实例
    charts,
    indicators
  }
}