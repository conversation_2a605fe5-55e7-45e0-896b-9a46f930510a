<template>
  <div class="error-boundary">
    <!-- 正常渲染子组件 -->
    <slot v-if="!hasError" />
    
    <!-- 错误状态显示 -->
    <div v-else class="error-container">
      <div class="error-content">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="#ff6b6b"/>
          </svg>
        </div>
        
        <h3 class="error-title">{{ errorTitle }}</h3>
        <p class="error-message">{{ errorMessage }}</p>
        
        <!-- 错误详情（开发模式） -->
        <details v-if="showDetails" class="error-details">
          <summary>错误详情</summary>
          <pre class="error-stack">{{ errorInfo?.stack || '无堆栈信息' }}</pre>
          <div class="error-props" v-if="errorInfo?.componentStack">
            <strong>组件堆栈:</strong>
            <pre>{{ errorInfo.componentStack }}</pre>
          </div>
        </details>
        
        <!-- 操作按钮 -->
        <div class="error-actions">
          <button 
            class="btn btn-primary" 
            @click="handleRetry"
            :disabled="retrying"
          >
            <span v-if="retrying">重试中...</span>
            <span v-else>重试 ({{ retryCount }}/{{ maxRetries }})</span>
          </button>
          
          <button 
            class="btn btn-secondary" 
            @click="handleReset"
          >
            重置组件
          </button>
          
          <button 
            v-if="canReload"
            class="btn btn-outline" 
            @click="handleReload"
          >
            刷新页面
          </button>
        </div>
        
        <!-- 错误报告 -->
        <div class="error-report" v-if="enableReporting">
          <button 
            class="btn btn-link" 
            @click="handleReport"
            :disabled="reported"
          >
            <span v-if="reported">已报告</span>
            <span v-else>报告此错误</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import chartConfig from '@/config/chartConfig'
import performanceMonitor from '@/utils/performanceMonitor'

// Props
const props = defineProps({
  // 错误恢复策略
  recoveryStrategy: {
    type: String,
    default: 'graceful', // 'graceful' | 'aggressive' | 'manual'
    validator: value => ['graceful', 'aggressive', 'manual'].includes(value)
  },
  // 最大重试次数
  maxRetries: {
    type: Number,
    default: () => chartConfig.get('error.maxErrorCount', 3)
  },
  // 是否启用错误报告
  enableReporting: {
    type: Boolean,
    default: () => chartConfig.get('error.enableErrorReporting', false)
  },
  // 是否显示错误详情
  showDetails: {
    type: Boolean,
    default: () => chartConfig.get('development.enableDebug', false)
  },
  // 自定义错误标题
  customTitle: {
    type: String,
    default: ''
  },
  // 自定义错误消息
  customMessage: {
    type: String,
    default: ''
  },
  // 是否允许重新加载页面
  canReload: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits([
  'error',
  'retry',
  'reset',
  'recover'
])

// 响应式状态
const hasError = ref(false)
const errorInfo = ref(null)
const retryCount = ref(0)
const retrying = ref(false)
const reported = ref(false)
const recoveryTimer = ref(null)

// 计算属性
const errorTitle = computed(() => {
  if (props.customTitle) return props.customTitle
  
  switch (errorInfo.value?.type) {
    case 'ChartRenderError':
      return '图表渲染错误'
    case 'DataLoadError':
      return '数据加载错误'
    case 'NetworkError':
      return '网络连接错误'
    case 'ValidationError':
      return '数据验证错误'
    default:
      return '组件运行错误'
  }
})

const errorMessage = computed(() => {
  if (props.customMessage) return props.customMessage
  
  if (errorInfo.value?.userMessage) {
    return errorInfo.value.userMessage
  }
  
  switch (errorInfo.value?.type) {
    case 'ChartRenderError':
      return '图表渲染时发生错误，请尝试刷新或联系技术支持。'
    case 'DataLoadError':
      return '数据加载失败，请检查网络连接后重试。'
    case 'NetworkError':
      return '网络连接异常，请检查网络设置后重试。'
    case 'ValidationError':
      return '数据格式错误，请刷新页面重新加载。'
    default:
      return '组件运行时发生未知错误，请尝试重试或刷新页面。'
  }
})

// 错误捕获
const captureError = (error, errorInfo) => {
  console.error('ErrorBoundary 捕获错误:', error)
  
  hasError.value = true
  errorInfo.value = {
    error,
    stack: error.stack,
    message: error.message,
    type: error.name || 'UnknownError',
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
    componentStack: errorInfo?.componentStack,
    userMessage: error.userMessage
  }
  
  // 记录错误到性能监控
  performanceMonitor.recordError(error, 'ErrorBoundary')
  
  // 发出错误事件
  emit('error', errorInfo.value)
  
  // 根据恢复策略处理
  handleRecoveryStrategy()
}

// 处理恢复策略
const handleRecoveryStrategy = () => {
  const strategy = props.recoveryStrategy
  
  switch (strategy) {
    case 'graceful':
      // 优雅恢复：等待一段时间后自动重试
      if (retryCount.value < props.maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, retryCount.value), 10000) // 指数退避
        recoveryTimer.value = setTimeout(() => {
          handleRetry()
        }, delay)
      }
      break
      
    case 'aggressive':
      // 激进恢复：立即重试
      if (retryCount.value < props.maxRetries) {
        nextTick(() => {
          handleRetry()
        })
      }
      break
      
    case 'manual':
      // 手动恢复：不自动重试
      break
  }
}

// 重试处理
const handleRetry = async () => {
  if (retryCount.value >= props.maxRetries) {
    console.warn('已达到最大重试次数')
    return
  }
  
  retrying.value = true
  retryCount.value++
  
  try {
    // 清理错误状态
    await nextTick()
    
    // 等待一小段时间确保组件状态稳定
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // 重置错误状态
    hasError.value = false
    errorInfo.value = null
    
    emit('retry', retryCount.value)
    
    console.log(`错误边界重试 ${retryCount.value}/${props.maxRetries}`)
    
  } catch (error) {
    console.error('重试失败:', error)
    // 如果重试失败，恢复错误状态
    hasError.value = true
  } finally {
    retrying.value = false
  }
}

// 重置处理
const handleReset = () => {
  hasError.value = false
  errorInfo.value = null
  retryCount.value = 0
  retrying.value = false
  reported.value = false
  
  if (recoveryTimer.value) {
    clearTimeout(recoveryTimer.value)
    recoveryTimer.value = null
  }
  
  emit('reset')
  emit('recover')
  
  console.log('错误边界已重置')
}

// 重新加载页面
const handleReload = () => {
  if (confirm('确定要刷新页面吗？未保存的数据将丢失。')) {
    window.location.reload()
  }
}

// 错误报告
const handleReport = async () => {
  if (!props.enableReporting || reported.value) return
  
  try {
    // 这里可以集成错误报告服务
    const reportData = {
      ...errorInfo.value,
      retryCount: retryCount.value,
      recoveryStrategy: props.recoveryStrategy,
      performanceMetrics: performanceMonitor.getPerformanceReport()
    }
    
    console.log('错误报告数据:', reportData)
    
    // 模拟发送报告
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    reported.value = true
    console.log('错误报告已发送')
    
  } catch (error) {
    console.error('发送错误报告失败:', error)
  }
}

// 全局错误处理
const handleGlobalError = (event) => {
  const error = event.error || new Error(event.message)
  captureError(error, {
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno
  })
}

const handleUnhandledRejection = (event) => {
  const error = event.reason instanceof Error ? event.reason : new Error(event.reason)
  captureError(error, {
    type: 'UnhandledPromiseRejection'
  })
}

// 生命周期
onMounted(() => {
  // 监听全局错误
  window.addEventListener('error', handleGlobalError)
  window.addEventListener('unhandledrejection', handleUnhandledRejection)
})

onUnmounted(() => {
  // 清理定时器
  if (recoveryTimer.value) {
    clearTimeout(recoveryTimer.value)
  }
  
  // 移除全局错误监听
  window.removeEventListener('error', handleGlobalError)
  window.removeEventListener('unhandledrejection', handleUnhandledRejection)
})

// 暴露方法给父组件
defineExpose({
  captureError,
  reset: handleReset,
  retry: handleRetry,
  hasError: () => hasError.value,
  getErrorInfo: () => errorInfo.value
})
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.error-content {
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.error-icon {
  margin-bottom: 1rem;
}

.error-icon svg {
  opacity: 0.8;
}

.error-title {
  color: #dc3545;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.error-message {
  color: #6c757d;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.error-details {
  text-align: left;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.error-details summary {
  cursor: pointer;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.error-stack,
.error-props pre {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.75rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: #495057;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.error-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
}

.btn-outline {
  background: transparent;
  color: #007bff;
  border: 1px solid #007bff;
}

.btn-outline:hover:not(:disabled) {
  background: #007bff;
  color: white;
}

.btn-link {
  background: transparent;
  color: #007bff;
  border: none;
  text-decoration: underline;
}

.btn-link:hover:not(:disabled) {
  color: #0056b3;
}

.error-report {
  border-top: 1px solid #dee2e6;
  padding-top: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-container {
    padding: 1rem;
    min-height: 200px;
  }
  
  .error-title {
    font-size: 1.25rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 200px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .error-container {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    border-color: #4a5568;
  }
  
  .error-title {
    color: #fc8181;
  }
  
  .error-message {
    color: #a0aec0;
  }
  
  .error-details {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .error-details summary {
    color: #e2e8f0;
  }
  
  .error-stack,
  .error-props pre {
    background: #1a202c;
    border-color: #4a5568;
    color: #e2e8f0;
  }
}
</style>