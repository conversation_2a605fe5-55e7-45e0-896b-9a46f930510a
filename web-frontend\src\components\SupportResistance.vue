<!--
  SupportResistance.vue
  支撑阻力位组件

  该组件显示计算出的支撑位和阻力位。
  数据来源于 chartStore。
-->
<template>
  <div class="support-resistance">
    <div class="sr-header">支撑/阻力位</div>
    <div class="sr-grid">
      <div class="sr-item support">
        <div class="sr-label">支撑位 1</div>
        <div class="sr-value">{{ formatPrice(supportResistance.s1) }}</div>
      </div>
      <div class="sr-item support">
        <div class="sr-label">支撑位 2</div>
        <div class="sr-value">{{ formatPrice(supportResistance.s2) }}</div>
      </div>
      <div class="sr-item resistance">
        <div class="sr-label">阻力位 1</div>
        <div class="sr-value">{{ formatPrice(supportResistance.r1) }}</div>
      </div>
      <div class="sr-item resistance">
        <div class="sr-label">阻力位 2</div>
        <div class="sr-value">{{ formatPrice(supportResistance.r2) }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useInfoPanelStore } from '@/stores/infoPanelStore'
import { formatPrice } from '@/utils/formatters'
import { storeToRefs } from 'pinia'

const infoPanelStore = useInfoPanelStore()
const { supportResistance } = storeToRefs(infoPanelStore)


</script>

<style scoped>
.support-resistance {
  padding: 12px;
  background: #fafafa;
  border-radius: 4px;
}

.sr-header {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.sr-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.sr-item {
  padding: 8px;
  border-radius: 4px;
  background: #f0f0f0;
}

.sr-label {
  font-size: 11px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.sr-value {
  font-size: 12px;
  font-weight: 600;
}

.support .sr-value {
  color: #52c41a;
}

.resistance .sr-value {
  color: #ff4d4f;
}

.dark-theme .support-resistance {
  background: #2a2e39;
}

.dark-theme .sr-header {
  color: #ffffff;
}

.dark-theme .sr-item {
  background: #363a45;
}

.dark-theme .sr-label {
  color: #a0a0a0;
}
</style>