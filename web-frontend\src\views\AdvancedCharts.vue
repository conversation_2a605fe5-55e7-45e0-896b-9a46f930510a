<template>
  <div class="advanced-charts">
    <a-row :gutter="24">
      <!-- 主图表区域 -->
      <a-col :span="18">
        <a-card title="高级图表分析" style="margin-bottom: 24px;">
          <div class="chart-controls" style="margin-bottom: 16px;">
            <a-space>
              <a-select
                v-model:value="selectedSymbol"
                style="width: 200px"
                @change="onSymbolChange"
              >
                <a-select-option value="BTC-USDT-SWAP">BTC-USDT-SWAP</a-select-option>
                <a-select-option value="ETH-USDT-SWAP">ETH-USDT-SWAP</a-select-option>
                <a-select-option value="SOL-USDT-SWAP">SOL-USDT-SWAP</a-select-option>
              </a-select>

              <a-select
                v-model:value="selectedTimeframe"
                style="width: 120px"
                @change="onTimeframeChange"
              >
                <a-select-option value="1m">1分钟</a-select-option>
                <a-select-option value="5m">5分钟</a-select-option>
                <a-select-option value="15m">15分钟</a-select-option>
                <a-select-option value="1H">1小时</a-select-option>
                <a-select-option value="4H">4小时</a-select-option>
                <a-select-option value="1D">1天</a-select-option>
              </a-select>

              <a-button @click="refreshChart" :loading="loadingChart">
                刷新数据
              </a-button>

              <a-button @click="toggleIndicators">
                {{ showIndicators ? '隐藏指标' : '显示指标' }}
              </a-button>

              <a-dropdown>
                <template #overlay>
                  <a-menu @click="addIndicator">
                    <a-menu-item key="MACD">
                      <span>MACD</span>
                    </a-menu-item>
                    <a-menu-item key="BOLL">
                      <span>布林带</span>
                    </a-menu-item>
                    <a-menu-item key="KDJ">
                      <span>KDJ</span>
                    </a-menu-item>
                    <a-menu-item key="MA">
                      <span>移动平均线</span>
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button>
                  添加指标 <DownOutlined />
                </a-button>
              </a-dropdown>

              <a-button @click="enableDrawing" :type="drawingMode ? 'primary' : 'default'">
                {{ drawingMode ? '退出绘图' : '绘图工具' }}
              </a-button>

              <a-button @click="refreshChart" :loading="loadingChart" type="primary">
                刷新数据
              </a-button>

              <a-badge :dot="true" :color="priceUpdateInterval ? 'green' : 'red'">
                <span style="margin-left: 8px;">
                  {{ priceUpdateInterval ? '实时更新' : '已停止' }}
                </span>
              </a-badge>
            </a-space>
          </div>

          <!-- K线图容器 -->
          <div id="candlestick-chart" style="height: 500px; width: 100%;"></div>

          <!-- 技术指标图表 -->
          <div v-if="showIndicators" style="margin-top: 16px;">
            <a-row :gutter="16">
              <a-col :span="8">
                <div id="volume-chart" style="height: 200px; width: 100%;"></div>
              </a-col>
              <a-col :span="8">
                <div id="rsi-chart" style="height: 200px; width: 100%;"></div>
              </a-col>
              <a-col :span="8">
                <div id="macd-chart" style="height: 200px; width: 100%;"></div>
              </a-col>
            </a-row>
            <a-row :gutter="16" style="margin-top: 16px;">
              <a-col :span="12">
                <div id="kdj-chart" style="height: 200px; width: 100%;"></div>
              </a-col>
              <a-col :span="12">
                <div id="boll-chart" style="height: 200px; width: 100%;"></div>
              </a-col>
            </a-row>
          </div>
        </a-card>

        <!-- 交易信号和快速交易 -->
        <a-card title="AI交易信号">
          <a-tabs>
            <a-tab-pane key="signals" tab="交易信号">
              <a-table
                :dataSource="tradingSignals"
                :columns="signalColumns"
                size="small"
                :pagination="{ pageSize: 5 }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'signal'">
                    <a-tag :color="record.signal === 'BUY' ? 'green' : record.signal === 'SELL' ? 'red' : 'orange'">
                      {{ record.signal }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'strength'">
                    <a-progress
                      :percent="record.strength * 100"
                      size="small"
                      :stroke-color="record.strength > 0.7 ? '#52c41a' : record.strength > 0.4 ? '#faad14' : '#ff4d4f'"
                    />
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <a-button
                      size="small"
                      :type="record.signal === 'BUY' ? 'primary' : 'danger'"
                      @click="quickTrade(record)"
                    >
                      一键{{ record.signal === 'BUY' ? '买入' : '卖出' }}
                    </a-button>
                  </template>
                </template>
              </a-table>
            </a-tab-pane>

            <a-tab-pane key="quicktrade" tab="快速交易">
              <a-form layout="inline" style="margin-bottom: 16px;">
                <a-form-item label="交易量">
                  <a-input-number
                    v-model:value="quickTradeAmount"
                    placeholder="请输入数量"
                    style="width: 120px;"
                    :min="0.001"
                    :step="0.001"
                  />
                </a-form-item>
                <a-form-item label="价格类型">
                  <a-select v-model:value="quickTradeType" style="width: 100px;">
                    <a-select-option value="market">市价</a-select-option>
                    <a-select-option value="limit">限价</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item v-if="quickTradeType === 'limit'" label="价格">
                  <a-input-number
                    v-model:value="quickTradePrice"
                    placeholder="请输入价格"
                    style="width: 120px;"
                    :min="0.01"
                    :step="0.01"
                  />
                </a-form-item>
              </a-form>

              <a-space>
                <a-button
                  type="primary"
                  @click="quickBuy"
                  :loading="trading"
                  style="background: #52c41a; border-color: #52c41a;"
                >
                  一键买入
                </a-button>
                <a-button
                  danger
                  @click="quickSell"
                  :loading="trading"
                >
                  一键卖出
                </a-button>
                <a-button @click="closeAllPositions" :loading="trading">
                  全部平仓
                </a-button>
              </a-space>
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </a-col>

      <!-- 右侧信息面板 -->
      <a-col :span="6">
        <!-- 实时价格 -->
        <a-card title="实时行情" style="margin-bottom: 16px;">
          <div v-if="currentPrice">
            <a-statistic
              :title="selectedSymbol"
              :value="currentPrice.last"
              :precision="2"
              suffix="USDT"
              :value-style="{
                color: parseFloat(currentPrice.sodUtc8) >= 0 ? '#52c41a' : '#ff4d4f',
                fontSize: '24px'
              }"
            />
            <a-divider />
            <a-descriptions size="small" :column="1">
              <a-descriptions-item label="24h涨跌">
                <span :style="{ color: parseFloat(currentPrice.sodUtc8) >= 0 ? '#52c41a' : '#ff4d4f' }">
                  {{ currentPrice.sodUtc8 }}%
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="24h最高">{{ currentPrice.high24h }}</a-descriptions-item>
              <a-descriptions-item label="24h最低">{{ currentPrice.low24h }}</a-descriptions-item>
              <a-descriptions-item label="24h成交量">{{ currentPrice.vol24h }}</a-descriptions-item>
            </a-descriptions>
          </div>
          <a-skeleton v-else active />
        </a-card>

        <!-- 技术指标数值 -->
        <a-card title="技术指标" style="margin-bottom: 16px;">
          <a-descriptions size="small" :column="1">
            <a-descriptions-item label="RSI(14)">
              <span :style="{ color: technicalIndicators.rsi > 70 ? '#ff4d4f' : technicalIndicators.rsi < 30 ? '#52c41a' : '#1890ff' }">
                {{ technicalIndicators.rsi.toFixed(2) }}
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="MACD">
              <span :style="{ color: technicalIndicators.macd > 0 ? '#52c41a' : '#ff4d4f' }">
                {{ technicalIndicators.macd.toFixed(4) }}
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="MA20">{{ technicalIndicators.ma20.toFixed(2) }}</a-descriptions-item>
            <a-descriptions-item label="MA50">{{ technicalIndicators.ma50.toFixed(2) }}</a-descriptions-item>
            <a-descriptions-item label="布林带上轨">{{ technicalIndicators.bbUpper.toFixed(2) }}</a-descriptions-item>
            <a-descriptions-item label="布林带下轨">{{ technicalIndicators.bbLower.toFixed(2) }}</a-descriptions-item>
            <a-descriptions-item label="KDJ-K">{{ technicalIndicators.kdjK.toFixed(2) }}</a-descriptions-item>
            <a-descriptions-item label="KDJ-D">{{ technicalIndicators.kdjD.toFixed(2) }}</a-descriptions-item>
            <a-descriptions-item label="KDJ-J">{{ technicalIndicators.kdjJ.toFixed(2) }}</a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 市场情绪 -->
        <a-card title="市场情绪" style="margin-bottom: 16px;">
          <div style="text-align: center;">
            <a-progress
              type="circle"
              :percent="marketSentiment.score"
              :stroke-color="marketSentiment.score > 60 ? '#52c41a' : marketSentiment.score > 40 ? '#faad14' : '#ff4d4f'"
            />
            <div style="margin-top: 16px;">
              <a-tag :color="marketSentiment.score > 60 ? 'green' : marketSentiment.score > 40 ? 'orange' : 'red'">
                {{ marketSentiment.label }}
              </a-tag>
            </div>
            <div style="margin-top: 8px; font-size: 12px; color: #666;">
              基于技术指标和成交量分析
            </div>
          </div>
        </a-card>

        <!-- 交易信号统计 -->
        <TradingSignals
          :chart-data="chartData"
          :technical-indicators="technicalIndicators"
          :current-price="currentPrice"
        />

        <!-- 调试组件 -->
        <TradingSignalsDebug
          :technical-indicators="technicalIndicators"
          :current-price="currentPrice"
          @update-data="handleDebugDataUpdate"
          style="margin-top: 16px;"
        />
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { DownOutlined } from '@ant-design/icons-vue'
import axios from 'axios'
import * as echarts from 'echarts'
import TradingSignals from '../components/TradingSignals.vue'
import TradingSignalsDebug from '../components/TradingSignalsDebug.vue'

// 响应式数据
const selectedSymbol = ref('BTC-USDT-SWAP')
const selectedTimeframe = ref('1H')
const loadingChart = ref(false)
const showIndicators = ref(true)
const currentPrice = ref(null)
const chartData = ref([])
const tradingSignals = ref([])

// 新增功能数据
const drawingMode = ref(false)
const quickTradeAmount = ref(0.01)
const quickTradeType = ref('market')
const quickTradePrice = ref(0)
const trading = ref(false)
const activeIndicators = ref(['RSI', 'VOLUME'])

// 技术指标数据
const technicalIndicators = ref({
  rsi: 50,
  macd: 0,
  // 移动平均线
  ma5: 0,
  ma10: 0,
  ma20: 0,
  ma50: 0,
  // 指数移动平均线
  ema5: 0,
  ema10: 0,
  ema20: 0,
  ema50: 0,
  // 布林带
  bbUpper: 0,
  bbLower: 0,
  // KDJ指标
  kdjK: 50,
  kdjD: 50,
  kdjJ: 50
})

// 市场情绪
const marketSentiment = ref({
  score: 50,
  label: '中性'
})

// 交易信号表格列
const signalColumns = [
  { title: '时间', dataIndex: 'time', key: 'time' },
  { title: '信号', dataIndex: 'signal', key: 'signal' },
  { title: '价格', dataIndex: 'price', key: 'price' },
  { title: '强度', dataIndex: 'strength', key: 'strength' },
  { title: '描述', dataIndex: 'description', key: 'description' },
  { title: '操作', key: 'action' }
]

// 图表实例
let candlestickChart = null
let volumeChart = null
let rsiChart = null
let macdChart = null
let kdjChart = null
let bollChart = null
let priceUpdateInterval = null

// 初始化图表
const initCharts = async () => {
  await nextTick()

  // 初始化K线图
  const candlestickContainer = document.getElementById('candlestick-chart')
  if (candlestickContainer && !candlestickChart) {
    candlestickChart = echarts.init(candlestickContainer)

    // K线图配置
    const candlestickOption = {
      title: {
        text: `${selectedSymbol.value} K线图`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['K线', 'MA5', 'MA10', 'MA20'],
        top: 30
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '15%',
        top: '15%'
      },
      xAxis: {
        type: 'category',
        data: [],
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        splitLine: { show: false },
        splitNumber: 20,
        min: 'dataMin',
        max: 'dataMax'
      },
      yAxis: {
        scale: true,
        splitArea: {
          show: true
        }
      },
      dataZoom: [
        {
          type: 'inside',
          start: 50,
          end: 100
        },
        {
          show: true,
          type: 'slider',
          top: '90%',
          start: 50,
          end: 100
        }
      ],
      series: [
        {
          name: 'K线',
          type: 'candlestick',
          data: [],
          itemStyle: {
            color: '#ec0000',
            color0: '#00da3c',
            borderColor: '#8A0000',
            borderColor0: '#008F28'
          }
        },
        {
          name: 'MA5',
          type: 'line',
          data: [],
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 1
          },
          symbol: 'none'
        },
        {
          name: 'MA10',
          type: 'line',
          data: [],
          smooth: true,
          lineStyle: {
            color: '#52c41a',
            width: 1
          },
          symbol: 'none'
        },
        {
          name: 'MA20',
          type: 'line',
          data: [],
          smooth: true,
          lineStyle: {
            color: '#faad14',
            width: 1
          },
          symbol: 'none'
        }
      ]
    }

    candlestickChart.setOption(candlestickOption)
  }

  // 初始化成交量图
  if (showIndicators.value) {
    const volumeContainer = document.getElementById('volume-chart')
    if (volumeContainer && !volumeChart) {
      volumeChart = echarts.init(volumeContainer)

      const volumeOption = {
        title: {
          text: '成交量',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%',
          top: '15%'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '成交量',
            type: 'bar',
            data: [],
            itemStyle: {
              color: '#1890ff'
            }
          }
        ]
      }

      volumeChart.setOption(volumeOption)
    }

    // 初始化RSI图
    const rsiContainer = document.getElementById('rsi-chart')
    if (rsiContainer && !rsiChart) {
      rsiChart = echarts.init(rsiContainer)

      const rsiOption = {
        title: {
          text: 'RSI指标',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%',
          top: '15%'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100,
          axisLine: {
            lineStyle: {
              color: '#999'
            }
          }
        },
        series: [
          {
            name: 'RSI',
            type: 'line',
            data: [],
            lineStyle: {
              color: '#ff7300'
            }
          }
        ]
      }

      rsiChart.setOption(rsiOption)
    }

    // 初始化MACD图
    const macdContainer = document.getElementById('macd-chart')
    if (macdContainer && !macdChart) {
      macdChart = echarts.init(macdContainer)

      const macdOption = {
        title: {
          text: 'MACD指标',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%',
          top: '15%'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: 'MACD',
            type: 'line',
            data: [],
            lineStyle: {
              color: '#1890ff'
            }
          }
        ]
      }

      macdChart.setOption(macdOption)
    }

    // 初始化KDJ图
    const kdjContainer = document.getElementById('kdj-chart')
    if (kdjContainer && !kdjChart) {
      kdjChart = echarts.init(kdjContainer)

      const kdjOption = {
        title: {
          text: 'KDJ指标',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['K', 'D', 'J'],
          top: 30
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%',
          top: '20%'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100
        },
        series: [
          {
            name: 'K',
            type: 'line',
            data: [],
            lineStyle: {
              color: '#1890ff'
            }
          },
          {
            name: 'D',
            type: 'line',
            data: [],
            lineStyle: {
              color: '#52c41a'
            }
          },
          {
            name: 'J',
            type: 'line',
            data: [],
            lineStyle: {
              color: '#faad14'
            }
          }
        ]
      }

      kdjChart.setOption(kdjOption)
    }

    // 初始化布林带图
    const bollContainer = document.getElementById('boll-chart')
    if (bollContainer && !bollChart) {
      bollChart = echarts.init(bollContainer)

      const bollOption = {
        title: {
          text: '布林带指标',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['上轨', '中轨', '下轨'],
          top: 30
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%',
          top: '20%'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '上轨',
            type: 'line',
            data: [],
            lineStyle: {
              color: '#ff4d4f'
            }
          },
          {
            name: '中轨',
            type: 'line',
            data: [],
            lineStyle: {
              color: '#1890ff'
            }
          },
          {
            name: '下轨',
            type: 'line',
            data: [],
            lineStyle: {
              color: '#52c41a'
            }
          }
        ]
      }

      bollChart.setOption(bollOption)
    }
  }
}

// 时间格式化函数
const formatChartTime = (timestamp, timeframe) => {
  const date = new Date(timestamp)

  // 根据时间周期选择不同的显示格式
  switch (timeframe) {
    case '1m':
    case '5m':
    case '15m':
    case '30m':
      // 分钟级别：显示时:分
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    case '1H':
    case '2H':
    case '4H':
    case '6H':
    case '12H':
      // 小时级别：显示月-日 时:00
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    case '1D':
    case '3D':
    case '1W':
    case '1M':
      // 日级别及以上：显示月-日
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    default:
      // 默认显示完整时间
      return date.toLocaleString('zh-CN')
  }
}

// 获取K线数据
const loadChartData = async () => {
  loadingChart.value = true
  try {
    console.log('正在获取K线数据...', {
      instId: selectedSymbol.value,
      bar: selectedTimeframe.value,
      limit: 100
    })

    // 获取更多最新数据，并按时间排序
    const response = await axios.get(`/api/okx/market/candles`, {
      params: {
        instId: selectedSymbol.value,
        bar: selectedTimeframe.value,
        limit: 200, // 增加数据量以获取更多历史数据
        after: '', // 获取最新数据
        before: '' // 不限制结束时间
      }
    })

    console.log('K线数据响应:', response.data)

    if (response.data.code === '0' && response.data.data) {
      // OKX返回的数据是按时间倒序的，需要反转为正序
      const rawData = response.data.data
      chartData.value = rawData.reverse() // 反转为时间正序

      console.log('获取到K线数据:', chartData.value.length, '条')
      console.log('最新数据时间:', new Date(parseInt(chartData.value[chartData.value.length - 1][0])).toLocaleString())
      console.log('最早数据时间:', new Date(parseInt(chartData.value[0][0])).toLocaleString())

      calculateTechnicalIndicators()
      updateCharts()
    } else {
      console.warn('K线数据格式错误:', response.data)
      generateMockData()
    }
  } catch (error) {
    console.error('获取K线数据失败:', error)
    message.error('获取K线数据失败: ' + error.message)
    // 使用模拟数据
    generateMockData()
  } finally {
    loadingChart.value = false
  }
}

// 获取实时价格
const loadCurrentPrice = async () => {
  try {
    const response = await axios.get(`/api/okx/market/ticker?instId=${selectedSymbol.value}`)
    if (response.data.code === 0) {
      currentPrice.value = response.data.data[0]
    }
  } catch (error) {
    console.warn('获取实时价格失败:', error.message)
    // 使用模拟数据
    currentPrice.value = {
      instId: selectedSymbol.value,
      last: '50000.00',
      sodUtc8: '2.5',
      high24h: '51000.00',
      low24h: '49000.00',
      vol24h: '1234567'
    }
  }
}

// 生成模拟数据
const generateMockData = () => {
  const mockData = []
  let basePrice = 50000

  for (let i = 0; i < 100; i++) {
    const timestamp = Date.now() - (100 - i) * 60 * 60 * 1000
    const open = basePrice + (Math.random() - 0.5) * 1000
    const close = open + (Math.random() - 0.5) * 500
    const high = Math.max(open, close) + Math.random() * 200
    const low = Math.min(open, close) - Math.random() * 200
    const volume = Math.random() * 1000000

    mockData.push([timestamp, open, high, low, close, volume])
    basePrice = close
  }

  chartData.value = mockData
  calculateTechnicalIndicators()
}

// 计算技术指标
const calculateTechnicalIndicators = () => {
  if (chartData.value.length === 0) return

  const closes = chartData.value.map(item => parseFloat(item[4]))
  const highs = chartData.value.map(item => parseFloat(item[2]))
  const lows = chartData.value.map(item => parseFloat(item[3]))
  const volumes = chartData.value.map(item => parseFloat(item[5]))

  // 计算RSI
  technicalIndicators.value.rsi = calculateRSI(closes, 14)

  // 计算移动平均线（添加缺失的MA5和MA10）
  technicalIndicators.value.ma5 = calculateMA(closes, 5)
  technicalIndicators.value.ma10 = calculateMA(closes, 10)
  technicalIndicators.value.ma20 = calculateMA(closes, 20)
  technicalIndicators.value.ma50 = calculateMA(closes, 50)

  // 计算EMA（指数移动平均）
  technicalIndicators.value.ema5 = calculateEMA(closes, 5)
  technicalIndicators.value.ema10 = calculateEMA(closes, 10)
  technicalIndicators.value.ema20 = calculateEMA(closes, 20)
  technicalIndicators.value.ema50 = calculateEMA(closes, 50)

  // 调试信息
  console.log('技术指标计算结果:', {
    数据点数: closes.length,
    最后价格: closes[closes.length - 1],
    MA5: technicalIndicators.value.ma5,
    MA10: technicalIndicators.value.ma10,
    MA20: technicalIndicators.value.ma20,
    EMA5: technicalIndicators.value.ema5,
    EMA10: technicalIndicators.value.ema10,
    EMA20: technicalIndicators.value.ema20,
    RSI: technicalIndicators.value.rsi
  })

  // 计算MACD
  technicalIndicators.value.macd = calculateMACD(closes)

  // 计算布林带
  const bb = calculateBollingerBands(closes, 20)
  technicalIndicators.value.bbUpper = bb.upper
  technicalIndicators.value.bbLower = bb.lower

  // 计算KDJ
  const kdj = calculateKDJ(highs, lows, closes, 9, 3, 3)
  technicalIndicators.value.kdjK = kdj.K
  technicalIndicators.value.kdjD = kdj.D
  technicalIndicators.value.kdjJ = kdj.J

  // 计算市场情绪
  calculateMarketSentiment()
}

// 标准RSI计算方法
const calculateRSIArray = (prices, period = 14) => {
  const rsiArray = []

  if (prices.length < period + 1) {
    return prices.map(() => null)
  }

  // 计算价格变化
  const changes = []
  for (let i = 1; i < prices.length; i++) {
    changes.push(prices[i] - prices[i - 1])
  }

  // 初始化前14个为null
  for (let i = 0; i < period; i++) {
    rsiArray.push(null)
  }

  // 计算初始的平均收益和平均损失
  let gains = 0
  let losses = 0

  for (let i = 0; i < period; i++) {
    if (changes[i] > 0) {
      gains += changes[i]
    } else {
      losses += Math.abs(changes[i])
    }
  }

  let avgGain = gains / period
  let avgLoss = losses / period

  // 计算第一个RSI值
  if (avgLoss === 0) {
    rsiArray.push(avgGain === 0 ? 50 : 100)
  } else {
    const rs = avgGain / avgLoss
    const rsi = 100 - (100 / (1 + rs))
    rsiArray.push(rsi)
  }

  // 使用指数移动平均计算后续的RSI值
  for (let i = period; i < changes.length; i++) {
    const change = changes[i]
    const gain = change > 0 ? change : 0
    const loss = change < 0 ? Math.abs(change) : 0

    // 指数移动平均
    avgGain = (avgGain * (period - 1) + gain) / period
    avgLoss = (avgLoss * (period - 1) + loss) / period

    if (avgLoss === 0) {
      rsiArray.push(avgGain === 0 ? 50 : 100)
    } else {
      const rs = avgGain / avgLoss
      const rsi = 100 - (100 / (1 + rs))
      rsiArray.push(rsi)
    }
  }

  return rsiArray
}

// 简单的RSI计算（用于单个值）
const calculateRSI = (prices, period = 14) => {
  const rsiArray = calculateRSIArray(prices, period)
  return rsiArray[rsiArray.length - 1] || 50
}

// 移动平均线计算
const calculateMA = (prices, period) => {
  if (prices.length < period) return 0

  const sum = prices.slice(-period).reduce((a, b) => a + b, 0)
  return sum / period
}

// MACD计算（简化版）
const calculateMACD = (prices) => {
  if (prices.length < 26) return 0

  const ema12 = calculateEMA(prices, 12)
  const ema26 = calculateEMA(prices, 26)

  return ema12 - ema26
}

// EMA计算
const calculateEMA = (prices, period) => {
  if (prices.length < period) return 0

  const multiplier = 2 / (period + 1)
  let ema = prices[0]

  for (let i = 1; i < prices.length; i++) {
    ema = (prices[i] * multiplier) + (ema * (1 - multiplier))
  }

  return ema
}

// 布林带计算
const calculateBollingerBands = (prices, period) => {
  if (prices.length < period) return { upper: 0, lower: 0 }

  const ma = calculateMA(prices, period)
  const recentPrices = prices.slice(-period)

  const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - ma, 2), 0) / period
  const stdDev = Math.sqrt(variance)

  return {
    upper: ma + (stdDev * 2),
    lower: ma - (stdDev * 2)
  }
}

// KDJ计算
const calculateKDJ = (highs, lows, closes, period, k_period, d_period) => {
  if (closes.length < period) return { K: 50, D: 50, J: 50 }

  const rsv = []

  // 计算RSV
  for (let i = period - 1; i < closes.length; i++) {
    const periodHighs = highs.slice(i - period + 1, i + 1)
    const periodLows = lows.slice(i - period + 1, i + 1)
    const currentClose = closes[i]

    const highestHigh = Math.max(...periodHighs)
    const lowestLow = Math.min(...periodLows)

    const rsvValue = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100
    rsv.push(isNaN(rsvValue) ? 50 : rsvValue)
  }

  // 计算K值
  let K = 50
  const kValues = []
  for (let i = 0; i < rsv.length; i++) {
    K = (rsv[i] + (k_period - 1) * K) / k_period
    kValues.push(K)
  }

  // 计算D值
  let D = 50
  const dValues = []
  for (let i = 0; i < kValues.length; i++) {
    D = (kValues[i] + (d_period - 1) * D) / d_period
    dValues.push(D)
  }

  // 计算J值
  const latestK = kValues[kValues.length - 1] || 50
  const latestD = dValues[dValues.length - 1] || 50
  const J = 3 * latestK - 2 * latestD

  return {
    K: latestK,
    D: latestD,
    J: J
  }
}

// 计算市场情绪
const calculateMarketSentiment = () => {
  const rsi = technicalIndicators.value.rsi
  const currentPrice = parseFloat(chartData.value[chartData.value.length - 1][4])
  const ma20 = technicalIndicators.value.ma20

  let score = 50 // 基础分数

  // RSI影响
  if (rsi > 70) score -= 20
  else if (rsi < 30) score += 20
  else if (rsi > 50) score += 10
  else score -= 10

  // 价格相对于MA20的位置
  if (currentPrice > ma20) score += 15
  else score -= 15

  // MACD影响
  if (technicalIndicators.value.macd > 0) score += 10
  else score -= 10

  score = Math.max(0, Math.min(100, score))

  let label = '中性'
  if (score > 70) label = '极度贪婪'
  else if (score > 60) label = '贪婪'
  else if (score > 40) label = '中性'
  else if (score > 30) label = '恐惧'
  else label = '极度恐惧'

  marketSentiment.value = { score, label }
}

// 生成交易信号
const generateTradingSignals = () => {
  const signals = []
  const currentTime = new Date()

  // 基于技术指标生成信号
  if (technicalIndicators.value.rsi < 30) {
    signals.push({
      time: currentTime.toLocaleTimeString(),
      signal: 'BUY',
      price: currentPrice.value?.last || '50000',
      strength: 0.8,
      description: 'RSI超卖信号'
    })
  }

  if (technicalIndicators.value.rsi > 70) {
    signals.push({
      time: currentTime.toLocaleTimeString(),
      signal: 'SELL',
      price: currentPrice.value?.last || '50000',
      strength: 0.7,
      description: 'RSI超买信号'
    })
  }

  if (technicalIndicators.value.macd > 0) {
    signals.push({
      time: currentTime.toLocaleTimeString(),
      signal: 'BUY',
      price: currentPrice.value?.last || '50000',
      strength: 0.6,
      description: 'MACD金叉信号'
    })
  }

  tradingSignals.value = signals
}

// 更新图表
const updateCharts = () => {
  if (chartData.value.length === 0) return

  // 准备数据
  const dates = []
  const candleData = []
  const volumeData = []
  const rsiData = []
  const ma5Data = []
  const ma10Data = []
  const ma20Data = []

  // 获取所有收盘价
  const closes = chartData.value.map(item => parseFloat(item[4]))

  // 使用新的RSI计算方法生成整个数组
  const rsiArray = calculateRSIArray(closes, 14)

  // 调试信息
  console.log('RSI计算调试:', {
    数据点数: closes.length,
    最后5个价格: closes.slice(-5),
    最后5个RSI: rsiArray.slice(-5),
    RSI范围: {
      最小: Math.min(...rsiArray.filter(v => v !== null)),
      最大: Math.max(...rsiArray.filter(v => v !== null))
    }
  })

  chartData.value.forEach((item, index) => {
    const timestamp = parseInt(item[0])
    // 修复时间显示：显示完整的日期和时间
    const date = formatChartTime(timestamp, selectedTimeframe.value)
    const open = parseFloat(item[1])
    const high = parseFloat(item[2])
    const low = parseFloat(item[3])
    const close = parseFloat(item[4])
    const volume = parseFloat(item[5])

    dates.push(date)
    candleData.push([open, close, low, high])
    volumeData.push(volume)

    // 计算移动平均线
    if (index >= 4) {
      ma5Data.push(calculateMA(closes.slice(0, index + 1), 5))
    } else {
      ma5Data.push(null)
    }

    if (index >= 9) {
      ma10Data.push(calculateMA(closes.slice(0, index + 1), 10))
    } else {
      ma10Data.push(null)
    }

    if (index >= 19) {
      ma20Data.push(calculateMA(closes.slice(0, index + 1), 20))
    } else {
      ma20Data.push(null)
    }

    // 使用预计算的RSI数组
    rsiData.push(rsiArray[index])
  })

  // 更新K线图（包含移动平均线）
  if (candlestickChart) {
    const series = [
      {
        name: 'K线',
        type: 'candlestick',
        data: candleData,
        itemStyle: {
          color: '#ec0000',
          color0: '#00da3c',
          borderColor: '#8A0000',
          borderColor0: '#008F28'
        }
      },
      {
        name: 'MA5',
        type: 'line',
        data: ma5Data,
        smooth: true,
        lineStyle: {
          color: '#1890ff',
          width: 1
        },
        symbol: 'none'
      },
      {
        name: 'MA10',
        type: 'line',
        data: ma10Data,
        smooth: true,
        lineStyle: {
          color: '#52c41a',
          width: 1
        },
        symbol: 'none'
      },
      {
        name: 'MA20',
        type: 'line',
        data: ma20Data,
        smooth: true,
        lineStyle: {
          color: '#faad14',
          width: 1
        },
        symbol: 'none'
      }
    ]

    candlestickChart.setOption({
      legend: {
        data: ['K线', 'MA5', 'MA10', 'MA20']
      },
      xAxis: {
        data: dates
      },
      series: series
    })
  }

  // 更新成交量图
  if (volumeChart && showIndicators.value) {
    volumeChart.setOption({
      xAxis: {
        data: dates
      },
      series: [{
        data: volumeData
      }]
    })
  }

  // 更新RSI图
  if (rsiChart && showIndicators.value) {
    rsiChart.setOption({
      xAxis: {
        data: dates
      },
      series: [{
        data: rsiData
      }]
    })
  }

  // 更新MACD图
  if (macdChart && showIndicators.value) {
    const macdData = chartData.value.map((_, index) => {
      if (index >= 25) {
        const closes = chartData.value.slice(0, index + 1).map(d => parseFloat(d[4]))
        return calculateMACD(closes)
      }
      return 0
    })

    macdChart.setOption({
      xAxis: {
        data: dates
      },
      series: [{
        data: macdData
      }]
    })
  }

  // 更新KDJ图
  if (kdjChart && showIndicators.value) {
    const highs = chartData.value.map(item => parseFloat(item[2]))
    const lows = chartData.value.map(item => parseFloat(item[3]))

    const kData = []
    const dData = []
    const jData = []

    chartData.value.forEach((_, index) => {
      if (index >= 8) {
        const kdj = calculateKDJ(
          highs.slice(0, index + 1),
          lows.slice(0, index + 1),
          closes.slice(0, index + 1),
          9, 3, 3
        )
        kData.push(kdj.K)
        dData.push(kdj.D)
        jData.push(kdj.J)
      } else {
        kData.push(50)
        dData.push(50)
        jData.push(50)
      }
    })

    kdjChart.setOption({
      xAxis: {
        data: dates
      },
      series: [
        { data: kData },
        { data: dData },
        { data: jData }
      ]
    })
  }

  // 更新布林带图
  if (bollChart && showIndicators.value) {
    const upperData = []
    const middleData = []
    const lowerData = []

    chartData.value.forEach((_, index) => {
      if (index >= 19) {
        const bb = calculateBollingerBands(closes.slice(0, index + 1), 20)
        const middle = calculateMA(closes.slice(0, index + 1), 20)
        upperData.push(bb.upper)
        middleData.push(middle)
        lowerData.push(bb.lower)
      } else {
        upperData.push(null)
        middleData.push(null)
        lowerData.push(null)
      }
    })

    bollChart.setOption({
      xAxis: {
        data: dates
      },
      series: [
        { data: upperData },
        { data: middleData },
        { data: lowerData }
      ]
    })
  }

  generateTradingSignals()
}

// 刷新图表
const refreshChart = () => {
  loadChartData()
  loadCurrentPrice()
}

// 切换指标显示
const toggleIndicators = () => {
  showIndicators.value = !showIndicators.value

  if (showIndicators.value) {
    // 显示指标时初始化图表
    setTimeout(() => {
      initCharts()
      updateCharts()
    }, 100)
  } else {
    // 隐藏指标时销毁图表
    if (volumeChart) {
      volumeChart.dispose()
      volumeChart = null
    }
    if (rsiChart) {
      rsiChart.dispose()
      rsiChart = null
    }
    if (macdChart) {
      macdChart.dispose()
      macdChart = null
    }
    if (kdjChart) {
      kdjChart.dispose()
      kdjChart = null
    }
    if (bollChart) {
      bollChart.dispose()
      bollChart = null
    }
  }
}

// 交易对变化
const onSymbolChange = () => {
  refreshChart()
}

// 时间周期变化
const onTimeframeChange = () => {
  loadChartData()
}

// 启动实时价格更新
const startPriceUpdates = () => {
  // 立即更新一次
  loadCurrentPrice()

  priceUpdateInterval = setInterval(() => {
    loadCurrentPrice()
    // 每分钟更新一次K线数据（仅对于小时级别及以下）
    if (['1m', '5m', '15m', '30m', '1H'].includes(selectedTimeframe.value)) {
      // 每30秒更新一次K线数据
      const now = Date.now()
      if (now % 30000 < 5000) {
        console.log('定时更新K线数据...')
        loadChartData()
      }
    }
  }, 5000) // 每5秒更新一次
}

// 停止实时价格更新
const stopPriceUpdates = () => {
  if (priceUpdateInterval) {
    clearInterval(priceUpdateInterval)
    priceUpdateInterval = null
  }
}

// 新增交易功能

// 添加指标
const addIndicator = ({ key }) => {
  if (!activeIndicators.value.includes(key)) {
    activeIndicators.value.push(key)
    message.success(`已添加${key}指标`)
    setTimeout(() => {
      initCharts()
      updateCharts()
    }, 100)
  } else {
    message.info(`${key}指标已存在`)
  }
}

// 启用绘图模式
const enableDrawing = () => {
  drawingMode.value = !drawingMode.value
  if (drawingMode.value) {
    message.info('绘图模式已启用，可在图表上绘制趋势线和支撑位')
  } else {
    message.info('已退出绘图模式')
  }
}

// 快速交易功能
const quickTrade = async (signal) => {
  trading.value = true
  try {
    const orderData = {
      instId: selectedSymbol.value,
      side: signal.signal.toLowerCase(),
      ordType: 'market',
      sz: quickTradeAmount.value.toString()
    }

    // 这里应该调用真实的交易API
    console.log('执行交易:', orderData)
    message.success(`成功执行${signal.signal}信号交易`)

  } catch (error) {
    message.error('交易执行失败: ' + error.message)
  } finally {
    trading.value = false
  }
}

const quickBuy = async () => {
  trading.value = true
  try {
    const orderData = {
      instId: selectedSymbol.value,
      side: 'buy',
      ordType: quickTradeType.value,
      sz: quickTradeAmount.value.toString()
    }

    if (quickTradeType.value === 'limit') {
      orderData.px = quickTradePrice.value.toString()
    }

    console.log('执行买入交易:', orderData)
    message.success('买入订单已提交')

  } catch (error) {
    message.error('买入失败: ' + error.message)
  } finally {
    trading.value = false
  }
}

const quickSell = async () => {
  trading.value = true
  try {
    const orderData = {
      instId: selectedSymbol.value,
      side: 'sell',
      ordType: quickTradeType.value,
      sz: quickTradeAmount.value.toString()
    }

    if (quickTradeType.value === 'limit') {
      orderData.px = quickTradePrice.value.toString()
    }

    console.log('执行卖出交易:', orderData)
    message.success('卖出订单已提交')

  } catch (error) {
    message.error('卖出失败: ' + error.message)
  } finally {
    trading.value = false
  }
}

const closeAllPositions = async () => {
  trading.value = true
  try {
    console.log('执行全部平仓')
    message.success('全部平仓订单已提交')
  } catch (error) {
    message.error('平仓失败: ' + error.message)
  } finally {
    trading.value = false
  }
}

// 页面加载时初始化
onMounted(async () => {
  await initCharts()
  await loadChartData()
  loadCurrentPrice()
  startPriceUpdates()
})

// 调试数据更新处理
const handleDebugDataUpdate = ({ indicators, price }) => {
  console.log('收到调试数据更新:', { indicators, price })

  if (indicators && Object.keys(indicators).length > 0) {
    // 更新技术指标
    Object.assign(technicalIndicators.value, indicators)
  }

  if (price) {
    // 更新当前价格
    currentPrice.value = price
  }

  message.success('调试数据已更新')
}

// 页面卸载时清理
onUnmounted(() => {
  stopPriceUpdates()

  // 销毁所有图表实例
  if (candlestickChart) {
    candlestickChart.dispose()
    candlestickChart = null
  }
  if (volumeChart) {
    volumeChart.dispose()
    volumeChart = null
  }
  if (rsiChart) {
    rsiChart.dispose()
    rsiChart = null
  }
  if (macdChart) {
    macdChart.dispose()
    macdChart = null
  }
  if (kdjChart) {
    kdjChart.dispose()
    kdjChart = null
  }
  if (bollChart) {
    bollChart.dispose()
    bollChart = null
  }
})
</script>

<style scoped>
.advanced-charts {
  padding: 24px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

#candlestick-chart,
#volume-chart,
#rsi-chart {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}
</style>
