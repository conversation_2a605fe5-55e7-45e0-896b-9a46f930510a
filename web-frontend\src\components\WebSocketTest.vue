<template>
  <div class="websocket-test">
    <div class="header">
      <h2>WebSocket 实时推送测试</h2>
      <div class="status-indicator" :class="connectionStatus">
        {{ statusText }}
      </div>
    </div>

    <div class="controls">
      <button @click="connect" :disabled="isConnected" class="btn btn-primary">
        连接 WebSocket
      </button>
      <button @click="disconnect" :disabled="!isConnected" class="btn btn-secondary">
        断开连接
      </button>
      <button @click="clearMessages" class="btn btn-warning">
        清空消息
      </button>
    </div>

    <div class="subscription-controls">
      <h3>订阅控制</h3>
      <div class="subscription-form">
        <select v-model="selectedChannel" class="form-select">
          <option value="">选择频道类型</option>
          <option value="tickers">Ticker 数据</option>
          <option value="candle">K线数据</option>
        </select>
        <select v-model="selectedBar" v-if="selectedChannel === 'candle'" class="form-select">
          <option value="1m">1分钟</option>
          <option value="5m">5分钟</option>
          <option value="15m">15分钟</option>
          <option value="1H">1小时</option>
          <option value="1D">1天</option>
        </select>
        <input 
          v-model="instrumentId" 
          placeholder="交易对 (如: BTC-USDT)" 
          class="form-input"
        />
        <button @click="subscribe" :disabled="!canSubscribe" class="btn btn-success">
          订阅
        </button>
        <button @click="unsubscribe" :disabled="!isConnected" class="btn btn-danger">
          取消订阅
        </button>
      </div>
    </div>

    <div class="statistics">
      <div class="stat-item">
        <span class="stat-label">连接时长:</span>
        <span class="stat-value">{{ connectionDuration }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">消息总数:</span>
        <span class="stat-value">{{ messageCount }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">消息速率:</span>
        <span class="stat-value">{{ messageRate }} msg/s</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">平均延迟:</span>
        <span class="stat-value">{{ averageLatency }} ms</span>
      </div>
    </div>

    <div class="message-container">
      <h3>实时消息 (最新 {{ maxMessages }} 条)</h3>
      <div class="message-list" ref="messageList">
        <div 
          v-for="(message, index) in displayMessages" 
          :key="index"
          class="message-item"
          :class="message.type"
        >
          <div class="message-header">
            <span class="timestamp">{{ formatTime(message.timestamp) }}</span>
            <span class="message-type">{{ message.type }}</span>
            <span v-if="message.latency" class="latency">{{ message.latency }}ms</span>
          </div>
          <div class="message-content">
            <pre>{{ formatMessage(message.data) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useWebSocket } from '../services/webSocketService.js'
import webSocketService from '../services/webSocketService.js'

export default {
  name: 'WebSocketTest',
  setup() {
    const { 
      connect: wsConnect, 
      disconnect: wsDisconnect, 
      subscribe: wsSubscribe,
      unsubscribe: wsUnsubscribe,
      connectionStatus,
      isConnected
    } = useWebSocket()

    // 状态管理
    const messages = ref([])
    const selectedChannel = ref('candle')
    const selectedBar = ref('1m')
    const instrumentId = ref('BTC-USDT')
    const maxMessages = ref(100)
    const connectionStartTime = ref(null)
    const connectionDuration = ref('00:00:00')
    const messageCount = ref(0)
    const messageRate = ref(0)
    const latencies = ref([])
    const messageList = ref(null)

    // 计算属性
    const statusText = computed(() => {
      const statusMap = {
        'disconnected': '已断开',
        'connecting': '连接中...',
        'connected': '已连接',
        'reconnecting': '重连中...',
        'error': '连接错误'
      }
      return statusMap[connectionStatus?.value] || '未知状态'
    })

    const canSubscribe = computed(() => {
      if (selectedChannel.value === 'candle') {
        return isConnected?.value && selectedChannel.value && selectedBar.value && instrumentId.value
      }
      return isConnected?.value && selectedChannel.value && instrumentId.value
    })

    const displayMessages = computed(() => {
      return messages.value.slice(-maxMessages.value).reverse()
    })

    const averageLatency = computed(() => {
      if (latencies.value.length === 0) return 0
      const sum = latencies.value.reduce((a, b) => a + b, 0)
      return Math.round(sum / latencies.value.length)
    })

    // 方法
    const connect = async () => {
      try {
        // 如果有选择的频道和交易对，直接连接并订阅
        if (canSubscribe.value) {
          let channelName = selectedChannel.value
          if (selectedChannel.value === 'candle') {
            channelName = `candle${selectedBar.value}`
          }
          
          const channels = [{
            channel: channelName,
            instId: instrumentId.value
          }]
          
          await wsConnect(channels)
          const displayName = selectedChannel.value === 'candle' ? `${selectedChannel.value}(${selectedBar.value})` : selectedChannel.value
          addMessage('system', `连接成功并订阅: ${displayName} - ${instrumentId.value}`, 'success')
        } else {
          // 如果没有选择频道，只连接不订阅
          await wsConnect([])
          addMessage('system', '连接成功', 'success')
        }
        connectionStartTime.value = Date.now()
      } catch (error) {
        addMessage('system', `连接失败: ${error.message}`, 'error')
      }
    }

    const disconnect = () => {
      wsDisconnect()
      connectionStartTime.value = null
      addMessage('system', '连接已断开', 'info')
    }

    const subscribe = () => {
      if (!canSubscribe.value) return
      
      let channelName = selectedChannel.value
      if (selectedChannel.value === 'candle') {
        channelName = `candle${selectedBar.value}`
      }
      
      const channels = [{
        channel: channelName,
        instId: instrumentId.value
      }]
      
      // 发送订阅请求到已连接的WebSocket
      if (webSocketService && webSocketService.sendSubscription) {
        webSocketService.sendSubscription(channels)
        const displayName = selectedChannel.value === 'candle' ? `${selectedChannel.value}(${selectedBar.value})` : selectedChannel.value
        addMessage('system', `订阅: ${displayName} - ${instrumentId.value}`, 'info')
        
        // 添加本地监听器来接收数据
        webSocketService.subscribe(channelName, (data) => {
          addMessage('market', data, 'success')
        })
      } else {
        addMessage('system', 'WebSocket服务不可用', 'error')
      }
    }

    const unsubscribe = () => {
      wsUnsubscribe()
      addMessage('system', '取消所有订阅', 'info')
    }

    const clearMessages = () => {
      messages.value = []
      messageCount.value = 0
      latencies.value = []
    }

    const addMessage = (type, data, level = 'info') => {
      const message = {
        type: level,
        timestamp: Date.now(),
        data: data
      }
      
      // 计算延迟
      if (data && data.received_at) {
        const latency = Date.now() - data.received_at
        message.latency = latency
        latencies.value.push(latency)
        if (latencies.value.length > 100) {
          latencies.value = latencies.value.slice(-100)
        }
      }
      
      messages.value.push(message)
      messageCount.value++
      
      // 限制消息数量
      if (messages.value.length > maxMessages.value * 2) {
        messages.value = messages.value.slice(-maxMessages.value)
      }
      
      // 自动滚动到底部
      setTimeout(() => {
        if (messageList.value) {
          messageList.value.scrollTop = 0
        }
      }, 10)
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString()
    }

    const formatMessage = (data) => {
      if (typeof data === 'string') return data
      return JSON.stringify(data, null, 2)
    }

    // 定时器
    let durationTimer = null
    let rateTimer = null
    let lastMessageCount = 0

    const updateDuration = () => {
      if (connectionStartTime.value) {
        const elapsed = Date.now() - connectionStartTime.value
        const hours = Math.floor(elapsed / 3600000)
        const minutes = Math.floor((elapsed % 3600000) / 60000)
        const seconds = Math.floor((elapsed % 60000) / 1000)
        connectionDuration.value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      } else {
        connectionDuration.value = '00:00:00'
      }
    }

    const updateMessageRate = () => {
      const currentCount = messageCount.value
      const rate = currentCount - lastMessageCount
      messageRate.value = rate
      lastMessageCount = currentCount
    }

    // 生命周期
    onMounted(() => {
      // 监听WebSocket消息
      window.addEventListener('websocket-message', (event) => {
        addMessage('data', event.detail)
      })
      
      window.addEventListener('websocket-error', (event) => {
        addMessage('system', `错误: ${event.detail}`, 'error')
      })
      
      window.addEventListener('websocket-close', (event) => {
        addMessage('system', '连接关闭', 'warning')
        connectionStartTime.value = null
      })
      
      // 启动定时器
      durationTimer = setInterval(updateDuration, 1000)
      rateTimer = setInterval(updateMessageRate, 1000)
      
      // 自动连接并订阅
      setTimeout(async () => {
        try {
          await wsConnect([])
          if (selectedChannel.value && instrumentId.value) {
            subscribe()
          }
        } catch (error) {
          addMessage('system', `自动连接失败: ${error.message}`, 'error')
        }
      }, 1000)
    })

    onUnmounted(() => {
      if (durationTimer) clearInterval(durationTimer)
      if (rateTimer) clearInterval(rateTimer)
      disconnect()
    })

    return {
      // 状态
      connectionStatus,
      isConnected,
      statusText,
      selectedChannel,
      selectedBar,
      instrumentId,
      maxMessages,
      connectionDuration,
      messageCount,
      messageRate,
      averageLatency,
      displayMessages,
      canSubscribe,
      messageList,
      
      // 方法
      connect,
      disconnect,
      subscribe,
      unsubscribe,
      clearMessages,
      formatTime,
      formatMessage
    }
  }
}
</script>

<style scoped>
.websocket-test {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e0e0e0;
}

.header h2 {
  margin: 0;
  color: #333;
}

.status-indicator {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 12px;
}

.status-indicator.disconnected {
  background-color: #f8d7da;
  color: #721c24;
}

.status-indicator.connecting {
  background-color: #fff3cd;
  color: #856404;
}

.status-indicator.connected {
  background-color: #d4edda;
  color: #155724;
}

.status-indicator.reconnecting {
  background-color: #cce7ff;
  color: #004085;
}

.status-indicator.error {
  background-color: #f8d7da;
  color: #721c24;
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.subscription-controls {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.subscription-controls h3 {
  margin: 0 0 10px 0;
  color: #495057;
}

.subscription-form {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #545b62;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #1e7e34;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover:not(:disabled) {
  background-color: #e0a800;
}

.form-select, .form-input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.form-select {
  min-width: 150px;
}

.form-input {
  min-width: 200px;
}

.statistics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
  margin-bottom: 5px;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #495057;
}

.message-container {
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.message-container h3 {
  margin: 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  color: #495057;
}

.message-list {
  height: 400px;
  overflow-y: auto;
  padding: 10px;
}

.message-item {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 6px;
  border-left: 4px solid #dee2e6;
}

.message-item.success {
  background-color: #d4edda;
  border-left-color: #28a745;
}

.message-item.error {
  background-color: #f8d7da;
  border-left-color: #dc3545;
}

.message-item.warning {
  background-color: #fff3cd;
  border-left-color: #ffc107;
}

.message-item.info {
  background-color: #cce7ff;
  border-left-color: #007bff;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  font-size: 12px;
}

.timestamp {
  color: #6c757d;
}

.message-type {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  text-transform: uppercase;
  font-weight: bold;
}

.latency {
  color: #28a745;
  font-weight: bold;
}

.message-content {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.message-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .controls {
    flex-direction: column;
  }
  
  .subscription-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-select, .form-input {
    min-width: auto;
    width: 100%;
  }
  
  .statistics {
    grid-template-columns: 1fr;
  }
}
</style>