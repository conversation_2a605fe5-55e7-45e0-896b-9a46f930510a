<template>
  <a-card title="技术指标" size="small" class="technical-indicators-panel" :class="{ 'dark-theme': isDarkTheme }">
    <template #extra>
      <div class="header-actions">
        <!-- 错误状态显示 -->
        <a-tooltip v-if="hasError" :title="latestError?.message">
          <a-badge status="error" />
        </a-tooltip>
        
        <a-button 
          type="text" 
          size="small" 
          @click="refreshIndicators"
          :loading="isRefreshing"
          class="refresh-btn"
          title="刷新指标"
        >
          <template #icon>
            <ReloadOutlined />
          </template>
        </a-button>
      </div>
    </template>
    
    <div class="indicators-container">
      <!-- 表头 -->
      <div class="indicators-header">
        <div class="header-cell signal">信号</div>
        <div class="header-cell status">状态</div>
        <div class="header-cell remark">备注</div>
      </div>
      
      <!-- EMA交叉状态 -->
      <div class="indicator-row">
        <div class="indicator-cell signal">
          <span class="indicator-name">EMA交叉</span>
        </div>
        <div class="indicator-cell status">
          <a-tag :color="getEMACrossColor(indicators.emaCross)">
            {{ getEMACrossText(indicators.emaCross) }}
          </a-tag>
        </div>
        <div class="indicator-cell remark">
          <span class="remark-text">{{ getEMACrossRemark(indicators.emaCross) }}</span>
        </div>
      </div>
      
      <!-- EMA慢线/中性线状态 -->
      <div class="indicator-row">
        <div class="indicator-cell signal">
          <span class="indicator-name">EMA趋势</span>
        </div>
        <div class="indicator-cell status">
          <a-tag :color="getEMATrendColor(indicators.emaTrend)">
            {{ getEMATrendText(indicators.emaTrend) }}
          </a-tag>
        </div>
        <div class="indicator-cell remark">
          <span class="remark-text">{{ getEMATrendRemark(indicators.emaTrend, getCurrentIndicatorValues.ema12, getCurrentIndicatorValues.ema26) }}</span>
        </div>
      </div>
      
      <!-- MA状态 -->
      <div class="indicator-row">
        <div class="indicator-cell signal">
          <span class="indicator-name">MA趋势</span>
        </div>
        <div class="indicator-cell status">
          <a-tag :color="getMAStatusColor(indicators.maStatus)">
            {{ getMAStatusText(indicators.maStatus) }}
          </a-tag>
        </div>
        <div class="indicator-cell remark">
          <span class="remark-text">{{ getMAStatusRemark(indicators.maStatus, getCurrentIndicatorValues.ma5, getCurrentIndicatorValues.ma20, getCurrentIndicatorValues.ma60) }}</span>
        </div>
      </div>
      
      <!-- RSI超买超卖 -->
      <div class="indicator-row">
        <div class="indicator-cell signal">
          <span class="indicator-name">RSI</span>
          <span class="indicator-value">({{ indicators.rsi ? indicators.rsi.toFixed(2) : '50.00' }})</span>
        </div>
        <div class="indicator-cell status">
          <a-tag :color="getRSIColor(indicators.rsi)">
            {{ getRSIText(indicators.rsi) }}
          </a-tag>
        </div>
        <div class="indicator-cell remark">
          <span class="remark-text">{{ getRSIRemark(indicators.rsi) }}</span>
        </div>
      </div>
      
      <!-- MACD信号 -->
      <div class="indicator-row">
        <div class="indicator-cell signal">
          <span class="indicator-name">MACD</span>
        </div>
        <div class="indicator-cell status">
          <a-tag :color="getMACDColor(indicators.macd)">
            {{ getMACDText(indicators.macd) }}
          </a-tag>
        </div>
        <div class="indicator-cell remark">
          <span class="remark-text">{{ getMACDRemark(indicators.macd) }}</span>
        </div>
      </div>
      
      <!-- 熊猫多空指标 -->
      <div class="indicator-row">
        <div class="indicator-cell signal">
          <span class="indicator-name">熊猫指标</span>
        </div>
        <div class="indicator-cell status">
          <a-tag :color="getPandaColor(indicators.panda)">
            {{ getPandaText(indicators.panda) }}
          </a-tag>
        </div>
        <div class="indicator-cell remark">
          <span class="remark-text">{{ getPandaRemark(indicators.panda) }}</span>
        </div>
      </div>
      
      <!-- 超级趋势指标 -->
      <div class="indicator-row">
        <div class="indicator-cell signal">
          <span class="indicator-name">超级趋势</span>
        </div>
        <div class="indicator-cell status">
          <a-tag :color="getSupertrendColor(indicators.supertrend)">
            {{ getSupertrendText(indicators.supertrend) }}
          </a-tag>
        </div>
        <div class="indicator-cell remark">
          <span class="remark-text">{{ getSupertrendRemark(indicators.supertrend) }}</span>
        </div>
      </div>
      
      <!-- 多头空头统计 -->
      <div class="indicator-row">
        <div class="indicator-cell signal">
          <span class="indicator-name">多空统计</span>
        </div>
        <div class="indicator-cell status">
          <div class="bull-bear-stats">
            <span class="bull-count">多头: {{ indicators.bullCount || 0 }}</span>
            <span class="bear-count">空头: {{ indicators.bearCount || 0 }}</span>
          </div>
        </div>
        <div class="indicator-cell remark">
          <span class="remark-text">{{ getBullBearRemark(indicators.bullCount, indicators.bearCount) }}</span>
        </div>
      </div>
    </div>
    
    <!-- 最后更新时间 -->
    <div class="update-time">
      <span class="update-label">最后更新:</span>
      <span class="update-value">{{ formatUpdateTime(lastUpdateTime) }}</span>
    </div>
  </a-card>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import { useChartSettingsStore } from '@/stores/chartSettingsStore'
import { useChartDataStore } from '@/stores/chartDataStore'
import { storeToRefs } from 'pinia'
import { useTechnicalIndicators } from '@/composables/useTechnicalIndicators'
import { useIndicatorDisplay } from '@/composables/useIndicatorDisplay'
import { useErrorHandling } from '@/composables/useErrorHandling'
import { message } from 'ant-design-vue'

const chartSettingsStore = useChartSettingsStore()
const chartDataStore = useChartDataStore()
const { isDarkTheme, selectedSymbol } = storeToRefs(chartSettingsStore)
const { chartData } = storeToRefs(chartDataStore)

// 使用 composables
const {
  calculateAllIndicators,
  getEMACrossSignal,
  getEMATrendSignal,
  getMAStatusSignal,
  getMACDSignal,
  getPandaSignal,
  getSupertrendSignal
} = useTechnicalIndicators()

const {
  getEMACrossColor,
  getEMACrossText,
  getEMACrossRemark,
  getEMATrendColor,
  getEMATrendText,
  getEMATrendRemark,
  getMAStatusColor,
  getMAStatusText,
  getMAStatusRemark,
  getRSIColor,
  getRSIText,
  getRSIRemark,
  getMACDColor,
  getMACDText,
  getMACDRemark,
  getPandaColor,
  getPandaText,
  getPandaRemark,
  getSupertrendColor,
  getSupertrendText,
  getSupertrendRemark,
  getBullBearRemark,
  formatUpdateTime
} = useIndicatorDisplay('zh-CN', isDarkTheme)

const {
  safeExecute,
  validateKlineData,
  debounce,
  performanceMonitor,
  isLoading,
  hasError,
  latestError,
  clearErrors
} = useErrorHandling()

// 响应式数据
const isRefreshing = ref(false)
const lastUpdateTime = ref(new Date())
const updateInterval = ref(null)
const refreshKey = ref(0)

// 技术指标数据
const indicators = computed(() => {
  return safeExecute(
    () => {
      if (!validateKlineData(chartData.value)) {
        return {
          emaCross: 'neutral',
          emaTrend: 'neutral',
          maStatus: 'neutral',
          rsi: 50,
          macd: 'neutral',
          panda: 'neutral',
          supertrend: 'neutral',
          bullCount: 0,
          bearCount: 0,
          indicatorData: {
            ema12: [],
            ema26: [],
            ma5: [],
            ma20: [],
            ma60: [],
            rsi: [],
            macd: { line: [], signal: [], histogram: [] },
            supertrend: []
          }
        }
      }

      return performanceMonitor('计算技术指标', () => {
        return calculateIndicators(chartData.value)
      })
    },
    '计算技术指标',
    {
      emaCross: 'neutral',
      emaTrend: 'neutral',
      maStatus: 'neutral',
      rsi: 50,
      macd: 'neutral',
      panda: 'neutral',
      supertrend: 'neutral',
      bullCount: 0,
      bearCount: 0,
      indicatorData: {
        ema12: [],
        ema26: [],
        ma5: [],
        ma20: [],
        ma60: [],
        rsi: [],
        macd: { line: [], signal: [], histogram: [] },
        supertrend: []
      }
    }
  )
})

/**
 * 计算技术指标数据
 * @param {Array} data - K线数据
 * @returns {Object} 计算后的指标数据
 */
const calculateIndicators = (data) => {
  if (!data || data.length < 60) {
    return {
      emaCross: 'neutral',
      emaTrend: 'neutral',
      maStatus: 'neutral',
      rsi: 50,
      macd: 'neutral',
      panda: 'neutral',
      supertrend: 'neutral',
      bullCount: 0,
      bearCount: 0,
      indicatorData: {
        ema12: [],
        ema26: [],
        ma5: [],
        ma20: [],
        ma60: [],
        rsi: [],
        macd: { line: [], signal: [], histogram: [] },
        supertrend: []
      }
    }
  }

  const calculatedData = calculateAllIndicators(data)
  
  // 从 calculateAllIndicators 返回的数据中获取信号
  const emaCross = calculatedData.emaCross
  const emaTrend = calculatedData.emaTrend
  const maStatus = calculatedData.maStatus
  const macd = calculatedData.macd
  const panda = calculatedData.panda
  const supertrend = calculatedData.supertrend
  
  // 获取当前RSI值
  const currentRSI = calculatedData.rsi || 50
  
  // 统计多空信号
  const signals = [emaCross, emaTrend, maStatus, macd, panda, supertrend]
  const bullCount = signals.filter(signal => signal === 'bullish').length
  const bearCount = signals.filter(signal => signal === 'bearish').length
  
  return {
    emaCross,
    emaTrend,
    maStatus,
    rsi: currentRSI,
    macd,
    panda,
    supertrend,
    bullCount,
    bearCount,
    // 保存原始指标数据供显示使用
    indicatorData: calculatedData.indicatorData
  }
}

/**
 * 计算EMA指标
 * @param {Array} data - 价格数据
 * @param {number} period - 周期
 * @returns {Array} EMA数组
 */
const calculateEMA = (data, period) => {
  const ema = []
  const multiplier = 2 / (period + 1)
  
  ema[0] = data[0]
  for (let i = 1; i < data.length; i++) {
    ema[i] = (data[i] * multiplier) + (ema[i - 1] * (1 - multiplier))
  }
  
  return ema
}

/**
 * 计算MA指标
 * @param {Array} data - 价格数据
 * @param {number} period - 周期
 * @returns {Array} MA数组
 */
const calculateMA = (data, period) => {
  const ma = []
  for (let i = period - 1; i < data.length; i++) {
    const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)
    ma.push(sum / period)
  }
  return ma
}

/**
 * 计算RSI指标
 * @param {Array} data - 价格数据
 * @param {number} period - 周期
 * @returns {Array} RSI数组
 */
const calculateRSI = (data, period) => {
  const rsi = []
  const gains = []
  const losses = []
  
  for (let i = 1; i < data.length; i++) {
    const change = data[i] - data[i - 1]
    gains.push(change > 0 ? change : 0)
    losses.push(change < 0 ? Math.abs(change) : 0)
  }
  
  for (let i = period - 1; i < gains.length; i++) {
    const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period
    const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period
    
    if (avgLoss === 0) {
      rsi.push(100)
    } else {
      const rs = avgGain / avgLoss
      rsi.push(100 - (100 / (1 + rs)))
    }
  }
  
  return rsi
}

/**
 * 计算MACD指标
 * @param {Array} data - 价格数据
 * @returns {Object} MACD数据
 */
const calculateMACD = (data) => {
  const ema12 = calculateEMA(data, 12)
  const ema26 = calculateEMA(data, 26)
  const macdLine = ema12.map((val, i) => val - ema26[i])
  const signalLine = calculateEMA(macdLine, 9)
  const histogram = macdLine.map((val, i) => val - signalLine[i])
  
  return { macdLine, signalLine, histogram }
}

// 信号计算
const emaCrossSignal = computed(() => {
  return safeExecute(
    () => getEMACrossSignal(indicators.value),
    'EMA交叉信号计算',
    'neutral'
  )
})

const emaTrendSignal = computed(() => {
  return safeExecute(
    () => getEMATrendSignal(indicators.value),
    'EMA趋势信号计算',
    'neutral'
  )
})

const maStatusSignal = computed(() => {
  return safeExecute(
    () => getMAStatusSignal(indicators.value),
    'MA状态信号计算',
    'neutral'
  )
})

const macdSignal = computed(() => {
  return safeExecute(
    () => getMACDSignal(indicators.value),
    'MACD信号计算',
    'neutral'
  )
})

const pandaSignal = computed(() => {
  return safeExecute(
    () => getPandaSignal(indicators.value),
    '熊猫信号计算',
    'neutral'
  )
})

const supertrendSignal = computed(() => {
  return safeExecute(
    () => getSupertrendSignal(indicators.value),
    '超级趋势信号计算',
    'neutral'
  )
})

/**
 * 计算ATR指标
 * @param {Array} highs - 最高价数组
 * @param {Array} lows - 最低价数组
 * @param {Array} closes - 收盘价数组
 * @param {number} period - 周期
 * @returns {Array} ATR数组
 */
const calculateATR = (highs, lows, closes, period) => {
  const tr = []
  for (let i = 1; i < highs.length; i++) {
    const hl = highs[i] - lows[i]
    const hc = Math.abs(highs[i] - closes[i - 1])
    const lc = Math.abs(lows[i] - closes[i - 1])
    tr.push(Math.max(hl, hc, lc))
  }
  
  return calculateMA(tr, period)
}

// 获取当前指标值用于显示
const getCurrentIndicatorValues = computed(() => {
  const data = indicators.value?.indicatorData
  if (!data) return {}
  
  return {
    ema12: data.ema12?.length > 0 ? data.ema12[data.ema12.length - 1] : 0,
    ema26: data.ema26?.length > 0 ? data.ema26[data.ema26.length - 1] : 0,
    ma5: data.ma5?.length > 0 ? data.ma5[data.ma5.length - 1] : 0,
    ma20: data.ma20?.length > 0 ? data.ma20[data.ma20.length - 1] : 0,
    ma60: data.ma60?.length > 0 ? data.ma60[data.ma60.length - 1] : 0,
    supertrend: data.supertrend?.length > 0 ? data.supertrend[data.supertrend.length - 1]?.value : 0
  }
})

/**
 * 更新技术指标数据
 */
const updateIndicators = () => {
  if (chartData.value && chartData.value.length > 0) {
    // indicators 是 computed 属性，会自动根据 chartData 变化重新计算
    // 只需要更新时间戳
    lastUpdateTime.value = new Date()
  }
}

/**
 * 手动刷新指标
 */
const refreshIndicators = debounce(async () => {
  if (isLoading.value) return
  
  await safeExecute(
    async () => {
      // 清除之前的错误
      clearErrors()
      
      // 触发数据重新计算
      updateIndicators()
      
      // 更新时间
      lastUpdateTime.value = new Date()
      
      message.success('技术指标已更新')
    },
    '刷新技术指标'
  )
}, 500)

/**
 * 启动自动更新
 */
const startAutoUpdate = () => {
  updateInterval.value = setInterval(() => {
    updateIndicators()
  }, 20000) // 每20秒更新一次
}

/**
 * 停止自动更新
 */
const stopAutoUpdate = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
    updateInterval.value = null
  }
}

// 生命周期钩子
onMounted(() => {
  updateIndicators()
  startAutoUpdate()
})

onUnmounted(() => {
  stopAutoUpdate()
})

// 监听数据变化（使用防抖优化性能）
const debouncedUpdateIndicators = debounce(() => {
  updateIndicators()
}, 300)

watch(
  () => chartData.value,
  (newData) => {
    if (newData && newData.length > 0) {
      debouncedUpdateIndicators()
    }
  },
  { deep: true }
)

watch(
  () => refreshKey.value,
  () => {
    updateIndicators()
  }
)
</script>

<style scoped>
.technical-indicators-panel {
  margin-bottom: 16px;
}

.technical-indicators-panel.dark-theme {
  background: #1f1f1f;
  border-color: #434343;
}

.technical-indicators-panel.dark-theme :deep(.ant-card-head) {
  background: #262626;
  border-bottom-color: #434343;
}

.technical-indicators-panel.dark-theme :deep(.ant-card-head-title) {
  color: #fff;
}

.technical-indicators-panel.dark-theme :deep(.ant-card-body) {
  background: #1f1f1f;
  color: #fff;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn {
  color: #1890ff;
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s;
}

.refresh-btn:hover {
  background-color: rgba(24, 144, 255, 0.1);
  color: #40a9ff;
}

/* 错误状态样式 */
.ant-badge-status-error {
  background-color: #ff4d4f;
}

.ant-tooltip {
  cursor: help;
}

.dark-theme .refresh-btn {
  color: #177ddc;
}

.dark-theme .refresh-btn:hover {
  color: #40a9ff;
}

.indicators-container {
  width: 100%;
}

.indicators-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 12px;
  color: #666;
}

.dark-theme .indicators-header {
  border-bottom-color: #434343;
  color: #ccc;
}

.header-cell {
  text-align: center;
  padding: 4px;
}

.indicator-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  padding: 6px 0;
  border-bottom: 1px solid #f5f5f5;
  align-items: center;
}

.dark-theme .indicator-row {
  border-bottom-color: #303030;
}

.indicator-row:last-child {
  border-bottom: none;
}

.indicator-cell {
  padding: 4px;
  font-size: 12px;
}

.indicator-cell.signal {
  text-align: left;
}

.indicator-cell.status {
  text-align: center;
}

.indicator-cell.remark {
  text-align: right;
}

.indicator-name {
  font-weight: 500;
  color: #333;
}

.dark-theme .indicator-name {
  color: #fff;
}

.indicator-value {
  font-size: 10px;
  color: #666;
  margin-left: 4px;
}

.dark-theme .indicator-value {
  color: #ccc;
}

.remark-text {
  font-size: 11px;
  color: #666;
}

.dark-theme .remark-text {
  color: #ccc;
}

.bull-bear-stats {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 11px;
}

.bull-count {
  color: #52c41a;
  font-weight: 500;
}

.bear-count {
  color: #ff4d4f;
  font-weight: 500;
}

.update-time {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
  font-size: 11px;
  color: #999;
  text-align: center;
}

.dark-theme .update-time {
  border-top-color: #434343;
  color: #666;
}

.update-label {
  margin-right: 4px;
}

.update-value {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .indicators-header,
  .indicator-row {
    grid-template-columns: 1fr;
    gap: 4px;
  }
  
  .indicator-cell {
    text-align: left !important;
    padding: 2px 4px;
  }
  
  .indicator-cell.signal::before {
    content: '信号: ';
    font-weight: 600;
    color: #666;
  }
  
  .indicator-cell.status::before {
    content: '状态: ';
    font-weight: 600;
    color: #666;
  }
  
  .indicator-cell.remark::before {
    content: '备注: ';
    font-weight: 600;
    color: #666;
  }
  
  .dark-theme .indicator-cell.signal::before,
  .dark-theme .indicator-cell.status::before,
  .dark-theme .indicator-cell.remark::before {
    color: #ccc;
  }
}
</style>