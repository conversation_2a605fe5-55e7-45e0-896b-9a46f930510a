import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import InfoPanelSection from './InfoPanelSection.vue'
import { createPinia, setActivePinia } from 'pinia'
import { useChartStore } from '@/stores/chartStore'

// Mock child components
const PriceInfoPanel = {
  template: '<div class="mock-price-info-panel"></div>',
}
const TradingSignalsPanel = {
  template: '<div class="mock-trading-signals-panel"></div>',
}
const PositionPanel = {
  template: '<div class="mock-position-panel"></div>',
}
const MarketDepthPanel = {
  template: '<div class="mock-market-depth-panel"></div>',
}

describe('InfoPanelSection.vue', () => {
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    const chartStore = useChartStore()
    chartStore.$patch({
      selectedSymbol: 'BTC-USDT',
      selectedTimeframe: '1m',
      chartData: [],
      realTimePriceData: {
        price: 50000,
        direction: 'up',
      },
      tradingSignals: [],
    })
  })

  it('renders all child panels', () => {
    const wrapper = mount(InfoPanelSection, {
      global: {
        plugins: [pinia],
        stubs: {
          // Stubbing child components to avoid deep rendering
          PriceInfoPanel: true,
          TradingSignalsPanel: true,
          PositionPanel: true,
          MarketDepthPanel: true,
        },
      },
    })

    expect(wrapper.find('priceinfopanel-stub').exists()).toBe(true)
    expect(wrapper.find('tradingsignalspanel-stub').exists()).toBe(true)
    expect(wrapper.find('positionpanel-stub').exists()).toBe(true)
    expect(wrapper.find('marketdepthpanel-stub').exists()).toBe(true)
  })
})