/**
 * 技术指标计算 Composable
 * 提供各种技术指标的计算功能
 */
import { ref, computed } from 'vue'

// 指标配置常量
export const INDICATOR_CONFIG = {
  ema: { fast: 12, slow: 26, signal: 9 },
  rsi: { period: 14, overbought: 70, oversold: 30 },
  ma: { short: 5, medium: 20, long: 60 },
  macd: { fast: 12, slow: 26, signal: 9 },
  supertrend: { period: 10, multiplier: 3 }
}

// 最大数据长度限制，避免内存泄漏
const MAX_DATA_LENGTH = 1000

export function useTechnicalIndicators() {
  const isCalculating = ref(false)
  const calculationError = ref(null)

  /**
   * 验证图表数据有效性
   * @param {Array} data - 图表数据
   * @returns {boolean} 数据是否有效
   */
  const validateChartData = (data) => {
    return data && Array.isArray(data) && data.length > 0
  }

  /**
   * 限制数据长度，防止内存泄漏
   * @param {Array} data - 原始数据
   * @returns {Array} 截取后的数据
   */
  const trimData = (data) => {
    return data.length > MAX_DATA_LENGTH ? data.slice(-MAX_DATA_LENGTH) : data
  }

  /**
   * 计算EMA指标
   * @param {Array} data - 价格数据
   * @param {number} period - 周期
   * @returns {Array} EMA数组
   */
  const calculateEMA = (data, period) => {
    if (!data || data.length < period) return []
    
    const ema = []
    const multiplier = 2 / (period + 1)
    
    ema[0] = data[0]
    for (let i = 1; i < data.length; i++) {
      ema[i] = (data[i] * multiplier) + (ema[i - 1] * (1 - multiplier))
    }
    
    return ema
  }

  /**
   * 计算MA指标
   * @param {Array} data - 价格数据
   * @param {number} period - 周期
   * @returns {Array} MA数组
   */
  const calculateMA = (data, period) => {
    if (!data || data.length < period) return []
    
    const ma = []
    for (let i = period - 1; i < data.length; i++) {
      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)
      ma.push(sum / period)
    }
    return ma
  }

  /**
   * 计算RSI指标
   * @param {Array} data - 价格数据
   * @param {number} period - 周期
   * @returns {Array} RSI数组
   */
  const calculateRSI = (data, period = INDICATOR_CONFIG.rsi.period) => {
    if (!data || data.length < period + 1) return []
    
    const rsi = []
    const gains = []
    const losses = []
    
    for (let i = 1; i < data.length; i++) {
      const change = data[i] - data[i - 1]
      gains.push(change > 0 ? change : 0)
      losses.push(change < 0 ? Math.abs(change) : 0)
    }
    
    for (let i = period - 1; i < gains.length; i++) {
      const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period
      const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period
      
      if (avgLoss === 0) {
        rsi.push(100)
      } else {
        const rs = avgGain / avgLoss
        rsi.push(100 - (100 / (1 + rs)))
      }
    }
    
    return rsi
  }

  /**
   * 计算MACD指标
   * @param {Array} data - 价格数据
   * @returns {Object} MACD数据对象
   */
  const calculateMACD = (data) => {
    if (!data || data.length < INDICATOR_CONFIG.macd.slow) {
      return { macd: [], signal: [], histogram: [] }
    }
    
    const emaFast = calculateEMA(data, INDICATOR_CONFIG.macd.fast)
    const emaSlow = calculateEMA(data, INDICATOR_CONFIG.macd.slow)
    
    const macd = []
    for (let i = 0; i < Math.min(emaFast.length, emaSlow.length); i++) {
      macd.push(emaFast[i] - emaSlow[i])
    }
    
    const signal = calculateEMA(macd, INDICATOR_CONFIG.macd.signal)
    const histogram = []
    
    for (let i = 0; i < Math.min(macd.length, signal.length); i++) {
      histogram.push(macd[i] - signal[i])
    }
    
    return { macd, signal, histogram }
  }

  /**
   * 计算超级趋势指标
   * @param {Array} closes - 收盘价数组
   * @param {Array} highs - 最高价数组
   * @param {Array} lows - 最低价数组
   * @returns {Array} 超级趋势数组
   */
  const calculateSupertrend = (closes, highs, lows) => {
    if (!Array.isArray(closes) || !Array.isArray(highs) || !Array.isArray(lows) || 
        closes.length < INDICATOR_CONFIG.supertrend.period) {
      return []
    }
    
    try {
      const { period, multiplier } = INDICATOR_CONFIG.supertrend
      const atr = calculateATR(highs, lows, closes, period)
      const hl2 = highs.map((high, i) => (high + lows[i]) / 2)
    
    const upperBand = []
    const lowerBand = []
    const supertrend = []
    
    for (let i = 0; i < hl2.length; i++) {
      if (i < period - 1) {
        upperBand.push(0)
        lowerBand.push(0)
        supertrend.push(0)
        continue
      }
      
      const currentATR = atr[i - period + 1] || 0
      upperBand.push(hl2[i] + (multiplier * currentATR))
      lowerBand.push(hl2[i] - (multiplier * currentATR))
      
      if (i === period - 1) {
        supertrend.push(closes[i] <= lowerBand[i] ? upperBand[i] : lowerBand[i])
      } else {
        const prevSupertrend = supertrend[i - 1]
        if (closes[i] <= lowerBand[i]) {
          supertrend.push(upperBand[i])
        } else if (closes[i] >= upperBand[i]) {
          supertrend.push(lowerBand[i])
        } else {
          supertrend.push(prevSupertrend)
        }
      }
    }
    
    return supertrend
    } catch (error) {
      console.error('calculateSupertrend error:', error)
      return []
    }
  }

  /**
   * 计算ATR指标
   * @param {Array} highs - 最高价数组
   * @param {Array} lows - 最低价数组
   * @param {Array} closes - 收盘价数组
   * @param {number} period - 周期
   * @returns {Array} ATR数组
   */
  const calculateATR = (highs, lows, closes, period) => {
    if (!Array.isArray(highs) || !Array.isArray(lows) || !Array.isArray(closes) || 
        highs.length < 2 || lows.length < 2 || closes.length < 2) {
      return []
    }
    
    try {
      const tr = []
      
      for (let i = 1; i < highs.length; i++) {
        const high = highs[i]
        const low = lows[i]
        const prevClose = closes[i - 1]
        
        if (typeof high !== 'number' || typeof low !== 'number' || typeof prevClose !== 'number' ||
            isNaN(high) || isNaN(low) || isNaN(prevClose)) {
          continue
        }
        
        const tr1 = high - low
        const tr2 = Math.abs(high - prevClose)
        const tr3 = Math.abs(low - prevClose)
        
        tr.push(Math.max(tr1, tr2, tr3))
      }
      
      return calculateMA(tr, period)
    } catch (error) {
      console.error('calculateATR error:', error)
      return []
    }
  }

  /**
   * 获取EMA交叉信号
   * @param {Array} emaFast - 快线EMA
   * @param {Array} emaSlow - 慢线EMA
   * @returns {string} 信号类型
   */
  const getEMACrossSignal = (emaFast, emaSlow) => {
    if (!emaFast.length || !emaSlow.length || emaFast.length < 2 || emaSlow.length < 2) {
      return 'neutral'
    }
    
    const currentFast = emaFast[emaFast.length - 1]
    const currentSlow = emaSlow[emaSlow.length - 1]
    const prevFast = emaFast[emaFast.length - 2]
    const prevSlow = emaSlow[emaSlow.length - 2]
    
    if (prevFast <= prevSlow && currentFast > currentSlow) {
      return 'bullish' // 金叉
    } else if (prevFast >= prevSlow && currentFast < currentSlow) {
      return 'bearish' // 死叉
    }
    
    return currentFast > currentSlow ? 'bullish' : 'bearish'
  }

  /**
   * 获取EMA趋势信号
   * @param {Array} emaFast - 快线EMA
   * @param {Array} emaSlow - 慢线EMA
   * @param {Array} emaLong - 长线EMA
   * @returns {string} 趋势信号
   */
  const getEMATrendSignal = (emaFast, emaSlow, emaLong) => {
    if (!emaFast.length || !emaSlow.length || !emaLong.length) {
      return 'neutral'
    }
    
    const fast = emaFast[emaFast.length - 1]
    const slow = emaSlow[emaSlow.length - 1]
    const long = emaLong[emaLong.length - 1]
    
    if (fast > slow && slow > long) {
      return 'bullish'
    } else if (fast < slow && slow < long) {
      return 'bearish'
    }
    
    return 'neutral'
  }

  /**
   * 获取MA状态信号
   * @param {Array} maShort - 短期MA
   * @param {Array} maMedium - 中期MA
   * @param {Array} maLong - 长期MA
   * @returns {string} MA状态信号
   */
  const getMAStatusSignal = (maShort, maMedium, maLong) => {
    if (!maShort.length || !maMedium.length || !maLong.length) {
      return 'neutral'
    }
    
    const short = maShort[maShort.length - 1]
    const medium = maMedium[maMedium.length - 1]
    const long = maLong[maLong.length - 1]
    
    if (short > medium && medium > long) {
      return 'bullish'
    } else if (short < medium && medium < long) {
      return 'bearish'
    }
    
    return 'neutral'
  }

  /**
   * 获取MACD信号
   * @param {Object} macdData - MACD数据
   * @returns {string} MACD信号
   */
  const getMACDSignal = (macdData) => {
    const { macd, signal } = macdData
    
    if (!macd.length || !signal.length || macd.length < 2 || signal.length < 2) {
      return 'neutral'
    }
    
    const currentMACD = macd[macd.length - 1]
    const currentSignal = signal[signal.length - 1]
    const prevMACD = macd[macd.length - 2]
    const prevSignal = signal[signal.length - 2]
    
    if (prevMACD <= prevSignal && currentMACD > currentSignal) {
      return 'bullish' // 金叉
    } else if (prevMACD >= prevSignal && currentMACD < currentSignal) {
      return 'bearish' // 死叉
    }
    
    return 'neutral'
  }

  /**
   * 获取熊猫指标信号
   * @param {Array} closes - 收盘价
   * @param {Array} highs - 最高价
   * @param {Array} lows - 最低价
   * @returns {string} 熊猫信号
   */
  const getPandaSignal = (closes, highs, lows) => {
    if (!Array.isArray(closes) || !Array.isArray(highs) || !Array.isArray(lows) || 
        closes.length < 20 || highs.length < 20 || lows.length < 20) {
      return 'neutral'
    }
    
    try {
      const recentCloses = closes.slice(-20)
      const recentHighs = highs.slice(-20)
      const recentLows = lows.slice(-20)
      
      // 确保数据都是有效数字
      const validHighs = recentHighs.filter(h => typeof h === 'number' && !isNaN(h))
      const validLows = recentLows.filter(l => typeof l === 'number' && !isNaN(l))
      
      if (validHighs.length === 0 || validLows.length === 0) {
        return 'neutral'
      }
      
      const currentPrice = closes[closes.length - 1]
      const highestHigh = Math.max.apply(null, validHighs)
      const lowestLow = Math.min.apply(null, validLows)
      
      if (highestHigh === lowestLow) {
        return 'neutral'
      }
      
      const position = (currentPrice - lowestLow) / (highestHigh - lowestLow)
      
      if (position > 0.8) {
        return 'bullish'
      } else if (position < 0.2) {
        return 'bearish'
      }
      
      return 'neutral'
    } catch (error) {
      console.error('getPandaSignal error:', error)
      return 'neutral'
    }
  }

  /**
   * 获取超级趋势信号
   * @param {Array} closes - 收盘价
   * @param {Array} highs - 最高价
   * @param {Array} lows - 最低价
   * @returns {string} 超级趋势信号
   */
  const getSupertrendSignal = (closes, highs, lows) => {
    const supertrend = calculateSupertrend(closes, highs, lows)
    
    if (!supertrend.length || supertrend.length < 2) {
      return 'neutral'
    }
    
    const currentPrice = closes[closes.length - 1]
    const currentSupertrend = supertrend[supertrend.length - 1]
    
    return currentPrice > currentSupertrend ? 'bullish' : 'bearish'
  }

  /**
   * 计算所有技术指标
   * @param {Array} chartData - 图表数据
   * @returns {Object} 所有指标数据
   */
  const calculateAllIndicators = (chartData) => {
    try {
      isCalculating.value = true
      calculationError.value = null
      
      if (!validateChartData(chartData)) {
        throw new Error('无效的图表数据')
      }
      
      const trimmedData = trimData(chartData)
      
      // K线数据格式: [timestamp, open, high, low, close, volume, ...]
      const closes = trimmedData.map(item => {
        if (Array.isArray(item)) {
          return parseFloat(item[4]) || 0  // 收盘价在索引4
        } else if (item && typeof item === 'object') {
          return parseFloat(item.close) || 0
        }
        return 0
      })
      
      const highs = trimmedData.map(item => {
        if (Array.isArray(item)) {
          return parseFloat(item[2]) || 0  // 最高价在索引2
        } else if (item && typeof item === 'object') {
          return parseFloat(item.high) || 0
        }
        return 0
      })
      
      const lows = trimmedData.map(item => {
        if (Array.isArray(item)) {
          return parseFloat(item[3]) || 0  // 最低价在索引3
        } else if (item && typeof item === 'object') {
          return parseFloat(item.low) || 0
        }
        return 0
      })
      
      // 计算各种指标
      const ema12 = calculateEMA(closes, INDICATOR_CONFIG.ema.fast)
      const ema26 = calculateEMA(closes, INDICATOR_CONFIG.ema.slow)
      const ema50 = calculateEMA(closes, 50)
      
      const ma5 = calculateMA(closes, INDICATOR_CONFIG.ma.short)
      const ma20 = calculateMA(closes, INDICATOR_CONFIG.ma.medium)
      const ma60 = calculateMA(closes, INDICATOR_CONFIG.ma.long)
      
      const rsi = calculateRSI(closes)
      const macdData = calculateMACD(closes)
      
      // 判断各种信号
      const emaCross = getEMACrossSignal(ema12, ema26)
      const emaTrend = getEMATrendSignal(ema12, ema26, ema50)
      const maStatus = getMAStatusSignal(ma5, ma20, ma60)
      const macd = getMACDSignal(macdData)
      const panda = getPandaSignal(closes, highs, lows)
      const supertrend = getSupertrendSignal(closes, highs, lows)
      
      // 统计多空信号
      const signals = [emaCross, emaTrend, maStatus, macd, panda, supertrend]
      const bullCount = signals.filter(s => s === 'bullish').length
      const bearCount = signals.filter(s => s === 'bearish').length
      
      return {
        emaCross,
        emaTrend,
        maStatus,
        rsi: rsi.length > 0 ? rsi[rsi.length - 1] : 0,
        macd,
        panda,
        supertrend,
        bullCount,
        bearCount,
        // 添加完整的指标数据用于显示
        indicatorData: {
          ema12,
          ema26,
          ma5,
          ma20,
          ma60,
          rsi,
          macd: macdData,
          supertrend: supertrend === 'bullish' ? [{ value: closes[closes.length - 1] }] : [{ value: 0 }]
        }
      }
    } catch (error) {
      calculationError.value = error.message
      console.error('技术指标计算错误:', error)
      
      // 返回默认值
      return {
        emaCross: 'neutral',
        emaTrend: 'neutral',
        maStatus: 'neutral',
        rsi: 50,
        macd: 'neutral',
        panda: 'neutral',
        supertrend: 'neutral',
        bullCount: 0,
        bearCount: 0,
        // 添加默认的indicatorData
        indicatorData: {
          ema12: [],
          ema26: [],
          ma5: [],
          ma20: [],
          ma60: [],
          rsi: [],
          macd: { line: [], signal: [], histogram: [] },
          supertrend: []
        }
      }
    } finally {
      isCalculating.value = false
    }
  }

  return {
    isCalculating,
    calculationError,
    calculateAllIndicators,
    validateChartData,
    // 导出单独的计算函数供其他地方使用
    calculateEMA,
    calculateMA,
    calculateRSI,
    calculateMACD,
    calculateSupertrend,
    // 导出信号判断函数
    getEMACrossSignal,
    getEMATrendSignal,
    getMAStatusSignal,
    getMACDSignal,
    getPandaSignal,
    getSupertrendSignal
  }
}