<template>
  <div class="okx-order-history">
    <h2>OKX历史订单</h2>
    <a-form layout="inline" :model="query" @submit.prevent style="margin-bottom:16px">
      <a-form-item label="产品ID">
        <a-input v-model="query.instId" placeholder="如 BTC-USDT-SWAP" style="width:180px" />
      </a-form-item>
      <a-form-item label="订单类型">
        <a-select v-model="query.ordType" style="width:120px" allow-clear>
          <a-select-option value="market">市价</a-select-option>
          <a-select-option value="limit">限价</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="订单状态">
        <a-select v-model="query.state" style="width:120px" allow-clear>
          <a-select-option value="filled">已成交</a-select-option>
          <a-select-option value="canceled">已撤销</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="fetchOrders" :loading="loading">查询</a-button>
      </a-form-item>
    </a-form>
    <a-table :columns="columns" :data-source="orderList" row-key="ordId" :pagination="pagination" @change="onTableChange" :loading="loading" bordered>
      <template #empty>
        <div style="text-align:center; padding:32px;">暂无订单</div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ordType'">
          <span>{{ record.ordType === 'market' ? '市价' : '限价' }}</span>
        </template>
        <template v-else-if="column.key === 'side'">
          <span>{{ record.side === 'buy' ? '买入' : '卖出' }}</span>
        </template>
        <template v-else-if="column.key === 'state'">
          <span>{{ record.state === 'filled' ? '已成交' : '已撤销' }}</span>
        </template>
        <template v-else-if="column.key === 'details'">
          <a-button type="link" @click="showOrderDetails(record)">详情</a-button>
        </template>
        <template v-else>
          {{ record[column.dataIndex] }}
        </template>
      </template>
    </a-table>
    <div v-if="error" style="color:red; margin-top:16px;">{{ error }}</div>
    <a-modal
      title="订单详情"
      :open="modalVisible"
      @update:open="modalVisible = $event"
      :footer="null"
      @cancel="modalVisible = false"
    >
      <a-descriptions bordered>
        <a-descriptions-item label="订单ID">{{ currentOrder.ordId }}</a-descriptions-item>
        <a-descriptions-item label="产品ID">{{ currentOrder.instId }}</a-descriptions-item>
        <a-descriptions-item label="订单类型">{{ currentOrder.ordType === 'market' ? '市价' : '限价' }}</a-descriptions-item>
        <a-descriptions-item label="方向">{{ currentOrder.side === 'buy' ? '买入' : '卖出' }}</a-descriptions-item>
        <a-descriptions-item label="价格">{{ currentOrder.px }}</a-descriptions-item>
        <a-descriptions-item label="数量">{{ currentOrder.sz }}</a-descriptions-item>
        <a-descriptions-item label="状态">{{ currentOrder.state === 'filled' ? '已成交' : '已撤销' }}</a-descriptions-item>
        <a-descriptions-item label="成交均价">{{ currentOrder.avgPx }}</a-descriptions-item>
        <a-descriptions-item label="成交数量">{{ currentOrder.accFillSz }}</a-descriptions-item>
        <a-descriptions-item label="手续费">{{ currentOrder.fee }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ formatTime(currentOrder.cTime) }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ formatTime(currentOrder.uTime) }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'

const query = reactive({ instId: '', ordType: undefined, state: undefined })
const orderList = ref([])
const loading = ref(false)
const error = ref('')
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: (total) => `共${total}条`
})
const modalVisible = ref(false)
const currentOrder = ref({})

const columns = [
  { title: '订单ID', dataIndex: 'ordId', key: 'ordId' },
  { title: '产品ID', dataIndex: 'instId', key: 'instId' },
  { title: '订单类型', dataIndex: 'ordType', key: 'ordType' },
  { title: '方向', dataIndex: 'side', key: 'side' },
  { title: '价格', dataIndex: 'px', key: 'px' },
  { title: '数量', dataIndex: 'sz', key: 'sz' },
  { title: '状态', dataIndex: 'state', key: 'state' },
  { title: '操作', key: 'details' }
]

function fetchOrders() {
  loading.value = true
  error.value = ''
  const params = {}
  if (query.instId) params.instId = query.instId
  if (query.ordType) params.ordType = query.ordType
  if (query.state) params.state = query.state
  fetch('/api/okx/orders?' + new URLSearchParams(params))
    .then(res => res.json())
    .then(data => {
      if (data.code === 0) {
        orderList.value = data.data
        pagination.value.total = data.data.length
        error.value = ''
      } else {
        error.value = data.msg || '查询失败'
      }
    })
    .catch(err => {
      console.error(err)
      error.value = '查询失败'
    })
    .finally(() => {
      loading.value = false
    })
}

function onTableChange(pag) {
  pagination.value.current = pag.current
  fetchOrders()
}

function showOrderDetails(record) {
  currentOrder.value = record
  modalVisible.value = true
}

function formatTime(timestamp) {
  if (!timestamp) return ''
  const date = new Date(Number(timestamp))
  return date.toLocaleString()
}

fetchOrders()
</script>

<style scoped>
.okx-order-history {
  max-width: 1100px;
  margin: 0 auto;
  padding: 24px;
}
</style>