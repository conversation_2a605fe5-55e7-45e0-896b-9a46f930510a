import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'

/**
 * @description WebSocket连接管理的组合式函数
 * @returns {Object} 包含连接状态、订阅管理和消息处理的对象
 */
export function useWebSocket() {
  const userStore = useUserStore()
  
  // 连接状态
  const connected = ref(false)
  const connecting = ref(false)
  const error = ref(null)
  const lastHeartbeat = ref(null)
  
  // WebSocket实例
  let ws = null
  let heartbeatTimer = null
  let reconnectTimer = null
  let reconnectAttempts = 0
  const maxReconnectAttempts = 5
  const reconnectDelay = 3000
  
  // 订阅管理
  const subscriptions = reactive(new Map())
  const messageHandlers = reactive(new Map())
  
  /**
   * @description 获取WebSocket连接URL
   * @returns {string} WebSocket URL
   */
  const getWebSocketUrl = () => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = import.meta.env.VITE_WS_URL || `${protocol}//${window.location.host}/ws`
    return host
  }
  
  /**
   * @description 连接WebSocket
   */
  const connect = () => {
    if (connecting.value || connected.value) {
      return Promise.resolve()
    }
    
    connecting.value = true
    error.value = null
    
    return new Promise((resolve, reject) => {
      try {
        const wsUrl = getWebSocketUrl()
        ws = new WebSocket(wsUrl)
        
        ws.onopen = () => {
          handleOpen()
          resolve()
        }
        
        ws.onmessage = handleMessage
        ws.onclose = handleClose
        
        ws.onerror = (event) => {
          handleError(event)
          const errorObj = new Error(
            event.message || 
            event.type || 
            'WebSocket connection failed'
          )
          reject(errorObj)
        }
        
      } catch (err) {
        console.error('WebSocket连接失败:', err)
        error.value = err.message
        connecting.value = false
        reject(err)
      }
    })
  }
  
  /**
   * @description 处理连接打开事件
   */
  const handleOpen = () => {
    console.log('WebSocket连接已建立')
    connected.value = true
    connecting.value = false
    reconnectAttempts = 0
    error.value = null
    
    // 发送认证消息
    if (userStore.token) {
      authenticate()
    }
    
    // 启动心跳
    startHeartbeat()
    
    // 重新订阅之前的频道
    resubscribeAll()
  }
  
  /**
   * @description 处理消息接收事件
   * @param {MessageEvent} event - WebSocket消息事件
   */
  const handleMessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      
      // 处理心跳响应
      if (data.type === 'pong') {
        lastHeartbeat.value = new Date()
        return
      }
      
      // 处理认证响应
      if (data.type === 'auth') {
        if (data.success) {
          console.log('WebSocket认证成功')
        } else {
          console.error('WebSocket认证失败:', data.message)
          error.value = data.message
        }
        return
      }
      
      // 处理订阅确认
      if (data.type === 'subscribe' || data.type === 'unsubscribe') {
        console.log(`${data.type}操作确认:`, data.channel)
        return
      }
      
      // 处理数据推送
      const { type, channel, data: messageData } = data
      
      // 查找对应的消息处理器
      const handlerKey = `${type}.${channel}` || type
      const handler = messageHandlers.get(handlerKey)
      
      if (handler) {
        handler(messageData, data)
      } else {
        // 通用处理器
        const generalHandler = messageHandlers.get(type)
        if (generalHandler) {
          generalHandler(messageData, data)
        }
      }
      
    } catch (err) {
      console.error('解析WebSocket消息失败:', err, event.data)
    }
  }
  
  /**
   * @description 处理连接关闭事件
   * @param {CloseEvent} event - WebSocket关闭事件
   */
  const handleClose = (event) => {
    console.log('WebSocket连接已关闭:', event.code, event.reason)
    connected.value = false
    connecting.value = false
    
    stopHeartbeat()
    
    // 如果不是主动关闭，尝试重连
    if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
      scheduleReconnect()
    }
  }
  
  /**
   * @description 处理连接错误事件
   * @param {Event} event - WebSocket错误事件
   */
  const handleError = (event) => {
    console.error('WebSocket连接错误:', event)
    error.value = 'WebSocket连接错误'
    connecting.value = false
  }
  
  /**
   * @description 发送认证消息
   */
  const authenticate = () => {
    if (!connected.value || !userStore.token) {
      return
    }
    
    send({
      type: 'auth',
      token: userStore.token
    })
  }
  
  /**
   * @description 启动心跳
   */
  const startHeartbeat = () => {
    stopHeartbeat()
    
    heartbeatTimer = setInterval(() => {
      if (connected.value) {
        send({ type: 'ping' })
      }
    }, 30000) // 30秒心跳间隔
  }
  
  /**
   * @description 停止心跳
   */
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }
  
  /**
   * @description 安排重连
   */
  const scheduleReconnect = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
    }
    
    reconnectAttempts++
    const delay = reconnectDelay * Math.pow(2, reconnectAttempts - 1) // 指数退避
    
    console.log(`${delay}ms后尝试第${reconnectAttempts}次重连...`)
    
    reconnectTimer = setTimeout(() => {
      if (!connected.value) {
        connect()
      }
    }, delay)
  }
  
  /**
   * @description 发送消息
   * @param {Object} message - 要发送的消息对象
   */
  const send = (message) => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket未连接，无法发送消息:', message)
    }
  }
  
  /**
   * @description 订阅频道
   * @param {string} channel - 频道名称
   * @param {Object} params - 订阅参数
   * @param {Function} handler - 消息处理函数
   */
  const subscribe = (channel, params = {}, handler = null) => {
    const subscriptionKey = `${channel}.${JSON.stringify(params)}`
    
    // 保存订阅信息
    subscriptions.set(subscriptionKey, { channel, params, handler })
    
    // 注册消息处理器
    if (handler) {
      const handlerKey = `${channel}.${params.symbol || ''}`.replace(/\.$/, '')
      messageHandlers.set(handlerKey, handler)
    }
    
    // 发送订阅消息
    if (connected.value) {
      send({
        type: 'subscribe',
        channel,
        params
      })
    }
  }
  
  /**
   * @description 取消订阅频道
   * @param {string} channel - 频道名称
   * @param {Object} params - 订阅参数
   */
  const unsubscribe = (channel, params = {}) => {
    const subscriptionKey = `${channel}.${JSON.stringify(params)}`
    
    // 移除订阅信息
    subscriptions.delete(subscriptionKey)
    
    // 移除消息处理器
    const handlerKey = `${channel}.${params.symbol || ''}`.replace(/\.$/, '')
    messageHandlers.delete(handlerKey)
    
    // 发送取消订阅消息
    if (connected.value) {
      send({
        type: 'unsubscribe',
        channel,
        params
      })
    }
  }
  
  /**
   * @description 重新订阅所有频道
   */
  const resubscribeAll = () => {
    for (const [key, subscription] of subscriptions) {
      send({
        type: 'subscribe',
        channel: subscription.channel,
        params: subscription.params
      })
    }
  }
  
  /**
   * @description 注册消息处理器
   * @param {string} type - 消息类型
   * @param {Function} handler - 处理函数
   */
  const onMessage = (type, handler) => {
    messageHandlers.set(type, handler)
  }
  
  /**
   * @description 移除消息处理器
   * @param {string} type - 消息类型
   */
  const offMessage = (type) => {
    messageHandlers.delete(type)
  }
  
  /**
   * @description 断开连接
   */
  const disconnect = () => {
    if (ws) {
      ws.close(1000, '主动断开连接')
      ws = null
    }
    
    stopHeartbeat()
    
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    
    connected.value = false
    connecting.value = false
    reconnectAttempts = 0
  }
  
  // 组件卸载时清理
  onUnmounted(() => {
    disconnect()
  })
  
  return {
    // 状态
    connected,
    connecting,
    error,
    lastHeartbeat,
    
    // 方法
    connect,
    disconnect,
    send,
    subscribe,
    unsubscribe,
    onMessage,
    offMessage,
    authenticate,
    
    // 订阅信息（只读）
    subscriptions: readonly(subscriptions),
    messageHandlers: readonly(messageHandlers)
  }
}

/**
 * @description 市场数据WebSocket连接的组合式函数
 * @returns {Object} 包含市场数据订阅和处理的对象
 */
export function useMarketWebSocket() {
  const { 
    connected, 
    connecting, 
    error, 
    connect, 
    disconnect, 
    subscribe, 
    unsubscribe, 
    onMessage 
  } = useWebSocket()
  
  // 市场数据
  const klineData = ref([])
  const tickerData = ref({})
  const depthData = ref({ bids: [], asks: [] })
  const tradesData = ref([])
  
  /**
   * @description 订阅K线数据
   * @param {string} symbol - 交易对
   * @param {string} interval - 时间间隔
   */
  const subscribeKline = (symbol, interval) => {
    subscribe('kline', { symbol, interval }, (data) => {
      // 更新K线数据
      const newKline = {
        timestamp: data.timestamp,
        open: parseFloat(data.open),
        high: parseFloat(data.high),
        low: parseFloat(data.low),
        close: parseFloat(data.close),
        volume: parseFloat(data.volume)
      }
      
      // 更新或添加K线数据
      const existingIndex = klineData.value.findIndex(k => k.timestamp === newKline.timestamp)
      if (existingIndex >= 0) {
        klineData.value[existingIndex] = newKline
      } else {
        klineData.value.push(newKline)
        // 保持数据量在合理范围内
        if (klineData.value.length > 1000) {
          klineData.value = klineData.value.slice(-1000)
        }
      }
    })
  }
  
  /**
   * @description 订阅价格行情数据
   * @param {string} symbol - 交易对
   */
  const subscribeTicker = (symbol) => {
    subscribe('ticker', { symbol }, (data) => {
      tickerData.value = {
        symbol: data.symbol,
        last: parseFloat(data.last),
        bid: parseFloat(data.bid),
        ask: parseFloat(data.ask),
        high24h: parseFloat(data.high24h),
        low24h: parseFloat(data.low24h),
        volume24h: parseFloat(data.volume24h),
        change24h: parseFloat(data.change24h),
        changePercent24h: parseFloat(data.changePercent24h),
        timestamp: data.timestamp
      }
    })
  }
  
  /**
   * @description 订阅深度数据
   * @param {string} symbol - 交易对
   * @param {number} depth - 深度档位
   */
  const subscribeDepth = (symbol, depth = 20) => {
    subscribe('depth', { symbol, depth }, (data) => {
      depthData.value = {
        symbol: data.symbol,
        bids: data.bids.map(([price, amount]) => [parseFloat(price), parseFloat(amount)]),
        asks: data.asks.map(([price, amount]) => [parseFloat(price), parseFloat(amount)]),
        timestamp: data.timestamp
      }
    })
  }
  
  /**
   * @description 订阅交易数据
   * @param {string} symbol - 交易对
   */
  const subscribeTrades = (symbol) => {
    subscribe('trades', { symbol }, (data) => {
      const newTrade = {
        tradeId: data.tradeId,
        symbol: data.symbol,
        price: parseFloat(data.price),
        amount: parseFloat(data.amount),
        side: data.side,
        timestamp: data.timestamp
      }
      
      tradesData.value.unshift(newTrade)
      // 保持最新的100条交易记录
      if (tradesData.value.length > 100) {
        tradesData.value = tradesData.value.slice(0, 100)
      }
    })
  }
  
  return {
    // 连接状态
    connected,
    connecting,
    error,
    
    // 连接方法
    connect,
    disconnect,
    
    // 市场数据
    klineData,
    tickerData,
    depthData,
    tradesData,
    
    // 订阅方法
    subscribeKline,
    subscribeTicker,
    subscribeDepth,
    subscribeTrades,
    unsubscribe
  }
}