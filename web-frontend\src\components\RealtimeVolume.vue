<!--
  RealtimeVolume.vue
  实时成交量组件

  该组件显示指定时间范围内的实时估算买卖成交量。
  数据来源于 chartStore。
-->
<template>
  <div class="volume-info">
    <div class="volume-header">
      <span class="volume-title">实时成交量 ({{ selectedTimeframe }})</span>
      <a-tooltip title="基于价格变化估算的买卖成交量">
        <InfoCircleOutlined class="volume-info-icon" />
      </a-tooltip>
    </div>
    <div class="volume-stats">
      <div class="volume-item total">
        <div class="volume-label">总成交量</div>
        <div class="volume-value">{{ formatVolume(totalVolume) }}</div>
      </div>
      <div class="volume-item buy">
        <div class="volume-label">估算买量</div>
        <div class="volume-value buy-volume">
          {{ formatVolume(realtimeVolume.buy) }}
          <span class="volume-percent">({{ buyPercent }}%)</span>
        </div>
      </div>
      <div class="volume-item sell">
        <div class="volume-label">估算卖量</div>
        <div class="volume-value sell-volume">
          {{ formatVolume(realtimeVolume.sell) }}
          <span class="volume-percent">({{ sellPercent }}%)</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useInfoPanelStore } from '@/stores/infoPanelStore';
import { useChartSettingsStore } from '@/stores/chartSettingsStore';
import { storeToRefs } from 'pinia'
import { InfoCircleOutlined } from '@ant-design/icons-vue'
import { formatVolume } from '@/utils/formatters'

const infoPanelStore = useInfoPanelStore();
const chartSettingsStore = useChartSettingsStore();

const { realtimeVolume } = storeToRefs(infoPanelStore);
const { selectedTimeframe } = storeToRefs(chartSettingsStore);

const totalVolume = computed(() => {
  const total = realtimeVolume.value.buy + realtimeVolume.value.sell;
  console.log('RealtimeVolume: 计算总成交量:', {
    buy: realtimeVolume.value.buy,
    sell: realtimeVolume.value.sell,
    total,
    realtimeVolume: realtimeVolume.value,
    timestamp: new Date().toLocaleTimeString()
  });
  return total;
});
const buyPercent = computed(() => totalVolume.value > 0 ? ((realtimeVolume.value.buy / totalVolume.value) * 100).toFixed(1) : '0.0')
const sellPercent = computed(() => totalVolume.value > 0 ? ((realtimeVolume.value.sell / totalVolume.value) * 100).toFixed(1) : '0.0')


</script>

<style scoped>
.volume-info {
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.volume-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.volume-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.volume-info-icon {
  color: #8c8c8c;
  cursor: help;
}

.volume-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.volume-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
}

.volume-label {
  font-size: 12px;
  color: #8c8c8c;
}

.volume-value {
  font-size: 13px;
  font-weight: 600;
}

.volume-item.total .volume-value {
  color: #262626;
}

.buy-volume {
  color: #52c41a !important;
}

.sell-volume {
  color: #ff4d4f !important;
}

.volume-percent {
  font-size: 11px;
  margin-left: 4px;
  opacity: 0.8;
}

.dark-theme .volume-info {
  background: #2a2e39;
}

.dark-theme .volume-title,
.dark-theme .volume-value,
.dark-theme .volume-label {
  color: #ffffff;
}

.dark-theme .volume-info-icon {
  color: #8c8c8c;
}
</style>