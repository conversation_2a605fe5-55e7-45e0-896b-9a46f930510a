<template>
  <div class="chart-controls">
    <a-row :gutter="16" align="middle">
      <!-- 交易对选择 -->
      <a-col :span="4">
        <a-select
          v-model:value="selectedSymbol"
          placeholder="选择交易对"
          style="width: 100%"
          @change="handleSymbolChange"
        >
          <a-select-option value="BTC-USDT-SWAP">BTC/USDT</a-select-option>
          <a-select-option value="ETH-USDT-SWAP">ETH/USDT</a-select-option>
          <a-select-option value="BNB-USDT-SWAP">BNB/USDT</a-select-option>
          <a-select-option value="ADA-USDT-SWAP">ADA/USDT</a-select-option>
          <a-select-option value="SOL-USDT-SWAP">SOL/USDT</a-select-option>
          <a-select-option value="DOT-USDT-SWAP">DOT/USDT</a-select-option>
        </a-select>
      </a-col>

      <!-- 时间框架选择 -->
      <a-col :span="8">
        <div class="timeframe-buttons">
          <a-button
            v-for="timeframe in timeframeOptions"
            :key="timeframe.value"
            :type="selectedTimeframe === timeframe.value ? 'primary' : 'default'"
            size="small"
            class="timeframe-btn"
            @click="handleTimeframeChange(timeframe.value)"
          >
            {{ timeframe.label }}
          </a-button>
        </div>
      </a-col>

      <!-- 数据源选择 -->
      <a-col :span="3">
        <a-select
          v-model:value="selectedDataSource"
          placeholder="数据源"
          style="width: 100%"
          @change="handleDataSourceChange"
        >
          <a-select-option value="okx">OKX</a-select-option>
          <a-select-option value="binance">Binance</a-select-option>
          <a-select-option value="huobi">Huobi</a-select-option>
        </a-select>
      </a-col>

      <!-- 指标显示切换 -->
      <a-col :span="4">
        <a-space>
          <a-tooltip title="显示/隐藏技术指标">
            <a-button
              :type="showIndicators ? 'primary' : 'default'"
              size="small"
              @click="handleIndicatorsToggle"
            >
              <template #icon>
                <LineChartOutlined />
              </template>
              指标
            </a-button>
          </a-tooltip>
          
          <a-tooltip title="指标设置">
            <a-button
              size="small"
              @click="$emit('settings-open')"
            >
              <template #icon>
                <SettingOutlined />
              </template>
            </a-button>
          </a-tooltip>
        </a-space>
      </a-col>

      <!-- 主题切换 -->
      <a-col :span="2">
        <a-tooltip :title="isDarkTheme ? '切换到浅色主题' : '切换到深色主题'">
          <a-button
            size="small"
            @click="handleThemeToggle"
          >
            <template #icon>
              <BulbOutlined v-if="!isDarkTheme" />
              <BulbFilled v-else />
            </template>
          </a-button>
        </a-tooltip>
      </a-col>

      <!-- 操作按钮 -->
      <a-col :span="4">
        <a-space>
          <a-tooltip title="刷新数据">
            <a-button
              size="small"
              :loading="isLoading"
              @click="handleRefresh"
            >
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新
            </a-button>
          </a-tooltip>
          
          <a-tooltip title="实时更新">
            <a-button
              :type="isRealTimeUpdate ? 'primary' : 'default'"
              size="small"
              @click="handleRealTimeToggle"
            >
              <template #icon>
                <PlayCircleOutlined v-if="!isRealTimeUpdate" />
                <PauseCircleOutlined v-else />
              </template>
              {{ isRealTimeUpdate ? '暂停' : '实时' }}
            </a-button>
          </a-tooltip>
          
          <a-dropdown>
            <template #overlay>
              <a-menu @click="handleIndicatorAdd">
                <a-menu-item key="macd">
                  <BarChartOutlined />
                  MACD
                </a-menu-item>
                <a-menu-item key="boll">
                  <LineChartOutlined />
                  布林带
                </a-menu-item>
                <a-menu-item key="kdj">
                  <StockOutlined />
                  KDJ
                </a-menu-item>
                <a-menu-item key="ma">
                  <RiseOutlined />
                  移动平均线
                </a-menu-item>
                <a-menu-item key="rsi">
                  <FundOutlined />
                  RSI
                </a-menu-item>
                <a-menu-item key="supertrend">
                  <RiseOutlined />
                  Supertrend
                </a-menu-item>
              </a-menu>
            </template>
            <a-button size="small">
              <template #icon>
                <PlusOutlined />
              </template>
              添加指标
              <DownOutlined />
            </a-button>
          </a-dropdown>
        </a-space>
      </a-col>

      <!-- 绘图工具 -->
      <a-col :span="3">
        <a-space>
          <a-tooltip title="趋势线">
            <a-button
              :type="selectedDrawingTool === 'trendline' ? 'primary' : 'default'"
              size="small"
              @click="handleDrawingToolSelect('trendline')"
            >
              <template #icon>
                <LineOutlined />
              </template>
            </a-button>
          </a-tooltip>
          
          <a-tooltip title="水平线">
            <a-button
              :type="selectedDrawingTool === 'horizontal' ? 'primary' : 'default'"
              size="small"
              @click="handleDrawingToolSelect('horizontal')"
            >
              <template #icon>
                <MinusOutlined />
              </template>
            </a-button>
          </a-tooltip>
          
          <a-tooltip title="矩形">
            <a-button
              :type="selectedDrawingTool === 'rectangle' ? 'primary' : 'default'"
              size="small"
              @click="handleDrawingToolSelect('rectangle')"
            >
              <template #icon>
                <BorderOutlined />
              </template>
            </a-button>
          </a-tooltip>
          
          <a-tooltip title="清除绘图">
            <a-button
              size="small"
              @click="handleClearDrawings"
            >
              <template #icon>
                <ClearOutlined />
              </template>
            </a-button>
          </a-tooltip>
        </a-space>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useChartStore } from '@/stores/chartStore'
import {
  LineChartOutlined,
  SettingOutlined,
  BulbOutlined,
  BulbFilled,
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  PlusOutlined,
  DownOutlined,
  BarChartOutlined,
  StockOutlined,
  FundOutlined,
  RiseOutlined,
  LineOutlined,
  MinusOutlined,
  BorderOutlined,
  ClearOutlined
} from '@ant-design/icons-vue'

// 定义事件
const emit = defineEmits([
  'symbol-change',
  'timeframe-change',
  'theme-toggle',
  'indicators-toggle',
  'refresh',
  'settings-open',
  'real-time-toggle',
  'indicator-add',
  'drawing-tool-select',
  'clear-drawings'
])

// 使用状态管理
const chartStore = useChartStore()
const {
  selectedSymbol,
  selectedTimeframe,
  isDarkTheme,
  isLoading,
  displaySettings
} = storeToRefs(chartStore)

// 本地状态
const selectedDataSource = ref('okx')
const showIndicators = ref(true)
const isRealTimeUpdate = ref(false)
const selectedDrawingTool = ref('')

// 时间周期选项
const timeframeOptions = [
  { value: '1M', label: '1分' },
        { value: '5M', label: '5分' },
        { value: '15M', label: '15分' },
  { value: '30m', label: '30分' },
  { value: '1h', label: '1小时' },
  { value: '4h', label: '4小时' },
  { value: '1d', label: '天' },
  { value: '1w', label: '周' }
]

// 事件处理函数
const handleSymbolChange = (value) => {
  emit('symbol-change', value)
}

const handleTimeframeChange = (value) => {
  emit('timeframe-change', value)
}

const handleDataSourceChange = (value) => {
  console.log('数据源切换:', value)
  // 这里可以添加数据源切换逻辑
}

const handleThemeToggle = () => {
  emit('theme-toggle')
}

const handleIndicatorsToggle = () => {
  showIndicators.value = !showIndicators.value
  emit('indicators-toggle')
}

const handleRefresh = () => {
  emit('refresh')
}

const handleRealTimeToggle = () => {
  isRealTimeUpdate.value = !isRealTimeUpdate.value
  emit('real-time-toggle', isRealTimeUpdate.value)
}

const handleIndicatorAdd = ({ key }) => {
  emit('indicator-add', key)
}

const handleDrawingToolSelect = (tool) => {
  selectedDrawingTool.value = selectedDrawingTool.value === tool ? '' : tool
  emit('drawing-tool-select', selectedDrawingTool.value)
}

const handleClearDrawings = () => {
  selectedDrawingTool.value = ''
  emit('clear-drawings')
}
</script>

<style scoped>
.chart-controls {
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-controls .ant-select {
  border-radius: 6px;
}

.chart-controls .ant-btn {
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-controls .ant-btn-sm {
  height: 28px;
  padding: 0 8px;
  font-size: 12px;
}

.chart-controls .ant-space-item {
  display: flex;
  align-items: center;
}

/* 时间周期按钮组样式 */
.timeframe-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.timeframe-btn {
  min-width: 40px;
  height: 28px;
  padding: 0 8px;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background: #ffffff;
  color: #666666;
  transition: all 0.2s;
}

.timeframe-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.timeframe-btn.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
  color: #ffffff;
}

.timeframe-btn.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 深色主题样式 */
.chart-controls.dark-theme {
  background: #2a2e39;
  border-color: #434651;
}

.chart-controls.dark-theme .ant-select {
  background: #2a2e39;
  border-color: #434651;
  color: #ffffff;
}

.chart-controls.dark-theme .ant-btn {
  background: #2a2e39;
  border-color: #434651;
  color: #ffffff;
}

.chart-controls.dark-theme .ant-btn:hover {
  background: #434651;
  border-color: #5a5f6b;
}

.chart-controls.dark-theme .ant-btn-primary {
  background: #2962ff;
  border-color: #2962ff;
}

.chart-controls.dark-theme .ant-btn-primary:hover {
  background: #4c7fff;
  border-color: #4c7fff;
}

/* 深色主题时间周期按钮样式 */
.chart-controls.dark-theme .timeframe-btn {
  background: #2a2e39;
  border-color: #434651;
  color: #ffffff;
}

.chart-controls.dark-theme .timeframe-btn:hover {
  border-color: #2962ff;
  color: #2962ff;
}

.chart-controls.dark-theme .timeframe-btn.ant-btn-primary {
  background: #2962ff;
  border-color: #2962ff;
  color: #ffffff;
}

.chart-controls.dark-theme .timeframe-btn.ant-btn-primary:hover {
  background: #4c7fff;
  border-color: #4c7fff;
}
</style>