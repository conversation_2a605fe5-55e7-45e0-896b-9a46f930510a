#!/usr/bin/env python3
"""
后端服务启动脚本
"""

import sys
import os
import uvicorn

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

if __name__ == "__main__":
    print("启动后端服务器...")
    try:
        uvicorn.run(
            "webapp.main:app",
            host="127.0.0.1",
            port=8080,
            reload=False,
            log_level="info"
        )
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
