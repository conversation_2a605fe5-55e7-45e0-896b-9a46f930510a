#!/usr/bin/env python3
"""
后端服务启动脚本
"""

import sys
import os
import uvicorn

# 添加webapp目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
webapp_path = os.path.join(project_root, "webapp")
sys.path.insert(0, webapp_path)

if __name__ == "__main__":
    print("启动后端服务器...")
    try:
        uvicorn.run(
            "main:app",
            host="127.0.0.1",
            port=8080,
            reload=False,
            log_level="info"
        )
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
