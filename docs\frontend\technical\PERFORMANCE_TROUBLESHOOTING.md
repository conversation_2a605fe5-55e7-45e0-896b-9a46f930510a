# 性能监控故障排除指南

## 问题描述

当您遇到以下错误时：
```
帧率性能警告: 当前帧率 0 FPS 低于阈值
Uncaught ReferenceError: performanceMonitor is not defined
```

## 问题原因分析

### 1. performanceMonitor 未定义
- **原因**: performanceMonitor 对象未挂载到全局作用域
- **影响**: 无法在控制台中直接访问性能监控器
- **解决方案**: 已在 `main.js` 中添加全局挂载

### 2. 帧率为 0 FPS
可能的原因包括：
- 页面处于后台或隐藏状态
- 浏览器标签页未激活
- JavaScript 主线程被阻塞
- 图表未正确渲染
- 性能监控逻辑错误

## 解决方案

### 1. 立即修复

#### 重启应用
```bash
# 停止前端服务
# 然后重新启动
start-vite.bat
```

#### 在控制台中诊断
```javascript
// 检查性能监控状态
window.performanceMonitor.diagnose()

// 获取性能报告
window.performanceMonitor.getPerformanceReport()

// 重启性能监控
window.performanceMonitor.restart()

// 检查图表实例
window.chartInstances || 'Chart instances not found'
```

### 2. 诊断步骤

#### 步骤 1: 检查页面状态
```javascript
// 检查页面可见性
console.log('页面隐藏状态:', document.hidden)
console.log('可见性状态:', document.visibilityState)

// 检查浏览器支持
console.log('Performance API 支持:', !!performance)
console.log('Memory API 支持:', !!performance.memory)
```

#### 步骤 2: 检查性能监控状态
```javascript
// 获取详细诊断信息
const diagnosis = window.performanceMonitor.diagnose()
console.table(diagnosis)

// 检查监控是否启动
if (!diagnosis.isMonitoring) {
  console.log('性能监控未启动，正在启动...')
  window.performanceMonitor.startMonitoring()
}
```

#### 步骤 3: 检查图表状态
```javascript
// 检查图表实例
if (window.chartInstances) {
  console.log('图表实例:', window.chartInstances)
} else {
  console.log('未找到图表实例')
}

// 检查 ECharts 全局对象
if (window.echarts) {
  console.log('ECharts 版本:', window.echarts.version)
} else {
  console.log('ECharts 未加载')
}
```

### 3. 性能优化建议

#### 浏览器优化
- 确保浏览器标签页处于激活状态
- 关闭不必要的浏览器扩展
- 清理浏览器缓存和 Cookie
- 使用 Chrome 或 Edge 等现代浏览器

#### 系统优化
- 关闭其他占用 CPU 的应用程序
- 确保有足够的可用内存（建议 > 4GB）
- 检查系统 CPU 使用率

#### 应用优化
- 减少同时显示的技术指标数量
- 降低图表更新频率
- 使用性能模式配置

```javascript
// 应用高性能配置
import { applyConfigPreset } from '@/config/chartConfig'
applyConfigPreset('highPerformance')
```

### 4. 配置调整

#### 性能配置
```javascript
// 调整性能阈值
window.performanceMonitor.updateConfig({
  minFrameRate: 20, // 降低帧率阈值
  renderThreshold: 200, // 提高渲染时间阈值
  memoryThreshold: 0.9 // 提高内存阈值
})
```

#### 图表配置
```javascript
// 优化图表性能
const chartConfig = {
  animation: false, // 禁用动画
  progressive: 1000, // 启用渐进渲染
  progressiveThreshold: 3000, // 渐进渲染阈值
  hoverLayerThreshold: 3000 // 悬停层阈值
}
```

## 常见问题 FAQ

### Q1: 为什么帧率一直是 0 FPS？
**A**: 可能原因：
1. 页面处于后台 - 切换到前台
2. 浏览器性能限制 - 重启浏览器
3. JavaScript 错误 - 检查控制台错误
4. 性能监控未启动 - 调用 `restart()` 方法

### Q2: 如何提高图表渲染性能？
**A**: 优化建议：
1. 减少数据点数量
2. 禁用不必要的动画效果
3. 使用数据采样
4. 启用渐进渲染

### Q3: 内存使用过高怎么办？
**A**: 解决方案：
1. 定期清理图表数据
2. 限制历史数据保留量
3. 使用内存管理器
4. 重启应用释放内存

### Q4: 性能监控影响应用性能吗？
**A**: 影响很小：
1. 帧率监控使用 `requestAnimationFrame`
2. 内存监控每 5 秒执行一次
3. 可以通过配置禁用监控

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 浏览器版本和操作系统
2. 控制台错误信息
3. 性能诊断报告
4. 复现步骤

```javascript
// 生成诊断报告
const report = {
  userAgent: navigator.userAgent,
  performance: window.performanceMonitor.diagnose(),
  memory: performance.memory,
  timing: performance.timing
}
console.log('诊断报告:', JSON.stringify(report, null, 2))
```