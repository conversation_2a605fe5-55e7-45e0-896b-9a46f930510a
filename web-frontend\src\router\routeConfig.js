export const routesConfig = [
  { path: '/', component: 'SimpleDashboard', meta: { requiresAuth: true, roles: ['user', 'admin'] } },
  { path: '/trading/modular', component: 'ModularTradingDashboard', meta: { requiresAuth: false } },
  { path: '/test', component: 'Test' },
  { path: '/login', component: 'Login' },
  { path: '/register', component: 'Register' },
  { path: '/strategies', component: 'StrategyList' },
  { path: '/orders', component: 'OrderList' },
  { path: '/ai', component: 'AIAnalysis' },
  { path: '/ai/predict', component: 'AIPredict' },
  { path: '/ai/signal', component: 'AISignal' },
  { path: '/ai/risk', component: 'AIRisk' },
  { path: '/ai/risk-analysis', component: 'AIRiskAnalysis' },
  { path: '/ai/history', component: 'AIHistory' },
  { path: '/dashboard', component: 'Dashboard' },
  { path: '/account', component: 'Account' },
  { path: '/settings/api', component: 'APISettings' },
  { path: '/settings/user', component: 'UserSettings' },
  { path: '/statistics', component: 'Statistics' },
  { path: '/trading/real', component: 'RealTrading' },
  { path: '/charts/advanced', component: 'AdvancedCharts', meta: { requiresAuth: false } },
  { path: '/risk/management', component: 'RiskManagement' },
  { path: '/test/chart', component: 'ChartTest' },
  { path: '/charts/simple', component: 'SimpleCharts' },
  { path: '/alerts/price', component: 'PriceAlerts' },
  { path: '/test/auth', component: 'AuthTest', meta: { requiresAuth: false } },
  { path: '/test/realtime', component: 'RealTimeTest' },
  { path: '/trading/signals', component: 'TradingSignalsPage' },
  { path: '/403', component: 'Forbidden', meta: { requiresAuth: false } },
  { path: '/okx/market', component: 'OKXMarket' },
  { path: '/okx/account', component: 'OKXAccount' },
  { path: '/okx/order', component: 'OKXOrder' },
  { path: '/okx/positions', component: 'OKXPositions' },
  { path: '/okx/leverage', component: 'OKXLeverage' },
  { path: '/okx/transfer', component: 'OKXTransfer' },
  { path: '/okx/orders', component: 'OKXOrderHistory' },
  { path: '/okx/order-history', component: 'OKXOrderHistory' },
  { path: '/test/indicators', component: 'IndicatorTestPage', meta: { requiresAuth: false } },
];