<template>
  <div>
    <a-card title="OKX 下单" style="margin: 20px">
      <a-form layout="inline" @submit.prevent="onOrder">
        <a-form-item label="交易对">
          <a-input v-model="order.instId" placeholder="如 BTC-USDT-SWAP" style="width: 180px" />
        </a-form-item>
        <a-form-item label="方向">
          <a-select v-model="order.side" style="width: 100px">
            <a-select-option value="buy">买入</a-select-option>
            <a-select-option value="sell">卖出</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="价格">
          <a-input-number v-model="order.px" style="width: 120px" />
        </a-form-item>
        <a-form-item label="数量">
          <a-input-number v-model="order.sz" style="width: 100px" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="onOrder">下单</a-button>
        </a-form-item>
      </a-form>
    </a-card>
    <a-card title="OKX 订单列表" style="margin: 20px">
      <a-table :dataSource="orders" :columns="columns" rowKey="ordId" />
    </a-card>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import axios from 'axios'
import { message } from 'ant-design-vue'
const order = ref({ instId: '', side: 'buy', px: null, sz: null })
const orders = ref([])
const columns = [
  { title: '订单ID', dataIndex: 'ordId' },
  { title: '交易对', dataIndex: 'instId' },
  { title: '方向', dataIndex: 'side' },
  { title: '价格', dataIndex: 'px' },
  { title: '数量', dataIndex: 'sz' },
  { title: '状态', dataIndex: 'state' }
]
const onOrder = async () => {
  try {
    await axios.post('/api/okx/order/create', order.value)
    message.success('下单请求已发送（后端暂未实现真实下单）')
  } catch (e) {
    message.error(e.response?.data?.detail || '下单失败')
  }
}
const fetchOrders = async () => {
  try {
    await axios.get('/api/okx/order/list')
    // 后端暂未实现，orders.value = res.data.data
    orders.value = []
  } catch (e) {
    orders.value = []
  }
}
</script>