import { defineStore } from 'pinia';
import { ref, watch } from 'vue';
import { useChartDataStore } from './chartDataStore';
import { useChartSettingsStore } from './chartSettingsStore';
import { UPDATE_INTERVALS } from '@/constants/updateConfig';

export const useInfoPanelStore = defineStore('infoPanel', () => {
  const chartDataStore = useChartDataStore();
  const chartSettingsStore = useChartSettingsStore();

  // State
  const realtimeVolume = ref({ buy: 0, sell: 0, buyPercent: 0, sellPercent: 0 });
  const marketSentiment = ref({ sentiment: 'neutral', score: 50 });
  const supportResistance = ref({ support1: 0, support2: 0, resistance1: 0, resistance2: 0 });
  const priceAlerts = ref([]);
  const indicatorData = ref({ ma: [], rsi: [] });

  // Actions
  function updatePanelData() {
    console.log('infoPanelStore: updatePanelData 被调用');
    console.log('infoPanelStore: chartData状态:', {
      isArray: Array.isArray(chartDataStore.chartData),
      length: chartDataStore.chartData?.length,
      hasChartData: chartDataStore.hasChartData,
      sample: chartDataStore.chartData?.slice(0, 2)
    });
    
    if (!chartDataStore.hasChartData) {
      console.log('infoPanelStore: hasChartData为false，退出updatePanelData');
      return;
    }
    
    // 1. 计算实时成交量 - 基于最近的K线数据
    if (!Array.isArray(chartDataStore.chartData) || chartDataStore.chartData.length === 0) {
      console.log('infoPanelStore: 没有有效的chartData，退出updatePanelData');
      return; // 如果没有数据，直接返回
    }
    
    const recentData = chartDataStore.chartData.slice(-20); // 取最近20根K线
    let buyVolume = 0;
    let sellVolume = 0;
    
    recentData.forEach(candle => {
      let timestamp, open, high, low, close, volume;
      
      // 处理不同的数据格式
      if (Array.isArray(candle)) {
        // 数组格式: [timestamp, open, high, low, close, volume]
        if (candle.length < 6) return;
        [timestamp, open, high, low, close, volume] = candle;
      } else if (typeof candle === 'object' && candle !== null) {
        // 对象格式: {time, open, high, low, close, volume}
        timestamp = candle.time;
        open = candle.open;
        high = candle.high;
        low = candle.low;
        close = candle.close;
        volume = candle.volume;
      } else {
        return; // 跳过无效的candle数据
      }
      
      const openPrice = parseFloat(open);
      const closePrice = parseFloat(close);
      const volumeValue = parseFloat(volume);
      
      if (isNaN(openPrice) || isNaN(closePrice) || isNaN(volumeValue)) {
        return; // 跳过无效数据
      }
      
      // 简单判断：收盘价高于开盘价认为是买入，否则是卖出
      if (closePrice > openPrice) {
        buyVolume += volumeValue;
      } else {
        sellVolume += volumeValue;
      }
    });
    
    const totalVolume = buyVolume + sellVolume;
    realtimeVolume.value = {
        buy: buyVolume,
        sell: sellVolume,
        buyPercent: totalVolume > 0 ? (buyVolume / totalVolume) * 100 : 0,
        sellPercent: totalVolume > 0 ? (sellVolume / totalVolume) * 100 : 0
    };
    
    console.log('infoPanelStore: 成交量计算结果:', {
        recentDataLength: recentData.length,
        buyVolume,
        sellVolume,
        totalVolume,
        realtimeVolume: realtimeVolume.value
    });
    
    // 如果计算结果为0，检查原始数据
    if (totalVolume === 0) {
      console.log('infoPanelStore: 成交量为0，检查原始数据:', {
        recentDataSample: recentData.slice(0, 3),
        firstCandleDetails: recentData[0] ? {
          raw: recentData[0],
          isArray: Array.isArray(recentData[0]),
          keys: typeof recentData[0] === 'object' ? Object.keys(recentData[0]) : 'not object'
        } : 'no data'
      });
    }

    // 2. Mock Market Sentiment
    const lastPrice = chartDataStore.latestPrice.close;
    const firstPrice = chartDataStore.chartData[0][4];
    const priceChange = (lastPrice - firstPrice) / firstPrice;
    const rsiValue = indicatorData.value.rsi.length > 0 ? indicatorData.value.rsi[indicatorData.value.rsi.length - 1].value : 50;
    let score = 50 + priceChange * 200 + (rsiValue - 50);
    score = Math.max(0, Math.min(100, score));
    let sentiment = 'neutral';
    if (score > 70) sentiment = 'greed';
    if (score < 30) sentiment = 'fear';
    marketSentiment.value = { sentiment, score };

    // 3. Mock Support/Resistance
    const highPrices = chartDataStore.chartData.map(d => parseFloat(d[2]));
    const lowPrices = chartDataStore.chartData.map(d => parseFloat(d[3]));
    supportResistance.value = {
        support1: Math.min(...lowPrices.slice(-100)),
        support2: Math.min(...lowPrices.slice(-200)),
        resistance1: Math.max(...highPrices.slice(-100)),
        resistance2: Math.max(...highPrices.slice(-200)),
    };

    // 4. Update MA and RSI indicators
    calculateMAIndicators();
    calculateRSIIndicators();
  }

  const calculateMAIndicators = () => {
    const periods = chartSettingsStore.indicatorSettings.ma.periods;
    const closePrices = chartDataStore.chartData.map(p => parseFloat(p[4]));
    indicatorData.value.ma = periods.map(period => {
      if (closePrices.length < period) return { period, value: 0 };
      const sum = closePrices.slice(-period).reduce((acc, val) => acc + val, 0);
      return { period, value: parseFloat((sum / period).toFixed(2)) };
    });
  }

  const calculateRSIIndicators = () => {
    const periods = [chartSettingsStore.indicatorSettings.rsi.period];
    const closePrices = chartDataStore.chartData.map(p => parseFloat(p[4]));
    indicatorData.value.rsi = periods.map(period => {
      if (closePrices.length < period + 1) return { period, value: 50 };
      let gains = 0;
      let losses = 0;
      for (let i = closePrices.length - period; i < closePrices.length; i++) {
        const diff = closePrices[i] - closePrices[i-1];
        if (diff > 0) {
          gains += diff;
        } else {
          losses -= diff;
        }
      }
      const avgGain = gains / period;
      const avgLoss = losses / period;
      if (avgLoss === 0) return { period, value: 100 };
      const rs = avgGain / avgLoss;
      const rsi = 100 - (100 / (1 + rs));
      return { period, value: parseFloat(rsi.toFixed(2)) };
    });
  }

  const addPriceAlert = (alert) => {
    const newAlert = { ...alert, id: Date.now() };
    priceAlerts.value.push(newAlert);
  };

  const removePriceAlert = (id) => {
    priceAlerts.value = priceAlerts.value.filter(a => a.id !== id);
  };

  // 监听chartData变化
  watch(() => chartDataStore.chartData, () => {
    console.log('infoPanelStore: chartData变化，更新面板数据');
    updatePanelData();
  }, { deep: true, immediate: true });
  
  // 监听currentPrice变化
  watch(() => chartDataStore.currentPrice, () => {
    console.log('infoPanelStore: currentPrice变化，更新面板数据');
    updatePanelData();
  }, { deep: true });
  
  // 初始化时也调用一次
  if (chartDataStore.hasChartData) {
    updatePanelData();
  }

  return {
    realtimeVolume,
    marketSentiment,
    supportResistance,
    priceAlerts,
    indicatorData,
    updatePanelData,
    addPriceAlert,
    removePriceAlert
  };
});