<template>
  <div class="indicator-panel" :class="{ 'dark-theme': isDarkTheme }">
    <a-card title="技术指标" size="small" :bordered="false">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新数据">
            <a-button size="small" type="text" @click="refreshData">
              <ReloadOutlined />
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <div class="indicator-container">
        <!-- MA 指标 -->
        <div class="indicator-section">
          <h4 class="section-title">MA 指标</h4>
          <div class="indicator-list">
            <div class="indicator-item" v-for="ma in maIndicators" :key="ma.period">
              <div class="indicator-label">{{ ma.name }}</div>
              <div class="indicator-status" :class="ma.status">
                {{ ma.statusText }}
              </div>
            </div>
          </div>
        </div>

        <!-- RSI 指标 -->
        <div class="indicator-section">
          <h4 class="section-title">RSI 指标</h4>
          <div class="indicator-list">
            <div class="indicator-item" v-for="rsi in rsiIndicators" :key="rsi.period">
              <div class="indicator-label">{{ rsi.name }}</div>
              <div class="indicator-value">{{ rsi.value }}</div>
              <div class="indicator-status" :class="rsi.status">
                {{ rsi.statusText }}
              </div>
            </div>
          </div>
        </div>


      </div>
    </a-card>


  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { useChartStore } from '@/stores/chartStore'
import {
  ReloadOutlined
} from '@ant-design/icons-vue'

// 使用状态管理
const chartStore = useChartStore()
const { isDarkTheme } = storeToRefs(chartStore)

// 本地状态
const updateInterval = ref(null)

// MA 指标数据
const maIndicators = ref([
  {
    name: 'MA (5)',
    period: 5,
    value: 45230.5,
    status: 'neutral',
    statusText: '中性'
  },
  {
    name: 'MA (10)',
    period: 10,
    value: 45180.2,
    status: 'neutral',
    statusText: '中性'
  },
  {
    name: 'MA (20)',
    period: 20,
    value: 45120.8,
    status: 'neutral',
    statusText: '中性'
  },
  {
    name: 'MA (50)',
    period: 50,
    value: 44980.3,
    status: 'neutral',
    statusText: '中性'
  },
  {
    name: 'MA (200)',
    period: 200,
    value: 44250.7,
    status: 'neutral',
    statusText: '中性'
  }
])

// RSI 指标数据
const rsiIndicators = ref([
  {
    name: 'RSI (14)',
    period: 14,
    value: 'NaN',
    status: 'neutral',
    statusText: '中性'
  }
])

// 设置
const settings = ref({
  updateInterval: 5000,
  autoUpdate: true,
  showValues: true
})

/**
 * 刷新数据
 */
const refreshData = () => {
  // 模拟MA指标数据更新
  maIndicators.value.forEach(ma => {
    ma.value = ma.value + (Math.random() - 0.5) * 100
    // 随机更新状态
    const statuses = ['bullish', 'bearish', 'neutral']
    const statusTexts = ['看涨', '看跌', '中性']
    const randomIndex = Math.floor(Math.random() * 3)
    ma.status = statuses[randomIndex]
    ma.statusText = statusTexts[randomIndex]
  })
  
  // 模拟RSI指标数据更新
  rsiIndicators.value.forEach(rsi => {
    const randomValue = Math.floor(Math.random() * 100)
    rsi.value = randomValue.toString()
    
    if (randomValue > 70) {
      rsi.status = 'overbought'
      rsi.statusText = '超买'
    } else if (randomValue < 30) {
      rsi.status = 'oversold'
      rsi.statusText = '超卖'
    } else {
      rsi.status = 'neutral'
      rsi.statusText = '中性'
    }
  })
  
  message.success('指标数据已刷新')
}

/**
 * 开始自动更新
 */
const startAutoUpdate = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
  }
  
  updateInterval.value = setInterval(() => {
    refreshData()
  }, 30000) // 30秒更新一次
}

/**
 * 停止自动更新
 */
const stopAutoUpdate = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
    updateInterval.value = null
  }
}

// 生命周期
onMounted(() => {
  startAutoUpdate()
})

onUnmounted(() => {
  stopAutoUpdate()
})
</script>

<style scoped>
.indicator-panel {
  height: 100%;
}

.indicator-panel .ant-card {
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.indicator-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

.indicator-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.indicator-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.indicator-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.indicator-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.indicator-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.indicator-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.indicator-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

.indicator-status.bullish {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.indicator-status.bearish {
  background: #fff2e8;
  color: #fa541c;
  border: 1px solid #ffbb96;
}

.indicator-status.neutral {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.indicator-status.overbought {
  background: #fff1f0;
  color: #f5222d;
  border: 1px solid #ffa39e;
}

.indicator-status.oversold {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.refresh-button {
  margin-top: 16px;
  width: 100%;
}

/* 深色主题样式 */
.indicator-panel.dark-theme .ant-card {
  background: #1f1f1f;
  border-color: #434343;
}

.indicator-panel.dark-theme .indicator-section {
  background: #2a2a2a;
}

.indicator-panel.dark-theme .indicator-item {
  background: #333;
  border-color: #434343;
}

.indicator-panel.dark-theme .indicator-item:hover {
  background: #3a3a3a;
}

.indicator-panel.dark-theme .section-title,
.indicator-panel.dark-theme .indicator-name,
.indicator-panel.dark-theme .indicator-value {
  color: #fff;
}

.indicator-panel.dark-theme .indicator-status.bullish {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  border-color: rgba(82, 196, 26, 0.3);
}

.indicator-panel.dark-theme .indicator-status.bearish {
  background: rgba(250, 84, 28, 0.1);
  color: #fa541c;
  border-color: rgba(250, 84, 28, 0.3);
}

.indicator-panel.dark-theme .indicator-status.neutral {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  border-color: rgba(24, 144, 255, 0.3);
}

.indicator-panel.dark-theme .indicator-status.overbought {
  background: rgba(245, 34, 45, 0.1);
  color: #f5222d;
  border-color: rgba(245, 34, 45, 0.3);
}

.indicator-panel.dark-theme .indicator-status.oversold {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  border-color: rgba(82, 196, 26, 0.3);
}

</style>