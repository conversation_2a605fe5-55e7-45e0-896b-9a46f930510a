import { ref, reactive, computed, watch } from 'vue'
import { message, notification } from 'ant-design-vue'

/**
 * 智能预警系统组合式函数
 * 提供基于技术指标、价格行为和市场情绪的智能预警功能
 */
export function useSmartAlerts() {
  // 响应式数据
  const alerts = ref([])
  const activeAlerts = ref([])
  const alertHistory = ref([])
  const isMonitoring = ref(false)
  const alertSettings = reactive({
    enabled: true,
    soundEnabled: true,
    emailEnabled: false,
    pushEnabled: true,
    sensitivity: 'medium', // low, medium, high
    categories: {
      price: true,
      technical: true,
      volume: true,
      sentiment: true,
      pattern: true
    }
  })
  
  // 预警类型定义
  const ALERT_TYPES = {
    PRICE_BREAKOUT: {
      id: 'price_breakout',
      name: '价格突破',
      category: 'price',
      priority: 'high',
      icon: '📈',
      color: '#52c41a'
    },
    PRICE_BREAKDOWN: {
      id: 'price_breakdown',
      name: '价格跌破',
      category: 'price',
      priority: 'high',
      icon: '📉',
      color: '#ff4d4f'
    },
    SUPPORT_RESISTANCE: {
      id: 'support_resistance',
      name: '支撑阻力',
      category: 'price',
      priority: 'medium',
      icon: '🔄',
      color: '#faad14'
    },
    RSI_EXTREME: {
      id: 'rsi_extreme',
      name: 'RSI极值',
      category: 'technical',
      priority: 'medium',
      icon: '⚡',
      color: '#722ed1'
    },
    MACD_SIGNAL: {
      id: 'macd_signal',
      name: 'MACD信号',
      category: 'technical',
      priority: 'medium',
      icon: '🎯',
      color: '#1890ff'
    },
    VOLUME_SPIKE: {
      id: 'volume_spike',
      name: '成交量异动',
      category: 'volume',
      priority: 'high',
      icon: '📊',
      color: '#13c2c2'
    },
    SENTIMENT_EXTREME: {
      id: 'sentiment_extreme',
      name: '情绪极值',
      category: 'sentiment',
      priority: 'high',
      icon: '😱',
      color: '#eb2f96'
    },
    PATTERN_DETECTED: {
      id: 'pattern_detected',
      name: '形态识别',
      category: 'pattern',
      priority: 'medium',
      icon: '🔍',
      color: '#f5222d'
    },
    TREND_REVERSAL: {
      id: 'trend_reversal',
      name: '趋势反转',
      category: 'technical',
      priority: 'high',
      icon: '🔄',
      color: '#fa8c16'
    },
    VOLATILITY_SPIKE: {
      id: 'volatility_spike',
      name: '波动率异常',
      category: 'technical',
      priority: 'medium',
      icon: '⚠️',
      color: '#fadb14'
    }
  }
  
  // 预警优先级
  const PRIORITY_LEVELS = {
    low: { weight: 1, threshold: 0.3 },
    medium: { weight: 2, threshold: 0.5 },
    high: { weight: 3, threshold: 0.7 }
  }
  
  // 敏感度设置
  const SENSITIVITY_SETTINGS = {
    low: {
      priceThreshold: 0.05,
      volumeThreshold: 2.0,
      rsiThreshold: { oversold: 20, overbought: 80 },
      sentimentThreshold: { fear: 15, greed: 85 }
    },
    medium: {
      priceThreshold: 0.03,
      volumeThreshold: 1.5,
      rsiThreshold: { oversold: 30, overbought: 70 },
      sentimentThreshold: { fear: 25, greed: 75 }
    },
    high: {
      priceThreshold: 0.02,
      volumeThreshold: 1.2,
      rsiThreshold: { oversold: 35, overbought: 65 },
      sentimentThreshold: { fear: 35, greed: 65 }
    }
  }
  
  /**
   * 开始监控
   * @param {Object} options - 监控选项
   */
  const startMonitoring = (options = {}) => {
    if (isMonitoring.value) {
      message.warning('预警系统已在运行中')
      return
    }
    
    isMonitoring.value = true
    message.success('智能预警系统已启动')
    
    // 这里可以添加定时器或WebSocket连接来实时监控
    console.log('预警监控已启动', options)
  }
  
  /**
   * 停止监控
   */
  const stopMonitoring = () => {
    isMonitoring.value = false
    message.info('智能预警系统已停止')
  }
  
  /**
   * 分析并生成预警
   * @param {Object} marketData - 市场数据
   * @param {Object} technicalIndicators - 技术指标
   * @param {Object} sentimentData - 情绪数据
   * @param {Array} patterns - 识别的形态
   * @returns {Array} 生成的预警列表
   */
  const analyzeAndGenerateAlerts = (marketData, technicalIndicators, sentimentData, patterns = []) => {
    if (!alertSettings.enabled || !isMonitoring.value) {
      return []
    }
    
    const newAlerts = []
    const currentSettings = SENSITIVITY_SETTINGS[alertSettings.sensitivity]
    
    try {
      // 价格预警
      if (alertSettings.categories.price) {
        newAlerts.push(...analyzePriceAlerts(marketData, currentSettings))
      }
      
      // 技术指标预警
      if (alertSettings.categories.technical) {
        newAlerts.push(...analyzeTechnicalAlerts(technicalIndicators, currentSettings))
      }
      
      // 成交量预警
      if (alertSettings.categories.volume) {
        newAlerts.push(...analyzeVolumeAlerts(marketData, currentSettings))
      }
      
      // 情绪预警
      if (alertSettings.categories.sentiment && sentimentData) {
        newAlerts.push(...analyzeSentimentAlerts(sentimentData, currentSettings))
      }
      
      // 形态预警
      if (alertSettings.categories.pattern && patterns.length > 0) {
        newAlerts.push(...analyzePatternAlerts(patterns))
      }
      
      // 处理新预警
      if (newAlerts.length > 0) {
        processNewAlerts(newAlerts)
      }
      
      return newAlerts
      
    } catch (error) {
      console.error('预警分析失败:', error)
      return []
    }
  }
  
  /**
   * 分析价格预警
   * @param {Object} marketData - 市场数据
   * @param {Object} settings - 敏感度设置
   * @returns {Array} 价格预警列表
   */
  const analyzePriceAlerts = (marketData, settings) => {
    const alerts = []
    const { klineData, currentPrice } = marketData
    
    if (!klineData || klineData.length < 20) return alerts
    
    const recent = klineData.slice(-20)
    const latest = recent[recent.length - 1]
    
    // 计算支撑阻力位
    const supportResistance = calculateSupportResistance(recent)
    
    // 价格突破预警
    const priceChange = (currentPrice - latest.open) / latest.open
    if (Math.abs(priceChange) > settings.priceThreshold) {
      alerts.push(createAlert(
        priceChange > 0 ? ALERT_TYPES.PRICE_BREAKOUT : ALERT_TYPES.PRICE_BREAKDOWN,
        {
          price: currentPrice,
          change: priceChange,
          changePercent: (priceChange * 100).toFixed(2)
        },
        `价格${priceChange > 0 ? '突破' : '跌破'} ${(Math.abs(priceChange) * 100).toFixed(2)}%`,
        0.8
      ))
    }
    
    // 支撑阻力位预警
    supportResistance.forEach(level => {
      const distance = Math.abs(currentPrice - level.price) / level.price
      if (distance < 0.01) { // 接近支撑阻力位
        alerts.push(createAlert(
          ALERT_TYPES.SUPPORT_RESISTANCE,
          {
            price: currentPrice,
            level: level.price,
            type: level.type,
            strength: level.strength
          },
          `价格接近${level.type === 'support' ? '支撑' : '阻力'}位 ${level.price}`,
          level.strength
        ))
      }
    })
    
    return alerts
  }
  
  /**
   * 分析技术指标预警
   * @param {Object} indicators - 技术指标
   * @param {Object} settings - 敏感度设置
   * @returns {Array} 技术指标预警列表
   */
  const analyzeTechnicalAlerts = (indicators, settings) => {
    const alerts = []
    
    // RSI预警
    if (indicators.rsi && indicators.rsi.length > 0) {
      const latestRSI = indicators.rsi[indicators.rsi.length - 1]
      
      if (latestRSI <= settings.rsiThreshold.oversold) {
        alerts.push(createAlert(
          ALERT_TYPES.RSI_EXTREME,
          { rsi: latestRSI, type: 'oversold' },
          `RSI超卖信号: ${latestRSI.toFixed(2)}`,
          0.7
        ))
      } else if (latestRSI >= settings.rsiThreshold.overbought) {
        alerts.push(createAlert(
          ALERT_TYPES.RSI_EXTREME,
          { rsi: latestRSI, type: 'overbought' },
          `RSI超买信号: ${latestRSI.toFixed(2)}`,
          0.7
        ))
      }
    }
    
    // MACD预警
    if (indicators.macd && indicators.macd.length >= 2) {
      const latest = indicators.macd[indicators.macd.length - 1]
      const previous = indicators.macd[indicators.macd.length - 2]
      
      // 金叉死叉检测
      if (latest.macd > latest.signal && previous.macd <= previous.signal) {
        alerts.push(createAlert(
          ALERT_TYPES.MACD_SIGNAL,
          { type: 'golden_cross', macd: latest.macd, signal: latest.signal },
          'MACD金叉信号',
          0.75
        ))
      } else if (latest.macd < latest.signal && previous.macd >= previous.signal) {
        alerts.push(createAlert(
          ALERT_TYPES.MACD_SIGNAL,
          { type: 'death_cross', macd: latest.macd, signal: latest.signal },
          'MACD死叉信号',
          0.75
        ))
      }
    }
    
    // 趋势反转检测
    const trendReversal = detectTrendReversal(indicators)
    if (trendReversal) {
      alerts.push(createAlert(
        ALERT_TYPES.TREND_REVERSAL,
        trendReversal,
        `检测到${trendReversal.direction}趋势反转信号`,
        trendReversal.confidence
      ))
    }
    
    return alerts
  }
  
  /**
   * 分析成交量预警
   * @param {Object} marketData - 市场数据
   * @param {Object} settings - 敏感度设置
   * @returns {Array} 成交量预警列表
   */
  const analyzeVolumeAlerts = (marketData, settings) => {
    const alerts = []
    const { volumeData, klineData } = marketData
    
    if (!volumeData || volumeData.length < 20) return alerts
    
    const recent = volumeData.slice(-20)
    const avgVolume = recent.slice(0, -1).reduce((sum, vol) => sum + vol, 0) / (recent.length - 1)
    const latestVolume = recent[recent.length - 1]
    
    const volumeRatio = latestVolume / avgVolume
    
    if (volumeRatio > settings.volumeThreshold) {
      const latestCandle = klineData[klineData.length - 1]
      const isGreen = latestCandle.close > latestCandle.open
      
      alerts.push(createAlert(
        ALERT_TYPES.VOLUME_SPIKE,
        {
          volume: latestVolume,
          avgVolume,
          ratio: volumeRatio,
          direction: isGreen ? 'up' : 'down'
        },
        `成交量异常放大 ${(volumeRatio * 100).toFixed(0)}%`,
        Math.min(0.9, volumeRatio / 3)
      ))
    }
    
    return alerts
  }
  
  /**
   * 分析情绪预警
   * @param {Object} sentimentData - 情绪数据
   * @param {Object} settings - 敏感度设置
   * @returns {Array} 情绪预警列表
   */
  const analyzeSentimentAlerts = (sentimentData, settings) => {
    const alerts = []
    
    if (!sentimentData.fearGreedIndex) return alerts
    
    const fearGreedIndex = sentimentData.fearGreedIndex.index
    
    if (fearGreedIndex <= settings.sentimentThreshold.fear) {
      alerts.push(createAlert(
        ALERT_TYPES.SENTIMENT_EXTREME,
        {
          index: fearGreedIndex,
          type: 'extreme_fear',
          level: sentimentData.overallSentiment.level
        },
        `市场极度恐慌 (恐慌贪婪指数: ${fearGreedIndex})`,
        0.8
      ))
    } else if (fearGreedIndex >= settings.sentimentThreshold.greed) {
      alerts.push(createAlert(
        ALERT_TYPES.SENTIMENT_EXTREME,
        {
          index: fearGreedIndex,
          type: 'extreme_greed',
          level: sentimentData.overallSentiment.level
        },
        `市场极度贪婪 (恐慌贪婪指数: ${fearGreedIndex})`,
        0.8
      ))
    }
    
    return alerts
  }
  
  /**
   * 分析形态预警
   * @param {Array} patterns - 识别的形态
   * @returns {Array} 形态预警列表
   */
  const analyzePatternAlerts = (patterns) => {
    const alerts = []
    
    patterns.forEach(pattern => {
      if (pattern.confidence > 0.6) {
        alerts.push(createAlert(
          ALERT_TYPES.PATTERN_DETECTED,
          {
            pattern: pattern.name,
            type: pattern.type,
            confidence: pattern.confidence,
            signal: pattern.signal
          },
          `检测到${pattern.name}形态 (置信度: ${(pattern.confidence * 100).toFixed(0)}%)`,
          pattern.confidence
        ))
      }
    })
    
    return alerts
  }
  
  /**
   * 创建预警对象
   * @param {Object} type - 预警类型
   * @param {Object} data - 预警数据
   * @param {string} message - 预警消息
   * @param {number} confidence - 置信度
   * @returns {Object} 预警对象
   */
  const createAlert = (type, data, message, confidence) => {
    return {
      id: `${type.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      message,
      confidence,
      timestamp: Date.now(),
      status: 'active',
      acknowledged: false,
      priority: type.priority
    }
  }
  
  /**
   * 处理新预警
   * @param {Array} newAlerts - 新预警列表
   */
  const processNewAlerts = (newAlerts) => {
    newAlerts.forEach(alert => {
      // 检查是否重复
      const isDuplicate = activeAlerts.value.some(existing => 
        existing.type.id === alert.type.id && 
        Date.now() - existing.timestamp < 300000 // 5分钟内不重复
      )
      
      if (!isDuplicate) {
        // 添加到活跃预警
        activeAlerts.value.unshift(alert)
        
        // 添加到总预警列表
        alerts.value.unshift(alert)
        
        // 发送通知
        sendNotification(alert)
        
        // 限制活跃预警数量
        if (activeAlerts.value.length > 50) {
          activeAlerts.value = activeAlerts.value.slice(0, 50)
        }
        
        // 限制总预警数量
        if (alerts.value.length > 200) {
          alerts.value = alerts.value.slice(0, 200)
        }
      }
    })
  }
  
  /**
   * 发送通知
   * @param {Object} alert - 预警对象
   */
  const sendNotification = (alert) => {
    const config = {
      message: alert.type.name,
      description: alert.message,
      icon: alert.type.icon,
      duration: alert.priority === 'high' ? 0 : 4.5,
      style: {
        borderLeft: `4px solid ${alert.type.color}`
      }
    }
    
    // 根据优先级选择通知类型
    switch (alert.priority) {
      case 'high':
        notification.error(config)
        break
      case 'medium':
        notification.warning(config)
        break
      default:
        notification.info(config)
    }
    
    // 播放声音
    if (alertSettings.soundEnabled) {
      playAlertSound(alert.priority)
    }
  }
  
  /**
   * 播放预警声音
   * @param {string} priority - 优先级
   */
  const playAlertSound = (priority) => {
    try {
      const audio = new Audio()
      
      // 根据优先级选择不同的声音
      switch (priority) {
        case 'high':
          audio.src = '/sounds/alert-high.mp3'
          break
        case 'medium':
          audio.src = '/sounds/alert-medium.mp3'
          break
        default:
          audio.src = '/sounds/alert-low.mp3'
      }
      
      audio.volume = 0.5
      audio.play().catch(e => console.log('无法播放预警声音:', e))
    } catch (error) {
      console.log('播放预警声音失败:', error)
    }
  }
  
  /**
   * 确认预警
   * @param {string} alertId - 预警ID
   */
  const acknowledgeAlert = (alertId) => {
    const alert = activeAlerts.value.find(a => a.id === alertId)
    if (alert) {
      alert.acknowledged = true
      alert.acknowledgedAt = Date.now()
    }
  }
  
  /**
   * 关闭预警
   * @param {string} alertId - 预警ID
   */
  const dismissAlert = (alertId) => {
    const index = activeAlerts.value.findIndex(a => a.id === alertId)
    if (index !== -1) {
      const alert = activeAlerts.value[index]
      alert.status = 'dismissed'
      alert.dismissedAt = Date.now()
      
      // 移动到历史记录
      alertHistory.value.unshift(alert)
      activeAlerts.value.splice(index, 1)
      
      // 限制历史记录数量
      if (alertHistory.value.length > 500) {
        alertHistory.value = alertHistory.value.slice(0, 500)
      }
    }
  }
  
  /**
   * 清除所有预警
   */
  const clearAllAlerts = () => {
    activeAlerts.value.forEach(alert => {
      alert.status = 'dismissed'
      alert.dismissedAt = Date.now()
      alertHistory.value.unshift(alert)
    })
    
    activeAlerts.value = []
    message.success('已清除所有预警')
  }
  
  /**
   * 计算支撑阻力位
   * @param {Array} klineData - K线数据
   * @returns {Array} 支撑阻力位列表
   */
  const calculateSupportResistance = (klineData) => {
    const levels = []
    
    // 简单的支撑阻力位计算
    const highs = klineData.map(candle => candle.high)
    const lows = klineData.map(candle => candle.low)
    
    // 找出局部高点和低点
    for (let i = 2; i < klineData.length - 2; i++) {
      const high = highs[i]
      const low = lows[i]
      
      // 局部高点（阻力位）
      if (high > highs[i-1] && high > highs[i-2] && high > highs[i+1] && high > highs[i+2]) {
        levels.push({
          price: high,
          type: 'resistance',
          strength: 0.6,
          timestamp: klineData[i].timestamp
        })
      }
      
      // 局部低点（支撑位）
      if (low < lows[i-1] && low < lows[i-2] && low < lows[i+1] && low < lows[i+2]) {
        levels.push({
          price: low,
          type: 'support',
          strength: 0.6,
          timestamp: klineData[i].timestamp
        })
      }
    }
    
    return levels.slice(-10) // 返回最近的10个支撑阻力位
  }
  
  /**
   * 检测趋势反转
   * @param {Object} indicators - 技术指标
   * @returns {Object|null} 趋势反转信号
   */
  const detectTrendReversal = (indicators) => {
    // 这里可以实现更复杂的趋势反转检测逻辑
    // 目前返回null，表示未检测到反转
    return null
  }
  
  /**
   * 更新预警设置
   * @param {Object} newSettings - 新设置
   */
  const updateAlertSettings = (newSettings) => {
    Object.assign(alertSettings, newSettings)
    message.success('预警设置已更新')
  }
  
  /**
   * 获取预警统计
   */
  const getAlertStatistics = computed(() => {
    const total = alerts.value.length
    const active = activeAlerts.value.length
    const acknowledged = activeAlerts.value.filter(a => a.acknowledged).length
    const byPriority = {
      high: alerts.value.filter(a => a.priority === 'high').length,
      medium: alerts.value.filter(a => a.priority === 'medium').length,
      low: alerts.value.filter(a => a.priority === 'low').length
    }
    const byCategory = {}
    
    Object.keys(alertSettings.categories).forEach(category => {
      byCategory[category] = alerts.value.filter(a => a.type.category === category).length
    })
    
    return {
      total,
      active,
      acknowledged,
      dismissed: total - active,
      byPriority,
      byCategory
    }
  })
  
  /**
   * 获取最近的高优先级预警
   */
  const getRecentHighPriorityAlerts = computed(() => {
    return activeAlerts.value
      .filter(alert => alert.priority === 'high')
      .slice(0, 5)
  })
  
  /**
   * 导出预警数据
   * @param {string} format - 导出格式 (json, csv)
   * @returns {string} 导出的数据
   */
  const exportAlerts = (format = 'json') => {
    const data = {
      alerts: alerts.value,
      settings: alertSettings,
      statistics: getAlertStatistics.value,
      exportTime: new Date().toISOString()
    }
    
    if (format === 'json') {
      return JSON.stringify(data, null, 2)
    } else if (format === 'csv') {
      // 简单的CSV导出
      const headers = ['时间', '类型', '消息', '优先级', '置信度', '状态']
      const rows = alerts.value.map(alert => [
        new Date(alert.timestamp).toLocaleString(),
        alert.type.name,
        alert.message,
        alert.priority,
        alert.confidence,
        alert.status
      ])
      
      return [headers, ...rows].map(row => row.join(',')).join('\n')
    }
    
    return JSON.stringify(data)
  }
  
  return {
    // 响应式数据
    alerts,
    activeAlerts,
    alertHistory,
    isMonitoring,
    alertSettings,
    
    // 计算属性
    getAlertStatistics,
    getRecentHighPriorityAlerts,
    
    // 方法
    startMonitoring,
    stopMonitoring,
    analyzeAndGenerateAlerts,
    acknowledgeAlert,
    dismissAlert,
    clearAllAlerts,
    updateAlertSettings,
    exportAlerts,
    
    // 常量
    ALERT_TYPES,
    PRIORITY_LEVELS,
    SENSITIVITY_SETTINGS
  }
}

export default useSmartAlerts