{"name": "web-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:e2e": "playwright test", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/", "prepare": "husky install"}, "dependencies": {"ant-design-vue": "^4.2.6", "echarts": "^5.6.0", "html2canvas": "^1.4.1", "klinecharts": "^9.8.0", "pinia": "^3.0.3", "vue": "^3.4.15", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "@vue/test-utils": "^2.4.6", "eslint": "^8.57.0", "@vue/eslint-config-prettier": "^9.0.0", "eslint-plugin-vue": "^9.20.1", "prettier": "^3.2.5", "husky": "^9.0.11", "lint-staged": "^15.2.2", "@playwright/test": "^1.41.2", "web-vitals": "^3.5.2", "jsdom": "^26.1.0", "vite": "^5.4.19", "vitest": "^1.6.1"}}