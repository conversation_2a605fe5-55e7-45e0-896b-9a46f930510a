/**
 * 性能监控工具
 * 用于实时跟踪图表渲染性能和系统资源使用情况
 */

/**
 * 性能指标收集器
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      chartRenderTime: [],
      memoryUsage: [],
      frameRate: [],
      apiResponseTime: [],
      errorCount: 0,
      lastUpdate: Date.now()
    }
    
    this.observers = []
    this.isMonitoring = false
    this.frameCount = 0
    this.lastFrameTime = performance.now()
  }

  /**
   * 开始性能监控
   */
  startMonitoring() {
    if (this.isMonitoring) return
    
    this.isMonitoring = true
    this.startFrameRateMonitoring()
    this.startMemoryMonitoring()
    
    console.log('性能监控已启动')
  }

  /**
   * 停止性能监控
   */
  stopMonitoring() {
    this.isMonitoring = false
    
    // 清理帧率监控
    if (this.frameRateAnimationId) {
      cancelAnimationFrame(this.frameRateAnimationId)
      this.frameRateAnimationId = null
    }
    
    // 清理内存监控定时器
    if (this.memoryMonitoringTimer) {
      clearTimeout(this.memoryMonitoringTimer)
      this.memoryMonitoringTimer = null
    }
    
    // 清理观察者
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    
    console.log('性能监控已停止')
  }

  /**
   * 记录图表渲染时间
   * @param {string} chartType - 图表类型
   * @param {number} renderTime - 渲染时间（毫秒）
   */
  recordChartRenderTime(chartType, renderTime) {
    const record = {
      type: chartType,
      time: renderTime,
      timestamp: Date.now()
    }
    
    this.metrics.chartRenderTime.push(record)
    
    // 保持最近100条记录
    if (this.metrics.chartRenderTime.length > 100) {
      this.metrics.chartRenderTime.shift()
    }
    
    // 如果渲染时间超过阈值，发出警告
    if (renderTime > 100) {
      console.warn(`图表渲染性能警告: ${chartType} 渲染时间 ${renderTime}ms 超过阈值`)
    }
  }

  /**
   * 记录API响应时间
   * @param {string} apiName - API名称
   * @param {number} responseTime - 响应时间（毫秒）
   */
  recordApiResponseTime(apiName, responseTime) {
    const record = {
      api: apiName,
      time: responseTime,
      timestamp: Date.now()
    }
    
    this.metrics.apiResponseTime.push(record)
    
    // 保持最近50条记录
    if (this.metrics.apiResponseTime.length > 50) {
      this.metrics.apiResponseTime.shift()
    }
  }

  /**
   * 记录错误
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   */
  recordError(error, context) {
    this.metrics.errorCount++
    
    console.error(`性能监控记录错误 [${context}]:`, error)
    
    // 可以在这里添加错误上报逻辑
  }

  /**
   * 开始帧率监控
   */
  startFrameRateMonitoring() {
    // 初始化帧率监控变量
    this.frameCount = 0
    this.lastFrameTime = performance.now()
    this.frameRateAnimationId = null
    
    const measureFrameRate = () => {
      if (!this.isMonitoring) {
        if (this.frameRateAnimationId) {
          cancelAnimationFrame(this.frameRateAnimationId)
          this.frameRateAnimationId = null
        }
        return
      }
      
      const now = performance.now()
      const delta = now - this.lastFrameTime
      
      if (delta >= 1000) { // 每秒计算一次帧率
        // 确保delta不为0，避免除零错误
        const fps = delta > 0 ? Math.round((this.frameCount * 1000) / delta) : 0
        
        this.metrics.frameRate.push({
          fps,
          timestamp: Date.now()
        })
        
        // 保持最近30条记录
        if (this.metrics.frameRate.length > 30) {
          this.metrics.frameRate.shift()
        }
        
        // 重置计数器
        this.frameCount = 0
        this.lastFrameTime = now
        
        // 只有在帧率真正过低且页面可见时才发出警告
        if (fps < 30 && fps > 0 && !document.hidden) {
          console.warn(`帧率性能警告: 当前帧率 ${fps} FPS 低于阈值`)
        }
        
        // 如果帧率为0且页面可见，可能是监控有问题
        if (fps === 0 && !document.hidden && this.frameCount === 0) {
          console.warn('帧率监控异常: 检测到0 FPS，可能是页面未在渲染或监控逻辑有误')
        }
      }
      
      this.frameCount++
      this.frameRateAnimationId = requestAnimationFrame(measureFrameRate)
    }
    
    this.frameRateAnimationId = requestAnimationFrame(measureFrameRate)
  }

  /**
   * 开始内存监控
   */
  startMemoryMonitoring() {
    // 清理之前的定时器
    if (this.memoryMonitoringTimer) {
      clearTimeout(this.memoryMonitoringTimer)
    }
    
    const measureMemory = () => {
      if (!this.isMonitoring) {
        this.memoryMonitoringTimer = null
        return
      }
      
      if (performance.memory) {
        const memory = {
          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024), // MB
          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024), // MB
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024), // MB
          timestamp: Date.now()
        }
        
        this.metrics.memoryUsage.push(memory)
        
        // 保持最近60条记录
        if (this.metrics.memoryUsage.length > 60) {
          this.metrics.memoryUsage.shift()
        }
        
        // 内存使用率警告
        const usageRatio = memory.used / memory.limit
        if (usageRatio > 0.8) {
          console.warn(`内存使用警告: 当前使用 ${memory.used}MB，使用率 ${(usageRatio * 100).toFixed(1)}%`)
        }
      }
      
      this.memoryMonitoringTimer = setTimeout(measureMemory, 5000) // 每5秒检查一次内存
    }
    
    measureMemory()
  }

  /**
   * 获取性能报告
   * @returns {Object} 性能报告
   */
  getPerformanceReport() {
    const now = Date.now()
    const report = {
      timestamp: now,
      uptime: now - this.metrics.lastUpdate,
      charts: this.getChartPerformanceStats(),
      memory: this.getMemoryStats(),
      frameRate: this.getFrameRateStats(),
      api: this.getApiStats(),
      errors: this.metrics.errorCount
    }
    
    return report
  }

  /**
   * 获取图表性能统计
   */
  getChartPerformanceStats() {
    if (this.metrics.chartRenderTime.length === 0) {
      return { average: 0, max: 0, min: 0, count: 0 }
    }
    
    const times = this.metrics.chartRenderTime.map(r => r.time)
    return {
      average: Math.round(times.reduce((a, b) => a + b, 0) / times.length),
      max: Math.max(...times),
      min: Math.min(...times),
      count: times.length
    }
  }

  /**
   * 获取内存统计
   */
  getMemoryStats() {
    if (this.metrics.memoryUsage.length === 0) {
      return { current: 0, peak: 0, average: 0 }
    }
    
    const latest = this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1]
    const used = this.metrics.memoryUsage.map(m => m.used)
    
    return {
      current: latest.used,
      peak: Math.max(...used),
      average: Math.round(used.reduce((a, b) => a + b, 0) / used.length)
    }
  }

  /**
   * 获取帧率统计
   */
  getFrameRateStats() {
    if (this.metrics.frameRate.length === 0) {
      return { current: 0, average: 0, min: 0 }
    }
    
    const latest = this.metrics.frameRate[this.metrics.frameRate.length - 1]
    const fps = this.metrics.frameRate.map(f => f.fps)
    
    return {
      current: latest.fps,
      average: Math.round(fps.reduce((a, b) => a + b, 0) / fps.length),
      min: Math.min(...fps)
    }
  }

  /**
   * 获取API统计
   */
  getApiStats() {
    if (this.metrics.apiResponseTime.length === 0) {
      return { average: 0, max: 0, count: 0 }
    }
    
    const times = this.metrics.apiResponseTime.map(r => r.time)
    return {
      average: Math.round(times.reduce((a, b) => a + b, 0) / times.length),
      max: Math.max(...times),
      count: times.length
    }
  }

  /**
   * 清理性能指标
   */
  clearMetrics() {
    this.metrics = {
      chartRenderTime: [],
      apiResponseTime: [],
      memoryUsage: [],
      frameRate: [],
      errorCount: 0,
      lastUpdate: Date.now()
    }
  }

  /**
   * 诊断性能监控状态
   * @returns {Object} 诊断信息
   */
  diagnose() {
    const diagnosis = {
      isMonitoring: this.isMonitoring,
      frameRateMonitoring: {
        active: !!this.frameRateAnimationId,
        animationId: this.frameRateAnimationId,
        frameCount: this.frameCount,
        lastFrameTime: this.lastFrameTime
      },
      memoryMonitoring: {
        active: !!this.memoryMonitoringTimer,
        timerId: this.memoryMonitoringTimer
      },
      metrics: {
        frameRateRecords: this.metrics.frameRate.length,
        memoryRecords: this.metrics.memoryUsage.length,
        chartRenderRecords: this.metrics.chartRenderTime.length,
        apiRecords: this.metrics.apiResponseTime.length,
        errorCount: this.metrics.errorCount
      },
      pageVisibility: {
        hidden: document.hidden,
        visibilityState: document.visibilityState
      },
      performance: {
        memorySupported: !!performance.memory,
        navigationTiming: !!performance.navigation,
        now: performance.now()
      }
    }
    
    console.log('性能监控诊断信息:', diagnosis)
    return diagnosis
  }

  /**
   * 重启性能监控
   */
  restart() {
    console.log('重启性能监控...')
    this.stopMonitoring()
    setTimeout(() => {
      this.startMonitoring()
      console.log('性能监控已重启')
    }, 100)
  }
}

// 创建全局性能监控实例
const performanceMonitor = new PerformanceMonitor()

// 导出性能监控工具
export default performanceMonitor

/**
 * 性能监控装饰器
 * 用于自动监控函数执行时间
 * @param {string} name - 监控名称
 */
export function withPerformanceMonitoring(name) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function(...args) {
      const startTime = performance.now()
      
      try {
        const result = await originalMethod.apply(this, args)
        const endTime = performance.now()
        
        performanceMonitor.recordChartRenderTime(name, endTime - startTime)
        
        return result
      } catch (error) {
        performanceMonitor.recordError(error, name)
        throw error
      }
    }
    
    return descriptor
  }
}

/**
 * 性能监控函数包装器
 * 用于包装普通函数进行性能监控
 * @param {string} name - 监控名称
 * @param {Function} fn - 要包装的函数
 * @returns {Function} 包装后的函数
 */
export function wrapWithPerformanceMonitoring(name, fn) {
  return async function(...args) {
    const startTime = performance.now()
    
    try {
      const result = await fn.apply(this, args)
      const endTime = performance.now()
      
      performanceMonitor.recordChartRenderTime(name, endTime - startTime)
      
      return result
    } catch (error) {
      performanceMonitor.recordError(error, name)
      throw error
    }
  }
}

/**
 * API性能监控包装器
 * @param {string} apiName - API名称
 * @param {Function} apiFunction - API函数
 */
export function withApiMonitoring(apiName, apiFunction) {
  return async function(...args) {
    const startTime = performance.now()
    
    try {
      const result = await apiFunction.apply(this, args)
      const endTime = performance.now()
      
      performanceMonitor.recordApiResponseTime(apiName, endTime - startTime)
      
      return result
    } catch (error) {
      performanceMonitor.recordError(error, `API: ${apiName}`)
      throw error
    }
  }
}